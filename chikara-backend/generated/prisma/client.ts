
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 * If you're looking for something you can import in the client-side of your application, please refer to the `browser.ts` file instead.
 * 
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
globalThis['__dirname'] = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/client"
import * as $Enums from "./enums.js"
import * as $Class from "./internal/class.js"
import * as Prisma from "./internal/prismaNamespace.js"

export * as $Enums from './enums.js'
export * from "./enums.js"
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Action_logs
 * const action_logs = await prisma.action_log.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<LogOpts extends Prisma.LogLevel = never, OmitOpts extends Prisma.PrismaClientOptions["omit"] = Prisma.PrismaClientOptions["omit"], ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<LogOpts, OmitOpts, ExtArgs>
export { Prisma }



/**
 * Model action_log
 * 
 */
export type action_log = Prisma.action_logModel
/**
 * Model game_config
 * 
 */
export type game_config = Prisma.game_configModel
/**
 * Model auction_item
 * 
 */
export type auction_item = Prisma.auction_itemModel
/**
 * Model bank_transaction
 * 
 */
export type bank_transaction = Prisma.bank_transactionModel
/**
 * Model battle_log
 * 
 */
export type battle_log = Prisma.battle_logModel
/**
 * Model bounty
 * 
 */
export type bounty = Prisma.bountyModel
/**
 * Model chat_message
 * 
 */
export type chat_message = Prisma.chat_messageModel
/**
 * Model chat_room
 * 
 */
export type chat_room = Prisma.chat_roomModel
/**
 * Model crafting_recipe
 * 
 */
export type crafting_recipe = Prisma.crafting_recipeModel
/**
 * Model creature
 * 
 */
export type creature = Prisma.creatureModel
/**
 * Model daily_mission
 * 
 */
export type daily_mission = Prisma.daily_missionModel
/**
 * Model drop_chance
 * 
 */
export type drop_chance = Prisma.drop_chanceModel
/**
 * Model equipped_item
 * 
 */
export type equipped_item = Prisma.equipped_itemModel
/**
 * Model game_stats
 * 
 */
export type game_stats = Prisma.game_statsModel
/**
 * Model gang
 * 
 */
export type gang = Prisma.gangModel
/**
 * Model gang_invite
 * 
 */
export type gang_invite = Prisma.gang_inviteModel
/**
 * Model gang_log
 * 
 */
export type gang_log = Prisma.gang_logModel
/**
 * Model gang_member
 * 
 */
export type gang_member = Prisma.gang_memberModel
/**
 * Model item
 * 
 */
export type item = Prisma.itemModel
/**
 * Model job
 * 
 */
export type job = Prisma.jobModel
/**
 * Model lottery
 * 
 */
export type lottery = Prisma.lotteryModel
/**
 * Model lottery_entry
 * 
 */
export type lottery_entry = Prisma.lottery_entryModel
/**
 * Model notification
 * 
 */
export type notification = Prisma.notificationModel
/**
 * Model poll
 * 
 */
export type poll = Prisma.pollModel
/**
 * Model poll_response
 * 
 */
export type poll_response = Prisma.poll_responseModel
/**
 * Model private_message
 * 
 */
export type private_message = Prisma.private_messageModel
/**
 * Model profile_comment
 * 
 */
export type profile_comment = Prisma.profile_commentModel
/**
 * Model push_token
 * 
 */
export type push_token = Prisma.push_tokenModel
/**
 * Model daily_quest
 * 
 */
export type daily_quest = Prisma.daily_questModel
/**
 * Model quest
 * 
 */
export type quest = Prisma.questModel
/**
 * Model quest_progress
 * 
 */
export type quest_progress = Prisma.quest_progressModel
/**
 * Model quest_objective
 * 
 */
export type quest_objective = Prisma.quest_objectiveModel
/**
 * Model quest_objective_progress
 * 
 */
export type quest_objective_progress = Prisma.quest_objective_progressModel
/**
 * Model quest_reward
 * 
 */
export type quest_reward = Prisma.quest_rewardModel
/**
 * Model recipe_item
 * 
 */
export type recipe_item = Prisma.recipe_itemModel
/**
 * Model registration_code
 * 
 */
export type registration_code = Prisma.registration_codeModel
/**
 * Model shop
 * 
 */
export type shop = Prisma.shopModel
/**
 * Model shop_listing
 * 
 */
export type shop_listing = Prisma.shop_listingModel
/**
 * Model shrine_donation
 * 
 */
export type shrine_donation = Prisma.shrine_donationModel
/**
 * Model shrine_goal
 * 
 */
export type shrine_goal = Prisma.shrine_goalModel
/**
 * Model suggestion
 * 
 */
export type suggestion = Prisma.suggestionModel
/**
 * Model suggestion_comment
 * 
 */
export type suggestion_comment = Prisma.suggestion_commentModel
/**
 * Model suggestion_vote
 * 
 */
export type suggestion_vote = Prisma.suggestion_voteModel
/**
 * Model talent
 * 
 */
export type talent = Prisma.talentModel
/**
 * Model trader_rep
 * 
 */
export type trader_rep = Prisma.trader_repModel
/**
 * Model user
 * 
 */
export type user = Prisma.userModel
/**
 * Model user_equipped_abilities
 * 
 */
export type user_equipped_abilities = Prisma.user_equipped_abilitiesModel
/**
 * Model session
 * 
 */
export type session = Prisma.sessionModel
/**
 * Model account
 * 
 */
export type account = Prisma.accountModel
/**
 * Model verification
 * 
 */
export type verification = Prisma.verificationModel
/**
 * Model user_item
 * 
 */
export type user_item = Prisma.user_itemModel
/**
 * Model user_recipe
 * 
 */
export type user_recipe = Prisma.user_recipeModel
/**
 * Model user_completed_course
 * 
 */
export type user_completed_course = Prisma.user_completed_courseModel
/**
 * Model user_talent
 * 
 */
export type user_talent = Prisma.user_talentModel
/**
 * Model user_achievements
 * 
 */
export type user_achievements = Prisma.user_achievementsModel
/**
 * Model user_crafting_queue
 * 
 */
export type user_crafting_queue = Prisma.user_crafting_queueModel
/**
 * Model property
 * 
 */
export type property = Prisma.propertyModel
/**
 * Model user_property
 * 
 */
export type user_property = Prisma.user_propertyModel
/**
 * Model pet
 * 
 */
export type pet = Prisma.petModel
/**
 * Model user_pet
 * 
 */
export type user_pet = Prisma.user_petModel
/**
 * Model friend_request
 * 
 */
export type friend_request = Prisma.friend_requestModel
/**
 * Model friend
 * 
 */
export type friend = Prisma.friendModel
/**
 * Model rival
 * 
 */
export type rival = Prisma.rivalModel
/**
 * Model status_effect
 * 
 */
export type status_effect = Prisma.status_effectModel
/**
 * Model user_status_effect
 * 
 */
export type user_status_effect = Prisma.user_status_effectModel
/**
 * Model user_skill
 * 
 */
export type user_skill = Prisma.user_skillModel
/**
 * Model explore_static_node
 * 
 */
export type explore_static_node = Prisma.explore_static_nodeModel
/**
 * Model explore_player_node
 * 
 */
export type explore_player_node = Prisma.explore_player_nodeModel
/**
 * Model story_season
 * 
 */
export type story_season = Prisma.story_seasonModel
/**
 * Model story_chapter
 * 
 */
export type story_chapter = Prisma.story_chapterModel
/**
 * Model story_episode
 * 
 */
export type story_episode = Prisma.story_episodeModel
