
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports various common sort, input & filter types that are not directly linked to a particular model.
 *
 * 🟢 You can import this file directly.
 */

import type * as runtime from "@prisma/client/runtime/client"
import * as $Enums from "./enums.js"
import type * as Prisma from "./internal/prismaNamespace.js"


export type IntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type StringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | null
  notIn?: string[] | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type JsonFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

export type JsonFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type DateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[]
  notIn?: Date[] | string[]
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type IntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type SortOrderInput = {
  sort: Prisma.SortOrder
  nulls?: Prisma.NullsOrder
}

export type IntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | null
  notIn?: string[] | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type JsonWithAggregatesFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonWithAggregatesFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedJsonFilter<$PrismaModel>
  _max?: Prisma.NestedJsonFilter<$PrismaModel>
}

export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[]
  notIn?: Date[] | string[]
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type StringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[]
  notIn?: string[]
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type BoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type StringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[]
  notIn?: string[]
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type BoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type EnumAuctionItemStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.AuctionItemStatus | Prisma.EnumAuctionItemStatusFieldRefInput<$PrismaModel>
  in?: $Enums.AuctionItemStatus[]
  notIn?: $Enums.AuctionItemStatus[]
  not?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel> | $Enums.AuctionItemStatus
}

export type EnumAuctionItemStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.AuctionItemStatus | Prisma.EnumAuctionItemStatusFieldRefInput<$PrismaModel>
  in?: $Enums.AuctionItemStatus[]
  notIn?: $Enums.AuctionItemStatus[]
  not?: Prisma.NestedEnumAuctionItemStatusWithAggregatesFilter<$PrismaModel> | $Enums.AuctionItemStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel>
}

export type EnumBankTransactionTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.BankTransactionTypes | Prisma.EnumBankTransactionTypesFieldRefInput<$PrismaModel>
  in?: $Enums.BankTransactionTypes[]
  notIn?: $Enums.BankTransactionTypes[]
  not?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel> | $Enums.BankTransactionTypes
}

export type EnumBankTransactionTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.BankTransactionTypes | Prisma.EnumBankTransactionTypesFieldRefInput<$PrismaModel>
  in?: $Enums.BankTransactionTypes[]
  notIn?: $Enums.BankTransactionTypes[]
  not?: Prisma.NestedEnumBankTransactionTypesWithAggregatesFilter<$PrismaModel> | $Enums.BankTransactionTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel>
}

export type BoolNullableFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableFilter<$PrismaModel> | boolean | null
}

export type BoolNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBoolNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBoolNullableFilter<$PrismaModel>
}

export type EnumCraftingSkillsNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.CraftingSkills | Prisma.EnumCraftingSkillsFieldRefInput<$PrismaModel> | null
  in?: $Enums.CraftingSkills[] | null
  notIn?: $Enums.CraftingSkills[] | null
  not?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel> | $Enums.CraftingSkills | null
}

export type EnumCraftingSkillsNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.CraftingSkills | Prisma.EnumCraftingSkillsFieldRefInput<$PrismaModel> | null
  in?: $Enums.CraftingSkills[] | null
  notIn?: $Enums.CraftingSkills[] | null
  not?: Prisma.NestedEnumCraftingSkillsNullableWithAggregatesFilter<$PrismaModel> | $Enums.CraftingSkills | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel>
}

export type EnumLocationTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.LocationTypes | Prisma.EnumLocationTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.LocationTypes[] | null
  notIn?: $Enums.LocationTypes[] | null
  not?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel> | $Enums.LocationTypes | null
}

export type EnumCreatureStatTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.CreatureStatTypes | Prisma.EnumCreatureStatTypesFieldRefInput<$PrismaModel>
  in?: $Enums.CreatureStatTypes[]
  notIn?: $Enums.CreatureStatTypes[]
  not?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel> | $Enums.CreatureStatTypes
}

export type EnumLocationTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LocationTypes | Prisma.EnumLocationTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.LocationTypes[] | null
  notIn?: $Enums.LocationTypes[] | null
  not?: Prisma.NestedEnumLocationTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.LocationTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel>
}

export type EnumCreatureStatTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.CreatureStatTypes | Prisma.EnumCreatureStatTypesFieldRefInput<$PrismaModel>
  in?: $Enums.CreatureStatTypes[]
  notIn?: $Enums.CreatureStatTypes[]
  not?: Prisma.NestedEnumCreatureStatTypesWithAggregatesFilter<$PrismaModel> | $Enums.CreatureStatTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel>
}

export type FloatFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatFilter<$PrismaModel> | number
}

export type EnumDropChanceTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.DropChanceTypes | Prisma.EnumDropChanceTypesFieldRefInput<$PrismaModel>
  in?: $Enums.DropChanceTypes[]
  notIn?: $Enums.DropChanceTypes[]
  not?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel> | $Enums.DropChanceTypes
}

export type FloatWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedFloatFilter<$PrismaModel>
  _min?: Prisma.NestedFloatFilter<$PrismaModel>
  _max?: Prisma.NestedFloatFilter<$PrismaModel>
}

export type EnumDropChanceTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DropChanceTypes | Prisma.EnumDropChanceTypesFieldRefInput<$PrismaModel>
  in?: $Enums.DropChanceTypes[]
  notIn?: $Enums.DropChanceTypes[]
  not?: Prisma.NestedEnumDropChanceTypesWithAggregatesFilter<$PrismaModel> | $Enums.DropChanceTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel>
}

export type EnumEquipSlotsFilter<$PrismaModel = never> = {
  equals?: $Enums.EquipSlots | Prisma.EnumEquipSlotsFieldRefInput<$PrismaModel>
  in?: $Enums.EquipSlots[]
  notIn?: $Enums.EquipSlots[]
  not?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel> | $Enums.EquipSlots
}

export type EnumEquipSlotsWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.EquipSlots | Prisma.EnumEquipSlotsFieldRefInput<$PrismaModel>
  in?: $Enums.EquipSlots[]
  notIn?: $Enums.EquipSlots[]
  not?: Prisma.NestedEnumEquipSlotsWithAggregatesFilter<$PrismaModel> | $Enums.EquipSlots
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel>
  _max?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel>
}

export type EnumGangInviteTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.GangInviteTypes | Prisma.EnumGangInviteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.GangInviteTypes[]
  notIn?: $Enums.GangInviteTypes[]
  not?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel> | $Enums.GangInviteTypes
}

export type EnumGangInviteTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.GangInviteTypes | Prisma.EnumGangInviteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.GangInviteTypes[]
  notIn?: $Enums.GangInviteTypes[]
  not?: Prisma.NestedEnumGangInviteTypesWithAggregatesFilter<$PrismaModel> | $Enums.GangInviteTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel>
}

export type EnumItemTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemTypes | Prisma.EnumItemTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemTypes[]
  notIn?: $Enums.ItemTypes[]
  not?: Prisma.NestedEnumItemTypesFilter<$PrismaModel> | $Enums.ItemTypes
}

export type EnumItemRaritiesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemRarities | Prisma.EnumItemRaritiesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemRarities[]
  notIn?: $Enums.ItemRarities[]
  not?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel> | $Enums.ItemRarities
}

export type JsonNullableFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonNullableFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

export type JsonNullableFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type EnumItemTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemTypes | Prisma.EnumItemTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemTypes[]
  notIn?: $Enums.ItemTypes[]
  not?: Prisma.NestedEnumItemTypesWithAggregatesFilter<$PrismaModel> | $Enums.ItemTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemTypesFilter<$PrismaModel>
}

export type EnumItemRaritiesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemRarities | Prisma.EnumItemRaritiesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemRarities[]
  notIn?: $Enums.ItemRarities[]
  not?: Prisma.NestedEnumItemRaritiesWithAggregatesFilter<$PrismaModel> | $Enums.ItemRarities
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel>
}

export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedJsonNullableFilter<$PrismaModel>
  _max?: Prisma.NestedJsonNullableFilter<$PrismaModel>
}

export type EnumQuestObjectiveTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestObjectiveTypes | Prisma.EnumQuestObjectiveTypesFieldRefInput<$PrismaModel>
  in?: $Enums.QuestObjectiveTypes[]
  notIn?: $Enums.QuestObjectiveTypes[]
  not?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel> | $Enums.QuestObjectiveTypes
}

export type EnumQuestProgressStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestProgressStatus | Prisma.EnumQuestProgressStatusFieldRefInput<$PrismaModel>
  in?: $Enums.QuestProgressStatus[]
  notIn?: $Enums.QuestProgressStatus[]
  not?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel> | $Enums.QuestProgressStatus
}

export type EnumQuestObjectiveTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestObjectiveTypes | Prisma.EnumQuestObjectiveTypesFieldRefInput<$PrismaModel>
  in?: $Enums.QuestObjectiveTypes[]
  notIn?: $Enums.QuestObjectiveTypes[]
  not?: Prisma.NestedEnumQuestObjectiveTypesWithAggregatesFilter<$PrismaModel> | $Enums.QuestObjectiveTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel>
}

export type EnumQuestProgressStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestProgressStatus | Prisma.EnumQuestProgressStatusFieldRefInput<$PrismaModel>
  in?: $Enums.QuestProgressStatus[]
  notIn?: $Enums.QuestProgressStatus[]
  not?: Prisma.NestedEnumQuestProgressStatusWithAggregatesFilter<$PrismaModel> | $Enums.QuestProgressStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel>
}

export type EnumQuestRewardTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestRewardType | Prisma.EnumQuestRewardTypeFieldRefInput<$PrismaModel>
  in?: $Enums.QuestRewardType[]
  notIn?: $Enums.QuestRewardType[]
  not?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel> | $Enums.QuestRewardType
}

export type EnumQuestRewardTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestRewardType | Prisma.EnumQuestRewardTypeFieldRefInput<$PrismaModel>
  in?: $Enums.QuestRewardType[]
  notIn?: $Enums.QuestRewardType[]
  not?: Prisma.NestedEnumQuestRewardTypeWithAggregatesFilter<$PrismaModel> | $Enums.QuestRewardType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel>
}

export type EnumRecipeItemTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.RecipeItemTypes | Prisma.EnumRecipeItemTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.RecipeItemTypes[] | null
  notIn?: $Enums.RecipeItemTypes[] | null
  not?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel> | $Enums.RecipeItemTypes | null
}

export type EnumRecipeItemTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RecipeItemTypes | Prisma.EnumRecipeItemTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.RecipeItemTypes[] | null
  notIn?: $Enums.RecipeItemTypes[] | null
  not?: Prisma.NestedEnumRecipeItemTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.RecipeItemTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel>
}

export type EnumShopTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopTypes | Prisma.EnumShopTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ShopTypes[]
  notIn?: $Enums.ShopTypes[]
  not?: Prisma.NestedEnumShopTypesFilter<$PrismaModel> | $Enums.ShopTypes
}

export type EnumShopTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopTypes | Prisma.EnumShopTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ShopTypes[]
  notIn?: $Enums.ShopTypes[]
  not?: Prisma.NestedEnumShopTypesWithAggregatesFilter<$PrismaModel> | $Enums.ShopTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumShopTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumShopTypesFilter<$PrismaModel>
}

export type EnumShopListingCurrencyFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopListingCurrency | Prisma.EnumShopListingCurrencyFieldRefInput<$PrismaModel>
  in?: $Enums.ShopListingCurrency[]
  notIn?: $Enums.ShopListingCurrency[]
  not?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel> | $Enums.ShopListingCurrency
}

export type EnumShopListingCurrencyWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopListingCurrency | Prisma.EnumShopListingCurrencyFieldRefInput<$PrismaModel>
  in?: $Enums.ShopListingCurrency[]
  notIn?: $Enums.ShopListingCurrency[]
  not?: Prisma.NestedEnumShopListingCurrencyWithAggregatesFilter<$PrismaModel> | $Enums.ShopListingCurrency
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel>
  _max?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel>
}

export type EnumSuggestionStatesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionStates | Prisma.EnumSuggestionStatesFieldRefInput<$PrismaModel> | null
  in?: $Enums.SuggestionStates[] | null
  notIn?: $Enums.SuggestionStates[] | null
  not?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel> | $Enums.SuggestionStates | null
}

export type EnumSuggestionStatesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionStates | Prisma.EnumSuggestionStatesFieldRefInput<$PrismaModel> | null
  in?: $Enums.SuggestionStates[] | null
  notIn?: $Enums.SuggestionStates[] | null
  not?: Prisma.NestedEnumSuggestionStatesNullableWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionStates | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel>
}

export type EnumSuggestionVoteTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionVoteTypes | Prisma.EnumSuggestionVoteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.SuggestionVoteTypes[]
  notIn?: $Enums.SuggestionVoteTypes[]
  not?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel> | $Enums.SuggestionVoteTypes
}

export type EnumSuggestionVoteTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionVoteTypes | Prisma.EnumSuggestionVoteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.SuggestionVoteTypes[]
  notIn?: $Enums.SuggestionVoteTypes[]
  not?: Prisma.NestedEnumSuggestionVoteTypesWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionVoteTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel>
}

export type EnumTalentTreeFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentTree | Prisma.EnumTalentTreeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentTree[]
  notIn?: $Enums.TalentTree[]
  not?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel> | $Enums.TalentTree
}

export type EnumTalentTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentType | Prisma.EnumTalentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentType[]
  notIn?: $Enums.TalentType[]
  not?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel> | $Enums.TalentType
}

export type FloatNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableFilter<$PrismaModel> | number | null
}

export type EnumTalentTreeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentTree | Prisma.EnumTalentTreeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentTree[]
  notIn?: $Enums.TalentTree[]
  not?: Prisma.NestedEnumTalentTreeWithAggregatesFilter<$PrismaModel> | $Enums.TalentTree
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel>
}

export type EnumTalentTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentType | Prisma.EnumTalentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentType[]
  notIn?: $Enums.TalentType[]
  not?: Prisma.NestedEnumTalentTypeWithAggregatesFilter<$PrismaModel> | $Enums.TalentType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel>
}

export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _min?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _max?: Prisma.NestedFloatNullableFilter<$PrismaModel>
}

export type DateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | null
  notIn?: Date[] | string[] | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type BigIntNullableFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel> | null
  in?: bigint[] | number[] | null
  notIn?: bigint[] | number[] | null
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntNullableFilter<$PrismaModel> | bigint | number | null
}

export type EnumUserTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.UserTypes | Prisma.EnumUserTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.UserTypes[] | null
  notIn?: $Enums.UserTypes[] | null
  not?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel> | $Enums.UserTypes | null
}

export type EnumHospitalisedHealingTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.HospitalisedHealingTypes | Prisma.EnumHospitalisedHealingTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.HospitalisedHealingTypes[] | null
  notIn?: $Enums.HospitalisedHealingTypes[] | null
  not?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel> | $Enums.HospitalisedHealingTypes | null
}

export type EnumExploreNodeLocationNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel> | null
  in?: $Enums.ExploreNodeLocation[] | null
  notIn?: $Enums.ExploreNodeLocation[] | null
  not?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel> | $Enums.ExploreNodeLocation | null
}

export type EnumTravelMethodNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.TravelMethod | Prisma.EnumTravelMethodFieldRefInput<$PrismaModel> | null
  in?: $Enums.TravelMethod[] | null
  notIn?: $Enums.TravelMethod[] | null
  not?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel> | $Enums.TravelMethod | null
}

export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | null
  notIn?: Date[] | string[] | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type BigIntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel> | null
  in?: bigint[] | number[] | null
  notIn?: bigint[] | number[] | null
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntNullableWithAggregatesFilter<$PrismaModel> | bigint | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
}

export type EnumUserTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.UserTypes | Prisma.EnumUserTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.UserTypes[] | null
  notIn?: $Enums.UserTypes[] | null
  not?: Prisma.NestedEnumUserTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.UserTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel>
}

export type EnumHospitalisedHealingTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.HospitalisedHealingTypes | Prisma.EnumHospitalisedHealingTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.HospitalisedHealingTypes[] | null
  notIn?: $Enums.HospitalisedHealingTypes[] | null
  not?: Prisma.NestedEnumHospitalisedHealingTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.HospitalisedHealingTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel>
}

export type EnumExploreNodeLocationNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel> | null
  in?: $Enums.ExploreNodeLocation[] | null
  notIn?: $Enums.ExploreNodeLocation[] | null
  not?: Prisma.NestedEnumExploreNodeLocationNullableWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeLocation | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel>
}

export type EnumTravelMethodNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TravelMethod | Prisma.EnumTravelMethodFieldRefInput<$PrismaModel> | null
  in?: $Enums.TravelMethod[] | null
  notIn?: $Enums.TravelMethod[] | null
  not?: Prisma.NestedEnumTravelMethodNullableWithAggregatesFilter<$PrismaModel> | $Enums.TravelMethod | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel>
}

export type EnumItemQualityFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemQuality | Prisma.EnumItemQualityFieldRefInput<$PrismaModel>
  in?: $Enums.ItemQuality[]
  notIn?: $Enums.ItemQuality[]
  not?: Prisma.NestedEnumItemQualityFilter<$PrismaModel> | $Enums.ItemQuality
}

export type EnumItemQualityWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemQuality | Prisma.EnumItemQualityFieldRefInput<$PrismaModel>
  in?: $Enums.ItemQuality[]
  notIn?: $Enums.ItemQuality[]
  not?: Prisma.NestedEnumItemQualityWithAggregatesFilter<$PrismaModel> | $Enums.ItemQuality
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemQualityFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemQualityFilter<$PrismaModel>
}

export type BigIntFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  in?: bigint[] | number[]
  notIn?: bigint[] | number[]
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntFilter<$PrismaModel> | bigint | number
}

export type BigIntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  in?: bigint[] | number[]
  notIn?: bigint[] | number[]
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntWithAggregatesFilter<$PrismaModel> | bigint | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedBigIntFilter<$PrismaModel>
  _min?: Prisma.NestedBigIntFilter<$PrismaModel>
  _max?: Prisma.NestedBigIntFilter<$PrismaModel>
}

export type EnumStatusEffectTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectType | Prisma.EnumStatusEffectTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectType[]
  notIn?: $Enums.StatusEffectType[]
  not?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel> | $Enums.StatusEffectType
}

export type EnumStatusEffectTierNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectTier | Prisma.EnumStatusEffectTierFieldRefInput<$PrismaModel> | null
  in?: $Enums.StatusEffectTier[] | null
  notIn?: $Enums.StatusEffectTier[] | null
  not?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel> | $Enums.StatusEffectTier | null
}

export type EnumStatusEffectModifierTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectModifierType | Prisma.EnumStatusEffectModifierTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectModifierType[]
  notIn?: $Enums.StatusEffectModifierType[]
  not?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel> | $Enums.StatusEffectModifierType
}

export type EnumStatusEffectTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectType | Prisma.EnumStatusEffectTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectType[]
  notIn?: $Enums.StatusEffectType[]
  not?: Prisma.NestedEnumStatusEffectTypeWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel>
}

export type EnumStatusEffectTierNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectTier | Prisma.EnumStatusEffectTierFieldRefInput<$PrismaModel> | null
  in?: $Enums.StatusEffectTier[] | null
  notIn?: $Enums.StatusEffectTier[] | null
  not?: Prisma.NestedEnumStatusEffectTierNullableWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectTier | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel>
}

export type EnumStatusEffectModifierTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectModifierType | Prisma.EnumStatusEffectModifierTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectModifierType[]
  notIn?: $Enums.StatusEffectModifierType[]
  not?: Prisma.NestedEnumStatusEffectModifierTypeWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectModifierType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel>
}

export type EnumSkillTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.SkillType | Prisma.EnumSkillTypeFieldRefInput<$PrismaModel>
  in?: $Enums.SkillType[]
  notIn?: $Enums.SkillType[]
  not?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel> | $Enums.SkillType
}

export type EnumSkillTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SkillType | Prisma.EnumSkillTypeFieldRefInput<$PrismaModel>
  in?: $Enums.SkillType[]
  notIn?: $Enums.SkillType[]
  not?: Prisma.NestedEnumSkillTypeWithAggregatesFilter<$PrismaModel> | $Enums.SkillType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel>
}

export type EnumExploreNodeTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeType | Prisma.EnumExploreNodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeType[]
  notIn?: $Enums.ExploreNodeType[]
  not?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel> | $Enums.ExploreNodeType
}

export type EnumExploreNodeLocationFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeLocation[]
  notIn?: $Enums.ExploreNodeLocation[]
  not?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel> | $Enums.ExploreNodeLocation
}

export type EnumExploreNodeTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeType | Prisma.EnumExploreNodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeType[]
  notIn?: $Enums.ExploreNodeType[]
  not?: Prisma.NestedEnumExploreNodeTypeWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel>
}

export type EnumExploreNodeLocationWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeLocation[]
  notIn?: $Enums.ExploreNodeLocation[]
  not?: Prisma.NestedEnumExploreNodeLocationWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeLocation
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel>
}

export type EnumExploreNodeStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeStatus | Prisma.EnumExploreNodeStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeStatus[]
  notIn?: $Enums.ExploreNodeStatus[]
  not?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel> | $Enums.ExploreNodeStatus
}

export type EnumExploreNodeStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeStatus | Prisma.EnumExploreNodeStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeStatus[]
  notIn?: $Enums.ExploreNodeStatus[]
  not?: Prisma.NestedEnumExploreNodeStatusWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel>
}

export type EnumStoryEpisodeTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StoryEpisodeType | Prisma.EnumStoryEpisodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StoryEpisodeType[]
  notIn?: $Enums.StoryEpisodeType[]
  not?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel> | $Enums.StoryEpisodeType
}

export type EnumStoryEpisodeTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StoryEpisodeType | Prisma.EnumStoryEpisodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StoryEpisodeType[]
  notIn?: $Enums.StoryEpisodeType[]
  not?: Prisma.NestedEnumStoryEpisodeTypeWithAggregatesFilter<$PrismaModel> | $Enums.StoryEpisodeType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel>
}

export type NestedIntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type NestedStringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | null
  notIn?: string[] | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type NestedDateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[]
  notIn?: Date[] | string[]
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type NestedIntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type NestedFloatFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatFilter<$PrismaModel> | number
}

export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | null
  notIn?: string[] | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type NestedJsonFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
    Required<NestedJsonFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

export type NestedJsonFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[]
  notIn?: Date[] | string[]
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type NestedFloatNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableFilter<$PrismaModel> | number | null
}

export type NestedStringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[]
  notIn?: string[]
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type NestedBoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[]
  notIn?: string[]
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  search?: string
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type NestedEnumAuctionItemStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.AuctionItemStatus | Prisma.EnumAuctionItemStatusFieldRefInput<$PrismaModel>
  in?: $Enums.AuctionItemStatus[]
  notIn?: $Enums.AuctionItemStatus[]
  not?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel> | $Enums.AuctionItemStatus
}

export type NestedEnumAuctionItemStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.AuctionItemStatus | Prisma.EnumAuctionItemStatusFieldRefInput<$PrismaModel>
  in?: $Enums.AuctionItemStatus[]
  notIn?: $Enums.AuctionItemStatus[]
  not?: Prisma.NestedEnumAuctionItemStatusWithAggregatesFilter<$PrismaModel> | $Enums.AuctionItemStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumAuctionItemStatusFilter<$PrismaModel>
}

export type NestedEnumBankTransactionTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.BankTransactionTypes | Prisma.EnumBankTransactionTypesFieldRefInput<$PrismaModel>
  in?: $Enums.BankTransactionTypes[]
  notIn?: $Enums.BankTransactionTypes[]
  not?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel> | $Enums.BankTransactionTypes
}

export type NestedEnumBankTransactionTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.BankTransactionTypes | Prisma.EnumBankTransactionTypesFieldRefInput<$PrismaModel>
  in?: $Enums.BankTransactionTypes[]
  notIn?: $Enums.BankTransactionTypes[]
  not?: Prisma.NestedEnumBankTransactionTypesWithAggregatesFilter<$PrismaModel> | $Enums.BankTransactionTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumBankTransactionTypesFilter<$PrismaModel>
}

export type NestedBoolNullableFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableFilter<$PrismaModel> | boolean | null
}

export type NestedBoolNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBoolNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBoolNullableFilter<$PrismaModel>
}

export type NestedEnumCraftingSkillsNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.CraftingSkills | Prisma.EnumCraftingSkillsFieldRefInput<$PrismaModel> | null
  in?: $Enums.CraftingSkills[] | null
  notIn?: $Enums.CraftingSkills[] | null
  not?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel> | $Enums.CraftingSkills | null
}

export type NestedEnumCraftingSkillsNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.CraftingSkills | Prisma.EnumCraftingSkillsFieldRefInput<$PrismaModel> | null
  in?: $Enums.CraftingSkills[] | null
  notIn?: $Enums.CraftingSkills[] | null
  not?: Prisma.NestedEnumCraftingSkillsNullableWithAggregatesFilter<$PrismaModel> | $Enums.CraftingSkills | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumCraftingSkillsNullableFilter<$PrismaModel>
}

export type NestedEnumLocationTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.LocationTypes | Prisma.EnumLocationTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.LocationTypes[] | null
  notIn?: $Enums.LocationTypes[] | null
  not?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel> | $Enums.LocationTypes | null
}

export type NestedEnumCreatureStatTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.CreatureStatTypes | Prisma.EnumCreatureStatTypesFieldRefInput<$PrismaModel>
  in?: $Enums.CreatureStatTypes[]
  notIn?: $Enums.CreatureStatTypes[]
  not?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel> | $Enums.CreatureStatTypes
}

export type NestedEnumLocationTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LocationTypes | Prisma.EnumLocationTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.LocationTypes[] | null
  notIn?: $Enums.LocationTypes[] | null
  not?: Prisma.NestedEnumLocationTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.LocationTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLocationTypesNullableFilter<$PrismaModel>
}

export type NestedEnumCreatureStatTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.CreatureStatTypes | Prisma.EnumCreatureStatTypesFieldRefInput<$PrismaModel>
  in?: $Enums.CreatureStatTypes[]
  notIn?: $Enums.CreatureStatTypes[]
  not?: Prisma.NestedEnumCreatureStatTypesWithAggregatesFilter<$PrismaModel> | $Enums.CreatureStatTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumCreatureStatTypesFilter<$PrismaModel>
}

export type NestedEnumDropChanceTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.DropChanceTypes | Prisma.EnumDropChanceTypesFieldRefInput<$PrismaModel>
  in?: $Enums.DropChanceTypes[]
  notIn?: $Enums.DropChanceTypes[]
  not?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel> | $Enums.DropChanceTypes
}

export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[]
  notIn?: number[]
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedFloatFilter<$PrismaModel>
  _min?: Prisma.NestedFloatFilter<$PrismaModel>
  _max?: Prisma.NestedFloatFilter<$PrismaModel>
}

export type NestedEnumDropChanceTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DropChanceTypes | Prisma.EnumDropChanceTypesFieldRefInput<$PrismaModel>
  in?: $Enums.DropChanceTypes[]
  notIn?: $Enums.DropChanceTypes[]
  not?: Prisma.NestedEnumDropChanceTypesWithAggregatesFilter<$PrismaModel> | $Enums.DropChanceTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDropChanceTypesFilter<$PrismaModel>
}

export type NestedEnumEquipSlotsFilter<$PrismaModel = never> = {
  equals?: $Enums.EquipSlots | Prisma.EnumEquipSlotsFieldRefInput<$PrismaModel>
  in?: $Enums.EquipSlots[]
  notIn?: $Enums.EquipSlots[]
  not?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel> | $Enums.EquipSlots
}

export type NestedEnumEquipSlotsWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.EquipSlots | Prisma.EnumEquipSlotsFieldRefInput<$PrismaModel>
  in?: $Enums.EquipSlots[]
  notIn?: $Enums.EquipSlots[]
  not?: Prisma.NestedEnumEquipSlotsWithAggregatesFilter<$PrismaModel> | $Enums.EquipSlots
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel>
  _max?: Prisma.NestedEnumEquipSlotsFilter<$PrismaModel>
}

export type NestedEnumGangInviteTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.GangInviteTypes | Prisma.EnumGangInviteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.GangInviteTypes[]
  notIn?: $Enums.GangInviteTypes[]
  not?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel> | $Enums.GangInviteTypes
}

export type NestedEnumGangInviteTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.GangInviteTypes | Prisma.EnumGangInviteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.GangInviteTypes[]
  notIn?: $Enums.GangInviteTypes[]
  not?: Prisma.NestedEnumGangInviteTypesWithAggregatesFilter<$PrismaModel> | $Enums.GangInviteTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumGangInviteTypesFilter<$PrismaModel>
}

export type NestedEnumItemTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemTypes | Prisma.EnumItemTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemTypes[]
  notIn?: $Enums.ItemTypes[]
  not?: Prisma.NestedEnumItemTypesFilter<$PrismaModel> | $Enums.ItemTypes
}

export type NestedEnumItemRaritiesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemRarities | Prisma.EnumItemRaritiesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemRarities[]
  notIn?: $Enums.ItemRarities[]
  not?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel> | $Enums.ItemRarities
}

export type NestedEnumItemTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemTypes | Prisma.EnumItemTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemTypes[]
  notIn?: $Enums.ItemTypes[]
  not?: Prisma.NestedEnumItemTypesWithAggregatesFilter<$PrismaModel> | $Enums.ItemTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemTypesFilter<$PrismaModel>
}

export type NestedEnumItemRaritiesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemRarities | Prisma.EnumItemRaritiesFieldRefInput<$PrismaModel>
  in?: $Enums.ItemRarities[]
  notIn?: $Enums.ItemRarities[]
  not?: Prisma.NestedEnumItemRaritiesWithAggregatesFilter<$PrismaModel> | $Enums.ItemRarities
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemRaritiesFilter<$PrismaModel>
}

export type NestedJsonNullableFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
    Required<NestedJsonNullableFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue
  lte?: runtime.InputJsonValue
  gt?: runtime.InputJsonValue
  gte?: runtime.InputJsonValue
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type NestedEnumQuestObjectiveTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestObjectiveTypes | Prisma.EnumQuestObjectiveTypesFieldRefInput<$PrismaModel>
  in?: $Enums.QuestObjectiveTypes[]
  notIn?: $Enums.QuestObjectiveTypes[]
  not?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel> | $Enums.QuestObjectiveTypes
}

export type NestedEnumQuestProgressStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestProgressStatus | Prisma.EnumQuestProgressStatusFieldRefInput<$PrismaModel>
  in?: $Enums.QuestProgressStatus[]
  notIn?: $Enums.QuestProgressStatus[]
  not?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel> | $Enums.QuestProgressStatus
}

export type NestedEnumQuestObjectiveTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestObjectiveTypes | Prisma.EnumQuestObjectiveTypesFieldRefInput<$PrismaModel>
  in?: $Enums.QuestObjectiveTypes[]
  notIn?: $Enums.QuestObjectiveTypes[]
  not?: Prisma.NestedEnumQuestObjectiveTypesWithAggregatesFilter<$PrismaModel> | $Enums.QuestObjectiveTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestObjectiveTypesFilter<$PrismaModel>
}

export type NestedEnumQuestProgressStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestProgressStatus | Prisma.EnumQuestProgressStatusFieldRefInput<$PrismaModel>
  in?: $Enums.QuestProgressStatus[]
  notIn?: $Enums.QuestProgressStatus[]
  not?: Prisma.NestedEnumQuestProgressStatusWithAggregatesFilter<$PrismaModel> | $Enums.QuestProgressStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestProgressStatusFilter<$PrismaModel>
}

export type NestedEnumQuestRewardTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestRewardType | Prisma.EnumQuestRewardTypeFieldRefInput<$PrismaModel>
  in?: $Enums.QuestRewardType[]
  notIn?: $Enums.QuestRewardType[]
  not?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel> | $Enums.QuestRewardType
}

export type NestedEnumQuestRewardTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.QuestRewardType | Prisma.EnumQuestRewardTypeFieldRefInput<$PrismaModel>
  in?: $Enums.QuestRewardType[]
  notIn?: $Enums.QuestRewardType[]
  not?: Prisma.NestedEnumQuestRewardTypeWithAggregatesFilter<$PrismaModel> | $Enums.QuestRewardType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumQuestRewardTypeFilter<$PrismaModel>
}

export type NestedEnumRecipeItemTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.RecipeItemTypes | Prisma.EnumRecipeItemTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.RecipeItemTypes[] | null
  notIn?: $Enums.RecipeItemTypes[] | null
  not?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel> | $Enums.RecipeItemTypes | null
}

export type NestedEnumRecipeItemTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RecipeItemTypes | Prisma.EnumRecipeItemTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.RecipeItemTypes[] | null
  notIn?: $Enums.RecipeItemTypes[] | null
  not?: Prisma.NestedEnumRecipeItemTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.RecipeItemTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRecipeItemTypesNullableFilter<$PrismaModel>
}

export type NestedEnumShopTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopTypes | Prisma.EnumShopTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ShopTypes[]
  notIn?: $Enums.ShopTypes[]
  not?: Prisma.NestedEnumShopTypesFilter<$PrismaModel> | $Enums.ShopTypes
}

export type NestedEnumShopTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopTypes | Prisma.EnumShopTypesFieldRefInput<$PrismaModel>
  in?: $Enums.ShopTypes[]
  notIn?: $Enums.ShopTypes[]
  not?: Prisma.NestedEnumShopTypesWithAggregatesFilter<$PrismaModel> | $Enums.ShopTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumShopTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumShopTypesFilter<$PrismaModel>
}

export type NestedEnumShopListingCurrencyFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopListingCurrency | Prisma.EnumShopListingCurrencyFieldRefInput<$PrismaModel>
  in?: $Enums.ShopListingCurrency[]
  notIn?: $Enums.ShopListingCurrency[]
  not?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel> | $Enums.ShopListingCurrency
}

export type NestedEnumShopListingCurrencyWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ShopListingCurrency | Prisma.EnumShopListingCurrencyFieldRefInput<$PrismaModel>
  in?: $Enums.ShopListingCurrency[]
  notIn?: $Enums.ShopListingCurrency[]
  not?: Prisma.NestedEnumShopListingCurrencyWithAggregatesFilter<$PrismaModel> | $Enums.ShopListingCurrency
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel>
  _max?: Prisma.NestedEnumShopListingCurrencyFilter<$PrismaModel>
}

export type NestedEnumSuggestionStatesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionStates | Prisma.EnumSuggestionStatesFieldRefInput<$PrismaModel> | null
  in?: $Enums.SuggestionStates[] | null
  notIn?: $Enums.SuggestionStates[] | null
  not?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel> | $Enums.SuggestionStates | null
}

export type NestedEnumSuggestionStatesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionStates | Prisma.EnumSuggestionStatesFieldRefInput<$PrismaModel> | null
  in?: $Enums.SuggestionStates[] | null
  notIn?: $Enums.SuggestionStates[] | null
  not?: Prisma.NestedEnumSuggestionStatesNullableWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionStates | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSuggestionStatesNullableFilter<$PrismaModel>
}

export type NestedEnumSuggestionVoteTypesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionVoteTypes | Prisma.EnumSuggestionVoteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.SuggestionVoteTypes[]
  notIn?: $Enums.SuggestionVoteTypes[]
  not?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel> | $Enums.SuggestionVoteTypes
}

export type NestedEnumSuggestionVoteTypesWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SuggestionVoteTypes | Prisma.EnumSuggestionVoteTypesFieldRefInput<$PrismaModel>
  in?: $Enums.SuggestionVoteTypes[]
  notIn?: $Enums.SuggestionVoteTypes[]
  not?: Prisma.NestedEnumSuggestionVoteTypesWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionVoteTypes
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSuggestionVoteTypesFilter<$PrismaModel>
}

export type NestedEnumTalentTreeFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentTree | Prisma.EnumTalentTreeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentTree[]
  notIn?: $Enums.TalentTree[]
  not?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel> | $Enums.TalentTree
}

export type NestedEnumTalentTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentType | Prisma.EnumTalentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentType[]
  notIn?: $Enums.TalentType[]
  not?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel> | $Enums.TalentType
}

export type NestedEnumTalentTreeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentTree | Prisma.EnumTalentTreeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentTree[]
  notIn?: $Enums.TalentTree[]
  not?: Prisma.NestedEnumTalentTreeWithAggregatesFilter<$PrismaModel> | $Enums.TalentTree
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTalentTreeFilter<$PrismaModel>
}

export type NestedEnumTalentTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TalentType | Prisma.EnumTalentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.TalentType[]
  notIn?: $Enums.TalentType[]
  not?: Prisma.NestedEnumTalentTypeWithAggregatesFilter<$PrismaModel> | $Enums.TalentType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTalentTypeFilter<$PrismaModel>
}

export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | null
  notIn?: number[] | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _min?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _max?: Prisma.NestedFloatNullableFilter<$PrismaModel>
}

export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | null
  notIn?: Date[] | string[] | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type NestedBigIntNullableFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel> | null
  in?: bigint[] | number[] | null
  notIn?: bigint[] | number[] | null
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntNullableFilter<$PrismaModel> | bigint | number | null
}

export type NestedEnumUserTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.UserTypes | Prisma.EnumUserTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.UserTypes[] | null
  notIn?: $Enums.UserTypes[] | null
  not?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel> | $Enums.UserTypes | null
}

export type NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.HospitalisedHealingTypes | Prisma.EnumHospitalisedHealingTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.HospitalisedHealingTypes[] | null
  notIn?: $Enums.HospitalisedHealingTypes[] | null
  not?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel> | $Enums.HospitalisedHealingTypes | null
}

export type NestedEnumExploreNodeLocationNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel> | null
  in?: $Enums.ExploreNodeLocation[] | null
  notIn?: $Enums.ExploreNodeLocation[] | null
  not?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel> | $Enums.ExploreNodeLocation | null
}

export type NestedEnumTravelMethodNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.TravelMethod | Prisma.EnumTravelMethodFieldRefInput<$PrismaModel> | null
  in?: $Enums.TravelMethod[] | null
  notIn?: $Enums.TravelMethod[] | null
  not?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel> | $Enums.TravelMethod | null
}

export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | null
  notIn?: Date[] | string[] | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type NestedBigIntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel> | null
  in?: bigint[] | number[] | null
  notIn?: bigint[] | number[] | null
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntNullableWithAggregatesFilter<$PrismaModel> | bigint | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBigIntNullableFilter<$PrismaModel>
}

export type NestedEnumUserTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.UserTypes | Prisma.EnumUserTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.UserTypes[] | null
  notIn?: $Enums.UserTypes[] | null
  not?: Prisma.NestedEnumUserTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.UserTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumUserTypesNullableFilter<$PrismaModel>
}

export type NestedEnumHospitalisedHealingTypesNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.HospitalisedHealingTypes | Prisma.EnumHospitalisedHealingTypesFieldRefInput<$PrismaModel> | null
  in?: $Enums.HospitalisedHealingTypes[] | null
  notIn?: $Enums.HospitalisedHealingTypes[] | null
  not?: Prisma.NestedEnumHospitalisedHealingTypesNullableWithAggregatesFilter<$PrismaModel> | $Enums.HospitalisedHealingTypes | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumHospitalisedHealingTypesNullableFilter<$PrismaModel>
}

export type NestedEnumExploreNodeLocationNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel> | null
  in?: $Enums.ExploreNodeLocation[] | null
  notIn?: $Enums.ExploreNodeLocation[] | null
  not?: Prisma.NestedEnumExploreNodeLocationNullableWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeLocation | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeLocationNullableFilter<$PrismaModel>
}

export type NestedEnumTravelMethodNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TravelMethod | Prisma.EnumTravelMethodFieldRefInput<$PrismaModel> | null
  in?: $Enums.TravelMethod[] | null
  notIn?: $Enums.TravelMethod[] | null
  not?: Prisma.NestedEnumTravelMethodNullableWithAggregatesFilter<$PrismaModel> | $Enums.TravelMethod | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTravelMethodNullableFilter<$PrismaModel>
}

export type NestedEnumItemQualityFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemQuality | Prisma.EnumItemQualityFieldRefInput<$PrismaModel>
  in?: $Enums.ItemQuality[]
  notIn?: $Enums.ItemQuality[]
  not?: Prisma.NestedEnumItemQualityFilter<$PrismaModel> | $Enums.ItemQuality
}

export type NestedEnumItemQualityWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ItemQuality | Prisma.EnumItemQualityFieldRefInput<$PrismaModel>
  in?: $Enums.ItemQuality[]
  notIn?: $Enums.ItemQuality[]
  not?: Prisma.NestedEnumItemQualityWithAggregatesFilter<$PrismaModel> | $Enums.ItemQuality
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumItemQualityFilter<$PrismaModel>
  _max?: Prisma.NestedEnumItemQualityFilter<$PrismaModel>
}

export type NestedBigIntFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  in?: bigint[] | number[]
  notIn?: bigint[] | number[]
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntFilter<$PrismaModel> | bigint | number
}

export type NestedBigIntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  in?: bigint[] | number[]
  notIn?: bigint[] | number[]
  lt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  lte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gt?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  gte?: bigint | number | Prisma.BigIntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBigIntWithAggregatesFilter<$PrismaModel> | bigint | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedBigIntFilter<$PrismaModel>
  _min?: Prisma.NestedBigIntFilter<$PrismaModel>
  _max?: Prisma.NestedBigIntFilter<$PrismaModel>
}

export type NestedEnumStatusEffectTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectType | Prisma.EnumStatusEffectTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectType[]
  notIn?: $Enums.StatusEffectType[]
  not?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel> | $Enums.StatusEffectType
}

export type NestedEnumStatusEffectTierNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectTier | Prisma.EnumStatusEffectTierFieldRefInput<$PrismaModel> | null
  in?: $Enums.StatusEffectTier[] | null
  notIn?: $Enums.StatusEffectTier[] | null
  not?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel> | $Enums.StatusEffectTier | null
}

export type NestedEnumStatusEffectModifierTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectModifierType | Prisma.EnumStatusEffectModifierTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectModifierType[]
  notIn?: $Enums.StatusEffectModifierType[]
  not?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel> | $Enums.StatusEffectModifierType
}

export type NestedEnumStatusEffectTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectType | Prisma.EnumStatusEffectTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectType[]
  notIn?: $Enums.StatusEffectType[]
  not?: Prisma.NestedEnumStatusEffectTypeWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectTypeFilter<$PrismaModel>
}

export type NestedEnumStatusEffectTierNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectTier | Prisma.EnumStatusEffectTierFieldRefInput<$PrismaModel> | null
  in?: $Enums.StatusEffectTier[] | null
  notIn?: $Enums.StatusEffectTier[] | null
  not?: Prisma.NestedEnumStatusEffectTierNullableWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectTier | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectTierNullableFilter<$PrismaModel>
}

export type NestedEnumStatusEffectModifierTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StatusEffectModifierType | Prisma.EnumStatusEffectModifierTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StatusEffectModifierType[]
  notIn?: $Enums.StatusEffectModifierType[]
  not?: Prisma.NestedEnumStatusEffectModifierTypeWithAggregatesFilter<$PrismaModel> | $Enums.StatusEffectModifierType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStatusEffectModifierTypeFilter<$PrismaModel>
}

export type NestedEnumSkillTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.SkillType | Prisma.EnumSkillTypeFieldRefInput<$PrismaModel>
  in?: $Enums.SkillType[]
  notIn?: $Enums.SkillType[]
  not?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel> | $Enums.SkillType
}

export type NestedEnumSkillTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.SkillType | Prisma.EnumSkillTypeFieldRefInput<$PrismaModel>
  in?: $Enums.SkillType[]
  notIn?: $Enums.SkillType[]
  not?: Prisma.NestedEnumSkillTypeWithAggregatesFilter<$PrismaModel> | $Enums.SkillType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumSkillTypeFilter<$PrismaModel>
}

export type NestedEnumExploreNodeTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeType | Prisma.EnumExploreNodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeType[]
  notIn?: $Enums.ExploreNodeType[]
  not?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel> | $Enums.ExploreNodeType
}

export type NestedEnumExploreNodeLocationFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeLocation[]
  notIn?: $Enums.ExploreNodeLocation[]
  not?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel> | $Enums.ExploreNodeLocation
}

export type NestedEnumExploreNodeTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeType | Prisma.EnumExploreNodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeType[]
  notIn?: $Enums.ExploreNodeType[]
  not?: Prisma.NestedEnumExploreNodeTypeWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeTypeFilter<$PrismaModel>
}

export type NestedEnumExploreNodeLocationWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeLocation | Prisma.EnumExploreNodeLocationFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeLocation[]
  notIn?: $Enums.ExploreNodeLocation[]
  not?: Prisma.NestedEnumExploreNodeLocationWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeLocation
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeLocationFilter<$PrismaModel>
}

export type NestedEnumExploreNodeStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeStatus | Prisma.EnumExploreNodeStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeStatus[]
  notIn?: $Enums.ExploreNodeStatus[]
  not?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel> | $Enums.ExploreNodeStatus
}

export type NestedEnumExploreNodeStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExploreNodeStatus | Prisma.EnumExploreNodeStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ExploreNodeStatus[]
  notIn?: $Enums.ExploreNodeStatus[]
  not?: Prisma.NestedEnumExploreNodeStatusWithAggregatesFilter<$PrismaModel> | $Enums.ExploreNodeStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExploreNodeStatusFilter<$PrismaModel>
}

export type NestedEnumStoryEpisodeTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.StoryEpisodeType | Prisma.EnumStoryEpisodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StoryEpisodeType[]
  notIn?: $Enums.StoryEpisodeType[]
  not?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel> | $Enums.StoryEpisodeType
}

export type NestedEnumStoryEpisodeTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.StoryEpisodeType | Prisma.EnumStoryEpisodeTypeFieldRefInput<$PrismaModel>
  in?: $Enums.StoryEpisodeType[]
  notIn?: $Enums.StoryEpisodeType[]
  not?: Prisma.NestedEnumStoryEpisodeTypeWithAggregatesFilter<$PrismaModel> | $Enums.StoryEpisodeType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumStoryEpisodeTypeFilter<$PrismaModel>
}


