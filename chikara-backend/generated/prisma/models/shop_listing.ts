
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `shop_listing` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model shop_listing
 * 
 */
export type shop_listingModel = runtime.Types.Result.DefaultSelection<Prisma.$shop_listingPayload>

export type AggregateShop_listing = {
  _count: Shop_listingCountAggregateOutputType | null
  _avg: Shop_listingAvgAggregateOutputType | null
  _sum: Shop_listingSumAggregateOutputType | null
  _min: Shop_listingMinAggregateOutputType | null
  _max: Shop_listingMaxAggregateOutputType | null
}

export type Shop_listingAvgAggregateOutputType = {
  id: number | null
  customCost: number | null
  repRequired: number | null
  stock: number | null
  shopId: number | null
  itemId: number | null
}

export type Shop_listingSumAggregateOutputType = {
  id: number | null
  customCost: number | null
  repRequired: number | null
  stock: number | null
  shopId: number | null
  itemId: number | null
}

export type Shop_listingMinAggregateOutputType = {
  id: number | null
  customCost: number | null
  repRequired: number | null
  stock: number | null
  currency: $Enums.ShopListingCurrency | null
  shopId: number | null
  itemId: number | null
}

export type Shop_listingMaxAggregateOutputType = {
  id: number | null
  customCost: number | null
  repRequired: number | null
  stock: number | null
  currency: $Enums.ShopListingCurrency | null
  shopId: number | null
  itemId: number | null
}

export type Shop_listingCountAggregateOutputType = {
  id: number
  customCost: number
  repRequired: number
  stock: number
  currency: number
  shopId: number
  itemId: number
  _all: number
}


export type Shop_listingAvgAggregateInputType = {
  id?: true
  customCost?: true
  repRequired?: true
  stock?: true
  shopId?: true
  itemId?: true
}

export type Shop_listingSumAggregateInputType = {
  id?: true
  customCost?: true
  repRequired?: true
  stock?: true
  shopId?: true
  itemId?: true
}

export type Shop_listingMinAggregateInputType = {
  id?: true
  customCost?: true
  repRequired?: true
  stock?: true
  currency?: true
  shopId?: true
  itemId?: true
}

export type Shop_listingMaxAggregateInputType = {
  id?: true
  customCost?: true
  repRequired?: true
  stock?: true
  currency?: true
  shopId?: true
  itemId?: true
}

export type Shop_listingCountAggregateInputType = {
  id?: true
  customCost?: true
  repRequired?: true
  stock?: true
  currency?: true
  shopId?: true
  itemId?: true
  _all?: true
}

export type Shop_listingAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shop_listing to aggregate.
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shop_listings to fetch.
   */
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.shop_listingWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shop_listings from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shop_listings.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned shop_listings
  **/
  _count?: true | Shop_listingCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Shop_listingAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Shop_listingSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Shop_listingMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Shop_listingMaxAggregateInputType
}

export type GetShop_listingAggregateType<T extends Shop_listingAggregateArgs> = {
      [P in keyof T & keyof AggregateShop_listing]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateShop_listing[P]>
    : Prisma.GetScalarType<T[P], AggregateShop_listing[P]>
}




export type shop_listingGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shop_listingWhereInput
  orderBy?: Prisma.shop_listingOrderByWithAggregationInput | Prisma.shop_listingOrderByWithAggregationInput[]
  by: Prisma.Shop_listingScalarFieldEnum[] | Prisma.Shop_listingScalarFieldEnum
  having?: Prisma.shop_listingScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Shop_listingCountAggregateInputType | true
  _avg?: Shop_listingAvgAggregateInputType
  _sum?: Shop_listingSumAggregateInputType
  _min?: Shop_listingMinAggregateInputType
  _max?: Shop_listingMaxAggregateInputType
}

export type Shop_listingGroupByOutputType = {
  id: number
  customCost: number | null
  repRequired: number | null
  stock: number | null
  currency: $Enums.ShopListingCurrency
  shopId: number | null
  itemId: number | null
  _count: Shop_listingCountAggregateOutputType | null
  _avg: Shop_listingAvgAggregateOutputType | null
  _sum: Shop_listingSumAggregateOutputType | null
  _min: Shop_listingMinAggregateOutputType | null
  _max: Shop_listingMaxAggregateOutputType | null
}

type GetShop_listingGroupByPayload<T extends shop_listingGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Shop_listingGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Shop_listingGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Shop_listingGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Shop_listingGroupByOutputType[P]>
      }
    >
  >



export type shop_listingWhereInput = {
  AND?: Prisma.shop_listingWhereInput | Prisma.shop_listingWhereInput[]
  OR?: Prisma.shop_listingWhereInput[]
  NOT?: Prisma.shop_listingWhereInput | Prisma.shop_listingWhereInput[]
  id?: Prisma.IntFilter<"shop_listing"> | number
  customCost?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  repRequired?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  stock?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  currency?: Prisma.EnumShopListingCurrencyFilter<"shop_listing"> | $Enums.ShopListingCurrency
  shopId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  itemId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}

export type shop_listingOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrderInput | Prisma.SortOrder
  repRequired?: Prisma.SortOrderInput | Prisma.SortOrder
  stock?: Prisma.SortOrderInput | Prisma.SortOrder
  currency?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  shop?: Prisma.shopOrderByWithRelationInput
  item?: Prisma.itemOrderByWithRelationInput
}

export type shop_listingWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.shop_listingWhereInput | Prisma.shop_listingWhereInput[]
  OR?: Prisma.shop_listingWhereInput[]
  NOT?: Prisma.shop_listingWhereInput | Prisma.shop_listingWhereInput[]
  customCost?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  repRequired?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  stock?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  currency?: Prisma.EnumShopListingCurrencyFilter<"shop_listing"> | $Enums.ShopListingCurrency
  shopId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  itemId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}, "id">

export type shop_listingOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrderInput | Prisma.SortOrder
  repRequired?: Prisma.SortOrderInput | Prisma.SortOrder
  stock?: Prisma.SortOrderInput | Prisma.SortOrder
  currency?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.shop_listingCountOrderByAggregateInput
  _avg?: Prisma.shop_listingAvgOrderByAggregateInput
  _max?: Prisma.shop_listingMaxOrderByAggregateInput
  _min?: Prisma.shop_listingMinOrderByAggregateInput
  _sum?: Prisma.shop_listingSumOrderByAggregateInput
}

export type shop_listingScalarWhereWithAggregatesInput = {
  AND?: Prisma.shop_listingScalarWhereWithAggregatesInput | Prisma.shop_listingScalarWhereWithAggregatesInput[]
  OR?: Prisma.shop_listingScalarWhereWithAggregatesInput[]
  NOT?: Prisma.shop_listingScalarWhereWithAggregatesInput | Prisma.shop_listingScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"shop_listing"> | number
  customCost?: Prisma.IntNullableWithAggregatesFilter<"shop_listing"> | number | null
  repRequired?: Prisma.IntNullableWithAggregatesFilter<"shop_listing"> | number | null
  stock?: Prisma.IntNullableWithAggregatesFilter<"shop_listing"> | number | null
  currency?: Prisma.EnumShopListingCurrencyWithAggregatesFilter<"shop_listing"> | $Enums.ShopListingCurrency
  shopId?: Prisma.IntNullableWithAggregatesFilter<"shop_listing"> | number | null
  itemId?: Prisma.IntNullableWithAggregatesFilter<"shop_listing"> | number | null
}

export type shop_listingCreateInput = {
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shop?: Prisma.shopCreateNestedOneWithoutShop_listingInput
  item?: Prisma.itemCreateNestedOneWithoutShop_listingInput
}

export type shop_listingUncheckedCreateInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shopId?: number | null
  itemId?: number | null
}

export type shop_listingUpdateInput = {
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shop?: Prisma.shopUpdateOneWithoutShop_listingNestedInput
  item?: Prisma.itemUpdateOneWithoutShop_listingNestedInput
}

export type shop_listingUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shop_listingCreateManyInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shopId?: number | null
  itemId?: number | null
}

export type shop_listingUpdateManyMutationInput = {
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
}

export type shop_listingUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Shop_listingListRelationFilter = {
  every?: Prisma.shop_listingWhereInput
  some?: Prisma.shop_listingWhereInput
  none?: Prisma.shop_listingWhereInput
}

export type shop_listingOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type shop_listingCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrder
  repRequired?: Prisma.SortOrder
  stock?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type shop_listingAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrder
  repRequired?: Prisma.SortOrder
  stock?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type shop_listingMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrder
  repRequired?: Prisma.SortOrder
  stock?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type shop_listingMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrder
  repRequired?: Prisma.SortOrder
  stock?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type shop_listingSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  customCost?: Prisma.SortOrder
  repRequired?: Prisma.SortOrder
  stock?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type shop_listingCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput> | Prisma.shop_listingCreateWithoutItemInput[] | Prisma.shop_listingUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutItemInput | Prisma.shop_listingCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.shop_listingCreateManyItemInputEnvelope
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
}

export type shop_listingUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput> | Prisma.shop_listingCreateWithoutItemInput[] | Prisma.shop_listingUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutItemInput | Prisma.shop_listingCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.shop_listingCreateManyItemInputEnvelope
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
}

export type shop_listingUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput> | Prisma.shop_listingCreateWithoutItemInput[] | Prisma.shop_listingUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutItemInput | Prisma.shop_listingCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.shop_listingUpsertWithWhereUniqueWithoutItemInput | Prisma.shop_listingUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.shop_listingCreateManyItemInputEnvelope
  set?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  disconnect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  delete?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  update?: Prisma.shop_listingUpdateWithWhereUniqueWithoutItemInput | Prisma.shop_listingUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.shop_listingUpdateManyWithWhereWithoutItemInput | Prisma.shop_listingUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
}

export type shop_listingUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput> | Prisma.shop_listingCreateWithoutItemInput[] | Prisma.shop_listingUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutItemInput | Prisma.shop_listingCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.shop_listingUpsertWithWhereUniqueWithoutItemInput | Prisma.shop_listingUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.shop_listingCreateManyItemInputEnvelope
  set?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  disconnect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  delete?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  update?: Prisma.shop_listingUpdateWithWhereUniqueWithoutItemInput | Prisma.shop_listingUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.shop_listingUpdateManyWithWhereWithoutItemInput | Prisma.shop_listingUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
}

export type shop_listingCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput> | Prisma.shop_listingCreateWithoutShopInput[] | Prisma.shop_listingUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutShopInput | Prisma.shop_listingCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.shop_listingCreateManyShopInputEnvelope
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
}

export type shop_listingUncheckedCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput> | Prisma.shop_listingCreateWithoutShopInput[] | Prisma.shop_listingUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutShopInput | Prisma.shop_listingCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.shop_listingCreateManyShopInputEnvelope
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
}

export type shop_listingUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput> | Prisma.shop_listingCreateWithoutShopInput[] | Prisma.shop_listingUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutShopInput | Prisma.shop_listingCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.shop_listingUpsertWithWhereUniqueWithoutShopInput | Prisma.shop_listingUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.shop_listingCreateManyShopInputEnvelope
  set?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  disconnect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  delete?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  update?: Prisma.shop_listingUpdateWithWhereUniqueWithoutShopInput | Prisma.shop_listingUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.shop_listingUpdateManyWithWhereWithoutShopInput | Prisma.shop_listingUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
}

export type shop_listingUncheckedUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput> | Prisma.shop_listingCreateWithoutShopInput[] | Prisma.shop_listingUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.shop_listingCreateOrConnectWithoutShopInput | Prisma.shop_listingCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.shop_listingUpsertWithWhereUniqueWithoutShopInput | Prisma.shop_listingUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.shop_listingCreateManyShopInputEnvelope
  set?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  disconnect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  delete?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  connect?: Prisma.shop_listingWhereUniqueInput | Prisma.shop_listingWhereUniqueInput[]
  update?: Prisma.shop_listingUpdateWithWhereUniqueWithoutShopInput | Prisma.shop_listingUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.shop_listingUpdateManyWithWhereWithoutShopInput | Prisma.shop_listingUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
}

export type EnumShopListingCurrencyFieldUpdateOperationsInput = {
  set?: $Enums.ShopListingCurrency
}

export type shop_listingCreateWithoutItemInput = {
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shop?: Prisma.shopCreateNestedOneWithoutShop_listingInput
}

export type shop_listingUncheckedCreateWithoutItemInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shopId?: number | null
}

export type shop_listingCreateOrConnectWithoutItemInput = {
  where: Prisma.shop_listingWhereUniqueInput
  create: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput>
}

export type shop_listingCreateManyItemInputEnvelope = {
  data: Prisma.shop_listingCreateManyItemInput | Prisma.shop_listingCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type shop_listingUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.shop_listingWhereUniqueInput
  update: Prisma.XOR<Prisma.shop_listingUpdateWithoutItemInput, Prisma.shop_listingUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.shop_listingCreateWithoutItemInput, Prisma.shop_listingUncheckedCreateWithoutItemInput>
}

export type shop_listingUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.shop_listingWhereUniqueInput
  data: Prisma.XOR<Prisma.shop_listingUpdateWithoutItemInput, Prisma.shop_listingUncheckedUpdateWithoutItemInput>
}

export type shop_listingUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.shop_listingScalarWhereInput
  data: Prisma.XOR<Prisma.shop_listingUpdateManyMutationInput, Prisma.shop_listingUncheckedUpdateManyWithoutItemInput>
}

export type shop_listingScalarWhereInput = {
  AND?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
  OR?: Prisma.shop_listingScalarWhereInput[]
  NOT?: Prisma.shop_listingScalarWhereInput | Prisma.shop_listingScalarWhereInput[]
  id?: Prisma.IntFilter<"shop_listing"> | number
  customCost?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  repRequired?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  stock?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  currency?: Prisma.EnumShopListingCurrencyFilter<"shop_listing"> | $Enums.ShopListingCurrency
  shopId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
  itemId?: Prisma.IntNullableFilter<"shop_listing"> | number | null
}

export type shop_listingCreateWithoutShopInput = {
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  item?: Prisma.itemCreateNestedOneWithoutShop_listingInput
}

export type shop_listingUncheckedCreateWithoutShopInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  itemId?: number | null
}

export type shop_listingCreateOrConnectWithoutShopInput = {
  where: Prisma.shop_listingWhereUniqueInput
  create: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput>
}

export type shop_listingCreateManyShopInputEnvelope = {
  data: Prisma.shop_listingCreateManyShopInput | Prisma.shop_listingCreateManyShopInput[]
  skipDuplicates?: boolean
}

export type shop_listingUpsertWithWhereUniqueWithoutShopInput = {
  where: Prisma.shop_listingWhereUniqueInput
  update: Prisma.XOR<Prisma.shop_listingUpdateWithoutShopInput, Prisma.shop_listingUncheckedUpdateWithoutShopInput>
  create: Prisma.XOR<Prisma.shop_listingCreateWithoutShopInput, Prisma.shop_listingUncheckedCreateWithoutShopInput>
}

export type shop_listingUpdateWithWhereUniqueWithoutShopInput = {
  where: Prisma.shop_listingWhereUniqueInput
  data: Prisma.XOR<Prisma.shop_listingUpdateWithoutShopInput, Prisma.shop_listingUncheckedUpdateWithoutShopInput>
}

export type shop_listingUpdateManyWithWhereWithoutShopInput = {
  where: Prisma.shop_listingScalarWhereInput
  data: Prisma.XOR<Prisma.shop_listingUpdateManyMutationInput, Prisma.shop_listingUncheckedUpdateManyWithoutShopInput>
}

export type shop_listingCreateManyItemInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  shopId?: number | null
}

export type shop_listingUpdateWithoutItemInput = {
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shop?: Prisma.shopUpdateOneWithoutShop_listingNestedInput
}

export type shop_listingUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shop_listingUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shop_listingCreateManyShopInput = {
  id?: number
  customCost?: number | null
  repRequired?: number | null
  stock?: number | null
  currency?: $Enums.ShopListingCurrency
  itemId?: number | null
}

export type shop_listingUpdateWithoutShopInput = {
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  item?: Prisma.itemUpdateOneWithoutShop_listingNestedInput
}

export type shop_listingUncheckedUpdateWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shop_listingUncheckedUpdateManyWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  customCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  repRequired?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  stock?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  currency?: Prisma.EnumShopListingCurrencyFieldUpdateOperationsInput | $Enums.ShopListingCurrency
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type shop_listingSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  customCost?: boolean
  repRequired?: boolean
  stock?: boolean
  currency?: boolean
  shopId?: boolean
  itemId?: boolean
  shop?: boolean | Prisma.shop_listing$shopArgs<ExtArgs>
  item?: boolean | Prisma.shop_listing$itemArgs<ExtArgs>
}, ExtArgs["result"]["shop_listing"]>



export type shop_listingSelectScalar = {
  id?: boolean
  customCost?: boolean
  repRequired?: boolean
  stock?: boolean
  currency?: boolean
  shopId?: boolean
  itemId?: boolean
}

export type shop_listingOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "customCost" | "repRequired" | "stock" | "currency" | "shopId" | "itemId", ExtArgs["result"]["shop_listing"]>
export type shop_listingInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shop?: boolean | Prisma.shop_listing$shopArgs<ExtArgs>
  item?: boolean | Prisma.shop_listing$itemArgs<ExtArgs>
}

export type $shop_listingPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "shop_listing"
  objects: {
    shop: Prisma.$shopPayload<ExtArgs> | null
    item: Prisma.$itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    customCost: number | null
    repRequired: number | null
    stock: number | null
    currency: $Enums.ShopListingCurrency
    shopId: number | null
    itemId: number | null
  }, ExtArgs["result"]["shop_listing"]>
  composites: {}
}

export type shop_listingGetPayload<S extends boolean | null | undefined | shop_listingDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$shop_listingPayload, S>

export type shop_listingCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<shop_listingFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Shop_listingCountAggregateInputType | true
  }

export interface shop_listingDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['shop_listing'], meta: { name: 'shop_listing' } }
  /**
   * Find zero or one Shop_listing that matches the filter.
   * @param {shop_listingFindUniqueArgs} args - Arguments to find a Shop_listing
   * @example
   * // Get one Shop_listing
   * const shop_listing = await prisma.shop_listing.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends shop_listingFindUniqueArgs>(args: Prisma.SelectSubset<T, shop_listingFindUniqueArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Shop_listing that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {shop_listingFindUniqueOrThrowArgs} args - Arguments to find a Shop_listing
   * @example
   * // Get one Shop_listing
   * const shop_listing = await prisma.shop_listing.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends shop_listingFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, shop_listingFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shop_listing that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingFindFirstArgs} args - Arguments to find a Shop_listing
   * @example
   * // Get one Shop_listing
   * const shop_listing = await prisma.shop_listing.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends shop_listingFindFirstArgs>(args?: Prisma.SelectSubset<T, shop_listingFindFirstArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shop_listing that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingFindFirstOrThrowArgs} args - Arguments to find a Shop_listing
   * @example
   * // Get one Shop_listing
   * const shop_listing = await prisma.shop_listing.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends shop_listingFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, shop_listingFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Shop_listings that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Shop_listings
   * const shop_listings = await prisma.shop_listing.findMany()
   * 
   * // Get first 10 Shop_listings
   * const shop_listings = await prisma.shop_listing.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const shop_listingWithIdOnly = await prisma.shop_listing.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends shop_listingFindManyArgs>(args?: Prisma.SelectSubset<T, shop_listingFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Shop_listing.
   * @param {shop_listingCreateArgs} args - Arguments to create a Shop_listing.
   * @example
   * // Create one Shop_listing
   * const Shop_listing = await prisma.shop_listing.create({
   *   data: {
   *     // ... data to create a Shop_listing
   *   }
   * })
   * 
   */
  create<T extends shop_listingCreateArgs>(args: Prisma.SelectSubset<T, shop_listingCreateArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Shop_listings.
   * @param {shop_listingCreateManyArgs} args - Arguments to create many Shop_listings.
   * @example
   * // Create many Shop_listings
   * const shop_listing = await prisma.shop_listing.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends shop_listingCreateManyArgs>(args?: Prisma.SelectSubset<T, shop_listingCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Shop_listing.
   * @param {shop_listingDeleteArgs} args - Arguments to delete one Shop_listing.
   * @example
   * // Delete one Shop_listing
   * const Shop_listing = await prisma.shop_listing.delete({
   *   where: {
   *     // ... filter to delete one Shop_listing
   *   }
   * })
   * 
   */
  delete<T extends shop_listingDeleteArgs>(args: Prisma.SelectSubset<T, shop_listingDeleteArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Shop_listing.
   * @param {shop_listingUpdateArgs} args - Arguments to update one Shop_listing.
   * @example
   * // Update one Shop_listing
   * const shop_listing = await prisma.shop_listing.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends shop_listingUpdateArgs>(args: Prisma.SelectSubset<T, shop_listingUpdateArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Shop_listings.
   * @param {shop_listingDeleteManyArgs} args - Arguments to filter Shop_listings to delete.
   * @example
   * // Delete a few Shop_listings
   * const { count } = await prisma.shop_listing.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends shop_listingDeleteManyArgs>(args?: Prisma.SelectSubset<T, shop_listingDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Shop_listings.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Shop_listings
   * const shop_listing = await prisma.shop_listing.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends shop_listingUpdateManyArgs>(args: Prisma.SelectSubset<T, shop_listingUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Shop_listing.
   * @param {shop_listingUpsertArgs} args - Arguments to update or create a Shop_listing.
   * @example
   * // Update or create a Shop_listing
   * const shop_listing = await prisma.shop_listing.upsert({
   *   create: {
   *     // ... data to create a Shop_listing
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Shop_listing we want to update
   *   }
   * })
   */
  upsert<T extends shop_listingUpsertArgs>(args: Prisma.SelectSubset<T, shop_listingUpsertArgs<ExtArgs>>): Prisma.Prisma__shop_listingClient<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Shop_listings.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingCountArgs} args - Arguments to filter Shop_listings to count.
   * @example
   * // Count the number of Shop_listings
   * const count = await prisma.shop_listing.count({
   *   where: {
   *     // ... the filter for the Shop_listings we want to count
   *   }
   * })
  **/
  count<T extends shop_listingCountArgs>(
    args?: Prisma.Subset<T, shop_listingCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Shop_listingCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Shop_listing.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Shop_listingAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Shop_listingAggregateArgs>(args: Prisma.Subset<T, Shop_listingAggregateArgs>): Prisma.PrismaPromise<GetShop_listingAggregateType<T>>

  /**
   * Group by Shop_listing.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shop_listingGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends shop_listingGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: shop_listingGroupByArgs['orderBy'] }
      : { orderBy?: shop_listingGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, shop_listingGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetShop_listingGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the shop_listing model
 */
readonly fields: shop_listingFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for shop_listing.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__shop_listingClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  shop<T extends Prisma.shop_listing$shopArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop_listing$shopArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  item<T extends Prisma.shop_listing$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop_listing$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the shop_listing model
 */
export interface shop_listingFieldRefs {
  readonly id: Prisma.FieldRef<"shop_listing", 'Int'>
  readonly customCost: Prisma.FieldRef<"shop_listing", 'Int'>
  readonly repRequired: Prisma.FieldRef<"shop_listing", 'Int'>
  readonly stock: Prisma.FieldRef<"shop_listing", 'Int'>
  readonly currency: Prisma.FieldRef<"shop_listing", 'ShopListingCurrency'>
  readonly shopId: Prisma.FieldRef<"shop_listing", 'Int'>
  readonly itemId: Prisma.FieldRef<"shop_listing", 'Int'>
}
    

// Custom InputTypes
/**
 * shop_listing findUnique
 */
export type shop_listingFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter, which shop_listing to fetch.
   */
  where: Prisma.shop_listingWhereUniqueInput
}

/**
 * shop_listing findUniqueOrThrow
 */
export type shop_listingFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter, which shop_listing to fetch.
   */
  where: Prisma.shop_listingWhereUniqueInput
}

/**
 * shop_listing findFirst
 */
export type shop_listingFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter, which shop_listing to fetch.
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shop_listings to fetch.
   */
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shop_listings.
   */
  cursor?: Prisma.shop_listingWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shop_listings from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shop_listings.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shop_listings.
   */
  distinct?: Prisma.Shop_listingScalarFieldEnum | Prisma.Shop_listingScalarFieldEnum[]
}

/**
 * shop_listing findFirstOrThrow
 */
export type shop_listingFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter, which shop_listing to fetch.
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shop_listings to fetch.
   */
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shop_listings.
   */
  cursor?: Prisma.shop_listingWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shop_listings from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shop_listings.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shop_listings.
   */
  distinct?: Prisma.Shop_listingScalarFieldEnum | Prisma.Shop_listingScalarFieldEnum[]
}

/**
 * shop_listing findMany
 */
export type shop_listingFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter, which shop_listings to fetch.
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shop_listings to fetch.
   */
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing shop_listings.
   */
  cursor?: Prisma.shop_listingWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shop_listings from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shop_listings.
   */
  skip?: number
  distinct?: Prisma.Shop_listingScalarFieldEnum | Prisma.Shop_listingScalarFieldEnum[]
}

/**
 * shop_listing create
 */
export type shop_listingCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * The data needed to create a shop_listing.
   */
  data?: Prisma.XOR<Prisma.shop_listingCreateInput, Prisma.shop_listingUncheckedCreateInput>
}

/**
 * shop_listing createMany
 */
export type shop_listingCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many shop_listings.
   */
  data: Prisma.shop_listingCreateManyInput | Prisma.shop_listingCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * shop_listing update
 */
export type shop_listingUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * The data needed to update a shop_listing.
   */
  data: Prisma.XOR<Prisma.shop_listingUpdateInput, Prisma.shop_listingUncheckedUpdateInput>
  /**
   * Choose, which shop_listing to update.
   */
  where: Prisma.shop_listingWhereUniqueInput
}

/**
 * shop_listing updateMany
 */
export type shop_listingUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update shop_listings.
   */
  data: Prisma.XOR<Prisma.shop_listingUpdateManyMutationInput, Prisma.shop_listingUncheckedUpdateManyInput>
  /**
   * Filter which shop_listings to update
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * Limit how many shop_listings to update.
   */
  limit?: number
}

/**
 * shop_listing upsert
 */
export type shop_listingUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * The filter to search for the shop_listing to update in case it exists.
   */
  where: Prisma.shop_listingWhereUniqueInput
  /**
   * In case the shop_listing found by the `where` argument doesn't exist, create a new shop_listing with this data.
   */
  create: Prisma.XOR<Prisma.shop_listingCreateInput, Prisma.shop_listingUncheckedCreateInput>
  /**
   * In case the shop_listing was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.shop_listingUpdateInput, Prisma.shop_listingUncheckedUpdateInput>
}

/**
 * shop_listing delete
 */
export type shop_listingDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  /**
   * Filter which shop_listing to delete.
   */
  where: Prisma.shop_listingWhereUniqueInput
}

/**
 * shop_listing deleteMany
 */
export type shop_listingDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shop_listings to delete
   */
  where?: Prisma.shop_listingWhereInput
  /**
   * Limit how many shop_listings to delete.
   */
  limit?: number
}

/**
 * shop_listing.shop
 */
export type shop_listing$shopArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  where?: Prisma.shopWhereInput
}

/**
 * shop_listing.item
 */
export type shop_listing$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * shop_listing without action
 */
export type shop_listingDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
}
