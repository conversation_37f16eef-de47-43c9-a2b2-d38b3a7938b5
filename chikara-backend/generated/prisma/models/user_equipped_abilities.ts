
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_equipped_abilities` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_equipped_abilities
 * 
 */
export type user_equipped_abilitiesModel = runtime.Types.Result.DefaultSelection<Prisma.$user_equipped_abilitiesPayload>

export type AggregateUser_equipped_abilities = {
  _count: User_equipped_abilitiesCountAggregateOutputType | null
  _avg: User_equipped_abilitiesAvgAggregateOutputType | null
  _sum: User_equipped_abilitiesSumAggregateOutputType | null
  _min: User_equipped_abilitiesMinAggregateOutputType | null
  _max: User_equipped_abilitiesMaxAggregateOutputType | null
}

export type User_equipped_abilitiesAvgAggregateOutputType = {
  userId: number | null
  equippedAbility1Id: number | null
  equippedAbility2Id: number | null
  equippedAbility3Id: number | null
  equippedAbility4Id: number | null
}

export type User_equipped_abilitiesSumAggregateOutputType = {
  userId: number | null
  equippedAbility1Id: number | null
  equippedAbility2Id: number | null
  equippedAbility3Id: number | null
  equippedAbility4Id: number | null
}

export type User_equipped_abilitiesMinAggregateOutputType = {
  userId: number | null
  equippedAbility1Id: number | null
  equippedAbility2Id: number | null
  equippedAbility3Id: number | null
  equippedAbility4Id: number | null
}

export type User_equipped_abilitiesMaxAggregateOutputType = {
  userId: number | null
  equippedAbility1Id: number | null
  equippedAbility2Id: number | null
  equippedAbility3Id: number | null
  equippedAbility4Id: number | null
}

export type User_equipped_abilitiesCountAggregateOutputType = {
  userId: number
  equippedAbility1Id: number
  equippedAbility2Id: number
  equippedAbility3Id: number
  equippedAbility4Id: number
  _all: number
}


export type User_equipped_abilitiesAvgAggregateInputType = {
  userId?: true
  equippedAbility1Id?: true
  equippedAbility2Id?: true
  equippedAbility3Id?: true
  equippedAbility4Id?: true
}

export type User_equipped_abilitiesSumAggregateInputType = {
  userId?: true
  equippedAbility1Id?: true
  equippedAbility2Id?: true
  equippedAbility3Id?: true
  equippedAbility4Id?: true
}

export type User_equipped_abilitiesMinAggregateInputType = {
  userId?: true
  equippedAbility1Id?: true
  equippedAbility2Id?: true
  equippedAbility3Id?: true
  equippedAbility4Id?: true
}

export type User_equipped_abilitiesMaxAggregateInputType = {
  userId?: true
  equippedAbility1Id?: true
  equippedAbility2Id?: true
  equippedAbility3Id?: true
  equippedAbility4Id?: true
}

export type User_equipped_abilitiesCountAggregateInputType = {
  userId?: true
  equippedAbility1Id?: true
  equippedAbility2Id?: true
  equippedAbility3Id?: true
  equippedAbility4Id?: true
  _all?: true
}

export type User_equipped_abilitiesAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_equipped_abilities to aggregate.
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_equipped_abilities to fetch.
   */
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_equipped_abilities from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_equipped_abilities.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_equipped_abilities
  **/
  _count?: true | User_equipped_abilitiesCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_equipped_abilitiesAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_equipped_abilitiesSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_equipped_abilitiesMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_equipped_abilitiesMaxAggregateInputType
}

export type GetUser_equipped_abilitiesAggregateType<T extends User_equipped_abilitiesAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_equipped_abilities]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_equipped_abilities[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_equipped_abilities[P]>
}




export type user_equipped_abilitiesGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_equipped_abilitiesWhereInput
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithAggregationInput | Prisma.user_equipped_abilitiesOrderByWithAggregationInput[]
  by: Prisma.User_equipped_abilitiesScalarFieldEnum[] | Prisma.User_equipped_abilitiesScalarFieldEnum
  having?: Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_equipped_abilitiesCountAggregateInputType | true
  _avg?: User_equipped_abilitiesAvgAggregateInputType
  _sum?: User_equipped_abilitiesSumAggregateInputType
  _min?: User_equipped_abilitiesMinAggregateInputType
  _max?: User_equipped_abilitiesMaxAggregateInputType
}

export type User_equipped_abilitiesGroupByOutputType = {
  userId: number
  equippedAbility1Id: number | null
  equippedAbility2Id: number | null
  equippedAbility3Id: number | null
  equippedAbility4Id: number | null
  _count: User_equipped_abilitiesCountAggregateOutputType | null
  _avg: User_equipped_abilitiesAvgAggregateOutputType | null
  _sum: User_equipped_abilitiesSumAggregateOutputType | null
  _min: User_equipped_abilitiesMinAggregateOutputType | null
  _max: User_equipped_abilitiesMaxAggregateOutputType | null
}

type GetUser_equipped_abilitiesGroupByPayload<T extends user_equipped_abilitiesGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_equipped_abilitiesGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_equipped_abilitiesGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_equipped_abilitiesGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_equipped_abilitiesGroupByOutputType[P]>
      }
    >
  >



export type user_equipped_abilitiesWhereInput = {
  AND?: Prisma.user_equipped_abilitiesWhereInput | Prisma.user_equipped_abilitiesWhereInput[]
  OR?: Prisma.user_equipped_abilitiesWhereInput[]
  NOT?: Prisma.user_equipped_abilitiesWhereInput | Prisma.user_equipped_abilitiesWhereInput[]
  userId?: Prisma.IntFilter<"user_equipped_abilities"> | number
  equippedAbility1Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility2Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility3Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility4Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  talent_equippedAbility1?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility2?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility3?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility4?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
}

export type user_equipped_abilitiesOrderByWithRelationInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  talent_equippedAbility1?: Prisma.talentOrderByWithRelationInput
  talent_equippedAbility2?: Prisma.talentOrderByWithRelationInput
  talent_equippedAbility3?: Prisma.talentOrderByWithRelationInput
  talent_equippedAbility4?: Prisma.talentOrderByWithRelationInput
}

export type user_equipped_abilitiesWhereUniqueInput = Prisma.AtLeast<{
  userId?: number
  AND?: Prisma.user_equipped_abilitiesWhereInput | Prisma.user_equipped_abilitiesWhereInput[]
  OR?: Prisma.user_equipped_abilitiesWhereInput[]
  NOT?: Prisma.user_equipped_abilitiesWhereInput | Prisma.user_equipped_abilitiesWhereInput[]
  equippedAbility1Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility2Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility3Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility4Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  talent_equippedAbility1?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility2?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility3?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
  talent_equippedAbility4?: Prisma.XOR<Prisma.TalentNullableScalarRelationFilter, Prisma.talentWhereInput> | null
}, "userId">

export type user_equipped_abilitiesOrderByWithAggregationInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrderInput | Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_equipped_abilitiesCountOrderByAggregateInput
  _avg?: Prisma.user_equipped_abilitiesAvgOrderByAggregateInput
  _max?: Prisma.user_equipped_abilitiesMaxOrderByAggregateInput
  _min?: Prisma.user_equipped_abilitiesMinOrderByAggregateInput
  _sum?: Prisma.user_equipped_abilitiesSumOrderByAggregateInput
}

export type user_equipped_abilitiesScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput | Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput | Prisma.user_equipped_abilitiesScalarWhereWithAggregatesInput[]
  userId?: Prisma.IntWithAggregatesFilter<"user_equipped_abilities"> | number
  equippedAbility1Id?: Prisma.IntNullableWithAggregatesFilter<"user_equipped_abilities"> | number | null
  equippedAbility2Id?: Prisma.IntNullableWithAggregatesFilter<"user_equipped_abilities"> | number | null
  equippedAbility3Id?: Prisma.IntNullableWithAggregatesFilter<"user_equipped_abilities"> | number | null
  equippedAbility4Id?: Prisma.IntNullableWithAggregatesFilter<"user_equipped_abilities"> | number | null
}

export type user_equipped_abilitiesCreateInput = {
  user: Prisma.userCreateNestedOneWithoutUser_equipped_abilitiesInput
  talent_equippedAbility1?: Prisma.talentCreateNestedOneWithoutUsersWithAbility1Input
  talent_equippedAbility2?: Prisma.talentCreateNestedOneWithoutUsersWithAbility2Input
  talent_equippedAbility3?: Prisma.talentCreateNestedOneWithoutUsersWithAbility3Input
  talent_equippedAbility4?: Prisma.talentCreateNestedOneWithoutUsersWithAbility4Input
}

export type user_equipped_abilitiesUncheckedCreateInput = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesUpdateInput = {
  user?: Prisma.userUpdateOneRequiredWithoutUser_equipped_abilitiesNestedInput
  talent_equippedAbility1?: Prisma.talentUpdateOneWithoutUsersWithAbility1NestedInput
  talent_equippedAbility2?: Prisma.talentUpdateOneWithoutUsersWithAbility2NestedInput
  talent_equippedAbility3?: Prisma.talentUpdateOneWithoutUsersWithAbility3NestedInput
  talent_equippedAbility4?: Prisma.talentUpdateOneWithoutUsersWithAbility4NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateInput = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesCreateManyInput = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesUpdateManyMutationInput = {

}

export type user_equipped_abilitiesUncheckedUpdateManyInput = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_equipped_abilitiesListRelationFilter = {
  every?: Prisma.user_equipped_abilitiesWhereInput
  some?: Prisma.user_equipped_abilitiesWhereInput
  none?: Prisma.user_equipped_abilitiesWhereInput
}

export type user_equipped_abilitiesOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type User_equipped_abilitiesNullableScalarRelationFilter = {
  is?: Prisma.user_equipped_abilitiesWhereInput | null
  isNot?: Prisma.user_equipped_abilitiesWhereInput | null
}

export type user_equipped_abilitiesCountOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrder
}

export type user_equipped_abilitiesAvgOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrder
}

export type user_equipped_abilitiesMaxOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrder
}

export type user_equipped_abilitiesMinOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrder
}

export type user_equipped_abilitiesSumOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  equippedAbility1Id?: Prisma.SortOrder
  equippedAbility2Id?: Prisma.SortOrder
  equippedAbility3Id?: Prisma.SortOrder
  equippedAbility4Id?: Prisma.SortOrder
}

export type user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4InputEnvelope
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
}

export type user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility1Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility1Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility1Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility2Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility2Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility2Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility3Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility3Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility3Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility4Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility4Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility4Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility1Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility1Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility1Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility2Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility2Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility2Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility3Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility3Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility3Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input> | Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input[] | Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input[]
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input[]
  upsert?: Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility4Input[]
  createMany?: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4InputEnvelope
  set?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  disconnect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  delete?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput | Prisma.user_equipped_abilitiesWhereUniqueInput[]
  update?: Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility4Input[]
  updateMany?: Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility4Input[]
  deleteMany?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
}

export type user_equipped_abilitiesCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutUserInput
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput
}

export type user_equipped_abilitiesUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutUserInput
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput
}

export type user_equipped_abilitiesUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutUserInput
  upsert?: Prisma.user_equipped_abilitiesUpsertWithoutUserInput
  disconnect?: Prisma.user_equipped_abilitiesWhereInput | boolean
  delete?: Prisma.user_equipped_abilitiesWhereInput | boolean
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.user_equipped_abilitiesUpdateToOneWithWhereWithoutUserInput, Prisma.user_equipped_abilitiesUpdateWithoutUserInput>, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutUserInput>
}

export type user_equipped_abilitiesUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_equipped_abilitiesCreateOrConnectWithoutUserInput
  upsert?: Prisma.user_equipped_abilitiesUpsertWithoutUserInput
  disconnect?: Prisma.user_equipped_abilitiesWhereInput | boolean
  delete?: Prisma.user_equipped_abilitiesWhereInput | boolean
  connect?: Prisma.user_equipped_abilitiesWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.user_equipped_abilitiesUpdateToOneWithWhereWithoutUserInput, Prisma.user_equipped_abilitiesUpdateWithoutUserInput>, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutUserInput>
}

export type user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input = {
  user: Prisma.userCreateNestedOneWithoutUser_equipped_abilitiesInput
  talent_equippedAbility2?: Prisma.talentCreateNestedOneWithoutUsersWithAbility2Input
  talent_equippedAbility3?: Prisma.talentCreateNestedOneWithoutUsersWithAbility3Input
  talent_equippedAbility4?: Prisma.talentCreateNestedOneWithoutUsersWithAbility4Input
}

export type user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input = {
  userId: number
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility1Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input>
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility1InputEnvelope = {
  data: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1Input | Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility1Input[]
  skipDuplicates?: boolean
}

export type user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input = {
  user: Prisma.userCreateNestedOneWithoutUser_equipped_abilitiesInput
  talent_equippedAbility1?: Prisma.talentCreateNestedOneWithoutUsersWithAbility1Input
  talent_equippedAbility3?: Prisma.talentCreateNestedOneWithoutUsersWithAbility3Input
  talent_equippedAbility4?: Prisma.talentCreateNestedOneWithoutUsersWithAbility4Input
}

export type user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility2Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input>
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility2InputEnvelope = {
  data: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2Input | Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility2Input[]
  skipDuplicates?: boolean
}

export type user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input = {
  user: Prisma.userCreateNestedOneWithoutUser_equipped_abilitiesInput
  talent_equippedAbility1?: Prisma.talentCreateNestedOneWithoutUsersWithAbility1Input
  talent_equippedAbility2?: Prisma.talentCreateNestedOneWithoutUsersWithAbility2Input
  talent_equippedAbility4?: Prisma.talentCreateNestedOneWithoutUsersWithAbility4Input
}

export type user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility3Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input>
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility3InputEnvelope = {
  data: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3Input | Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility3Input[]
  skipDuplicates?: boolean
}

export type user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input = {
  user: Prisma.userCreateNestedOneWithoutUser_equipped_abilitiesInput
  talent_equippedAbility1?: Prisma.talentCreateNestedOneWithoutUsersWithAbility1Input
  talent_equippedAbility2?: Prisma.talentCreateNestedOneWithoutUsersWithAbility2Input
  talent_equippedAbility3?: Prisma.talentCreateNestedOneWithoutUsersWithAbility3Input
}

export type user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
}

export type user_equipped_abilitiesCreateOrConnectWithoutTalent_equippedAbility4Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input>
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility4InputEnvelope = {
  data: Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4Input | Prisma.user_equipped_abilitiesCreateManyTalent_equippedAbility4Input[]
  skipDuplicates?: boolean
}

export type user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility1Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility1Input>
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility1Input>
}

export type user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility1Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility1Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility1Input>
}

export type user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility1Input = {
  where: Prisma.user_equipped_abilitiesScalarWhereInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateManyMutationInput, Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1Input>
}

export type user_equipped_abilitiesScalarWhereInput = {
  AND?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
  OR?: Prisma.user_equipped_abilitiesScalarWhereInput[]
  NOT?: Prisma.user_equipped_abilitiesScalarWhereInput | Prisma.user_equipped_abilitiesScalarWhereInput[]
  userId?: Prisma.IntFilter<"user_equipped_abilities"> | number
  equippedAbility1Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility2Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility3Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
  equippedAbility4Id?: Prisma.IntNullableFilter<"user_equipped_abilities"> | number | null
}

export type user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility2Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility2Input>
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility2Input>
}

export type user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility2Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility2Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility2Input>
}

export type user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility2Input = {
  where: Prisma.user_equipped_abilitiesScalarWhereInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateManyMutationInput, Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2Input>
}

export type user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility3Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility3Input>
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility3Input>
}

export type user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility3Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility3Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility3Input>
}

export type user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility3Input = {
  where: Prisma.user_equipped_abilitiesScalarWhereInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateManyMutationInput, Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3Input>
}

export type user_equipped_abilitiesUpsertWithWhereUniqueWithoutTalent_equippedAbility4Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility4Input>
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedCreateWithoutTalent_equippedAbility4Input>
}

export type user_equipped_abilitiesUpdateWithWhereUniqueWithoutTalent_equippedAbility4Input = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutTalent_equippedAbility4Input, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility4Input>
}

export type user_equipped_abilitiesUpdateManyWithWhereWithoutTalent_equippedAbility4Input = {
  where: Prisma.user_equipped_abilitiesScalarWhereInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateManyMutationInput, Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4Input>
}

export type user_equipped_abilitiesCreateWithoutUserInput = {
  talent_equippedAbility1?: Prisma.talentCreateNestedOneWithoutUsersWithAbility1Input
  talent_equippedAbility2?: Prisma.talentCreateNestedOneWithoutUsersWithAbility2Input
  talent_equippedAbility3?: Prisma.talentCreateNestedOneWithoutUsersWithAbility3Input
  talent_equippedAbility4?: Prisma.talentCreateNestedOneWithoutUsersWithAbility4Input
}

export type user_equipped_abilitiesUncheckedCreateWithoutUserInput = {
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateOrConnectWithoutUserInput = {
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
}

export type user_equipped_abilitiesUpsertWithoutUserInput = {
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedCreateWithoutUserInput>
  where?: Prisma.user_equipped_abilitiesWhereInput
}

export type user_equipped_abilitiesUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.user_equipped_abilitiesWhereInput
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateWithoutUserInput, Prisma.user_equipped_abilitiesUncheckedUpdateWithoutUserInput>
}

export type user_equipped_abilitiesUpdateWithoutUserInput = {
  talent_equippedAbility1?: Prisma.talentUpdateOneWithoutUsersWithAbility1NestedInput
  talent_equippedAbility2?: Prisma.talentUpdateOneWithoutUsersWithAbility2NestedInput
  talent_equippedAbility3?: Prisma.talentUpdateOneWithoutUsersWithAbility3NestedInput
  talent_equippedAbility4?: Prisma.talentUpdateOneWithoutUsersWithAbility4NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateWithoutUserInput = {
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility1Input = {
  userId: number
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility2Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility3Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility3Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility4Id?: number | null
}

export type user_equipped_abilitiesCreateManyTalent_equippedAbility4Input = {
  userId: number
  equippedAbility1Id?: number | null
  equippedAbility2Id?: number | null
  equippedAbility3Id?: number | null
}

export type user_equipped_abilitiesUpdateWithoutTalent_equippedAbility1Input = {
  user?: Prisma.userUpdateOneRequiredWithoutUser_equipped_abilitiesNestedInput
  talent_equippedAbility2?: Prisma.talentUpdateOneWithoutUsersWithAbility2NestedInput
  talent_equippedAbility3?: Prisma.talentUpdateOneWithoutUsersWithAbility3NestedInput
  talent_equippedAbility4?: Prisma.talentUpdateOneWithoutUsersWithAbility4NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility1Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUpdateWithoutTalent_equippedAbility2Input = {
  user?: Prisma.userUpdateOneRequiredWithoutUser_equipped_abilitiesNestedInput
  talent_equippedAbility1?: Prisma.talentUpdateOneWithoutUsersWithAbility1NestedInput
  talent_equippedAbility3?: Prisma.talentUpdateOneWithoutUsersWithAbility3NestedInput
  talent_equippedAbility4?: Prisma.talentUpdateOneWithoutUsersWithAbility4NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility2Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUpdateWithoutTalent_equippedAbility3Input = {
  user?: Prisma.userUpdateOneRequiredWithoutUser_equipped_abilitiesNestedInput
  talent_equippedAbility1?: Prisma.talentUpdateOneWithoutUsersWithAbility1NestedInput
  talent_equippedAbility2?: Prisma.talentUpdateOneWithoutUsersWithAbility2NestedInput
  talent_equippedAbility4?: Prisma.talentUpdateOneWithoutUsersWithAbility4NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility3Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility4Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUpdateWithoutTalent_equippedAbility4Input = {
  user?: Prisma.userUpdateOneRequiredWithoutUser_equipped_abilitiesNestedInput
  talent_equippedAbility1?: Prisma.talentUpdateOneWithoutUsersWithAbility1NestedInput
  talent_equippedAbility2?: Prisma.talentUpdateOneWithoutUsersWithAbility2NestedInput
  talent_equippedAbility3?: Prisma.talentUpdateOneWithoutUsersWithAbility3NestedInput
}

export type user_equipped_abilitiesUncheckedUpdateWithoutTalent_equippedAbility4Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4Input = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  equippedAbility1Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility2Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equippedAbility3Id?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type user_equipped_abilitiesSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  userId?: boolean
  equippedAbility1Id?: boolean
  equippedAbility2Id?: boolean
  equippedAbility3Id?: boolean
  equippedAbility4Id?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  talent_equippedAbility1?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility1Args<ExtArgs>
  talent_equippedAbility2?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility2Args<ExtArgs>
  talent_equippedAbility3?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility3Args<ExtArgs>
  talent_equippedAbility4?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility4Args<ExtArgs>
}, ExtArgs["result"]["user_equipped_abilities"]>



export type user_equipped_abilitiesSelectScalar = {
  userId?: boolean
  equippedAbility1Id?: boolean
  equippedAbility2Id?: boolean
  equippedAbility3Id?: boolean
  equippedAbility4Id?: boolean
}

export type user_equipped_abilitiesOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"userId" | "equippedAbility1Id" | "equippedAbility2Id" | "equippedAbility3Id" | "equippedAbility4Id", ExtArgs["result"]["user_equipped_abilities"]>
export type user_equipped_abilitiesInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  talent_equippedAbility1?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility1Args<ExtArgs>
  talent_equippedAbility2?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility2Args<ExtArgs>
  talent_equippedAbility3?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility3Args<ExtArgs>
  talent_equippedAbility4?: boolean | Prisma.user_equipped_abilities$talent_equippedAbility4Args<ExtArgs>
}

export type $user_equipped_abilitiesPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_equipped_abilities"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    talent_equippedAbility1: Prisma.$talentPayload<ExtArgs> | null
    talent_equippedAbility2: Prisma.$talentPayload<ExtArgs> | null
    talent_equippedAbility3: Prisma.$talentPayload<ExtArgs> | null
    talent_equippedAbility4: Prisma.$talentPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    userId: number
    equippedAbility1Id: number | null
    equippedAbility2Id: number | null
    equippedAbility3Id: number | null
    equippedAbility4Id: number | null
  }, ExtArgs["result"]["user_equipped_abilities"]>
  composites: {}
}

export type user_equipped_abilitiesGetPayload<S extends boolean | null | undefined | user_equipped_abilitiesDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload, S>

export type user_equipped_abilitiesCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_equipped_abilitiesFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_equipped_abilitiesCountAggregateInputType | true
  }

export interface user_equipped_abilitiesDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_equipped_abilities'], meta: { name: 'user_equipped_abilities' } }
  /**
   * Find zero or one User_equipped_abilities that matches the filter.
   * @param {user_equipped_abilitiesFindUniqueArgs} args - Arguments to find a User_equipped_abilities
   * @example
   * // Get one User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_equipped_abilitiesFindUniqueArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_equipped_abilities that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_equipped_abilitiesFindUniqueOrThrowArgs} args - Arguments to find a User_equipped_abilities
   * @example
   * // Get one User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_equipped_abilitiesFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_equipped_abilities that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesFindFirstArgs} args - Arguments to find a User_equipped_abilities
   * @example
   * // Get one User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_equipped_abilitiesFindFirstArgs>(args?: Prisma.SelectSubset<T, user_equipped_abilitiesFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_equipped_abilities that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesFindFirstOrThrowArgs} args - Arguments to find a User_equipped_abilities
   * @example
   * // Get one User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_equipped_abilitiesFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_equipped_abilitiesFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_equipped_abilities that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findMany()
   * 
   * // Get first 10 User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.findMany({ take: 10 })
   * 
   * // Only select the `userId`
   * const user_equipped_abilitiesWithUserIdOnly = await prisma.user_equipped_abilities.findMany({ select: { userId: true } })
   * 
   */
  findMany<T extends user_equipped_abilitiesFindManyArgs>(args?: Prisma.SelectSubset<T, user_equipped_abilitiesFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_equipped_abilities.
   * @param {user_equipped_abilitiesCreateArgs} args - Arguments to create a User_equipped_abilities.
   * @example
   * // Create one User_equipped_abilities
   * const User_equipped_abilities = await prisma.user_equipped_abilities.create({
   *   data: {
   *     // ... data to create a User_equipped_abilities
   *   }
   * })
   * 
   */
  create<T extends user_equipped_abilitiesCreateArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesCreateArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_equipped_abilities.
   * @param {user_equipped_abilitiesCreateManyArgs} args - Arguments to create many User_equipped_abilities.
   * @example
   * // Create many User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_equipped_abilitiesCreateManyArgs>(args?: Prisma.SelectSubset<T, user_equipped_abilitiesCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_equipped_abilities.
   * @param {user_equipped_abilitiesDeleteArgs} args - Arguments to delete one User_equipped_abilities.
   * @example
   * // Delete one User_equipped_abilities
   * const User_equipped_abilities = await prisma.user_equipped_abilities.delete({
   *   where: {
   *     // ... filter to delete one User_equipped_abilities
   *   }
   * })
   * 
   */
  delete<T extends user_equipped_abilitiesDeleteArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesDeleteArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_equipped_abilities.
   * @param {user_equipped_abilitiesUpdateArgs} args - Arguments to update one User_equipped_abilities.
   * @example
   * // Update one User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_equipped_abilitiesUpdateArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesUpdateArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_equipped_abilities.
   * @param {user_equipped_abilitiesDeleteManyArgs} args - Arguments to filter User_equipped_abilities to delete.
   * @example
   * // Delete a few User_equipped_abilities
   * const { count } = await prisma.user_equipped_abilities.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_equipped_abilitiesDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_equipped_abilitiesDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_equipped_abilities.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_equipped_abilitiesUpdateManyArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_equipped_abilities.
   * @param {user_equipped_abilitiesUpsertArgs} args - Arguments to update or create a User_equipped_abilities.
   * @example
   * // Update or create a User_equipped_abilities
   * const user_equipped_abilities = await prisma.user_equipped_abilities.upsert({
   *   create: {
   *     // ... data to create a User_equipped_abilities
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_equipped_abilities we want to update
   *   }
   * })
   */
  upsert<T extends user_equipped_abilitiesUpsertArgs>(args: Prisma.SelectSubset<T, user_equipped_abilitiesUpsertArgs<ExtArgs>>): Prisma.Prisma__user_equipped_abilitiesClient<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_equipped_abilities.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesCountArgs} args - Arguments to filter User_equipped_abilities to count.
   * @example
   * // Count the number of User_equipped_abilities
   * const count = await prisma.user_equipped_abilities.count({
   *   where: {
   *     // ... the filter for the User_equipped_abilities we want to count
   *   }
   * })
  **/
  count<T extends user_equipped_abilitiesCountArgs>(
    args?: Prisma.Subset<T, user_equipped_abilitiesCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_equipped_abilitiesCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_equipped_abilities.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_equipped_abilitiesAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_equipped_abilitiesAggregateArgs>(args: Prisma.Subset<T, User_equipped_abilitiesAggregateArgs>): Prisma.PrismaPromise<GetUser_equipped_abilitiesAggregateType<T>>

  /**
   * Group by User_equipped_abilities.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_equipped_abilitiesGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_equipped_abilitiesGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_equipped_abilitiesGroupByArgs['orderBy'] }
      : { orderBy?: user_equipped_abilitiesGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_equipped_abilitiesGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_equipped_abilitiesGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_equipped_abilities model
 */
readonly fields: user_equipped_abilitiesFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_equipped_abilities.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_equipped_abilitiesClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  talent_equippedAbility1<T extends Prisma.user_equipped_abilities$talent_equippedAbility1Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_equipped_abilities$talent_equippedAbility1Args<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  talent_equippedAbility2<T extends Prisma.user_equipped_abilities$talent_equippedAbility2Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_equipped_abilities$talent_equippedAbility2Args<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  talent_equippedAbility3<T extends Prisma.user_equipped_abilities$talent_equippedAbility3Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_equipped_abilities$talent_equippedAbility3Args<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  talent_equippedAbility4<T extends Prisma.user_equipped_abilities$talent_equippedAbility4Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_equipped_abilities$talent_equippedAbility4Args<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_equipped_abilities model
 */
export interface user_equipped_abilitiesFieldRefs {
  readonly userId: Prisma.FieldRef<"user_equipped_abilities", 'Int'>
  readonly equippedAbility1Id: Prisma.FieldRef<"user_equipped_abilities", 'Int'>
  readonly equippedAbility2Id: Prisma.FieldRef<"user_equipped_abilities", 'Int'>
  readonly equippedAbility3Id: Prisma.FieldRef<"user_equipped_abilities", 'Int'>
  readonly equippedAbility4Id: Prisma.FieldRef<"user_equipped_abilities", 'Int'>
}
    

// Custom InputTypes
/**
 * user_equipped_abilities findUnique
 */
export type user_equipped_abilitiesFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter, which user_equipped_abilities to fetch.
   */
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
}

/**
 * user_equipped_abilities findUniqueOrThrow
 */
export type user_equipped_abilitiesFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter, which user_equipped_abilities to fetch.
   */
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
}

/**
 * user_equipped_abilities findFirst
 */
export type user_equipped_abilitiesFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter, which user_equipped_abilities to fetch.
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_equipped_abilities to fetch.
   */
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_equipped_abilities.
   */
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_equipped_abilities from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_equipped_abilities.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_equipped_abilities.
   */
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * user_equipped_abilities findFirstOrThrow
 */
export type user_equipped_abilitiesFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter, which user_equipped_abilities to fetch.
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_equipped_abilities to fetch.
   */
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_equipped_abilities.
   */
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_equipped_abilities from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_equipped_abilities.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_equipped_abilities.
   */
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * user_equipped_abilities findMany
 */
export type user_equipped_abilitiesFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter, which user_equipped_abilities to fetch.
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_equipped_abilities to fetch.
   */
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_equipped_abilities.
   */
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_equipped_abilities from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_equipped_abilities.
   */
  skip?: number
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * user_equipped_abilities create
 */
export type user_equipped_abilitiesCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * The data needed to create a user_equipped_abilities.
   */
  data: Prisma.XOR<Prisma.user_equipped_abilitiesCreateInput, Prisma.user_equipped_abilitiesUncheckedCreateInput>
}

/**
 * user_equipped_abilities createMany
 */
export type user_equipped_abilitiesCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_equipped_abilities.
   */
  data: Prisma.user_equipped_abilitiesCreateManyInput | Prisma.user_equipped_abilitiesCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_equipped_abilities update
 */
export type user_equipped_abilitiesUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * The data needed to update a user_equipped_abilities.
   */
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateInput, Prisma.user_equipped_abilitiesUncheckedUpdateInput>
  /**
   * Choose, which user_equipped_abilities to update.
   */
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
}

/**
 * user_equipped_abilities updateMany
 */
export type user_equipped_abilitiesUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_equipped_abilities.
   */
  data: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateManyMutationInput, Prisma.user_equipped_abilitiesUncheckedUpdateManyInput>
  /**
   * Filter which user_equipped_abilities to update
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * Limit how many user_equipped_abilities to update.
   */
  limit?: number
}

/**
 * user_equipped_abilities upsert
 */
export type user_equipped_abilitiesUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * The filter to search for the user_equipped_abilities to update in case it exists.
   */
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
  /**
   * In case the user_equipped_abilities found by the `where` argument doesn't exist, create a new user_equipped_abilities with this data.
   */
  create: Prisma.XOR<Prisma.user_equipped_abilitiesCreateInput, Prisma.user_equipped_abilitiesUncheckedCreateInput>
  /**
   * In case the user_equipped_abilities was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_equipped_abilitiesUpdateInput, Prisma.user_equipped_abilitiesUncheckedUpdateInput>
}

/**
 * user_equipped_abilities delete
 */
export type user_equipped_abilitiesDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  /**
   * Filter which user_equipped_abilities to delete.
   */
  where: Prisma.user_equipped_abilitiesWhereUniqueInput
}

/**
 * user_equipped_abilities deleteMany
 */
export type user_equipped_abilitiesDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_equipped_abilities to delete
   */
  where?: Prisma.user_equipped_abilitiesWhereInput
  /**
   * Limit how many user_equipped_abilities to delete.
   */
  limit?: number
}

/**
 * user_equipped_abilities.talent_equippedAbility1
 */
export type user_equipped_abilities$talent_equippedAbility1Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  where?: Prisma.talentWhereInput
}

/**
 * user_equipped_abilities.talent_equippedAbility2
 */
export type user_equipped_abilities$talent_equippedAbility2Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  where?: Prisma.talentWhereInput
}

/**
 * user_equipped_abilities.talent_equippedAbility3
 */
export type user_equipped_abilities$talent_equippedAbility3Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  where?: Prisma.talentWhereInput
}

/**
 * user_equipped_abilities.talent_equippedAbility4
 */
export type user_equipped_abilities$talent_equippedAbility4Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  where?: Prisma.talentWhereInput
}

/**
 * user_equipped_abilities without action
 */
export type user_equipped_abilitiesDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
}
