
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `suggestion_vote` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model suggestion_vote
 * 
 */
export type suggestion_voteModel = runtime.Types.Result.DefaultSelection<Prisma.$suggestion_votePayload>

export type AggregateSuggestion_vote = {
  _count: Suggestion_voteCountAggregateOutputType | null
  _avg: Suggestion_voteAvgAggregateOutputType | null
  _sum: Suggestion_voteSumAggregateOutputType | null
  _min: Suggestion_voteMinAggregateOutputType | null
  _max: Suggestion_voteMaxAggregateOutputType | null
}

export type Suggestion_voteAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_voteSumAggregateOutputType = {
  id: number | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_voteMinAggregateOutputType = {
  id: number | null
  voteType: $Enums.SuggestionVoteTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_voteMaxAggregateOutputType = {
  id: number | null
  voteType: $Enums.SuggestionVoteTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_voteCountAggregateOutputType = {
  id: number
  voteType: number
  createdAt: number
  updatedAt: number
  userId: number
  suggestionId: number
  _all: number
}


export type Suggestion_voteAvgAggregateInputType = {
  id?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_voteSumAggregateInputType = {
  id?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_voteMinAggregateInputType = {
  id?: true
  voteType?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_voteMaxAggregateInputType = {
  id?: true
  voteType?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_voteCountAggregateInputType = {
  id?: true
  voteType?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
  _all?: true
}

export type Suggestion_voteAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestion_vote to aggregate.
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_votes to fetch.
   */
  orderBy?: Prisma.suggestion_voteOrderByWithRelationInput | Prisma.suggestion_voteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.suggestion_voteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_votes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_votes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned suggestion_votes
  **/
  _count?: true | Suggestion_voteCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Suggestion_voteAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Suggestion_voteSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Suggestion_voteMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Suggestion_voteMaxAggregateInputType
}

export type GetSuggestion_voteAggregateType<T extends Suggestion_voteAggregateArgs> = {
      [P in keyof T & keyof AggregateSuggestion_vote]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSuggestion_vote[P]>
    : Prisma.GetScalarType<T[P], AggregateSuggestion_vote[P]>
}




export type suggestion_voteGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.suggestion_voteWhereInput
  orderBy?: Prisma.suggestion_voteOrderByWithAggregationInput | Prisma.suggestion_voteOrderByWithAggregationInput[]
  by: Prisma.Suggestion_voteScalarFieldEnum[] | Prisma.Suggestion_voteScalarFieldEnum
  having?: Prisma.suggestion_voteScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Suggestion_voteCountAggregateInputType | true
  _avg?: Suggestion_voteAvgAggregateInputType
  _sum?: Suggestion_voteSumAggregateInputType
  _min?: Suggestion_voteMinAggregateInputType
  _max?: Suggestion_voteMaxAggregateInputType
}

export type Suggestion_voteGroupByOutputType = {
  id: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt: Date
  updatedAt: Date
  userId: number | null
  suggestionId: number | null
  _count: Suggestion_voteCountAggregateOutputType | null
  _avg: Suggestion_voteAvgAggregateOutputType | null
  _sum: Suggestion_voteSumAggregateOutputType | null
  _min: Suggestion_voteMinAggregateOutputType | null
  _max: Suggestion_voteMaxAggregateOutputType | null
}

type GetSuggestion_voteGroupByPayload<T extends suggestion_voteGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Suggestion_voteGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Suggestion_voteGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Suggestion_voteGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Suggestion_voteGroupByOutputType[P]>
      }
    >
  >



export type suggestion_voteWhereInput = {
  AND?: Prisma.suggestion_voteWhereInput | Prisma.suggestion_voteWhereInput[]
  OR?: Prisma.suggestion_voteWhereInput[]
  NOT?: Prisma.suggestion_voteWhereInput | Prisma.suggestion_voteWhereInput[]
  id?: Prisma.IntFilter<"suggestion_vote"> | number
  voteType?: Prisma.EnumSuggestionVoteTypesFilter<"suggestion_vote"> | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion?: Prisma.XOR<Prisma.SuggestionNullableScalarRelationFilter, Prisma.suggestionWhereInput> | null
}

export type suggestion_voteOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  voteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  suggestion?: Prisma.suggestionOrderByWithRelationInput
}

export type suggestion_voteWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.suggestion_voteWhereInput | Prisma.suggestion_voteWhereInput[]
  OR?: Prisma.suggestion_voteWhereInput[]
  NOT?: Prisma.suggestion_voteWhereInput | Prisma.suggestion_voteWhereInput[]
  voteType?: Prisma.EnumSuggestionVoteTypesFilter<"suggestion_vote"> | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion?: Prisma.XOR<Prisma.SuggestionNullableScalarRelationFilter, Prisma.suggestionWhereInput> | null
}, "id">

export type suggestion_voteOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  voteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.suggestion_voteCountOrderByAggregateInput
  _avg?: Prisma.suggestion_voteAvgOrderByAggregateInput
  _max?: Prisma.suggestion_voteMaxOrderByAggregateInput
  _min?: Prisma.suggestion_voteMinOrderByAggregateInput
  _sum?: Prisma.suggestion_voteSumOrderByAggregateInput
}

export type suggestion_voteScalarWhereWithAggregatesInput = {
  AND?: Prisma.suggestion_voteScalarWhereWithAggregatesInput | Prisma.suggestion_voteScalarWhereWithAggregatesInput[]
  OR?: Prisma.suggestion_voteScalarWhereWithAggregatesInput[]
  NOT?: Prisma.suggestion_voteScalarWhereWithAggregatesInput | Prisma.suggestion_voteScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"suggestion_vote"> | number
  voteType?: Prisma.EnumSuggestionVoteTypesWithAggregatesFilter<"suggestion_vote"> | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion_vote"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion_vote"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"suggestion_vote"> | number | null
  suggestionId?: Prisma.IntNullableWithAggregatesFilter<"suggestion_vote"> | number | null
}

export type suggestion_voteCreateInput = {
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestion_voteInput
  suggestion?: Prisma.suggestionCreateNestedOneWithoutSuggestion_voteInput
}

export type suggestion_voteUncheckedCreateInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestionId?: number | null
}

export type suggestion_voteUpdateInput = {
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestion_voteNestedInput
  suggestion?: Prisma.suggestionUpdateOneWithoutSuggestion_voteNestedInput
}

export type suggestion_voteUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_voteCreateManyInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestionId?: number | null
}

export type suggestion_voteUpdateManyMutationInput = {
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type suggestion_voteUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Suggestion_voteListRelationFilter = {
  every?: Prisma.suggestion_voteWhereInput
  some?: Prisma.suggestion_voteWhereInput
  none?: Prisma.suggestion_voteWhereInput
}

export type suggestion_voteOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type suggestion_voteCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  voteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_voteAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_voteMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  voteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_voteMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  voteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_voteSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_voteCreateNestedManyWithoutSuggestionInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_voteCreateWithoutSuggestionInput[] | Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_voteCreateManySuggestionInputEnvelope
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
}

export type suggestion_voteUncheckedCreateNestedManyWithoutSuggestionInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_voteCreateWithoutSuggestionInput[] | Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_voteCreateManySuggestionInputEnvelope
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
}

export type suggestion_voteUpdateManyWithoutSuggestionNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_voteCreateWithoutSuggestionInput[] | Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput[]
  upsert?: Prisma.suggestion_voteUpsertWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_voteUpsertWithWhereUniqueWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_voteCreateManySuggestionInputEnvelope
  set?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  disconnect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  delete?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  update?: Prisma.suggestion_voteUpdateWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_voteUpdateWithWhereUniqueWithoutSuggestionInput[]
  updateMany?: Prisma.suggestion_voteUpdateManyWithWhereWithoutSuggestionInput | Prisma.suggestion_voteUpdateManyWithWhereWithoutSuggestionInput[]
  deleteMany?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
}

export type suggestion_voteUncheckedUpdateManyWithoutSuggestionNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_voteCreateWithoutSuggestionInput[] | Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_voteCreateOrConnectWithoutSuggestionInput[]
  upsert?: Prisma.suggestion_voteUpsertWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_voteUpsertWithWhereUniqueWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_voteCreateManySuggestionInputEnvelope
  set?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  disconnect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  delete?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  update?: Prisma.suggestion_voteUpdateWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_voteUpdateWithWhereUniqueWithoutSuggestionInput[]
  updateMany?: Prisma.suggestion_voteUpdateManyWithWhereWithoutSuggestionInput | Prisma.suggestion_voteUpdateManyWithWhereWithoutSuggestionInput[]
  deleteMany?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
}

export type EnumSuggestionVoteTypesFieldUpdateOperationsInput = {
  set?: $Enums.SuggestionVoteTypes
}

export type suggestion_voteCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput> | Prisma.suggestion_voteCreateWithoutUserInput[] | Prisma.suggestion_voteUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutUserInput | Prisma.suggestion_voteCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestion_voteCreateManyUserInputEnvelope
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
}

export type suggestion_voteUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput> | Prisma.suggestion_voteCreateWithoutUserInput[] | Prisma.suggestion_voteUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutUserInput | Prisma.suggestion_voteCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestion_voteCreateManyUserInputEnvelope
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
}

export type suggestion_voteUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput> | Prisma.suggestion_voteCreateWithoutUserInput[] | Prisma.suggestion_voteUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutUserInput | Prisma.suggestion_voteCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestion_voteUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestion_voteUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestion_voteCreateManyUserInputEnvelope
  set?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  disconnect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  delete?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  update?: Prisma.suggestion_voteUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestion_voteUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestion_voteUpdateManyWithWhereWithoutUserInput | Prisma.suggestion_voteUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
}

export type suggestion_voteUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput> | Prisma.suggestion_voteCreateWithoutUserInput[] | Prisma.suggestion_voteUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_voteCreateOrConnectWithoutUserInput | Prisma.suggestion_voteCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestion_voteUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestion_voteUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestion_voteCreateManyUserInputEnvelope
  set?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  disconnect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  delete?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  connect?: Prisma.suggestion_voteWhereUniqueInput | Prisma.suggestion_voteWhereUniqueInput[]
  update?: Prisma.suggestion_voteUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestion_voteUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestion_voteUpdateManyWithWhereWithoutUserInput | Prisma.suggestion_voteUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
}

export type suggestion_voteCreateWithoutSuggestionInput = {
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestion_voteInput
}

export type suggestion_voteUncheckedCreateWithoutSuggestionInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type suggestion_voteCreateOrConnectWithoutSuggestionInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput>
}

export type suggestion_voteCreateManySuggestionInputEnvelope = {
  data: Prisma.suggestion_voteCreateManySuggestionInput | Prisma.suggestion_voteCreateManySuggestionInput[]
  skipDuplicates?: boolean
}

export type suggestion_voteUpsertWithWhereUniqueWithoutSuggestionInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  update: Prisma.XOR<Prisma.suggestion_voteUpdateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedUpdateWithoutSuggestionInput>
  create: Prisma.XOR<Prisma.suggestion_voteCreateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedCreateWithoutSuggestionInput>
}

export type suggestion_voteUpdateWithWhereUniqueWithoutSuggestionInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  data: Prisma.XOR<Prisma.suggestion_voteUpdateWithoutSuggestionInput, Prisma.suggestion_voteUncheckedUpdateWithoutSuggestionInput>
}

export type suggestion_voteUpdateManyWithWhereWithoutSuggestionInput = {
  where: Prisma.suggestion_voteScalarWhereInput
  data: Prisma.XOR<Prisma.suggestion_voteUpdateManyMutationInput, Prisma.suggestion_voteUncheckedUpdateManyWithoutSuggestionInput>
}

export type suggestion_voteScalarWhereInput = {
  AND?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
  OR?: Prisma.suggestion_voteScalarWhereInput[]
  NOT?: Prisma.suggestion_voteScalarWhereInput | Prisma.suggestion_voteScalarWhereInput[]
  id?: Prisma.IntFilter<"suggestion_vote"> | number
  voteType?: Prisma.EnumSuggestionVoteTypesFilter<"suggestion_vote"> | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_vote"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_vote"> | number | null
}

export type suggestion_voteCreateWithoutUserInput = {
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestion?: Prisma.suggestionCreateNestedOneWithoutSuggestion_voteInput
}

export type suggestion_voteUncheckedCreateWithoutUserInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestionId?: number | null
}

export type suggestion_voteCreateOrConnectWithoutUserInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput>
}

export type suggestion_voteCreateManyUserInputEnvelope = {
  data: Prisma.suggestion_voteCreateManyUserInput | Prisma.suggestion_voteCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type suggestion_voteUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  update: Prisma.XOR<Prisma.suggestion_voteUpdateWithoutUserInput, Prisma.suggestion_voteUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.suggestion_voteCreateWithoutUserInput, Prisma.suggestion_voteUncheckedCreateWithoutUserInput>
}

export type suggestion_voteUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestion_voteWhereUniqueInput
  data: Prisma.XOR<Prisma.suggestion_voteUpdateWithoutUserInput, Prisma.suggestion_voteUncheckedUpdateWithoutUserInput>
}

export type suggestion_voteUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.suggestion_voteScalarWhereInput
  data: Prisma.XOR<Prisma.suggestion_voteUpdateManyMutationInput, Prisma.suggestion_voteUncheckedUpdateManyWithoutUserInput>
}

export type suggestion_voteCreateManySuggestionInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type suggestion_voteUpdateWithoutSuggestionInput = {
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestion_voteNestedInput
}

export type suggestion_voteUncheckedUpdateWithoutSuggestionInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_voteUncheckedUpdateManyWithoutSuggestionInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_voteCreateManyUserInput = {
  id?: number
  voteType: $Enums.SuggestionVoteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestionId?: number | null
}

export type suggestion_voteUpdateWithoutUserInput = {
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestion?: Prisma.suggestionUpdateOneWithoutSuggestion_voteNestedInput
}

export type suggestion_voteUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_voteUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  voteType?: Prisma.EnumSuggestionVoteTypesFieldUpdateOperationsInput | $Enums.SuggestionVoteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type suggestion_voteSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  voteType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  suggestionId?: boolean
  user?: boolean | Prisma.suggestion_vote$userArgs<ExtArgs>
  suggestion?: boolean | Prisma.suggestion_vote$suggestionArgs<ExtArgs>
}, ExtArgs["result"]["suggestion_vote"]>



export type suggestion_voteSelectScalar = {
  id?: boolean
  voteType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  suggestionId?: boolean
}

export type suggestion_voteOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "voteType" | "createdAt" | "updatedAt" | "userId" | "suggestionId", ExtArgs["result"]["suggestion_vote"]>
export type suggestion_voteInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.suggestion_vote$userArgs<ExtArgs>
  suggestion?: boolean | Prisma.suggestion_vote$suggestionArgs<ExtArgs>
}

export type $suggestion_votePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "suggestion_vote"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    suggestion: Prisma.$suggestionPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    voteType: $Enums.SuggestionVoteTypes
    createdAt: Date
    updatedAt: Date
    userId: number | null
    suggestionId: number | null
  }, ExtArgs["result"]["suggestion_vote"]>
  composites: {}
}

export type suggestion_voteGetPayload<S extends boolean | null | undefined | suggestion_voteDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload, S>

export type suggestion_voteCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<suggestion_voteFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Suggestion_voteCountAggregateInputType | true
  }

export interface suggestion_voteDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['suggestion_vote'], meta: { name: 'suggestion_vote' } }
  /**
   * Find zero or one Suggestion_vote that matches the filter.
   * @param {suggestion_voteFindUniqueArgs} args - Arguments to find a Suggestion_vote
   * @example
   * // Get one Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends suggestion_voteFindUniqueArgs>(args: Prisma.SelectSubset<T, suggestion_voteFindUniqueArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Suggestion_vote that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {suggestion_voteFindUniqueOrThrowArgs} args - Arguments to find a Suggestion_vote
   * @example
   * // Get one Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends suggestion_voteFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, suggestion_voteFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion_vote that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteFindFirstArgs} args - Arguments to find a Suggestion_vote
   * @example
   * // Get one Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends suggestion_voteFindFirstArgs>(args?: Prisma.SelectSubset<T, suggestion_voteFindFirstArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion_vote that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteFindFirstOrThrowArgs} args - Arguments to find a Suggestion_vote
   * @example
   * // Get one Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends suggestion_voteFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, suggestion_voteFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Suggestion_votes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Suggestion_votes
   * const suggestion_votes = await prisma.suggestion_vote.findMany()
   * 
   * // Get first 10 Suggestion_votes
   * const suggestion_votes = await prisma.suggestion_vote.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const suggestion_voteWithIdOnly = await prisma.suggestion_vote.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends suggestion_voteFindManyArgs>(args?: Prisma.SelectSubset<T, suggestion_voteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Suggestion_vote.
   * @param {suggestion_voteCreateArgs} args - Arguments to create a Suggestion_vote.
   * @example
   * // Create one Suggestion_vote
   * const Suggestion_vote = await prisma.suggestion_vote.create({
   *   data: {
   *     // ... data to create a Suggestion_vote
   *   }
   * })
   * 
   */
  create<T extends suggestion_voteCreateArgs>(args: Prisma.SelectSubset<T, suggestion_voteCreateArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Suggestion_votes.
   * @param {suggestion_voteCreateManyArgs} args - Arguments to create many Suggestion_votes.
   * @example
   * // Create many Suggestion_votes
   * const suggestion_vote = await prisma.suggestion_vote.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends suggestion_voteCreateManyArgs>(args?: Prisma.SelectSubset<T, suggestion_voteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Suggestion_vote.
   * @param {suggestion_voteDeleteArgs} args - Arguments to delete one Suggestion_vote.
   * @example
   * // Delete one Suggestion_vote
   * const Suggestion_vote = await prisma.suggestion_vote.delete({
   *   where: {
   *     // ... filter to delete one Suggestion_vote
   *   }
   * })
   * 
   */
  delete<T extends suggestion_voteDeleteArgs>(args: Prisma.SelectSubset<T, suggestion_voteDeleteArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Suggestion_vote.
   * @param {suggestion_voteUpdateArgs} args - Arguments to update one Suggestion_vote.
   * @example
   * // Update one Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends suggestion_voteUpdateArgs>(args: Prisma.SelectSubset<T, suggestion_voteUpdateArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Suggestion_votes.
   * @param {suggestion_voteDeleteManyArgs} args - Arguments to filter Suggestion_votes to delete.
   * @example
   * // Delete a few Suggestion_votes
   * const { count } = await prisma.suggestion_vote.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends suggestion_voteDeleteManyArgs>(args?: Prisma.SelectSubset<T, suggestion_voteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Suggestion_votes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Suggestion_votes
   * const suggestion_vote = await prisma.suggestion_vote.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends suggestion_voteUpdateManyArgs>(args: Prisma.SelectSubset<T, suggestion_voteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Suggestion_vote.
   * @param {suggestion_voteUpsertArgs} args - Arguments to update or create a Suggestion_vote.
   * @example
   * // Update or create a Suggestion_vote
   * const suggestion_vote = await prisma.suggestion_vote.upsert({
   *   create: {
   *     // ... data to create a Suggestion_vote
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Suggestion_vote we want to update
   *   }
   * })
   */
  upsert<T extends suggestion_voteUpsertArgs>(args: Prisma.SelectSubset<T, suggestion_voteUpsertArgs<ExtArgs>>): Prisma.Prisma__suggestion_voteClient<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Suggestion_votes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteCountArgs} args - Arguments to filter Suggestion_votes to count.
   * @example
   * // Count the number of Suggestion_votes
   * const count = await prisma.suggestion_vote.count({
   *   where: {
   *     // ... the filter for the Suggestion_votes we want to count
   *   }
   * })
  **/
  count<T extends suggestion_voteCountArgs>(
    args?: Prisma.Subset<T, suggestion_voteCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Suggestion_voteCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Suggestion_vote.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Suggestion_voteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Suggestion_voteAggregateArgs>(args: Prisma.Subset<T, Suggestion_voteAggregateArgs>): Prisma.PrismaPromise<GetSuggestion_voteAggregateType<T>>

  /**
   * Group by Suggestion_vote.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_voteGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends suggestion_voteGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: suggestion_voteGroupByArgs['orderBy'] }
      : { orderBy?: suggestion_voteGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, suggestion_voteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSuggestion_voteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the suggestion_vote model
 */
readonly fields: suggestion_voteFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for suggestion_vote.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__suggestion_voteClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.suggestion_vote$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion_vote$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  suggestion<T extends Prisma.suggestion_vote$suggestionArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion_vote$suggestionArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the suggestion_vote model
 */
export interface suggestion_voteFieldRefs {
  readonly id: Prisma.FieldRef<"suggestion_vote", 'Int'>
  readonly voteType: Prisma.FieldRef<"suggestion_vote", 'SuggestionVoteTypes'>
  readonly createdAt: Prisma.FieldRef<"suggestion_vote", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"suggestion_vote", 'DateTime'>
  readonly userId: Prisma.FieldRef<"suggestion_vote", 'Int'>
  readonly suggestionId: Prisma.FieldRef<"suggestion_vote", 'Int'>
}
    

// Custom InputTypes
/**
 * suggestion_vote findUnique
 */
export type suggestion_voteFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_vote to fetch.
   */
  where: Prisma.suggestion_voteWhereUniqueInput
}

/**
 * suggestion_vote findUniqueOrThrow
 */
export type suggestion_voteFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_vote to fetch.
   */
  where: Prisma.suggestion_voteWhereUniqueInput
}

/**
 * suggestion_vote findFirst
 */
export type suggestion_voteFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_vote to fetch.
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_votes to fetch.
   */
  orderBy?: Prisma.suggestion_voteOrderByWithRelationInput | Prisma.suggestion_voteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestion_votes.
   */
  cursor?: Prisma.suggestion_voteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_votes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_votes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestion_votes.
   */
  distinct?: Prisma.Suggestion_voteScalarFieldEnum | Prisma.Suggestion_voteScalarFieldEnum[]
}

/**
 * suggestion_vote findFirstOrThrow
 */
export type suggestion_voteFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_vote to fetch.
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_votes to fetch.
   */
  orderBy?: Prisma.suggestion_voteOrderByWithRelationInput | Prisma.suggestion_voteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestion_votes.
   */
  cursor?: Prisma.suggestion_voteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_votes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_votes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestion_votes.
   */
  distinct?: Prisma.Suggestion_voteScalarFieldEnum | Prisma.Suggestion_voteScalarFieldEnum[]
}

/**
 * suggestion_vote findMany
 */
export type suggestion_voteFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_votes to fetch.
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_votes to fetch.
   */
  orderBy?: Prisma.suggestion_voteOrderByWithRelationInput | Prisma.suggestion_voteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing suggestion_votes.
   */
  cursor?: Prisma.suggestion_voteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_votes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_votes.
   */
  skip?: number
  distinct?: Prisma.Suggestion_voteScalarFieldEnum | Prisma.Suggestion_voteScalarFieldEnum[]
}

/**
 * suggestion_vote create
 */
export type suggestion_voteCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * The data needed to create a suggestion_vote.
   */
  data: Prisma.XOR<Prisma.suggestion_voteCreateInput, Prisma.suggestion_voteUncheckedCreateInput>
}

/**
 * suggestion_vote createMany
 */
export type suggestion_voteCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many suggestion_votes.
   */
  data: Prisma.suggestion_voteCreateManyInput | Prisma.suggestion_voteCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * suggestion_vote update
 */
export type suggestion_voteUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * The data needed to update a suggestion_vote.
   */
  data: Prisma.XOR<Prisma.suggestion_voteUpdateInput, Prisma.suggestion_voteUncheckedUpdateInput>
  /**
   * Choose, which suggestion_vote to update.
   */
  where: Prisma.suggestion_voteWhereUniqueInput
}

/**
 * suggestion_vote updateMany
 */
export type suggestion_voteUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update suggestion_votes.
   */
  data: Prisma.XOR<Prisma.suggestion_voteUpdateManyMutationInput, Prisma.suggestion_voteUncheckedUpdateManyInput>
  /**
   * Filter which suggestion_votes to update
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * Limit how many suggestion_votes to update.
   */
  limit?: number
}

/**
 * suggestion_vote upsert
 */
export type suggestion_voteUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * The filter to search for the suggestion_vote to update in case it exists.
   */
  where: Prisma.suggestion_voteWhereUniqueInput
  /**
   * In case the suggestion_vote found by the `where` argument doesn't exist, create a new suggestion_vote with this data.
   */
  create: Prisma.XOR<Prisma.suggestion_voteCreateInput, Prisma.suggestion_voteUncheckedCreateInput>
  /**
   * In case the suggestion_vote was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.suggestion_voteUpdateInput, Prisma.suggestion_voteUncheckedUpdateInput>
}

/**
 * suggestion_vote delete
 */
export type suggestion_voteDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  /**
   * Filter which suggestion_vote to delete.
   */
  where: Prisma.suggestion_voteWhereUniqueInput
}

/**
 * suggestion_vote deleteMany
 */
export type suggestion_voteDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestion_votes to delete
   */
  where?: Prisma.suggestion_voteWhereInput
  /**
   * Limit how many suggestion_votes to delete.
   */
  limit?: number
}

/**
 * suggestion_vote.user
 */
export type suggestion_vote$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * suggestion_vote.suggestion
 */
export type suggestion_vote$suggestionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  where?: Prisma.suggestionWhereInput
}

/**
 * suggestion_vote without action
 */
export type suggestion_voteDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
}
