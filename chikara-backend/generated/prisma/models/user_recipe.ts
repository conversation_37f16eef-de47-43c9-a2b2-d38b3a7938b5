
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_recipe` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_recipe
 * 
 */
export type user_recipeModel = runtime.Types.Result.DefaultSelection<Prisma.$user_recipePayload>

export type AggregateUser_recipe = {
  _count: User_recipeCountAggregateOutputType | null
  _avg: User_recipeAvgAggregateOutputType | null
  _sum: User_recipeSumAggregateOutputType | null
  _min: User_recipeMinAggregateOutputType | null
  _max: User_recipeMaxAggregateOutputType | null
}

export type User_recipeAvgAggregateOutputType = {
  craftingRecipeId: number | null
  userId: number | null
}

export type User_recipeSumAggregateOutputType = {
  craftingRecipeId: number | null
  userId: number | null
}

export type User_recipeMinAggregateOutputType = {
  createdAt: Date | null
  updatedAt: Date | null
  craftingRecipeId: number | null
  userId: number | null
}

export type User_recipeMaxAggregateOutputType = {
  createdAt: Date | null
  updatedAt: Date | null
  craftingRecipeId: number | null
  userId: number | null
}

export type User_recipeCountAggregateOutputType = {
  createdAt: number
  updatedAt: number
  craftingRecipeId: number
  userId: number
  _all: number
}


export type User_recipeAvgAggregateInputType = {
  craftingRecipeId?: true
  userId?: true
}

export type User_recipeSumAggregateInputType = {
  craftingRecipeId?: true
  userId?: true
}

export type User_recipeMinAggregateInputType = {
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  userId?: true
}

export type User_recipeMaxAggregateInputType = {
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  userId?: true
}

export type User_recipeCountAggregateInputType = {
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  userId?: true
  _all?: true
}

export type User_recipeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_recipe to aggregate.
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_recipes to fetch.
   */
  orderBy?: Prisma.user_recipeOrderByWithRelationInput | Prisma.user_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_recipes
  **/
  _count?: true | User_recipeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_recipeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_recipeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_recipeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_recipeMaxAggregateInputType
}

export type GetUser_recipeAggregateType<T extends User_recipeAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_recipe]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_recipe[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_recipe[P]>
}




export type user_recipeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_recipeWhereInput
  orderBy?: Prisma.user_recipeOrderByWithAggregationInput | Prisma.user_recipeOrderByWithAggregationInput[]
  by: Prisma.User_recipeScalarFieldEnum[] | Prisma.User_recipeScalarFieldEnum
  having?: Prisma.user_recipeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_recipeCountAggregateInputType | true
  _avg?: User_recipeAvgAggregateInputType
  _sum?: User_recipeSumAggregateInputType
  _min?: User_recipeMinAggregateInputType
  _max?: User_recipeMaxAggregateInputType
}

export type User_recipeGroupByOutputType = {
  createdAt: Date
  updatedAt: Date
  craftingRecipeId: number
  userId: number
  _count: User_recipeCountAggregateOutputType | null
  _avg: User_recipeAvgAggregateOutputType | null
  _sum: User_recipeSumAggregateOutputType | null
  _min: User_recipeMinAggregateOutputType | null
  _max: User_recipeMaxAggregateOutputType | null
}

type GetUser_recipeGroupByPayload<T extends user_recipeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_recipeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_recipeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_recipeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_recipeGroupByOutputType[P]>
      }
    >
  >



export type user_recipeWhereInput = {
  AND?: Prisma.user_recipeWhereInput | Prisma.user_recipeWhereInput[]
  OR?: Prisma.user_recipeWhereInput[]
  NOT?: Prisma.user_recipeWhereInput | Prisma.user_recipeWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"user_recipe"> | number
  userId?: Prisma.IntFilter<"user_recipe"> | number
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeScalarRelationFilter, Prisma.crafting_recipeWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type user_recipeOrderByWithRelationInput = {
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  crafting_recipe?: Prisma.crafting_recipeOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type user_recipeWhereUniqueInput = Prisma.AtLeast<{
  craftingRecipeId_userId?: Prisma.user_recipeCraftingRecipeIdUserIdCompoundUniqueInput
  AND?: Prisma.user_recipeWhereInput | Prisma.user_recipeWhereInput[]
  OR?: Prisma.user_recipeWhereInput[]
  NOT?: Prisma.user_recipeWhereInput | Prisma.user_recipeWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"user_recipe"> | number
  userId?: Prisma.IntFilter<"user_recipe"> | number
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeScalarRelationFilter, Prisma.crafting_recipeWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "craftingRecipeId_userId">

export type user_recipeOrderByWithAggregationInput = {
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.user_recipeCountOrderByAggregateInput
  _avg?: Prisma.user_recipeAvgOrderByAggregateInput
  _max?: Prisma.user_recipeMaxOrderByAggregateInput
  _min?: Prisma.user_recipeMinOrderByAggregateInput
  _sum?: Prisma.user_recipeSumOrderByAggregateInput
}

export type user_recipeScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_recipeScalarWhereWithAggregatesInput | Prisma.user_recipeScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_recipeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_recipeScalarWhereWithAggregatesInput | Prisma.user_recipeScalarWhereWithAggregatesInput[]
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_recipe"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_recipe"> | Date | string
  craftingRecipeId?: Prisma.IntWithAggregatesFilter<"user_recipe"> | number
  userId?: Prisma.IntWithAggregatesFilter<"user_recipe"> | number
}

export type user_recipeCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  crafting_recipe: Prisma.crafting_recipeCreateNestedOneWithoutUser_recipeInput
  user: Prisma.userCreateNestedOneWithoutUser_recipeInput
}

export type user_recipeUncheckedCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
  userId: number
}

export type user_recipeUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  crafting_recipe?: Prisma.crafting_recipeUpdateOneRequiredWithoutUser_recipeNestedInput
  user?: Prisma.userUpdateOneRequiredWithoutUser_recipeNestedInput
}

export type user_recipeUncheckedUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_recipeCreateManyInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
  userId: number
}

export type user_recipeUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_recipeUncheckedUpdateManyInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type User_recipeListRelationFilter = {
  every?: Prisma.user_recipeWhereInput
  some?: Prisma.user_recipeWhereInput
  none?: Prisma.user_recipeWhereInput
}

export type user_recipeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_recipeCraftingRecipeIdUserIdCompoundUniqueInput = {
  craftingRecipeId: number
  userId: number
}

export type user_recipeCountOrderByAggregateInput = {
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_recipeAvgOrderByAggregateInput = {
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_recipeMaxOrderByAggregateInput = {
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_recipeMinOrderByAggregateInput = {
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_recipeSumOrderByAggregateInput = {
  craftingRecipeId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_recipeCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_recipeCreateWithoutCrafting_recipeInput[] | Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_recipeCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
}

export type user_recipeUncheckedCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_recipeCreateWithoutCrafting_recipeInput[] | Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_recipeCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
}

export type user_recipeUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_recipeCreateWithoutCrafting_recipeInput[] | Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.user_recipeUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_recipeUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_recipeCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  disconnect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  delete?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  update?: Prisma.user_recipeUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_recipeUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.user_recipeUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.user_recipeUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
}

export type user_recipeUncheckedUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_recipeCreateWithoutCrafting_recipeInput[] | Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_recipeCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.user_recipeUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_recipeUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_recipeCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  disconnect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  delete?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  update?: Prisma.user_recipeUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_recipeUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.user_recipeUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.user_recipeUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
}

export type user_recipeCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput> | Prisma.user_recipeCreateWithoutUserInput[] | Prisma.user_recipeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutUserInput | Prisma.user_recipeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_recipeCreateManyUserInputEnvelope
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
}

export type user_recipeUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput> | Prisma.user_recipeCreateWithoutUserInput[] | Prisma.user_recipeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutUserInput | Prisma.user_recipeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_recipeCreateManyUserInputEnvelope
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
}

export type user_recipeUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput> | Prisma.user_recipeCreateWithoutUserInput[] | Prisma.user_recipeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutUserInput | Prisma.user_recipeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_recipeUpsertWithWhereUniqueWithoutUserInput | Prisma.user_recipeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_recipeCreateManyUserInputEnvelope
  set?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  disconnect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  delete?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  update?: Prisma.user_recipeUpdateWithWhereUniqueWithoutUserInput | Prisma.user_recipeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_recipeUpdateManyWithWhereWithoutUserInput | Prisma.user_recipeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
}

export type user_recipeUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput> | Prisma.user_recipeCreateWithoutUserInput[] | Prisma.user_recipeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_recipeCreateOrConnectWithoutUserInput | Prisma.user_recipeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_recipeUpsertWithWhereUniqueWithoutUserInput | Prisma.user_recipeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_recipeCreateManyUserInputEnvelope
  set?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  disconnect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  delete?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  connect?: Prisma.user_recipeWhereUniqueInput | Prisma.user_recipeWhereUniqueInput[]
  update?: Prisma.user_recipeUpdateWithWhereUniqueWithoutUserInput | Prisma.user_recipeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_recipeUpdateManyWithWhereWithoutUserInput | Prisma.user_recipeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
}

export type user_recipeCreateWithoutCrafting_recipeInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_recipeInput
}

export type user_recipeUncheckedCreateWithoutCrafting_recipeInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_recipeCreateOrConnectWithoutCrafting_recipeInput = {
  where: Prisma.user_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput>
}

export type user_recipeCreateManyCrafting_recipeInputEnvelope = {
  data: Prisma.user_recipeCreateManyCrafting_recipeInput | Prisma.user_recipeCreateManyCrafting_recipeInput[]
  skipDuplicates?: boolean
}

export type user_recipeUpsertWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.user_recipeWhereUniqueInput
  update: Prisma.XOR<Prisma.user_recipeUpdateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedUpdateWithoutCrafting_recipeInput>
  create: Prisma.XOR<Prisma.user_recipeCreateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedCreateWithoutCrafting_recipeInput>
}

export type user_recipeUpdateWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.user_recipeWhereUniqueInput
  data: Prisma.XOR<Prisma.user_recipeUpdateWithoutCrafting_recipeInput, Prisma.user_recipeUncheckedUpdateWithoutCrafting_recipeInput>
}

export type user_recipeUpdateManyWithWhereWithoutCrafting_recipeInput = {
  where: Prisma.user_recipeScalarWhereInput
  data: Prisma.XOR<Prisma.user_recipeUpdateManyMutationInput, Prisma.user_recipeUncheckedUpdateManyWithoutCrafting_recipeInput>
}

export type user_recipeScalarWhereInput = {
  AND?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
  OR?: Prisma.user_recipeScalarWhereInput[]
  NOT?: Prisma.user_recipeScalarWhereInput | Prisma.user_recipeScalarWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_recipe"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"user_recipe"> | number
  userId?: Prisma.IntFilter<"user_recipe"> | number
}

export type user_recipeCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  crafting_recipe: Prisma.crafting_recipeCreateNestedOneWithoutUser_recipeInput
}

export type user_recipeUncheckedCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
}

export type user_recipeCreateOrConnectWithoutUserInput = {
  where: Prisma.user_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput>
}

export type user_recipeCreateManyUserInputEnvelope = {
  data: Prisma.user_recipeCreateManyUserInput | Prisma.user_recipeCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_recipeUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_recipeWhereUniqueInput
  update: Prisma.XOR<Prisma.user_recipeUpdateWithoutUserInput, Prisma.user_recipeUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_recipeCreateWithoutUserInput, Prisma.user_recipeUncheckedCreateWithoutUserInput>
}

export type user_recipeUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_recipeWhereUniqueInput
  data: Prisma.XOR<Prisma.user_recipeUpdateWithoutUserInput, Prisma.user_recipeUncheckedUpdateWithoutUserInput>
}

export type user_recipeUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_recipeScalarWhereInput
  data: Prisma.XOR<Prisma.user_recipeUpdateManyMutationInput, Prisma.user_recipeUncheckedUpdateManyWithoutUserInput>
}

export type user_recipeCreateManyCrafting_recipeInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_recipeUpdateWithoutCrafting_recipeInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_recipeNestedInput
}

export type user_recipeUncheckedUpdateWithoutCrafting_recipeInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_recipeUncheckedUpdateManyWithoutCrafting_recipeInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_recipeCreateManyUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
}

export type user_recipeUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  crafting_recipe?: Prisma.crafting_recipeUpdateOneRequiredWithoutUser_recipeNestedInput
}

export type user_recipeUncheckedUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_recipeUncheckedUpdateManyWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type user_recipeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  createdAt?: boolean
  updatedAt?: boolean
  craftingRecipeId?: boolean
  userId?: boolean
  crafting_recipe?: boolean | Prisma.crafting_recipeDefaultArgs<ExtArgs>
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_recipe"]>



export type user_recipeSelectScalar = {
  createdAt?: boolean
  updatedAt?: boolean
  craftingRecipeId?: boolean
  userId?: boolean
}

export type user_recipeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"createdAt" | "updatedAt" | "craftingRecipeId" | "userId", ExtArgs["result"]["user_recipe"]>
export type user_recipeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  crafting_recipe?: boolean | Prisma.crafting_recipeDefaultArgs<ExtArgs>
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $user_recipePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_recipe"
  objects: {
    crafting_recipe: Prisma.$crafting_recipePayload<ExtArgs>
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    createdAt: Date
    updatedAt: Date
    craftingRecipeId: number
    userId: number
  }, ExtArgs["result"]["user_recipe"]>
  composites: {}
}

export type user_recipeGetPayload<S extends boolean | null | undefined | user_recipeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_recipePayload, S>

export type user_recipeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_recipeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_recipeCountAggregateInputType | true
  }

export interface user_recipeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_recipe'], meta: { name: 'user_recipe' } }
  /**
   * Find zero or one User_recipe that matches the filter.
   * @param {user_recipeFindUniqueArgs} args - Arguments to find a User_recipe
   * @example
   * // Get one User_recipe
   * const user_recipe = await prisma.user_recipe.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_recipeFindUniqueArgs>(args: Prisma.SelectSubset<T, user_recipeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_recipe that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_recipeFindUniqueOrThrowArgs} args - Arguments to find a User_recipe
   * @example
   * // Get one User_recipe
   * const user_recipe = await prisma.user_recipe.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_recipeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_recipeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_recipe that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeFindFirstArgs} args - Arguments to find a User_recipe
   * @example
   * // Get one User_recipe
   * const user_recipe = await prisma.user_recipe.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_recipeFindFirstArgs>(args?: Prisma.SelectSubset<T, user_recipeFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_recipe that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeFindFirstOrThrowArgs} args - Arguments to find a User_recipe
   * @example
   * // Get one User_recipe
   * const user_recipe = await prisma.user_recipe.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_recipeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_recipeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_recipes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_recipes
   * const user_recipes = await prisma.user_recipe.findMany()
   * 
   * // Get first 10 User_recipes
   * const user_recipes = await prisma.user_recipe.findMany({ take: 10 })
   * 
   * // Only select the `createdAt`
   * const user_recipeWithCreatedAtOnly = await prisma.user_recipe.findMany({ select: { createdAt: true } })
   * 
   */
  findMany<T extends user_recipeFindManyArgs>(args?: Prisma.SelectSubset<T, user_recipeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_recipe.
   * @param {user_recipeCreateArgs} args - Arguments to create a User_recipe.
   * @example
   * // Create one User_recipe
   * const User_recipe = await prisma.user_recipe.create({
   *   data: {
   *     // ... data to create a User_recipe
   *   }
   * })
   * 
   */
  create<T extends user_recipeCreateArgs>(args: Prisma.SelectSubset<T, user_recipeCreateArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_recipes.
   * @param {user_recipeCreateManyArgs} args - Arguments to create many User_recipes.
   * @example
   * // Create many User_recipes
   * const user_recipe = await prisma.user_recipe.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_recipeCreateManyArgs>(args?: Prisma.SelectSubset<T, user_recipeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_recipe.
   * @param {user_recipeDeleteArgs} args - Arguments to delete one User_recipe.
   * @example
   * // Delete one User_recipe
   * const User_recipe = await prisma.user_recipe.delete({
   *   where: {
   *     // ... filter to delete one User_recipe
   *   }
   * })
   * 
   */
  delete<T extends user_recipeDeleteArgs>(args: Prisma.SelectSubset<T, user_recipeDeleteArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_recipe.
   * @param {user_recipeUpdateArgs} args - Arguments to update one User_recipe.
   * @example
   * // Update one User_recipe
   * const user_recipe = await prisma.user_recipe.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_recipeUpdateArgs>(args: Prisma.SelectSubset<T, user_recipeUpdateArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_recipes.
   * @param {user_recipeDeleteManyArgs} args - Arguments to filter User_recipes to delete.
   * @example
   * // Delete a few User_recipes
   * const { count } = await prisma.user_recipe.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_recipeDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_recipeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_recipes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_recipes
   * const user_recipe = await prisma.user_recipe.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_recipeUpdateManyArgs>(args: Prisma.SelectSubset<T, user_recipeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_recipe.
   * @param {user_recipeUpsertArgs} args - Arguments to update or create a User_recipe.
   * @example
   * // Update or create a User_recipe
   * const user_recipe = await prisma.user_recipe.upsert({
   *   create: {
   *     // ... data to create a User_recipe
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_recipe we want to update
   *   }
   * })
   */
  upsert<T extends user_recipeUpsertArgs>(args: Prisma.SelectSubset<T, user_recipeUpsertArgs<ExtArgs>>): Prisma.Prisma__user_recipeClient<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_recipes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeCountArgs} args - Arguments to filter User_recipes to count.
   * @example
   * // Count the number of User_recipes
   * const count = await prisma.user_recipe.count({
   *   where: {
   *     // ... the filter for the User_recipes we want to count
   *   }
   * })
  **/
  count<T extends user_recipeCountArgs>(
    args?: Prisma.Subset<T, user_recipeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_recipeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_recipe.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_recipeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_recipeAggregateArgs>(args: Prisma.Subset<T, User_recipeAggregateArgs>): Prisma.PrismaPromise<GetUser_recipeAggregateType<T>>

  /**
   * Group by User_recipe.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_recipeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_recipeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_recipeGroupByArgs['orderBy'] }
      : { orderBy?: user_recipeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_recipeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_recipeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_recipe model
 */
readonly fields: user_recipeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_recipe.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_recipeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  crafting_recipe<T extends Prisma.crafting_recipeDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipeDefaultArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_recipe model
 */
export interface user_recipeFieldRefs {
  readonly createdAt: Prisma.FieldRef<"user_recipe", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_recipe", 'DateTime'>
  readonly craftingRecipeId: Prisma.FieldRef<"user_recipe", 'Int'>
  readonly userId: Prisma.FieldRef<"user_recipe", 'Int'>
}
    

// Custom InputTypes
/**
 * user_recipe findUnique
 */
export type user_recipeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter, which user_recipe to fetch.
   */
  where: Prisma.user_recipeWhereUniqueInput
}

/**
 * user_recipe findUniqueOrThrow
 */
export type user_recipeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter, which user_recipe to fetch.
   */
  where: Prisma.user_recipeWhereUniqueInput
}

/**
 * user_recipe findFirst
 */
export type user_recipeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter, which user_recipe to fetch.
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_recipes to fetch.
   */
  orderBy?: Prisma.user_recipeOrderByWithRelationInput | Prisma.user_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_recipes.
   */
  cursor?: Prisma.user_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_recipes.
   */
  distinct?: Prisma.User_recipeScalarFieldEnum | Prisma.User_recipeScalarFieldEnum[]
}

/**
 * user_recipe findFirstOrThrow
 */
export type user_recipeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter, which user_recipe to fetch.
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_recipes to fetch.
   */
  orderBy?: Prisma.user_recipeOrderByWithRelationInput | Prisma.user_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_recipes.
   */
  cursor?: Prisma.user_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_recipes.
   */
  distinct?: Prisma.User_recipeScalarFieldEnum | Prisma.User_recipeScalarFieldEnum[]
}

/**
 * user_recipe findMany
 */
export type user_recipeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter, which user_recipes to fetch.
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_recipes to fetch.
   */
  orderBy?: Prisma.user_recipeOrderByWithRelationInput | Prisma.user_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_recipes.
   */
  cursor?: Prisma.user_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_recipes.
   */
  skip?: number
  distinct?: Prisma.User_recipeScalarFieldEnum | Prisma.User_recipeScalarFieldEnum[]
}

/**
 * user_recipe create
 */
export type user_recipeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * The data needed to create a user_recipe.
   */
  data: Prisma.XOR<Prisma.user_recipeCreateInput, Prisma.user_recipeUncheckedCreateInput>
}

/**
 * user_recipe createMany
 */
export type user_recipeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_recipes.
   */
  data: Prisma.user_recipeCreateManyInput | Prisma.user_recipeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_recipe update
 */
export type user_recipeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * The data needed to update a user_recipe.
   */
  data: Prisma.XOR<Prisma.user_recipeUpdateInput, Prisma.user_recipeUncheckedUpdateInput>
  /**
   * Choose, which user_recipe to update.
   */
  where: Prisma.user_recipeWhereUniqueInput
}

/**
 * user_recipe updateMany
 */
export type user_recipeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_recipes.
   */
  data: Prisma.XOR<Prisma.user_recipeUpdateManyMutationInput, Prisma.user_recipeUncheckedUpdateManyInput>
  /**
   * Filter which user_recipes to update
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * Limit how many user_recipes to update.
   */
  limit?: number
}

/**
 * user_recipe upsert
 */
export type user_recipeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * The filter to search for the user_recipe to update in case it exists.
   */
  where: Prisma.user_recipeWhereUniqueInput
  /**
   * In case the user_recipe found by the `where` argument doesn't exist, create a new user_recipe with this data.
   */
  create: Prisma.XOR<Prisma.user_recipeCreateInput, Prisma.user_recipeUncheckedCreateInput>
  /**
   * In case the user_recipe was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_recipeUpdateInput, Prisma.user_recipeUncheckedUpdateInput>
}

/**
 * user_recipe delete
 */
export type user_recipeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  /**
   * Filter which user_recipe to delete.
   */
  where: Prisma.user_recipeWhereUniqueInput
}

/**
 * user_recipe deleteMany
 */
export type user_recipeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_recipes to delete
   */
  where?: Prisma.user_recipeWhereInput
  /**
   * Limit how many user_recipes to delete.
   */
  limit?: number
}

/**
 * user_recipe without action
 */
export type user_recipeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
}
