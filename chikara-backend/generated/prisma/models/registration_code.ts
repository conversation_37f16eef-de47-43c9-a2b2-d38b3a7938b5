
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `registration_code` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model registration_code
 * 
 */
export type registration_codeModel = runtime.Types.Result.DefaultSelection<Prisma.$registration_codePayload>

export type AggregateRegistration_code = {
  _count: Registration_codeCountAggregateOutputType | null
  _avg: Registration_codeAvgAggregateOutputType | null
  _sum: Registration_codeSumAggregateOutputType | null
  _min: Registration_codeMinAggregateOutputType | null
  _max: Registration_codeMaxAggregateOutputType | null
}

export type Registration_codeAvgAggregateOutputType = {
  id: number | null
  referrerId: number | null
  claimerId: number | null
}

export type Registration_codeSumAggregateOutputType = {
  id: number | null
  referrerId: number | null
  claimerId: number | null
}

export type Registration_codeMinAggregateOutputType = {
  id: number | null
  code: string | null
  note: string | null
  unlimitedUse: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  referrerId: number | null
  claimerId: number | null
}

export type Registration_codeMaxAggregateOutputType = {
  id: number | null
  code: string | null
  note: string | null
  unlimitedUse: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  referrerId: number | null
  claimerId: number | null
}

export type Registration_codeCountAggregateOutputType = {
  id: number
  code: number
  note: number
  unlimitedUse: number
  createdAt: number
  updatedAt: number
  referrerId: number
  claimerId: number
  _all: number
}


export type Registration_codeAvgAggregateInputType = {
  id?: true
  referrerId?: true
  claimerId?: true
}

export type Registration_codeSumAggregateInputType = {
  id?: true
  referrerId?: true
  claimerId?: true
}

export type Registration_codeMinAggregateInputType = {
  id?: true
  code?: true
  note?: true
  unlimitedUse?: true
  createdAt?: true
  updatedAt?: true
  referrerId?: true
  claimerId?: true
}

export type Registration_codeMaxAggregateInputType = {
  id?: true
  code?: true
  note?: true
  unlimitedUse?: true
  createdAt?: true
  updatedAt?: true
  referrerId?: true
  claimerId?: true
}

export type Registration_codeCountAggregateInputType = {
  id?: true
  code?: true
  note?: true
  unlimitedUse?: true
  createdAt?: true
  updatedAt?: true
  referrerId?: true
  claimerId?: true
  _all?: true
}

export type Registration_codeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which registration_code to aggregate.
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of registration_codes to fetch.
   */
  orderBy?: Prisma.registration_codeOrderByWithRelationInput | Prisma.registration_codeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.registration_codeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` registration_codes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` registration_codes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned registration_codes
  **/
  _count?: true | Registration_codeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Registration_codeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Registration_codeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Registration_codeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Registration_codeMaxAggregateInputType
}

export type GetRegistration_codeAggregateType<T extends Registration_codeAggregateArgs> = {
      [P in keyof T & keyof AggregateRegistration_code]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRegistration_code[P]>
    : Prisma.GetScalarType<T[P], AggregateRegistration_code[P]>
}




export type registration_codeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.registration_codeWhereInput
  orderBy?: Prisma.registration_codeOrderByWithAggregationInput | Prisma.registration_codeOrderByWithAggregationInput[]
  by: Prisma.Registration_codeScalarFieldEnum[] | Prisma.Registration_codeScalarFieldEnum
  having?: Prisma.registration_codeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Registration_codeCountAggregateInputType | true
  _avg?: Registration_codeAvgAggregateInputType
  _sum?: Registration_codeSumAggregateInputType
  _min?: Registration_codeMinAggregateInputType
  _max?: Registration_codeMaxAggregateInputType
}

export type Registration_codeGroupByOutputType = {
  id: number
  code: string
  note: string
  unlimitedUse: boolean | null
  createdAt: Date
  updatedAt: Date
  referrerId: number | null
  claimerId: number | null
  _count: Registration_codeCountAggregateOutputType | null
  _avg: Registration_codeAvgAggregateOutputType | null
  _sum: Registration_codeSumAggregateOutputType | null
  _min: Registration_codeMinAggregateOutputType | null
  _max: Registration_codeMaxAggregateOutputType | null
}

type GetRegistration_codeGroupByPayload<T extends registration_codeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Registration_codeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Registration_codeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Registration_codeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Registration_codeGroupByOutputType[P]>
      }
    >
  >



export type registration_codeWhereInput = {
  AND?: Prisma.registration_codeWhereInput | Prisma.registration_codeWhereInput[]
  OR?: Prisma.registration_codeWhereInput[]
  NOT?: Prisma.registration_codeWhereInput | Prisma.registration_codeWhereInput[]
  id?: Prisma.IntFilter<"registration_code"> | number
  code?: Prisma.StringFilter<"registration_code"> | string
  note?: Prisma.StringFilter<"registration_code"> | string
  unlimitedUse?: Prisma.BoolNullableFilter<"registration_code"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  referrerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
  claimerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
  user_registration_code_referrerIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_registration_code_claimerIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type registration_codeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  code?: Prisma.SortOrder
  note?: Prisma.SortOrder
  unlimitedUse?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  referrerId?: Prisma.SortOrderInput | Prisma.SortOrder
  claimerId?: Prisma.SortOrderInput | Prisma.SortOrder
  user_registration_code_referrerIdTouser?: Prisma.userOrderByWithRelationInput
  user_registration_code_claimerIdTouser?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.registration_codeOrderByRelevanceInput
}

export type registration_codeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.registration_codeWhereInput | Prisma.registration_codeWhereInput[]
  OR?: Prisma.registration_codeWhereInput[]
  NOT?: Prisma.registration_codeWhereInput | Prisma.registration_codeWhereInput[]
  code?: Prisma.StringFilter<"registration_code"> | string
  note?: Prisma.StringFilter<"registration_code"> | string
  unlimitedUse?: Prisma.BoolNullableFilter<"registration_code"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  referrerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
  claimerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
  user_registration_code_referrerIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_registration_code_claimerIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type registration_codeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  code?: Prisma.SortOrder
  note?: Prisma.SortOrder
  unlimitedUse?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  referrerId?: Prisma.SortOrderInput | Prisma.SortOrder
  claimerId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.registration_codeCountOrderByAggregateInput
  _avg?: Prisma.registration_codeAvgOrderByAggregateInput
  _max?: Prisma.registration_codeMaxOrderByAggregateInput
  _min?: Prisma.registration_codeMinOrderByAggregateInput
  _sum?: Prisma.registration_codeSumOrderByAggregateInput
}

export type registration_codeScalarWhereWithAggregatesInput = {
  AND?: Prisma.registration_codeScalarWhereWithAggregatesInput | Prisma.registration_codeScalarWhereWithAggregatesInput[]
  OR?: Prisma.registration_codeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.registration_codeScalarWhereWithAggregatesInput | Prisma.registration_codeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"registration_code"> | number
  code?: Prisma.StringWithAggregatesFilter<"registration_code"> | string
  note?: Prisma.StringWithAggregatesFilter<"registration_code"> | string
  unlimitedUse?: Prisma.BoolNullableWithAggregatesFilter<"registration_code"> | boolean | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"registration_code"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"registration_code"> | Date | string
  referrerId?: Prisma.IntNullableWithAggregatesFilter<"registration_code"> | number | null
  claimerId?: Prisma.IntNullableWithAggregatesFilter<"registration_code"> | number | null
}

export type registration_codeCreateInput = {
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_registration_code_referrerIdTouser?: Prisma.userCreateNestedOneWithoutRegistration_code_registration_code_referrerIdTouserInput
  user_registration_code_claimerIdTouser?: Prisma.userCreateNestedOneWithoutRegistration_code_registration_code_claimerIdTouserInput
}

export type registration_codeUncheckedCreateInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  referrerId?: number | null
  claimerId?: number | null
}

export type registration_codeUpdateInput = {
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_registration_code_referrerIdTouser?: Prisma.userUpdateOneWithoutRegistration_code_registration_code_referrerIdTouserNestedInput
  user_registration_code_claimerIdTouser?: Prisma.userUpdateOneWithoutRegistration_code_registration_code_claimerIdTouserNestedInput
}

export type registration_codeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  referrerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type registration_codeCreateManyInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  referrerId?: number | null
  claimerId?: number | null
}

export type registration_codeUpdateManyMutationInput = {
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type registration_codeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  referrerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type registration_codeOrderByRelevanceInput = {
  fields: Prisma.registration_codeOrderByRelevanceFieldEnum | Prisma.registration_codeOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type registration_codeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  code?: Prisma.SortOrder
  note?: Prisma.SortOrder
  unlimitedUse?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  referrerId?: Prisma.SortOrder
  claimerId?: Prisma.SortOrder
}

export type registration_codeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  referrerId?: Prisma.SortOrder
  claimerId?: Prisma.SortOrder
}

export type registration_codeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  code?: Prisma.SortOrder
  note?: Prisma.SortOrder
  unlimitedUse?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  referrerId?: Prisma.SortOrder
  claimerId?: Prisma.SortOrder
}

export type registration_codeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  code?: Prisma.SortOrder
  note?: Prisma.SortOrder
  unlimitedUse?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  referrerId?: Prisma.SortOrder
  claimerId?: Prisma.SortOrder
}

export type registration_codeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  referrerId?: Prisma.SortOrder
  claimerId?: Prisma.SortOrder
}

export type Registration_codeListRelationFilter = {
  every?: Prisma.registration_codeWhereInput
  some?: Prisma.registration_codeWhereInput
  none?: Prisma.registration_codeWhereInput
}

export type registration_codeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type registration_codeCreateNestedManyWithoutUser_registration_code_referrerIdTouserInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInputEnvelope
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
}

export type registration_codeCreateNestedManyWithoutUser_registration_code_claimerIdTouserInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInputEnvelope
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
}

export type registration_codeUncheckedCreateNestedManyWithoutUser_registration_code_referrerIdTouserInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInputEnvelope
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
}

export type registration_codeUncheckedCreateNestedManyWithoutUser_registration_code_claimerIdTouserInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInputEnvelope
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
}

export type registration_codeUpdateManyWithoutUser_registration_code_referrerIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput[]
  upsert?: Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInputEnvelope
  set?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  disconnect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  delete?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  update?: Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput[]
  updateMany?: Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_referrerIdTouserInput[]
  deleteMany?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
}

export type registration_codeUpdateManyWithoutUser_registration_code_claimerIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput[]
  upsert?: Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInputEnvelope
  set?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  disconnect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  delete?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  update?: Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput[]
  updateMany?: Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_claimerIdTouserInput[]
  deleteMany?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
}

export type registration_codeUncheckedUpdateManyWithoutUser_registration_code_referrerIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput[]
  upsert?: Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInputEnvelope
  set?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  disconnect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  delete?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  update?: Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput[]
  updateMany?: Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_referrerIdTouserInput | Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_referrerIdTouserInput[]
  deleteMany?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
}

export type registration_codeUncheckedUpdateManyWithoutUser_registration_code_claimerIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput> | Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput[] | Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput[]
  connectOrCreate?: Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput[]
  upsert?: Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput[]
  createMany?: Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInputEnvelope
  set?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  disconnect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  delete?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  connect?: Prisma.registration_codeWhereUniqueInput | Prisma.registration_codeWhereUniqueInput[]
  update?: Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput[]
  updateMany?: Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_claimerIdTouserInput | Prisma.registration_codeUpdateManyWithWhereWithoutUser_registration_code_claimerIdTouserInput[]
  deleteMany?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
}

export type registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput = {
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_registration_code_claimerIdTouser?: Prisma.userCreateNestedOneWithoutRegistration_code_registration_code_claimerIdTouserInput
}

export type registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  claimerId?: number | null
}

export type registration_codeCreateOrConnectWithoutUser_registration_code_referrerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  create: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput>
}

export type registration_codeCreateManyUser_registration_code_referrerIdTouserInputEnvelope = {
  data: Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInput | Prisma.registration_codeCreateManyUser_registration_code_referrerIdTouserInput[]
  skipDuplicates?: boolean
}

export type registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput = {
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_registration_code_referrerIdTouser?: Prisma.userCreateNestedOneWithoutRegistration_code_registration_code_referrerIdTouserInput
}

export type registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  referrerId?: number | null
}

export type registration_codeCreateOrConnectWithoutUser_registration_code_claimerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  create: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput>
}

export type registration_codeCreateManyUser_registration_code_claimerIdTouserInputEnvelope = {
  data: Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInput | Prisma.registration_codeCreateManyUser_registration_code_claimerIdTouserInput[]
  skipDuplicates?: boolean
}

export type registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  update: Prisma.XOR<Prisma.registration_codeUpdateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedUpdateWithoutUser_registration_code_referrerIdTouserInput>
  create: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_referrerIdTouserInput>
}

export type registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_referrerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  data: Prisma.XOR<Prisma.registration_codeUpdateWithoutUser_registration_code_referrerIdTouserInput, Prisma.registration_codeUncheckedUpdateWithoutUser_registration_code_referrerIdTouserInput>
}

export type registration_codeUpdateManyWithWhereWithoutUser_registration_code_referrerIdTouserInput = {
  where: Prisma.registration_codeScalarWhereInput
  data: Prisma.XOR<Prisma.registration_codeUpdateManyMutationInput, Prisma.registration_codeUncheckedUpdateManyWithoutUser_registration_code_referrerIdTouserInput>
}

export type registration_codeScalarWhereInput = {
  AND?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
  OR?: Prisma.registration_codeScalarWhereInput[]
  NOT?: Prisma.registration_codeScalarWhereInput | Prisma.registration_codeScalarWhereInput[]
  id?: Prisma.IntFilter<"registration_code"> | number
  code?: Prisma.StringFilter<"registration_code"> | string
  note?: Prisma.StringFilter<"registration_code"> | string
  unlimitedUse?: Prisma.BoolNullableFilter<"registration_code"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"registration_code"> | Date | string
  referrerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
  claimerId?: Prisma.IntNullableFilter<"registration_code"> | number | null
}

export type registration_codeUpsertWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  update: Prisma.XOR<Prisma.registration_codeUpdateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedUpdateWithoutUser_registration_code_claimerIdTouserInput>
  create: Prisma.XOR<Prisma.registration_codeCreateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedCreateWithoutUser_registration_code_claimerIdTouserInput>
}

export type registration_codeUpdateWithWhereUniqueWithoutUser_registration_code_claimerIdTouserInput = {
  where: Prisma.registration_codeWhereUniqueInput
  data: Prisma.XOR<Prisma.registration_codeUpdateWithoutUser_registration_code_claimerIdTouserInput, Prisma.registration_codeUncheckedUpdateWithoutUser_registration_code_claimerIdTouserInput>
}

export type registration_codeUpdateManyWithWhereWithoutUser_registration_code_claimerIdTouserInput = {
  where: Prisma.registration_codeScalarWhereInput
  data: Prisma.XOR<Prisma.registration_codeUpdateManyMutationInput, Prisma.registration_codeUncheckedUpdateManyWithoutUser_registration_code_claimerIdTouserInput>
}

export type registration_codeCreateManyUser_registration_code_referrerIdTouserInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  claimerId?: number | null
}

export type registration_codeCreateManyUser_registration_code_claimerIdTouserInput = {
  id?: number
  code: string
  note: string
  unlimitedUse?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  referrerId?: number | null
}

export type registration_codeUpdateWithoutUser_registration_code_referrerIdTouserInput = {
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_registration_code_claimerIdTouser?: Prisma.userUpdateOneWithoutRegistration_code_registration_code_claimerIdTouserNestedInput
}

export type registration_codeUncheckedUpdateWithoutUser_registration_code_referrerIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  claimerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type registration_codeUncheckedUpdateManyWithoutUser_registration_code_referrerIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  claimerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type registration_codeUpdateWithoutUser_registration_code_claimerIdTouserInput = {
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_registration_code_referrerIdTouser?: Prisma.userUpdateOneWithoutRegistration_code_registration_code_referrerIdTouserNestedInput
}

export type registration_codeUncheckedUpdateWithoutUser_registration_code_claimerIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  referrerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type registration_codeUncheckedUpdateManyWithoutUser_registration_code_claimerIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  code?: Prisma.StringFieldUpdateOperationsInput | string
  note?: Prisma.StringFieldUpdateOperationsInput | string
  unlimitedUse?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  referrerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type registration_codeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  code?: boolean
  note?: boolean
  unlimitedUse?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  referrerId?: boolean
  claimerId?: boolean
  user_registration_code_referrerIdTouser?: boolean | Prisma.registration_code$user_registration_code_referrerIdTouserArgs<ExtArgs>
  user_registration_code_claimerIdTouser?: boolean | Prisma.registration_code$user_registration_code_claimerIdTouserArgs<ExtArgs>
}, ExtArgs["result"]["registration_code"]>



export type registration_codeSelectScalar = {
  id?: boolean
  code?: boolean
  note?: boolean
  unlimitedUse?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  referrerId?: boolean
  claimerId?: boolean
}

export type registration_codeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "code" | "note" | "unlimitedUse" | "createdAt" | "updatedAt" | "referrerId" | "claimerId", ExtArgs["result"]["registration_code"]>
export type registration_codeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_registration_code_referrerIdTouser?: boolean | Prisma.registration_code$user_registration_code_referrerIdTouserArgs<ExtArgs>
  user_registration_code_claimerIdTouser?: boolean | Prisma.registration_code$user_registration_code_claimerIdTouserArgs<ExtArgs>
}

export type $registration_codePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "registration_code"
  objects: {
    user_registration_code_referrerIdTouser: Prisma.$userPayload<ExtArgs> | null
    user_registration_code_claimerIdTouser: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    code: string
    note: string
    unlimitedUse: boolean | null
    createdAt: Date
    updatedAt: Date
    referrerId: number | null
    claimerId: number | null
  }, ExtArgs["result"]["registration_code"]>
  composites: {}
}

export type registration_codeGetPayload<S extends boolean | null | undefined | registration_codeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$registration_codePayload, S>

export type registration_codeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<registration_codeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Registration_codeCountAggregateInputType | true
  }

export interface registration_codeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['registration_code'], meta: { name: 'registration_code' } }
  /**
   * Find zero or one Registration_code that matches the filter.
   * @param {registration_codeFindUniqueArgs} args - Arguments to find a Registration_code
   * @example
   * // Get one Registration_code
   * const registration_code = await prisma.registration_code.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends registration_codeFindUniqueArgs>(args: Prisma.SelectSubset<T, registration_codeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Registration_code that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {registration_codeFindUniqueOrThrowArgs} args - Arguments to find a Registration_code
   * @example
   * // Get one Registration_code
   * const registration_code = await prisma.registration_code.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends registration_codeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, registration_codeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Registration_code that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeFindFirstArgs} args - Arguments to find a Registration_code
   * @example
   * // Get one Registration_code
   * const registration_code = await prisma.registration_code.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends registration_codeFindFirstArgs>(args?: Prisma.SelectSubset<T, registration_codeFindFirstArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Registration_code that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeFindFirstOrThrowArgs} args - Arguments to find a Registration_code
   * @example
   * // Get one Registration_code
   * const registration_code = await prisma.registration_code.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends registration_codeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, registration_codeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Registration_codes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Registration_codes
   * const registration_codes = await prisma.registration_code.findMany()
   * 
   * // Get first 10 Registration_codes
   * const registration_codes = await prisma.registration_code.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const registration_codeWithIdOnly = await prisma.registration_code.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends registration_codeFindManyArgs>(args?: Prisma.SelectSubset<T, registration_codeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Registration_code.
   * @param {registration_codeCreateArgs} args - Arguments to create a Registration_code.
   * @example
   * // Create one Registration_code
   * const Registration_code = await prisma.registration_code.create({
   *   data: {
   *     // ... data to create a Registration_code
   *   }
   * })
   * 
   */
  create<T extends registration_codeCreateArgs>(args: Prisma.SelectSubset<T, registration_codeCreateArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Registration_codes.
   * @param {registration_codeCreateManyArgs} args - Arguments to create many Registration_codes.
   * @example
   * // Create many Registration_codes
   * const registration_code = await prisma.registration_code.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends registration_codeCreateManyArgs>(args?: Prisma.SelectSubset<T, registration_codeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Registration_code.
   * @param {registration_codeDeleteArgs} args - Arguments to delete one Registration_code.
   * @example
   * // Delete one Registration_code
   * const Registration_code = await prisma.registration_code.delete({
   *   where: {
   *     // ... filter to delete one Registration_code
   *   }
   * })
   * 
   */
  delete<T extends registration_codeDeleteArgs>(args: Prisma.SelectSubset<T, registration_codeDeleteArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Registration_code.
   * @param {registration_codeUpdateArgs} args - Arguments to update one Registration_code.
   * @example
   * // Update one Registration_code
   * const registration_code = await prisma.registration_code.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends registration_codeUpdateArgs>(args: Prisma.SelectSubset<T, registration_codeUpdateArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Registration_codes.
   * @param {registration_codeDeleteManyArgs} args - Arguments to filter Registration_codes to delete.
   * @example
   * // Delete a few Registration_codes
   * const { count } = await prisma.registration_code.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends registration_codeDeleteManyArgs>(args?: Prisma.SelectSubset<T, registration_codeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Registration_codes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Registration_codes
   * const registration_code = await prisma.registration_code.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends registration_codeUpdateManyArgs>(args: Prisma.SelectSubset<T, registration_codeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Registration_code.
   * @param {registration_codeUpsertArgs} args - Arguments to update or create a Registration_code.
   * @example
   * // Update or create a Registration_code
   * const registration_code = await prisma.registration_code.upsert({
   *   create: {
   *     // ... data to create a Registration_code
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Registration_code we want to update
   *   }
   * })
   */
  upsert<T extends registration_codeUpsertArgs>(args: Prisma.SelectSubset<T, registration_codeUpsertArgs<ExtArgs>>): Prisma.Prisma__registration_codeClient<runtime.Types.Result.GetResult<Prisma.$registration_codePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Registration_codes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeCountArgs} args - Arguments to filter Registration_codes to count.
   * @example
   * // Count the number of Registration_codes
   * const count = await prisma.registration_code.count({
   *   where: {
   *     // ... the filter for the Registration_codes we want to count
   *   }
   * })
  **/
  count<T extends registration_codeCountArgs>(
    args?: Prisma.Subset<T, registration_codeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Registration_codeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Registration_code.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Registration_codeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Registration_codeAggregateArgs>(args: Prisma.Subset<T, Registration_codeAggregateArgs>): Prisma.PrismaPromise<GetRegistration_codeAggregateType<T>>

  /**
   * Group by Registration_code.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {registration_codeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends registration_codeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: registration_codeGroupByArgs['orderBy'] }
      : { orderBy?: registration_codeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, registration_codeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRegistration_codeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the registration_code model
 */
readonly fields: registration_codeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for registration_code.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__registration_codeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_registration_code_referrerIdTouser<T extends Prisma.registration_code$user_registration_code_referrerIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.registration_code$user_registration_code_referrerIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_registration_code_claimerIdTouser<T extends Prisma.registration_code$user_registration_code_claimerIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.registration_code$user_registration_code_claimerIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the registration_code model
 */
export interface registration_codeFieldRefs {
  readonly id: Prisma.FieldRef<"registration_code", 'Int'>
  readonly code: Prisma.FieldRef<"registration_code", 'String'>
  readonly note: Prisma.FieldRef<"registration_code", 'String'>
  readonly unlimitedUse: Prisma.FieldRef<"registration_code", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"registration_code", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"registration_code", 'DateTime'>
  readonly referrerId: Prisma.FieldRef<"registration_code", 'Int'>
  readonly claimerId: Prisma.FieldRef<"registration_code", 'Int'>
}
    

// Custom InputTypes
/**
 * registration_code findUnique
 */
export type registration_codeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter, which registration_code to fetch.
   */
  where: Prisma.registration_codeWhereUniqueInput
}

/**
 * registration_code findUniqueOrThrow
 */
export type registration_codeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter, which registration_code to fetch.
   */
  where: Prisma.registration_codeWhereUniqueInput
}

/**
 * registration_code findFirst
 */
export type registration_codeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter, which registration_code to fetch.
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of registration_codes to fetch.
   */
  orderBy?: Prisma.registration_codeOrderByWithRelationInput | Prisma.registration_codeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for registration_codes.
   */
  cursor?: Prisma.registration_codeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` registration_codes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` registration_codes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of registration_codes.
   */
  distinct?: Prisma.Registration_codeScalarFieldEnum | Prisma.Registration_codeScalarFieldEnum[]
}

/**
 * registration_code findFirstOrThrow
 */
export type registration_codeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter, which registration_code to fetch.
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of registration_codes to fetch.
   */
  orderBy?: Prisma.registration_codeOrderByWithRelationInput | Prisma.registration_codeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for registration_codes.
   */
  cursor?: Prisma.registration_codeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` registration_codes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` registration_codes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of registration_codes.
   */
  distinct?: Prisma.Registration_codeScalarFieldEnum | Prisma.Registration_codeScalarFieldEnum[]
}

/**
 * registration_code findMany
 */
export type registration_codeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter, which registration_codes to fetch.
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of registration_codes to fetch.
   */
  orderBy?: Prisma.registration_codeOrderByWithRelationInput | Prisma.registration_codeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing registration_codes.
   */
  cursor?: Prisma.registration_codeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` registration_codes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` registration_codes.
   */
  skip?: number
  distinct?: Prisma.Registration_codeScalarFieldEnum | Prisma.Registration_codeScalarFieldEnum[]
}

/**
 * registration_code create
 */
export type registration_codeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * The data needed to create a registration_code.
   */
  data: Prisma.XOR<Prisma.registration_codeCreateInput, Prisma.registration_codeUncheckedCreateInput>
}

/**
 * registration_code createMany
 */
export type registration_codeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many registration_codes.
   */
  data: Prisma.registration_codeCreateManyInput | Prisma.registration_codeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * registration_code update
 */
export type registration_codeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * The data needed to update a registration_code.
   */
  data: Prisma.XOR<Prisma.registration_codeUpdateInput, Prisma.registration_codeUncheckedUpdateInput>
  /**
   * Choose, which registration_code to update.
   */
  where: Prisma.registration_codeWhereUniqueInput
}

/**
 * registration_code updateMany
 */
export type registration_codeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update registration_codes.
   */
  data: Prisma.XOR<Prisma.registration_codeUpdateManyMutationInput, Prisma.registration_codeUncheckedUpdateManyInput>
  /**
   * Filter which registration_codes to update
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * Limit how many registration_codes to update.
   */
  limit?: number
}

/**
 * registration_code upsert
 */
export type registration_codeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * The filter to search for the registration_code to update in case it exists.
   */
  where: Prisma.registration_codeWhereUniqueInput
  /**
   * In case the registration_code found by the `where` argument doesn't exist, create a new registration_code with this data.
   */
  create: Prisma.XOR<Prisma.registration_codeCreateInput, Prisma.registration_codeUncheckedCreateInput>
  /**
   * In case the registration_code was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.registration_codeUpdateInput, Prisma.registration_codeUncheckedUpdateInput>
}

/**
 * registration_code delete
 */
export type registration_codeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
  /**
   * Filter which registration_code to delete.
   */
  where: Prisma.registration_codeWhereUniqueInput
}

/**
 * registration_code deleteMany
 */
export type registration_codeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which registration_codes to delete
   */
  where?: Prisma.registration_codeWhereInput
  /**
   * Limit how many registration_codes to delete.
   */
  limit?: number
}

/**
 * registration_code.user_registration_code_referrerIdTouser
 */
export type registration_code$user_registration_code_referrerIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * registration_code.user_registration_code_claimerIdTouser
 */
export type registration_code$user_registration_code_claimerIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * registration_code without action
 */
export type registration_codeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the registration_code
   */
  select?: Prisma.registration_codeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the registration_code
   */
  omit?: Prisma.registration_codeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.registration_codeInclude<ExtArgs> | null
}
