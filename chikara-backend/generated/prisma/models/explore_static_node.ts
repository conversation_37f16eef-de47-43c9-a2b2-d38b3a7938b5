
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `explore_static_node` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model explore_static_node
 * 
 */
export type explore_static_nodeModel = runtime.Types.Result.DefaultSelection<Prisma.$explore_static_nodePayload>

export type AggregateExplore_static_node = {
  _count: Explore_static_nodeCountAggregateOutputType | null
  _avg: Explore_static_nodeAvgAggregateOutputType | null
  _sum: Explore_static_nodeSumAggregateOutputType | null
  _min: Explore_static_nodeMinAggregateOutputType | null
  _max: Explore_static_nodeMaxAggregateOutputType | null
}

export type Explore_static_nodeAvgAggregateOutputType = {
  id: number | null
  shopId: number | null
}

export type Explore_static_nodeSumAggregateOutputType = {
  id: number | null
  shopId: number | null
}

export type Explore_static_nodeMinAggregateOutputType = {
  id: number | null
  nodeType: $Enums.ExploreNodeType | null
  title: string | null
  description: string | null
  location: $Enums.ExploreNodeLocation | null
  shopId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Explore_static_nodeMaxAggregateOutputType = {
  id: number | null
  nodeType: $Enums.ExploreNodeType | null
  title: string | null
  description: string | null
  location: $Enums.ExploreNodeLocation | null
  shopId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Explore_static_nodeCountAggregateOutputType = {
  id: number
  nodeType: number
  title: number
  description: number
  position: number
  metadata: number
  location: number
  shopId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Explore_static_nodeAvgAggregateInputType = {
  id?: true
  shopId?: true
}

export type Explore_static_nodeSumAggregateInputType = {
  id?: true
  shopId?: true
}

export type Explore_static_nodeMinAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  location?: true
  shopId?: true
  createdAt?: true
  updatedAt?: true
}

export type Explore_static_nodeMaxAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  location?: true
  shopId?: true
  createdAt?: true
  updatedAt?: true
}

export type Explore_static_nodeCountAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  position?: true
  metadata?: true
  location?: true
  shopId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Explore_static_nodeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which explore_static_node to aggregate.
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_static_nodes to fetch.
   */
  orderBy?: Prisma.explore_static_nodeOrderByWithRelationInput | Prisma.explore_static_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.explore_static_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_static_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_static_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned explore_static_nodes
  **/
  _count?: true | Explore_static_nodeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Explore_static_nodeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Explore_static_nodeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Explore_static_nodeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Explore_static_nodeMaxAggregateInputType
}

export type GetExplore_static_nodeAggregateType<T extends Explore_static_nodeAggregateArgs> = {
      [P in keyof T & keyof AggregateExplore_static_node]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateExplore_static_node[P]>
    : Prisma.GetScalarType<T[P], AggregateExplore_static_node[P]>
}




export type explore_static_nodeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.explore_static_nodeWhereInput
  orderBy?: Prisma.explore_static_nodeOrderByWithAggregationInput | Prisma.explore_static_nodeOrderByWithAggregationInput[]
  by: Prisma.Explore_static_nodeScalarFieldEnum[] | Prisma.Explore_static_nodeScalarFieldEnum
  having?: Prisma.explore_static_nodeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Explore_static_nodeCountAggregateInputType | true
  _avg?: Explore_static_nodeAvgAggregateInputType
  _sum?: Explore_static_nodeSumAggregateInputType
  _min?: Explore_static_nodeMinAggregateInputType
  _max?: Explore_static_nodeMaxAggregateInputType
}

export type Explore_static_nodeGroupByOutputType = {
  id: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position: runtime.JsonValue
  metadata: runtime.JsonValue | null
  location: $Enums.ExploreNodeLocation
  shopId: number | null
  createdAt: Date
  updatedAt: Date
  _count: Explore_static_nodeCountAggregateOutputType | null
  _avg: Explore_static_nodeAvgAggregateOutputType | null
  _sum: Explore_static_nodeSumAggregateOutputType | null
  _min: Explore_static_nodeMinAggregateOutputType | null
  _max: Explore_static_nodeMaxAggregateOutputType | null
}

type GetExplore_static_nodeGroupByPayload<T extends explore_static_nodeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Explore_static_nodeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Explore_static_nodeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Explore_static_nodeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Explore_static_nodeGroupByOutputType[P]>
      }
    >
  >



export type explore_static_nodeWhereInput = {
  AND?: Prisma.explore_static_nodeWhereInput | Prisma.explore_static_nodeWhereInput[]
  OR?: Prisma.explore_static_nodeWhereInput[]
  NOT?: Prisma.explore_static_nodeWhereInput | Prisma.explore_static_nodeWhereInput[]
  id?: Prisma.IntFilter<"explore_static_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_static_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_static_node"> | string
  description?: Prisma.StringFilter<"explore_static_node"> | string
  position?: Prisma.JsonFilter<"explore_static_node">
  metadata?: Prisma.JsonNullableFilter<"explore_static_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_static_node"> | $Enums.ExploreNodeLocation
  shopId?: Prisma.IntNullableFilter<"explore_static_node"> | number | null
  createdAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
}

export type explore_static_nodeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shop?: Prisma.shopOrderByWithRelationInput
  _relevance?: Prisma.explore_static_nodeOrderByRelevanceInput
}

export type explore_static_nodeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.explore_static_nodeWhereInput | Prisma.explore_static_nodeWhereInput[]
  OR?: Prisma.explore_static_nodeWhereInput[]
  NOT?: Prisma.explore_static_nodeWhereInput | Prisma.explore_static_nodeWhereInput[]
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_static_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_static_node"> | string
  description?: Prisma.StringFilter<"explore_static_node"> | string
  position?: Prisma.JsonFilter<"explore_static_node">
  metadata?: Prisma.JsonNullableFilter<"explore_static_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_static_node"> | $Enums.ExploreNodeLocation
  shopId?: Prisma.IntNullableFilter<"explore_static_node"> | number | null
  createdAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
}, "id">

export type explore_static_nodeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.explore_static_nodeCountOrderByAggregateInput
  _avg?: Prisma.explore_static_nodeAvgOrderByAggregateInput
  _max?: Prisma.explore_static_nodeMaxOrderByAggregateInput
  _min?: Prisma.explore_static_nodeMinOrderByAggregateInput
  _sum?: Prisma.explore_static_nodeSumOrderByAggregateInput
}

export type explore_static_nodeScalarWhereWithAggregatesInput = {
  AND?: Prisma.explore_static_nodeScalarWhereWithAggregatesInput | Prisma.explore_static_nodeScalarWhereWithAggregatesInput[]
  OR?: Prisma.explore_static_nodeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.explore_static_nodeScalarWhereWithAggregatesInput | Prisma.explore_static_nodeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"explore_static_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeWithAggregatesFilter<"explore_static_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringWithAggregatesFilter<"explore_static_node"> | string
  description?: Prisma.StringWithAggregatesFilter<"explore_static_node"> | string
  position?: Prisma.JsonWithAggregatesFilter<"explore_static_node">
  metadata?: Prisma.JsonNullableWithAggregatesFilter<"explore_static_node">
  location?: Prisma.EnumExploreNodeLocationWithAggregatesFilter<"explore_static_node"> | $Enums.ExploreNodeLocation
  shopId?: Prisma.IntNullableWithAggregatesFilter<"explore_static_node"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"explore_static_node"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"explore_static_node"> | Date | string
}

export type explore_static_nodeCreateInput = {
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  createdAt?: Date | string
  updatedAt?: Date | string
  shop?: Prisma.shopCreateNestedOneWithoutExplore_static_nodeInput
}

export type explore_static_nodeUncheckedCreateInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  shopId?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_static_nodeUpdateInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shop?: Prisma.shopUpdateOneWithoutExplore_static_nodeNestedInput
}

export type explore_static_nodeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_static_nodeCreateManyInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  shopId?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_static_nodeUpdateManyMutationInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_static_nodeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type Explore_static_nodeListRelationFilter = {
  every?: Prisma.explore_static_nodeWhereInput
  some?: Prisma.explore_static_nodeWhereInput
  none?: Prisma.explore_static_nodeWhereInput
}

export type explore_static_nodeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type explore_static_nodeOrderByRelevanceInput = {
  fields: Prisma.explore_static_nodeOrderByRelevanceFieldEnum | Prisma.explore_static_nodeOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type explore_static_nodeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
  location?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type explore_static_nodeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
}

export type explore_static_nodeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  location?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type explore_static_nodeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  location?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type explore_static_nodeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
}

export type explore_static_nodeCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput> | Prisma.explore_static_nodeCreateWithoutShopInput[] | Prisma.explore_static_nodeUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.explore_static_nodeCreateOrConnectWithoutShopInput | Prisma.explore_static_nodeCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.explore_static_nodeCreateManyShopInputEnvelope
  connect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
}

export type explore_static_nodeUncheckedCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput> | Prisma.explore_static_nodeCreateWithoutShopInput[] | Prisma.explore_static_nodeUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.explore_static_nodeCreateOrConnectWithoutShopInput | Prisma.explore_static_nodeCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.explore_static_nodeCreateManyShopInputEnvelope
  connect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
}

export type explore_static_nodeUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput> | Prisma.explore_static_nodeCreateWithoutShopInput[] | Prisma.explore_static_nodeUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.explore_static_nodeCreateOrConnectWithoutShopInput | Prisma.explore_static_nodeCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.explore_static_nodeUpsertWithWhereUniqueWithoutShopInput | Prisma.explore_static_nodeUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.explore_static_nodeCreateManyShopInputEnvelope
  set?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  disconnect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  delete?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  connect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  update?: Prisma.explore_static_nodeUpdateWithWhereUniqueWithoutShopInput | Prisma.explore_static_nodeUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.explore_static_nodeUpdateManyWithWhereWithoutShopInput | Prisma.explore_static_nodeUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.explore_static_nodeScalarWhereInput | Prisma.explore_static_nodeScalarWhereInput[]
}

export type explore_static_nodeUncheckedUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput> | Prisma.explore_static_nodeCreateWithoutShopInput[] | Prisma.explore_static_nodeUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.explore_static_nodeCreateOrConnectWithoutShopInput | Prisma.explore_static_nodeCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.explore_static_nodeUpsertWithWhereUniqueWithoutShopInput | Prisma.explore_static_nodeUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.explore_static_nodeCreateManyShopInputEnvelope
  set?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  disconnect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  delete?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  connect?: Prisma.explore_static_nodeWhereUniqueInput | Prisma.explore_static_nodeWhereUniqueInput[]
  update?: Prisma.explore_static_nodeUpdateWithWhereUniqueWithoutShopInput | Prisma.explore_static_nodeUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.explore_static_nodeUpdateManyWithWhereWithoutShopInput | Prisma.explore_static_nodeUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.explore_static_nodeScalarWhereInput | Prisma.explore_static_nodeScalarWhereInput[]
}

export type EnumExploreNodeTypeFieldUpdateOperationsInput = {
  set?: $Enums.ExploreNodeType
}

export type EnumExploreNodeLocationFieldUpdateOperationsInput = {
  set?: $Enums.ExploreNodeLocation
}

export type explore_static_nodeCreateWithoutShopInput = {
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_static_nodeUncheckedCreateWithoutShopInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_static_nodeCreateOrConnectWithoutShopInput = {
  where: Prisma.explore_static_nodeWhereUniqueInput
  create: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput>
}

export type explore_static_nodeCreateManyShopInputEnvelope = {
  data: Prisma.explore_static_nodeCreateManyShopInput | Prisma.explore_static_nodeCreateManyShopInput[]
  skipDuplicates?: boolean
}

export type explore_static_nodeUpsertWithWhereUniqueWithoutShopInput = {
  where: Prisma.explore_static_nodeWhereUniqueInput
  update: Prisma.XOR<Prisma.explore_static_nodeUpdateWithoutShopInput, Prisma.explore_static_nodeUncheckedUpdateWithoutShopInput>
  create: Prisma.XOR<Prisma.explore_static_nodeCreateWithoutShopInput, Prisma.explore_static_nodeUncheckedCreateWithoutShopInput>
}

export type explore_static_nodeUpdateWithWhereUniqueWithoutShopInput = {
  where: Prisma.explore_static_nodeWhereUniqueInput
  data: Prisma.XOR<Prisma.explore_static_nodeUpdateWithoutShopInput, Prisma.explore_static_nodeUncheckedUpdateWithoutShopInput>
}

export type explore_static_nodeUpdateManyWithWhereWithoutShopInput = {
  where: Prisma.explore_static_nodeScalarWhereInput
  data: Prisma.XOR<Prisma.explore_static_nodeUpdateManyMutationInput, Prisma.explore_static_nodeUncheckedUpdateManyWithoutShopInput>
}

export type explore_static_nodeScalarWhereInput = {
  AND?: Prisma.explore_static_nodeScalarWhereInput | Prisma.explore_static_nodeScalarWhereInput[]
  OR?: Prisma.explore_static_nodeScalarWhereInput[]
  NOT?: Prisma.explore_static_nodeScalarWhereInput | Prisma.explore_static_nodeScalarWhereInput[]
  id?: Prisma.IntFilter<"explore_static_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_static_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_static_node"> | string
  description?: Prisma.StringFilter<"explore_static_node"> | string
  position?: Prisma.JsonFilter<"explore_static_node">
  metadata?: Prisma.JsonNullableFilter<"explore_static_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_static_node"> | $Enums.ExploreNodeLocation
  shopId?: Prisma.IntNullableFilter<"explore_static_node"> | number | null
  createdAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_static_node"> | Date | string
}

export type explore_static_nodeCreateManyShopInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_static_nodeUpdateWithoutShopInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_static_nodeUncheckedUpdateWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_static_nodeUncheckedUpdateManyWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type explore_static_nodeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  nodeType?: boolean
  title?: boolean
  description?: boolean
  position?: boolean
  metadata?: boolean
  location?: boolean
  shopId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shop?: boolean | Prisma.explore_static_node$shopArgs<ExtArgs>
}, ExtArgs["result"]["explore_static_node"]>



export type explore_static_nodeSelectScalar = {
  id?: boolean
  nodeType?: boolean
  title?: boolean
  description?: boolean
  position?: boolean
  metadata?: boolean
  location?: boolean
  shopId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type explore_static_nodeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "nodeType" | "title" | "description" | "position" | "metadata" | "location" | "shopId" | "createdAt" | "updatedAt", ExtArgs["result"]["explore_static_node"]>
export type explore_static_nodeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shop?: boolean | Prisma.explore_static_node$shopArgs<ExtArgs>
}

export type $explore_static_nodePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "explore_static_node"
  objects: {
    shop: Prisma.$shopPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    nodeType: $Enums.ExploreNodeType
    title: string
    description: string
    position:unknown
    metadata:unknown | null
    location: $Enums.ExploreNodeLocation
    shopId: number | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["explore_static_node"]>
  composites: {}
}

export type explore_static_nodeGetPayload<S extends boolean | null | undefined | explore_static_nodeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload, S>

export type explore_static_nodeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<explore_static_nodeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Explore_static_nodeCountAggregateInputType | true
  }

export interface explore_static_nodeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['explore_static_node'], meta: { name: 'explore_static_node' } }
  /**
   * Find zero or one Explore_static_node that matches the filter.
   * @param {explore_static_nodeFindUniqueArgs} args - Arguments to find a Explore_static_node
   * @example
   * // Get one Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends explore_static_nodeFindUniqueArgs>(args: Prisma.SelectSubset<T, explore_static_nodeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Explore_static_node that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {explore_static_nodeFindUniqueOrThrowArgs} args - Arguments to find a Explore_static_node
   * @example
   * // Get one Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends explore_static_nodeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, explore_static_nodeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Explore_static_node that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeFindFirstArgs} args - Arguments to find a Explore_static_node
   * @example
   * // Get one Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends explore_static_nodeFindFirstArgs>(args?: Prisma.SelectSubset<T, explore_static_nodeFindFirstArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Explore_static_node that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeFindFirstOrThrowArgs} args - Arguments to find a Explore_static_node
   * @example
   * // Get one Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends explore_static_nodeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, explore_static_nodeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Explore_static_nodes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Explore_static_nodes
   * const explore_static_nodes = await prisma.explore_static_node.findMany()
   * 
   * // Get first 10 Explore_static_nodes
   * const explore_static_nodes = await prisma.explore_static_node.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const explore_static_nodeWithIdOnly = await prisma.explore_static_node.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends explore_static_nodeFindManyArgs>(args?: Prisma.SelectSubset<T, explore_static_nodeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Explore_static_node.
   * @param {explore_static_nodeCreateArgs} args - Arguments to create a Explore_static_node.
   * @example
   * // Create one Explore_static_node
   * const Explore_static_node = await prisma.explore_static_node.create({
   *   data: {
   *     // ... data to create a Explore_static_node
   *   }
   * })
   * 
   */
  create<T extends explore_static_nodeCreateArgs>(args: Prisma.SelectSubset<T, explore_static_nodeCreateArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Explore_static_nodes.
   * @param {explore_static_nodeCreateManyArgs} args - Arguments to create many Explore_static_nodes.
   * @example
   * // Create many Explore_static_nodes
   * const explore_static_node = await prisma.explore_static_node.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends explore_static_nodeCreateManyArgs>(args?: Prisma.SelectSubset<T, explore_static_nodeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Explore_static_node.
   * @param {explore_static_nodeDeleteArgs} args - Arguments to delete one Explore_static_node.
   * @example
   * // Delete one Explore_static_node
   * const Explore_static_node = await prisma.explore_static_node.delete({
   *   where: {
   *     // ... filter to delete one Explore_static_node
   *   }
   * })
   * 
   */
  delete<T extends explore_static_nodeDeleteArgs>(args: Prisma.SelectSubset<T, explore_static_nodeDeleteArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Explore_static_node.
   * @param {explore_static_nodeUpdateArgs} args - Arguments to update one Explore_static_node.
   * @example
   * // Update one Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends explore_static_nodeUpdateArgs>(args: Prisma.SelectSubset<T, explore_static_nodeUpdateArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Explore_static_nodes.
   * @param {explore_static_nodeDeleteManyArgs} args - Arguments to filter Explore_static_nodes to delete.
   * @example
   * // Delete a few Explore_static_nodes
   * const { count } = await prisma.explore_static_node.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends explore_static_nodeDeleteManyArgs>(args?: Prisma.SelectSubset<T, explore_static_nodeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Explore_static_nodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Explore_static_nodes
   * const explore_static_node = await prisma.explore_static_node.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends explore_static_nodeUpdateManyArgs>(args: Prisma.SelectSubset<T, explore_static_nodeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Explore_static_node.
   * @param {explore_static_nodeUpsertArgs} args - Arguments to update or create a Explore_static_node.
   * @example
   * // Update or create a Explore_static_node
   * const explore_static_node = await prisma.explore_static_node.upsert({
   *   create: {
   *     // ... data to create a Explore_static_node
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Explore_static_node we want to update
   *   }
   * })
   */
  upsert<T extends explore_static_nodeUpsertArgs>(args: Prisma.SelectSubset<T, explore_static_nodeUpsertArgs<ExtArgs>>): Prisma.Prisma__explore_static_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Explore_static_nodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeCountArgs} args - Arguments to filter Explore_static_nodes to count.
   * @example
   * // Count the number of Explore_static_nodes
   * const count = await prisma.explore_static_node.count({
   *   where: {
   *     // ... the filter for the Explore_static_nodes we want to count
   *   }
   * })
  **/
  count<T extends explore_static_nodeCountArgs>(
    args?: Prisma.Subset<T, explore_static_nodeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Explore_static_nodeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Explore_static_node.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Explore_static_nodeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Explore_static_nodeAggregateArgs>(args: Prisma.Subset<T, Explore_static_nodeAggregateArgs>): Prisma.PrismaPromise<GetExplore_static_nodeAggregateType<T>>

  /**
   * Group by Explore_static_node.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_static_nodeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends explore_static_nodeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: explore_static_nodeGroupByArgs['orderBy'] }
      : { orderBy?: explore_static_nodeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, explore_static_nodeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetExplore_static_nodeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the explore_static_node model
 */
readonly fields: explore_static_nodeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for explore_static_node.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__explore_static_nodeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  shop<T extends Prisma.explore_static_node$shopArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.explore_static_node$shopArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the explore_static_node model
 */
export interface explore_static_nodeFieldRefs {
  readonly id: Prisma.FieldRef<"explore_static_node", 'Int'>
  readonly nodeType: Prisma.FieldRef<"explore_static_node", 'ExploreNodeType'>
  readonly title: Prisma.FieldRef<"explore_static_node", 'String'>
  readonly description: Prisma.FieldRef<"explore_static_node", 'String'>
  readonly position: Prisma.FieldRef<"explore_static_node", 'Json'>
  readonly metadata: Prisma.FieldRef<"explore_static_node", 'Json'>
  readonly location: Prisma.FieldRef<"explore_static_node", 'ExploreNodeLocation'>
  readonly shopId: Prisma.FieldRef<"explore_static_node", 'Int'>
  readonly createdAt: Prisma.FieldRef<"explore_static_node", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"explore_static_node", 'DateTime'>
}
    

// Custom InputTypes
/**
 * explore_static_node findUnique
 */
export type explore_static_nodeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_static_node to fetch.
   */
  where: Prisma.explore_static_nodeWhereUniqueInput
}

/**
 * explore_static_node findUniqueOrThrow
 */
export type explore_static_nodeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_static_node to fetch.
   */
  where: Prisma.explore_static_nodeWhereUniqueInput
}

/**
 * explore_static_node findFirst
 */
export type explore_static_nodeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_static_node to fetch.
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_static_nodes to fetch.
   */
  orderBy?: Prisma.explore_static_nodeOrderByWithRelationInput | Prisma.explore_static_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for explore_static_nodes.
   */
  cursor?: Prisma.explore_static_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_static_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_static_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of explore_static_nodes.
   */
  distinct?: Prisma.Explore_static_nodeScalarFieldEnum | Prisma.Explore_static_nodeScalarFieldEnum[]
}

/**
 * explore_static_node findFirstOrThrow
 */
export type explore_static_nodeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_static_node to fetch.
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_static_nodes to fetch.
   */
  orderBy?: Prisma.explore_static_nodeOrderByWithRelationInput | Prisma.explore_static_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for explore_static_nodes.
   */
  cursor?: Prisma.explore_static_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_static_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_static_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of explore_static_nodes.
   */
  distinct?: Prisma.Explore_static_nodeScalarFieldEnum | Prisma.Explore_static_nodeScalarFieldEnum[]
}

/**
 * explore_static_node findMany
 */
export type explore_static_nodeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_static_nodes to fetch.
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_static_nodes to fetch.
   */
  orderBy?: Prisma.explore_static_nodeOrderByWithRelationInput | Prisma.explore_static_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing explore_static_nodes.
   */
  cursor?: Prisma.explore_static_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_static_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_static_nodes.
   */
  skip?: number
  distinct?: Prisma.Explore_static_nodeScalarFieldEnum | Prisma.Explore_static_nodeScalarFieldEnum[]
}

/**
 * explore_static_node create
 */
export type explore_static_nodeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * The data needed to create a explore_static_node.
   */
  data: Prisma.XOR<Prisma.explore_static_nodeCreateInput, Prisma.explore_static_nodeUncheckedCreateInput>
}

/**
 * explore_static_node createMany
 */
export type explore_static_nodeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many explore_static_nodes.
   */
  data: Prisma.explore_static_nodeCreateManyInput | Prisma.explore_static_nodeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * explore_static_node update
 */
export type explore_static_nodeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * The data needed to update a explore_static_node.
   */
  data: Prisma.XOR<Prisma.explore_static_nodeUpdateInput, Prisma.explore_static_nodeUncheckedUpdateInput>
  /**
   * Choose, which explore_static_node to update.
   */
  where: Prisma.explore_static_nodeWhereUniqueInput
}

/**
 * explore_static_node updateMany
 */
export type explore_static_nodeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update explore_static_nodes.
   */
  data: Prisma.XOR<Prisma.explore_static_nodeUpdateManyMutationInput, Prisma.explore_static_nodeUncheckedUpdateManyInput>
  /**
   * Filter which explore_static_nodes to update
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * Limit how many explore_static_nodes to update.
   */
  limit?: number
}

/**
 * explore_static_node upsert
 */
export type explore_static_nodeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * The filter to search for the explore_static_node to update in case it exists.
   */
  where: Prisma.explore_static_nodeWhereUniqueInput
  /**
   * In case the explore_static_node found by the `where` argument doesn't exist, create a new explore_static_node with this data.
   */
  create: Prisma.XOR<Prisma.explore_static_nodeCreateInput, Prisma.explore_static_nodeUncheckedCreateInput>
  /**
   * In case the explore_static_node was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.explore_static_nodeUpdateInput, Prisma.explore_static_nodeUncheckedUpdateInput>
}

/**
 * explore_static_node delete
 */
export type explore_static_nodeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  /**
   * Filter which explore_static_node to delete.
   */
  where: Prisma.explore_static_nodeWhereUniqueInput
}

/**
 * explore_static_node deleteMany
 */
export type explore_static_nodeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which explore_static_nodes to delete
   */
  where?: Prisma.explore_static_nodeWhereInput
  /**
   * Limit how many explore_static_nodes to delete.
   */
  limit?: number
}

/**
 * explore_static_node.shop
 */
export type explore_static_node$shopArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  where?: Prisma.shopWhereInput
}

/**
 * explore_static_node without action
 */
export type explore_static_nodeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
}
