
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `bounty` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model bounty
 * 
 */
export type bountyModel = runtime.Types.Result.DefaultSelection<Prisma.$bountyPayload>

export type AggregateBounty = {
  _count: BountyCountAggregateOutputType | null
  _avg: BountyAvgAggregateOutputType | null
  _sum: BountySumAggregateOutputType | null
  _min: BountyMinAggregateOutputType | null
  _max: BountyMaxAggregateOutputType | null
}

export type BountyAvgAggregateOutputType = {
  id: number | null
  amount: number | null
  placerId: number | null
  targetId: number | null
  claimedById: number | null
}

export type BountySumAggregateOutputType = {
  id: number | null
  amount: number | null
  placerId: number | null
  targetId: number | null
  claimedById: number | null
}

export type BountyMinAggregateOutputType = {
  id: number | null
  amount: number | null
  reason: string | null
  active: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  placerId: number | null
  targetId: number | null
  claimedById: number | null
}

export type BountyMaxAggregateOutputType = {
  id: number | null
  amount: number | null
  reason: string | null
  active: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  placerId: number | null
  targetId: number | null
  claimedById: number | null
}

export type BountyCountAggregateOutputType = {
  id: number
  amount: number
  reason: number
  active: number
  createdAt: number
  updatedAt: number
  placerId: number
  targetId: number
  claimedById: number
  _all: number
}


export type BountyAvgAggregateInputType = {
  id?: true
  amount?: true
  placerId?: true
  targetId?: true
  claimedById?: true
}

export type BountySumAggregateInputType = {
  id?: true
  amount?: true
  placerId?: true
  targetId?: true
  claimedById?: true
}

export type BountyMinAggregateInputType = {
  id?: true
  amount?: true
  reason?: true
  active?: true
  createdAt?: true
  updatedAt?: true
  placerId?: true
  targetId?: true
  claimedById?: true
}

export type BountyMaxAggregateInputType = {
  id?: true
  amount?: true
  reason?: true
  active?: true
  createdAt?: true
  updatedAt?: true
  placerId?: true
  targetId?: true
  claimedById?: true
}

export type BountyCountAggregateInputType = {
  id?: true
  amount?: true
  reason?: true
  active?: true
  createdAt?: true
  updatedAt?: true
  placerId?: true
  targetId?: true
  claimedById?: true
  _all?: true
}

export type BountyAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which bounty to aggregate.
   */
  where?: Prisma.bountyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bounties to fetch.
   */
  orderBy?: Prisma.bountyOrderByWithRelationInput | Prisma.bountyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.bountyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bounties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bounties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned bounties
  **/
  _count?: true | BountyCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: BountyAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: BountySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: BountyMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: BountyMaxAggregateInputType
}

export type GetBountyAggregateType<T extends BountyAggregateArgs> = {
      [P in keyof T & keyof AggregateBounty]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateBounty[P]>
    : Prisma.GetScalarType<T[P], AggregateBounty[P]>
}




export type bountyGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.bountyWhereInput
  orderBy?: Prisma.bountyOrderByWithAggregationInput | Prisma.bountyOrderByWithAggregationInput[]
  by: Prisma.BountyScalarFieldEnum[] | Prisma.BountyScalarFieldEnum
  having?: Prisma.bountyScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: BountyCountAggregateInputType | true
  _avg?: BountyAvgAggregateInputType
  _sum?: BountySumAggregateInputType
  _min?: BountyMinAggregateInputType
  _max?: BountyMaxAggregateInputType
}

export type BountyGroupByOutputType = {
  id: number
  amount: number
  reason: string | null
  active: boolean | null
  createdAt: Date
  updatedAt: Date
  placerId: number | null
  targetId: number | null
  claimedById: number | null
  _count: BountyCountAggregateOutputType | null
  _avg: BountyAvgAggregateOutputType | null
  _sum: BountySumAggregateOutputType | null
  _min: BountyMinAggregateOutputType | null
  _max: BountyMaxAggregateOutputType | null
}

type GetBountyGroupByPayload<T extends bountyGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<BountyGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof BountyGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], BountyGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], BountyGroupByOutputType[P]>
      }
    >
  >



export type bountyWhereInput = {
  AND?: Prisma.bountyWhereInput | Prisma.bountyWhereInput[]
  OR?: Prisma.bountyWhereInput[]
  NOT?: Prisma.bountyWhereInput | Prisma.bountyWhereInput[]
  id?: Prisma.IntFilter<"bounty"> | number
  amount?: Prisma.IntFilter<"bounty"> | number
  reason?: Prisma.StringNullableFilter<"bounty"> | string | null
  active?: Prisma.BoolNullableFilter<"bounty"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  placerId?: Prisma.IntNullableFilter<"bounty"> | number | null
  targetId?: Prisma.IntNullableFilter<"bounty"> | number | null
  claimedById?: Prisma.IntNullableFilter<"bounty"> | number | null
  placer?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  target?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  claimer?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type bountyOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  reason?: Prisma.SortOrderInput | Prisma.SortOrder
  active?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  placerId?: Prisma.SortOrderInput | Prisma.SortOrder
  targetId?: Prisma.SortOrderInput | Prisma.SortOrder
  claimedById?: Prisma.SortOrderInput | Prisma.SortOrder
  placer?: Prisma.userOrderByWithRelationInput
  target?: Prisma.userOrderByWithRelationInput
  claimer?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.bountyOrderByRelevanceInput
}

export type bountyWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.bountyWhereInput | Prisma.bountyWhereInput[]
  OR?: Prisma.bountyWhereInput[]
  NOT?: Prisma.bountyWhereInput | Prisma.bountyWhereInput[]
  amount?: Prisma.IntFilter<"bounty"> | number
  reason?: Prisma.StringNullableFilter<"bounty"> | string | null
  active?: Prisma.BoolNullableFilter<"bounty"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  placerId?: Prisma.IntNullableFilter<"bounty"> | number | null
  targetId?: Prisma.IntNullableFilter<"bounty"> | number | null
  claimedById?: Prisma.IntNullableFilter<"bounty"> | number | null
  placer?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  target?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  claimer?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type bountyOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  reason?: Prisma.SortOrderInput | Prisma.SortOrder
  active?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  placerId?: Prisma.SortOrderInput | Prisma.SortOrder
  targetId?: Prisma.SortOrderInput | Prisma.SortOrder
  claimedById?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.bountyCountOrderByAggregateInput
  _avg?: Prisma.bountyAvgOrderByAggregateInput
  _max?: Prisma.bountyMaxOrderByAggregateInput
  _min?: Prisma.bountyMinOrderByAggregateInput
  _sum?: Prisma.bountySumOrderByAggregateInput
}

export type bountyScalarWhereWithAggregatesInput = {
  AND?: Prisma.bountyScalarWhereWithAggregatesInput | Prisma.bountyScalarWhereWithAggregatesInput[]
  OR?: Prisma.bountyScalarWhereWithAggregatesInput[]
  NOT?: Prisma.bountyScalarWhereWithAggregatesInput | Prisma.bountyScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"bounty"> | number
  amount?: Prisma.IntWithAggregatesFilter<"bounty"> | number
  reason?: Prisma.StringNullableWithAggregatesFilter<"bounty"> | string | null
  active?: Prisma.BoolNullableWithAggregatesFilter<"bounty"> | boolean | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"bounty"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"bounty"> | Date | string
  placerId?: Prisma.IntNullableWithAggregatesFilter<"bounty"> | number | null
  targetId?: Prisma.IntNullableWithAggregatesFilter<"bounty"> | number | null
  claimedById?: Prisma.IntNullableWithAggregatesFilter<"bounty"> | number | null
}

export type bountyCreateInput = {
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placer?: Prisma.userCreateNestedOneWithoutPlacedBountiesInput
  target?: Prisma.userCreateNestedOneWithoutTargetedBountiesInput
  claimer?: Prisma.userCreateNestedOneWithoutClaimedBountiesInput
}

export type bountyUncheckedCreateInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  targetId?: number | null
  claimedById?: number | null
}

export type bountyUpdateInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placer?: Prisma.userUpdateOneWithoutPlacedBountiesNestedInput
  target?: Prisma.userUpdateOneWithoutTargetedBountiesNestedInput
  claimer?: Prisma.userUpdateOneWithoutClaimedBountiesNestedInput
}

export type bountyUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyCreateManyInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  targetId?: number | null
  claimedById?: number | null
}

export type bountyUpdateManyMutationInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type bountyUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyOrderByRelevanceInput = {
  fields: Prisma.bountyOrderByRelevanceFieldEnum | Prisma.bountyOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type bountyCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  active?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  placerId?: Prisma.SortOrder
  targetId?: Prisma.SortOrder
  claimedById?: Prisma.SortOrder
}

export type bountyAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  placerId?: Prisma.SortOrder
  targetId?: Prisma.SortOrder
  claimedById?: Prisma.SortOrder
}

export type bountyMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  active?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  placerId?: Prisma.SortOrder
  targetId?: Prisma.SortOrder
  claimedById?: Prisma.SortOrder
}

export type bountyMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  active?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  placerId?: Prisma.SortOrder
  targetId?: Prisma.SortOrder
  claimedById?: Prisma.SortOrder
}

export type bountySumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  placerId?: Prisma.SortOrder
  targetId?: Prisma.SortOrder
  claimedById?: Prisma.SortOrder
}

export type BountyListRelationFilter = {
  every?: Prisma.bountyWhereInput
  some?: Prisma.bountyWhereInput
  none?: Prisma.bountyWhereInput
}

export type bountyOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NullableBoolFieldUpdateOperationsInput = {
  set?: boolean | null
}

export type bountyCreateNestedManyWithoutPlacerInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput> | Prisma.bountyCreateWithoutPlacerInput[] | Prisma.bountyUncheckedCreateWithoutPlacerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutPlacerInput | Prisma.bountyCreateOrConnectWithoutPlacerInput[]
  createMany?: Prisma.bountyCreateManyPlacerInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyCreateNestedManyWithoutTargetInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput> | Prisma.bountyCreateWithoutTargetInput[] | Prisma.bountyUncheckedCreateWithoutTargetInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutTargetInput | Prisma.bountyCreateOrConnectWithoutTargetInput[]
  createMany?: Prisma.bountyCreateManyTargetInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyCreateNestedManyWithoutClaimerInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput> | Prisma.bountyCreateWithoutClaimerInput[] | Prisma.bountyUncheckedCreateWithoutClaimerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutClaimerInput | Prisma.bountyCreateOrConnectWithoutClaimerInput[]
  createMany?: Prisma.bountyCreateManyClaimerInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyUncheckedCreateNestedManyWithoutPlacerInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput> | Prisma.bountyCreateWithoutPlacerInput[] | Prisma.bountyUncheckedCreateWithoutPlacerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutPlacerInput | Prisma.bountyCreateOrConnectWithoutPlacerInput[]
  createMany?: Prisma.bountyCreateManyPlacerInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyUncheckedCreateNestedManyWithoutTargetInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput> | Prisma.bountyCreateWithoutTargetInput[] | Prisma.bountyUncheckedCreateWithoutTargetInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutTargetInput | Prisma.bountyCreateOrConnectWithoutTargetInput[]
  createMany?: Prisma.bountyCreateManyTargetInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyUncheckedCreateNestedManyWithoutClaimerInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput> | Prisma.bountyCreateWithoutClaimerInput[] | Prisma.bountyUncheckedCreateWithoutClaimerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutClaimerInput | Prisma.bountyCreateOrConnectWithoutClaimerInput[]
  createMany?: Prisma.bountyCreateManyClaimerInputEnvelope
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
}

export type bountyUpdateManyWithoutPlacerNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput> | Prisma.bountyCreateWithoutPlacerInput[] | Prisma.bountyUncheckedCreateWithoutPlacerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutPlacerInput | Prisma.bountyCreateOrConnectWithoutPlacerInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutPlacerInput | Prisma.bountyUpsertWithWhereUniqueWithoutPlacerInput[]
  createMany?: Prisma.bountyCreateManyPlacerInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutPlacerInput | Prisma.bountyUpdateWithWhereUniqueWithoutPlacerInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutPlacerInput | Prisma.bountyUpdateManyWithWhereWithoutPlacerInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyUpdateManyWithoutTargetNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput> | Prisma.bountyCreateWithoutTargetInput[] | Prisma.bountyUncheckedCreateWithoutTargetInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutTargetInput | Prisma.bountyCreateOrConnectWithoutTargetInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutTargetInput | Prisma.bountyUpsertWithWhereUniqueWithoutTargetInput[]
  createMany?: Prisma.bountyCreateManyTargetInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutTargetInput | Prisma.bountyUpdateWithWhereUniqueWithoutTargetInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutTargetInput | Prisma.bountyUpdateManyWithWhereWithoutTargetInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyUpdateManyWithoutClaimerNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput> | Prisma.bountyCreateWithoutClaimerInput[] | Prisma.bountyUncheckedCreateWithoutClaimerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutClaimerInput | Prisma.bountyCreateOrConnectWithoutClaimerInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutClaimerInput | Prisma.bountyUpsertWithWhereUniqueWithoutClaimerInput[]
  createMany?: Prisma.bountyCreateManyClaimerInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutClaimerInput | Prisma.bountyUpdateWithWhereUniqueWithoutClaimerInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutClaimerInput | Prisma.bountyUpdateManyWithWhereWithoutClaimerInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyUncheckedUpdateManyWithoutPlacerNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput> | Prisma.bountyCreateWithoutPlacerInput[] | Prisma.bountyUncheckedCreateWithoutPlacerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutPlacerInput | Prisma.bountyCreateOrConnectWithoutPlacerInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutPlacerInput | Prisma.bountyUpsertWithWhereUniqueWithoutPlacerInput[]
  createMany?: Prisma.bountyCreateManyPlacerInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutPlacerInput | Prisma.bountyUpdateWithWhereUniqueWithoutPlacerInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutPlacerInput | Prisma.bountyUpdateManyWithWhereWithoutPlacerInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyUncheckedUpdateManyWithoutTargetNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput> | Prisma.bountyCreateWithoutTargetInput[] | Prisma.bountyUncheckedCreateWithoutTargetInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutTargetInput | Prisma.bountyCreateOrConnectWithoutTargetInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutTargetInput | Prisma.bountyUpsertWithWhereUniqueWithoutTargetInput[]
  createMany?: Prisma.bountyCreateManyTargetInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutTargetInput | Prisma.bountyUpdateWithWhereUniqueWithoutTargetInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutTargetInput | Prisma.bountyUpdateManyWithWhereWithoutTargetInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyUncheckedUpdateManyWithoutClaimerNestedInput = {
  create?: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput> | Prisma.bountyCreateWithoutClaimerInput[] | Prisma.bountyUncheckedCreateWithoutClaimerInput[]
  connectOrCreate?: Prisma.bountyCreateOrConnectWithoutClaimerInput | Prisma.bountyCreateOrConnectWithoutClaimerInput[]
  upsert?: Prisma.bountyUpsertWithWhereUniqueWithoutClaimerInput | Prisma.bountyUpsertWithWhereUniqueWithoutClaimerInput[]
  createMany?: Prisma.bountyCreateManyClaimerInputEnvelope
  set?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  disconnect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  delete?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  connect?: Prisma.bountyWhereUniqueInput | Prisma.bountyWhereUniqueInput[]
  update?: Prisma.bountyUpdateWithWhereUniqueWithoutClaimerInput | Prisma.bountyUpdateWithWhereUniqueWithoutClaimerInput[]
  updateMany?: Prisma.bountyUpdateManyWithWhereWithoutClaimerInput | Prisma.bountyUpdateManyWithWhereWithoutClaimerInput[]
  deleteMany?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
}

export type bountyCreateWithoutPlacerInput = {
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  target?: Prisma.userCreateNestedOneWithoutTargetedBountiesInput
  claimer?: Prisma.userCreateNestedOneWithoutClaimedBountiesInput
}

export type bountyUncheckedCreateWithoutPlacerInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  targetId?: number | null
  claimedById?: number | null
}

export type bountyCreateOrConnectWithoutPlacerInput = {
  where: Prisma.bountyWhereUniqueInput
  create: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput>
}

export type bountyCreateManyPlacerInputEnvelope = {
  data: Prisma.bountyCreateManyPlacerInput | Prisma.bountyCreateManyPlacerInput[]
  skipDuplicates?: boolean
}

export type bountyCreateWithoutTargetInput = {
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placer?: Prisma.userCreateNestedOneWithoutPlacedBountiesInput
  claimer?: Prisma.userCreateNestedOneWithoutClaimedBountiesInput
}

export type bountyUncheckedCreateWithoutTargetInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  claimedById?: number | null
}

export type bountyCreateOrConnectWithoutTargetInput = {
  where: Prisma.bountyWhereUniqueInput
  create: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput>
}

export type bountyCreateManyTargetInputEnvelope = {
  data: Prisma.bountyCreateManyTargetInput | Prisma.bountyCreateManyTargetInput[]
  skipDuplicates?: boolean
}

export type bountyCreateWithoutClaimerInput = {
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placer?: Prisma.userCreateNestedOneWithoutPlacedBountiesInput
  target?: Prisma.userCreateNestedOneWithoutTargetedBountiesInput
}

export type bountyUncheckedCreateWithoutClaimerInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  targetId?: number | null
}

export type bountyCreateOrConnectWithoutClaimerInput = {
  where: Prisma.bountyWhereUniqueInput
  create: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput>
}

export type bountyCreateManyClaimerInputEnvelope = {
  data: Prisma.bountyCreateManyClaimerInput | Prisma.bountyCreateManyClaimerInput[]
  skipDuplicates?: boolean
}

export type bountyUpsertWithWhereUniqueWithoutPlacerInput = {
  where: Prisma.bountyWhereUniqueInput
  update: Prisma.XOR<Prisma.bountyUpdateWithoutPlacerInput, Prisma.bountyUncheckedUpdateWithoutPlacerInput>
  create: Prisma.XOR<Prisma.bountyCreateWithoutPlacerInput, Prisma.bountyUncheckedCreateWithoutPlacerInput>
}

export type bountyUpdateWithWhereUniqueWithoutPlacerInput = {
  where: Prisma.bountyWhereUniqueInput
  data: Prisma.XOR<Prisma.bountyUpdateWithoutPlacerInput, Prisma.bountyUncheckedUpdateWithoutPlacerInput>
}

export type bountyUpdateManyWithWhereWithoutPlacerInput = {
  where: Prisma.bountyScalarWhereInput
  data: Prisma.XOR<Prisma.bountyUpdateManyMutationInput, Prisma.bountyUncheckedUpdateManyWithoutPlacerInput>
}

export type bountyScalarWhereInput = {
  AND?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
  OR?: Prisma.bountyScalarWhereInput[]
  NOT?: Prisma.bountyScalarWhereInput | Prisma.bountyScalarWhereInput[]
  id?: Prisma.IntFilter<"bounty"> | number
  amount?: Prisma.IntFilter<"bounty"> | number
  reason?: Prisma.StringNullableFilter<"bounty"> | string | null
  active?: Prisma.BoolNullableFilter<"bounty"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bounty"> | Date | string
  placerId?: Prisma.IntNullableFilter<"bounty"> | number | null
  targetId?: Prisma.IntNullableFilter<"bounty"> | number | null
  claimedById?: Prisma.IntNullableFilter<"bounty"> | number | null
}

export type bountyUpsertWithWhereUniqueWithoutTargetInput = {
  where: Prisma.bountyWhereUniqueInput
  update: Prisma.XOR<Prisma.bountyUpdateWithoutTargetInput, Prisma.bountyUncheckedUpdateWithoutTargetInput>
  create: Prisma.XOR<Prisma.bountyCreateWithoutTargetInput, Prisma.bountyUncheckedCreateWithoutTargetInput>
}

export type bountyUpdateWithWhereUniqueWithoutTargetInput = {
  where: Prisma.bountyWhereUniqueInput
  data: Prisma.XOR<Prisma.bountyUpdateWithoutTargetInput, Prisma.bountyUncheckedUpdateWithoutTargetInput>
}

export type bountyUpdateManyWithWhereWithoutTargetInput = {
  where: Prisma.bountyScalarWhereInput
  data: Prisma.XOR<Prisma.bountyUpdateManyMutationInput, Prisma.bountyUncheckedUpdateManyWithoutTargetInput>
}

export type bountyUpsertWithWhereUniqueWithoutClaimerInput = {
  where: Prisma.bountyWhereUniqueInput
  update: Prisma.XOR<Prisma.bountyUpdateWithoutClaimerInput, Prisma.bountyUncheckedUpdateWithoutClaimerInput>
  create: Prisma.XOR<Prisma.bountyCreateWithoutClaimerInput, Prisma.bountyUncheckedCreateWithoutClaimerInput>
}

export type bountyUpdateWithWhereUniqueWithoutClaimerInput = {
  where: Prisma.bountyWhereUniqueInput
  data: Prisma.XOR<Prisma.bountyUpdateWithoutClaimerInput, Prisma.bountyUncheckedUpdateWithoutClaimerInput>
}

export type bountyUpdateManyWithWhereWithoutClaimerInput = {
  where: Prisma.bountyScalarWhereInput
  data: Prisma.XOR<Prisma.bountyUpdateManyMutationInput, Prisma.bountyUncheckedUpdateManyWithoutClaimerInput>
}

export type bountyCreateManyPlacerInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  targetId?: number | null
  claimedById?: number | null
}

export type bountyCreateManyTargetInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  claimedById?: number | null
}

export type bountyCreateManyClaimerInput = {
  id?: number
  amount: number
  reason?: string | null
  active?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  placerId?: number | null
  targetId?: number | null
}

export type bountyUpdateWithoutPlacerInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  target?: Prisma.userUpdateOneWithoutTargetedBountiesNestedInput
  claimer?: Prisma.userUpdateOneWithoutClaimedBountiesNestedInput
}

export type bountyUncheckedUpdateWithoutPlacerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyUncheckedUpdateManyWithoutPlacerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyUpdateWithoutTargetInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placer?: Prisma.userUpdateOneWithoutPlacedBountiesNestedInput
  claimer?: Prisma.userUpdateOneWithoutClaimedBountiesNestedInput
}

export type bountyUncheckedUpdateWithoutTargetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyUncheckedUpdateManyWithoutTargetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  claimedById?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyUpdateWithoutClaimerInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placer?: Prisma.userUpdateOneWithoutPlacedBountiesNestedInput
  target?: Prisma.userUpdateOneWithoutTargetedBountiesNestedInput
}

export type bountyUncheckedUpdateWithoutClaimerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bountyUncheckedUpdateManyWithoutClaimerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  reason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  active?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  placerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type bountySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  amount?: boolean
  reason?: boolean
  active?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  placerId?: boolean
  targetId?: boolean
  claimedById?: boolean
  placer?: boolean | Prisma.bounty$placerArgs<ExtArgs>
  target?: boolean | Prisma.bounty$targetArgs<ExtArgs>
  claimer?: boolean | Prisma.bounty$claimerArgs<ExtArgs>
}, ExtArgs["result"]["bounty"]>



export type bountySelectScalar = {
  id?: boolean
  amount?: boolean
  reason?: boolean
  active?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  placerId?: boolean
  targetId?: boolean
  claimedById?: boolean
}

export type bountyOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "amount" | "reason" | "active" | "createdAt" | "updatedAt" | "placerId" | "targetId" | "claimedById", ExtArgs["result"]["bounty"]>
export type bountyInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  placer?: boolean | Prisma.bounty$placerArgs<ExtArgs>
  target?: boolean | Prisma.bounty$targetArgs<ExtArgs>
  claimer?: boolean | Prisma.bounty$claimerArgs<ExtArgs>
}

export type $bountyPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "bounty"
  objects: {
    placer: Prisma.$userPayload<ExtArgs> | null
    target: Prisma.$userPayload<ExtArgs> | null
    claimer: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    amount: number
    reason: string | null
    active: boolean | null
    createdAt: Date
    updatedAt: Date
    placerId: number | null
    targetId: number | null
    claimedById: number | null
  }, ExtArgs["result"]["bounty"]>
  composites: {}
}

export type bountyGetPayload<S extends boolean | null | undefined | bountyDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$bountyPayload, S>

export type bountyCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<bountyFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: BountyCountAggregateInputType | true
  }

export interface bountyDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['bounty'], meta: { name: 'bounty' } }
  /**
   * Find zero or one Bounty that matches the filter.
   * @param {bountyFindUniqueArgs} args - Arguments to find a Bounty
   * @example
   * // Get one Bounty
   * const bounty = await prisma.bounty.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends bountyFindUniqueArgs>(args: Prisma.SelectSubset<T, bountyFindUniqueArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Bounty that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {bountyFindUniqueOrThrowArgs} args - Arguments to find a Bounty
   * @example
   * // Get one Bounty
   * const bounty = await prisma.bounty.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends bountyFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, bountyFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Bounty that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyFindFirstArgs} args - Arguments to find a Bounty
   * @example
   * // Get one Bounty
   * const bounty = await prisma.bounty.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends bountyFindFirstArgs>(args?: Prisma.SelectSubset<T, bountyFindFirstArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Bounty that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyFindFirstOrThrowArgs} args - Arguments to find a Bounty
   * @example
   * // Get one Bounty
   * const bounty = await prisma.bounty.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends bountyFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, bountyFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Bounties that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Bounties
   * const bounties = await prisma.bounty.findMany()
   * 
   * // Get first 10 Bounties
   * const bounties = await prisma.bounty.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const bountyWithIdOnly = await prisma.bounty.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends bountyFindManyArgs>(args?: Prisma.SelectSubset<T, bountyFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Bounty.
   * @param {bountyCreateArgs} args - Arguments to create a Bounty.
   * @example
   * // Create one Bounty
   * const Bounty = await prisma.bounty.create({
   *   data: {
   *     // ... data to create a Bounty
   *   }
   * })
   * 
   */
  create<T extends bountyCreateArgs>(args: Prisma.SelectSubset<T, bountyCreateArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Bounties.
   * @param {bountyCreateManyArgs} args - Arguments to create many Bounties.
   * @example
   * // Create many Bounties
   * const bounty = await prisma.bounty.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends bountyCreateManyArgs>(args?: Prisma.SelectSubset<T, bountyCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Bounty.
   * @param {bountyDeleteArgs} args - Arguments to delete one Bounty.
   * @example
   * // Delete one Bounty
   * const Bounty = await prisma.bounty.delete({
   *   where: {
   *     // ... filter to delete one Bounty
   *   }
   * })
   * 
   */
  delete<T extends bountyDeleteArgs>(args: Prisma.SelectSubset<T, bountyDeleteArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Bounty.
   * @param {bountyUpdateArgs} args - Arguments to update one Bounty.
   * @example
   * // Update one Bounty
   * const bounty = await prisma.bounty.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends bountyUpdateArgs>(args: Prisma.SelectSubset<T, bountyUpdateArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Bounties.
   * @param {bountyDeleteManyArgs} args - Arguments to filter Bounties to delete.
   * @example
   * // Delete a few Bounties
   * const { count } = await prisma.bounty.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends bountyDeleteManyArgs>(args?: Prisma.SelectSubset<T, bountyDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Bounties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Bounties
   * const bounty = await prisma.bounty.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends bountyUpdateManyArgs>(args: Prisma.SelectSubset<T, bountyUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Bounty.
   * @param {bountyUpsertArgs} args - Arguments to update or create a Bounty.
   * @example
   * // Update or create a Bounty
   * const bounty = await prisma.bounty.upsert({
   *   create: {
   *     // ... data to create a Bounty
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Bounty we want to update
   *   }
   * })
   */
  upsert<T extends bountyUpsertArgs>(args: Prisma.SelectSubset<T, bountyUpsertArgs<ExtArgs>>): Prisma.Prisma__bountyClient<runtime.Types.Result.GetResult<Prisma.$bountyPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Bounties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyCountArgs} args - Arguments to filter Bounties to count.
   * @example
   * // Count the number of Bounties
   * const count = await prisma.bounty.count({
   *   where: {
   *     // ... the filter for the Bounties we want to count
   *   }
   * })
  **/
  count<T extends bountyCountArgs>(
    args?: Prisma.Subset<T, bountyCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], BountyCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Bounty.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {BountyAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends BountyAggregateArgs>(args: Prisma.Subset<T, BountyAggregateArgs>): Prisma.PrismaPromise<GetBountyAggregateType<T>>

  /**
   * Group by Bounty.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bountyGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends bountyGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: bountyGroupByArgs['orderBy'] }
      : { orderBy?: bountyGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, bountyGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetBountyGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the bounty model
 */
readonly fields: bountyFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for bounty.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__bountyClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  placer<T extends Prisma.bounty$placerArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bounty$placerArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  target<T extends Prisma.bounty$targetArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bounty$targetArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  claimer<T extends Prisma.bounty$claimerArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bounty$claimerArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the bounty model
 */
export interface bountyFieldRefs {
  readonly id: Prisma.FieldRef<"bounty", 'Int'>
  readonly amount: Prisma.FieldRef<"bounty", 'Int'>
  readonly reason: Prisma.FieldRef<"bounty", 'String'>
  readonly active: Prisma.FieldRef<"bounty", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"bounty", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"bounty", 'DateTime'>
  readonly placerId: Prisma.FieldRef<"bounty", 'Int'>
  readonly targetId: Prisma.FieldRef<"bounty", 'Int'>
  readonly claimedById: Prisma.FieldRef<"bounty", 'Int'>
}
    

// Custom InputTypes
/**
 * bounty findUnique
 */
export type bountyFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter, which bounty to fetch.
   */
  where: Prisma.bountyWhereUniqueInput
}

/**
 * bounty findUniqueOrThrow
 */
export type bountyFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter, which bounty to fetch.
   */
  where: Prisma.bountyWhereUniqueInput
}

/**
 * bounty findFirst
 */
export type bountyFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter, which bounty to fetch.
   */
  where?: Prisma.bountyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bounties to fetch.
   */
  orderBy?: Prisma.bountyOrderByWithRelationInput | Prisma.bountyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for bounties.
   */
  cursor?: Prisma.bountyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bounties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bounties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of bounties.
   */
  distinct?: Prisma.BountyScalarFieldEnum | Prisma.BountyScalarFieldEnum[]
}

/**
 * bounty findFirstOrThrow
 */
export type bountyFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter, which bounty to fetch.
   */
  where?: Prisma.bountyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bounties to fetch.
   */
  orderBy?: Prisma.bountyOrderByWithRelationInput | Prisma.bountyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for bounties.
   */
  cursor?: Prisma.bountyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bounties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bounties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of bounties.
   */
  distinct?: Prisma.BountyScalarFieldEnum | Prisma.BountyScalarFieldEnum[]
}

/**
 * bounty findMany
 */
export type bountyFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter, which bounties to fetch.
   */
  where?: Prisma.bountyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bounties to fetch.
   */
  orderBy?: Prisma.bountyOrderByWithRelationInput | Prisma.bountyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing bounties.
   */
  cursor?: Prisma.bountyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bounties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bounties.
   */
  skip?: number
  distinct?: Prisma.BountyScalarFieldEnum | Prisma.BountyScalarFieldEnum[]
}

/**
 * bounty create
 */
export type bountyCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * The data needed to create a bounty.
   */
  data: Prisma.XOR<Prisma.bountyCreateInput, Prisma.bountyUncheckedCreateInput>
}

/**
 * bounty createMany
 */
export type bountyCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many bounties.
   */
  data: Prisma.bountyCreateManyInput | Prisma.bountyCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * bounty update
 */
export type bountyUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * The data needed to update a bounty.
   */
  data: Prisma.XOR<Prisma.bountyUpdateInput, Prisma.bountyUncheckedUpdateInput>
  /**
   * Choose, which bounty to update.
   */
  where: Prisma.bountyWhereUniqueInput
}

/**
 * bounty updateMany
 */
export type bountyUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update bounties.
   */
  data: Prisma.XOR<Prisma.bountyUpdateManyMutationInput, Prisma.bountyUncheckedUpdateManyInput>
  /**
   * Filter which bounties to update
   */
  where?: Prisma.bountyWhereInput
  /**
   * Limit how many bounties to update.
   */
  limit?: number
}

/**
 * bounty upsert
 */
export type bountyUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * The filter to search for the bounty to update in case it exists.
   */
  where: Prisma.bountyWhereUniqueInput
  /**
   * In case the bounty found by the `where` argument doesn't exist, create a new bounty with this data.
   */
  create: Prisma.XOR<Prisma.bountyCreateInput, Prisma.bountyUncheckedCreateInput>
  /**
   * In case the bounty was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.bountyUpdateInput, Prisma.bountyUncheckedUpdateInput>
}

/**
 * bounty delete
 */
export type bountyDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
  /**
   * Filter which bounty to delete.
   */
  where: Prisma.bountyWhereUniqueInput
}

/**
 * bounty deleteMany
 */
export type bountyDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which bounties to delete
   */
  where?: Prisma.bountyWhereInput
  /**
   * Limit how many bounties to delete.
   */
  limit?: number
}

/**
 * bounty.placer
 */
export type bounty$placerArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bounty.target
 */
export type bounty$targetArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bounty.claimer
 */
export type bounty$claimerArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bounty without action
 */
export type bountyDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bounty
   */
  select?: Prisma.bountySelect<ExtArgs> | null
  /**
   * Omit specific fields from the bounty
   */
  omit?: Prisma.bountyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bountyInclude<ExtArgs> | null
}
