
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_pet` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_pet
 * 
 */
export type user_petModel = runtime.Types.Result.DefaultSelection<Prisma.$user_petPayload>

export type AggregateUser_pet = {
  _count: User_petCountAggregateOutputType | null
  _avg: User_petAvgAggregateOutputType | null
  _sum: User_petSumAggregateOutputType | null
  _min: User_petMinAggregateOutputType | null
  _max: User_petMaxAggregateOutputType | null
}

export type User_petAvgAggregateOutputType = {
  id: number | null
  level: number | null
  xp: number | null
  nextLevelXp: number | null
  happiness: number | null
  energy: number | null
  userId: number | null
  petId: number | null
}

export type User_petSumAggregateOutputType = {
  id: number | null
  level: number | null
  xp: number | null
  nextLevelXp: number | null
  happiness: number | null
  energy: number | null
  userId: number | null
  petId: number | null
}

export type User_petMinAggregateOutputType = {
  id: number | null
  name: string | null
  level: number | null
  isActive: boolean | null
  xp: number | null
  nextLevelXp: number | null
  happiness: number | null
  energy: number | null
  userId: number | null
  petId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type User_petMaxAggregateOutputType = {
  id: number | null
  name: string | null
  level: number | null
  isActive: boolean | null
  xp: number | null
  nextLevelXp: number | null
  happiness: number | null
  energy: number | null
  userId: number | null
  petId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type User_petCountAggregateOutputType = {
  id: number
  name: number
  level: number
  isActive: number
  xp: number
  nextLevelXp: number
  happiness: number
  energy: number
  evolution: number
  userId: number
  petId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type User_petAvgAggregateInputType = {
  id?: true
  level?: true
  xp?: true
  nextLevelXp?: true
  happiness?: true
  energy?: true
  userId?: true
  petId?: true
}

export type User_petSumAggregateInputType = {
  id?: true
  level?: true
  xp?: true
  nextLevelXp?: true
  happiness?: true
  energy?: true
  userId?: true
  petId?: true
}

export type User_petMinAggregateInputType = {
  id?: true
  name?: true
  level?: true
  isActive?: true
  xp?: true
  nextLevelXp?: true
  happiness?: true
  energy?: true
  userId?: true
  petId?: true
  createdAt?: true
  updatedAt?: true
}

export type User_petMaxAggregateInputType = {
  id?: true
  name?: true
  level?: true
  isActive?: true
  xp?: true
  nextLevelXp?: true
  happiness?: true
  energy?: true
  userId?: true
  petId?: true
  createdAt?: true
  updatedAt?: true
}

export type User_petCountAggregateInputType = {
  id?: true
  name?: true
  level?: true
  isActive?: true
  xp?: true
  nextLevelXp?: true
  happiness?: true
  energy?: true
  evolution?: true
  userId?: true
  petId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type User_petAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_pet to aggregate.
   */
  where?: Prisma.user_petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_pets to fetch.
   */
  orderBy?: Prisma.user_petOrderByWithRelationInput | Prisma.user_petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_pets
  **/
  _count?: true | User_petCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_petAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_petSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_petMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_petMaxAggregateInputType
}

export type GetUser_petAggregateType<T extends User_petAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_pet]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_pet[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_pet[P]>
}




export type user_petGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_petWhereInput
  orderBy?: Prisma.user_petOrderByWithAggregationInput | Prisma.user_petOrderByWithAggregationInput[]
  by: Prisma.User_petScalarFieldEnum[] | Prisma.User_petScalarFieldEnum
  having?: Prisma.user_petScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_petCountAggregateInputType | true
  _avg?: User_petAvgAggregateInputType
  _sum?: User_petSumAggregateInputType
  _min?: User_petMinAggregateInputType
  _max?: User_petMaxAggregateInputType
}

export type User_petGroupByOutputType = {
  id: number
  name: string
  level: number
  isActive: boolean
  xp: number
  nextLevelXp: number
  happiness: number
  energy: number
  evolution: runtime.JsonValue
  userId: number
  petId: number
  createdAt: Date
  updatedAt: Date
  _count: User_petCountAggregateOutputType | null
  _avg: User_petAvgAggregateOutputType | null
  _sum: User_petSumAggregateOutputType | null
  _min: User_petMinAggregateOutputType | null
  _max: User_petMaxAggregateOutputType | null
}

type GetUser_petGroupByPayload<T extends user_petGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_petGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_petGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_petGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_petGroupByOutputType[P]>
      }
    >
  >



export type user_petWhereInput = {
  AND?: Prisma.user_petWhereInput | Prisma.user_petWhereInput[]
  OR?: Prisma.user_petWhereInput[]
  NOT?: Prisma.user_petWhereInput | Prisma.user_petWhereInput[]
  id?: Prisma.IntFilter<"user_pet"> | number
  name?: Prisma.StringFilter<"user_pet"> | string
  level?: Prisma.IntFilter<"user_pet"> | number
  isActive?: Prisma.BoolFilter<"user_pet"> | boolean
  xp?: Prisma.IntFilter<"user_pet"> | number
  nextLevelXp?: Prisma.IntFilter<"user_pet"> | number
  happiness?: Prisma.IntFilter<"user_pet"> | number
  energy?: Prisma.IntFilter<"user_pet"> | number
  evolution?: Prisma.JsonFilter<"user_pet">
  userId?: Prisma.IntFilter<"user_pet"> | number
  petId?: Prisma.IntFilter<"user_pet"> | number
  createdAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  pet?: Prisma.XOR<Prisma.PetScalarRelationFilter, Prisma.petWhereInput>
}

export type user_petOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  level?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  evolution?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  pet?: Prisma.petOrderByWithRelationInput
  _relevance?: Prisma.user_petOrderByRelevanceInput
}

export type user_petWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.user_petWhereInput | Prisma.user_petWhereInput[]
  OR?: Prisma.user_petWhereInput[]
  NOT?: Prisma.user_petWhereInput | Prisma.user_petWhereInput[]
  name?: Prisma.StringFilter<"user_pet"> | string
  level?: Prisma.IntFilter<"user_pet"> | number
  isActive?: Prisma.BoolFilter<"user_pet"> | boolean
  xp?: Prisma.IntFilter<"user_pet"> | number
  nextLevelXp?: Prisma.IntFilter<"user_pet"> | number
  happiness?: Prisma.IntFilter<"user_pet"> | number
  energy?: Prisma.IntFilter<"user_pet"> | number
  evolution?: Prisma.JsonFilter<"user_pet">
  userId?: Prisma.IntFilter<"user_pet"> | number
  petId?: Prisma.IntFilter<"user_pet"> | number
  createdAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  pet?: Prisma.XOR<Prisma.PetScalarRelationFilter, Prisma.petWhereInput>
}, "id">

export type user_petOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  level?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  evolution?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.user_petCountOrderByAggregateInput
  _avg?: Prisma.user_petAvgOrderByAggregateInput
  _max?: Prisma.user_petMaxOrderByAggregateInput
  _min?: Prisma.user_petMinOrderByAggregateInput
  _sum?: Prisma.user_petSumOrderByAggregateInput
}

export type user_petScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_petScalarWhereWithAggregatesInput | Prisma.user_petScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_petScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_petScalarWhereWithAggregatesInput | Prisma.user_petScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  name?: Prisma.StringWithAggregatesFilter<"user_pet"> | string
  level?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  isActive?: Prisma.BoolWithAggregatesFilter<"user_pet"> | boolean
  xp?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  nextLevelXp?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  happiness?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  energy?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  evolution?: Prisma.JsonWithAggregatesFilter<"user_pet">
  userId?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  petId?: Prisma.IntWithAggregatesFilter<"user_pet"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_pet"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_pet"> | Date | string
}

export type user_petCreateInput = {
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_petInput
  pet: Prisma.petCreateNestedOneWithoutUser_petInput
}

export type user_petUncheckedCreateInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  userId: number
  petId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_petNestedInput
  pet?: Prisma.petUpdateOneRequiredWithoutUser_petNestedInput
}

export type user_petUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  petId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_petCreateManyInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  userId: number
  petId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_petUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  petId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type User_petListRelationFilter = {
  every?: Prisma.user_petWhereInput
  some?: Prisma.user_petWhereInput
  none?: Prisma.user_petWhereInput
}

export type user_petOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_petOrderByRelevanceInput = {
  fields: Prisma.user_petOrderByRelevanceFieldEnum | Prisma.user_petOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type user_petCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  level?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  evolution?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type user_petAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
}

export type user_petMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  level?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type user_petMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  level?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type user_petSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  xp?: Prisma.SortOrder
  nextLevelXp?: Prisma.SortOrder
  happiness?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  petId?: Prisma.SortOrder
}

export type user_petCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput> | Prisma.user_petCreateWithoutUserInput[] | Prisma.user_petUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutUserInput | Prisma.user_petCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_petCreateManyUserInputEnvelope
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
}

export type user_petUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput> | Prisma.user_petCreateWithoutUserInput[] | Prisma.user_petUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutUserInput | Prisma.user_petCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_petCreateManyUserInputEnvelope
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
}

export type user_petUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput> | Prisma.user_petCreateWithoutUserInput[] | Prisma.user_petUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutUserInput | Prisma.user_petCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_petUpsertWithWhereUniqueWithoutUserInput | Prisma.user_petUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_petCreateManyUserInputEnvelope
  set?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  disconnect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  delete?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  update?: Prisma.user_petUpdateWithWhereUniqueWithoutUserInput | Prisma.user_petUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_petUpdateManyWithWhereWithoutUserInput | Prisma.user_petUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
}

export type user_petUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput> | Prisma.user_petCreateWithoutUserInput[] | Prisma.user_petUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutUserInput | Prisma.user_petCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_petUpsertWithWhereUniqueWithoutUserInput | Prisma.user_petUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_petCreateManyUserInputEnvelope
  set?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  disconnect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  delete?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  update?: Prisma.user_petUpdateWithWhereUniqueWithoutUserInput | Prisma.user_petUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_petUpdateManyWithWhereWithoutUserInput | Prisma.user_petUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
}

export type user_petCreateNestedManyWithoutPetInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput> | Prisma.user_petCreateWithoutPetInput[] | Prisma.user_petUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutPetInput | Prisma.user_petCreateOrConnectWithoutPetInput[]
  createMany?: Prisma.user_petCreateManyPetInputEnvelope
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
}

export type user_petUncheckedCreateNestedManyWithoutPetInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput> | Prisma.user_petCreateWithoutPetInput[] | Prisma.user_petUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutPetInput | Prisma.user_petCreateOrConnectWithoutPetInput[]
  createMany?: Prisma.user_petCreateManyPetInputEnvelope
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
}

export type user_petUpdateManyWithoutPetNestedInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput> | Prisma.user_petCreateWithoutPetInput[] | Prisma.user_petUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutPetInput | Prisma.user_petCreateOrConnectWithoutPetInput[]
  upsert?: Prisma.user_petUpsertWithWhereUniqueWithoutPetInput | Prisma.user_petUpsertWithWhereUniqueWithoutPetInput[]
  createMany?: Prisma.user_petCreateManyPetInputEnvelope
  set?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  disconnect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  delete?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  update?: Prisma.user_petUpdateWithWhereUniqueWithoutPetInput | Prisma.user_petUpdateWithWhereUniqueWithoutPetInput[]
  updateMany?: Prisma.user_petUpdateManyWithWhereWithoutPetInput | Prisma.user_petUpdateManyWithWhereWithoutPetInput[]
  deleteMany?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
}

export type user_petUncheckedUpdateManyWithoutPetNestedInput = {
  create?: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput> | Prisma.user_petCreateWithoutPetInput[] | Prisma.user_petUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.user_petCreateOrConnectWithoutPetInput | Prisma.user_petCreateOrConnectWithoutPetInput[]
  upsert?: Prisma.user_petUpsertWithWhereUniqueWithoutPetInput | Prisma.user_petUpsertWithWhereUniqueWithoutPetInput[]
  createMany?: Prisma.user_petCreateManyPetInputEnvelope
  set?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  disconnect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  delete?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  connect?: Prisma.user_petWhereUniqueInput | Prisma.user_petWhereUniqueInput[]
  update?: Prisma.user_petUpdateWithWhereUniqueWithoutPetInput | Prisma.user_petUpdateWithWhereUniqueWithoutPetInput[]
  updateMany?: Prisma.user_petUpdateManyWithWhereWithoutPetInput | Prisma.user_petUpdateManyWithWhereWithoutPetInput[]
  deleteMany?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
}

export type user_petCreateWithoutUserInput = {
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  createdAt?: Date | string
  updatedAt?: Date | string
  pet: Prisma.petCreateNestedOneWithoutUser_petInput
}

export type user_petUncheckedCreateWithoutUserInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  petId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petCreateOrConnectWithoutUserInput = {
  where: Prisma.user_petWhereUniqueInput
  create: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput>
}

export type user_petCreateManyUserInputEnvelope = {
  data: Prisma.user_petCreateManyUserInput | Prisma.user_petCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_petUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_petWhereUniqueInput
  update: Prisma.XOR<Prisma.user_petUpdateWithoutUserInput, Prisma.user_petUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_petCreateWithoutUserInput, Prisma.user_petUncheckedCreateWithoutUserInput>
}

export type user_petUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_petWhereUniqueInput
  data: Prisma.XOR<Prisma.user_petUpdateWithoutUserInput, Prisma.user_petUncheckedUpdateWithoutUserInput>
}

export type user_petUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_petScalarWhereInput
  data: Prisma.XOR<Prisma.user_petUpdateManyMutationInput, Prisma.user_petUncheckedUpdateManyWithoutUserInput>
}

export type user_petScalarWhereInput = {
  AND?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
  OR?: Prisma.user_petScalarWhereInput[]
  NOT?: Prisma.user_petScalarWhereInput | Prisma.user_petScalarWhereInput[]
  id?: Prisma.IntFilter<"user_pet"> | number
  name?: Prisma.StringFilter<"user_pet"> | string
  level?: Prisma.IntFilter<"user_pet"> | number
  isActive?: Prisma.BoolFilter<"user_pet"> | boolean
  xp?: Prisma.IntFilter<"user_pet"> | number
  nextLevelXp?: Prisma.IntFilter<"user_pet"> | number
  happiness?: Prisma.IntFilter<"user_pet"> | number
  energy?: Prisma.IntFilter<"user_pet"> | number
  evolution?: Prisma.JsonFilter<"user_pet">
  userId?: Prisma.IntFilter<"user_pet"> | number
  petId?: Prisma.IntFilter<"user_pet"> | number
  createdAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_pet"> | Date | string
}

export type user_petCreateWithoutPetInput = {
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_petInput
}

export type user_petUncheckedCreateWithoutPetInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petCreateOrConnectWithoutPetInput = {
  where: Prisma.user_petWhereUniqueInput
  create: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput>
}

export type user_petCreateManyPetInputEnvelope = {
  data: Prisma.user_petCreateManyPetInput | Prisma.user_petCreateManyPetInput[]
  skipDuplicates?: boolean
}

export type user_petUpsertWithWhereUniqueWithoutPetInput = {
  where: Prisma.user_petWhereUniqueInput
  update: Prisma.XOR<Prisma.user_petUpdateWithoutPetInput, Prisma.user_petUncheckedUpdateWithoutPetInput>
  create: Prisma.XOR<Prisma.user_petCreateWithoutPetInput, Prisma.user_petUncheckedCreateWithoutPetInput>
}

export type user_petUpdateWithWhereUniqueWithoutPetInput = {
  where: Prisma.user_petWhereUniqueInput
  data: Prisma.XOR<Prisma.user_petUpdateWithoutPetInput, Prisma.user_petUncheckedUpdateWithoutPetInput>
}

export type user_petUpdateManyWithWhereWithoutPetInput = {
  where: Prisma.user_petScalarWhereInput
  data: Prisma.XOR<Prisma.user_petUpdateManyMutationInput, Prisma.user_petUncheckedUpdateManyWithoutPetInput>
}

export type user_petCreateManyUserInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  petId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petUpdateWithoutUserInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  pet?: Prisma.petUpdateOneRequiredWithoutUser_petNestedInput
}

export type user_petUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  petId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_petUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  petId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_petCreateManyPetInput = {
  id?: number
  name: string
  level?: number
  isActive?: boolean
  xp?: number
  nextLevelXp?: number
  happiness?: number
  energy?: number
  evolution?:PrismaJson.Evolution
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_petUpdateWithoutPetInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_petNestedInput
}

export type user_petUncheckedUpdateWithoutPetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_petUncheckedUpdateManyWithoutPetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  level?: Prisma.IntFieldUpdateOperationsInput | number
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  xp?: Prisma.IntFieldUpdateOperationsInput | number
  nextLevelXp?: Prisma.IntFieldUpdateOperationsInput | number
  happiness?: Prisma.IntFieldUpdateOperationsInput | number
  energy?: Prisma.IntFieldUpdateOperationsInput | number
  evolution?:PrismaJson.Evolution
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type user_petSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  level?: boolean
  isActive?: boolean
  xp?: boolean
  nextLevelXp?: boolean
  happiness?: boolean
  energy?: boolean
  evolution?: boolean
  userId?: boolean
  petId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  pet?: boolean | Prisma.petDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_pet"]>



export type user_petSelectScalar = {
  id?: boolean
  name?: boolean
  level?: boolean
  isActive?: boolean
  xp?: boolean
  nextLevelXp?: boolean
  happiness?: boolean
  energy?: boolean
  evolution?: boolean
  userId?: boolean
  petId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type user_petOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "level" | "isActive" | "xp" | "nextLevelXp" | "happiness" | "energy" | "evolution" | "userId" | "petId" | "createdAt" | "updatedAt", ExtArgs["result"]["user_pet"]>
export type user_petInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  pet?: boolean | Prisma.petDefaultArgs<ExtArgs>
}

export type $user_petPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_pet"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    pet: Prisma.$petPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    level: number
    isActive: boolean
    xp: number
    nextLevelXp: number
    happiness: number
    energy: number
    /**
     * [Evolution]
     */
    evolution:PrismaJson.Evolution
    userId: number
    petId: number
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["user_pet"]>
  composites: {}
}

export type user_petGetPayload<S extends boolean | null | undefined | user_petDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_petPayload, S>

export type user_petCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_petFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_petCountAggregateInputType | true
  }

export interface user_petDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_pet'], meta: { name: 'user_pet' } }
  /**
   * Find zero or one User_pet that matches the filter.
   * @param {user_petFindUniqueArgs} args - Arguments to find a User_pet
   * @example
   * // Get one User_pet
   * const user_pet = await prisma.user_pet.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_petFindUniqueArgs>(args: Prisma.SelectSubset<T, user_petFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_pet that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_petFindUniqueOrThrowArgs} args - Arguments to find a User_pet
   * @example
   * // Get one User_pet
   * const user_pet = await prisma.user_pet.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_petFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_petFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_pet that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petFindFirstArgs} args - Arguments to find a User_pet
   * @example
   * // Get one User_pet
   * const user_pet = await prisma.user_pet.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_petFindFirstArgs>(args?: Prisma.SelectSubset<T, user_petFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_pet that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petFindFirstOrThrowArgs} args - Arguments to find a User_pet
   * @example
   * // Get one User_pet
   * const user_pet = await prisma.user_pet.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_petFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_petFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_pets that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_pets
   * const user_pets = await prisma.user_pet.findMany()
   * 
   * // Get first 10 User_pets
   * const user_pets = await prisma.user_pet.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_petWithIdOnly = await prisma.user_pet.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_petFindManyArgs>(args?: Prisma.SelectSubset<T, user_petFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_pet.
   * @param {user_petCreateArgs} args - Arguments to create a User_pet.
   * @example
   * // Create one User_pet
   * const User_pet = await prisma.user_pet.create({
   *   data: {
   *     // ... data to create a User_pet
   *   }
   * })
   * 
   */
  create<T extends user_petCreateArgs>(args: Prisma.SelectSubset<T, user_petCreateArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_pets.
   * @param {user_petCreateManyArgs} args - Arguments to create many User_pets.
   * @example
   * // Create many User_pets
   * const user_pet = await prisma.user_pet.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_petCreateManyArgs>(args?: Prisma.SelectSubset<T, user_petCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_pet.
   * @param {user_petDeleteArgs} args - Arguments to delete one User_pet.
   * @example
   * // Delete one User_pet
   * const User_pet = await prisma.user_pet.delete({
   *   where: {
   *     // ... filter to delete one User_pet
   *   }
   * })
   * 
   */
  delete<T extends user_petDeleteArgs>(args: Prisma.SelectSubset<T, user_petDeleteArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_pet.
   * @param {user_petUpdateArgs} args - Arguments to update one User_pet.
   * @example
   * // Update one User_pet
   * const user_pet = await prisma.user_pet.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_petUpdateArgs>(args: Prisma.SelectSubset<T, user_petUpdateArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_pets.
   * @param {user_petDeleteManyArgs} args - Arguments to filter User_pets to delete.
   * @example
   * // Delete a few User_pets
   * const { count } = await prisma.user_pet.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_petDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_petDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_pets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_pets
   * const user_pet = await prisma.user_pet.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_petUpdateManyArgs>(args: Prisma.SelectSubset<T, user_petUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_pet.
   * @param {user_petUpsertArgs} args - Arguments to update or create a User_pet.
   * @example
   * // Update or create a User_pet
   * const user_pet = await prisma.user_pet.upsert({
   *   create: {
   *     // ... data to create a User_pet
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_pet we want to update
   *   }
   * })
   */
  upsert<T extends user_petUpsertArgs>(args: Prisma.SelectSubset<T, user_petUpsertArgs<ExtArgs>>): Prisma.Prisma__user_petClient<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_pets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petCountArgs} args - Arguments to filter User_pets to count.
   * @example
   * // Count the number of User_pets
   * const count = await prisma.user_pet.count({
   *   where: {
   *     // ... the filter for the User_pets we want to count
   *   }
   * })
  **/
  count<T extends user_petCountArgs>(
    args?: Prisma.Subset<T, user_petCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_petCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_pet.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_petAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_petAggregateArgs>(args: Prisma.Subset<T, User_petAggregateArgs>): Prisma.PrismaPromise<GetUser_petAggregateType<T>>

  /**
   * Group by User_pet.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_petGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_petGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_petGroupByArgs['orderBy'] }
      : { orderBy?: user_petGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_petGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_petGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_pet model
 */
readonly fields: user_petFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_pet.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_petClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  pet<T extends Prisma.petDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.petDefaultArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_pet model
 */
export interface user_petFieldRefs {
  readonly id: Prisma.FieldRef<"user_pet", 'Int'>
  readonly name: Prisma.FieldRef<"user_pet", 'String'>
  readonly level: Prisma.FieldRef<"user_pet", 'Int'>
  readonly isActive: Prisma.FieldRef<"user_pet", 'Boolean'>
  readonly xp: Prisma.FieldRef<"user_pet", 'Int'>
  readonly nextLevelXp: Prisma.FieldRef<"user_pet", 'Int'>
  readonly happiness: Prisma.FieldRef<"user_pet", 'Int'>
  readonly energy: Prisma.FieldRef<"user_pet", 'Int'>
  readonly evolution: Prisma.FieldRef<"user_pet", 'Json'>
  readonly userId: Prisma.FieldRef<"user_pet", 'Int'>
  readonly petId: Prisma.FieldRef<"user_pet", 'Int'>
  readonly createdAt: Prisma.FieldRef<"user_pet", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_pet", 'DateTime'>
}
    

// Custom InputTypes
/**
 * user_pet findUnique
 */
export type user_petFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter, which user_pet to fetch.
   */
  where: Prisma.user_petWhereUniqueInput
}

/**
 * user_pet findUniqueOrThrow
 */
export type user_petFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter, which user_pet to fetch.
   */
  where: Prisma.user_petWhereUniqueInput
}

/**
 * user_pet findFirst
 */
export type user_petFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter, which user_pet to fetch.
   */
  where?: Prisma.user_petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_pets to fetch.
   */
  orderBy?: Prisma.user_petOrderByWithRelationInput | Prisma.user_petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_pets.
   */
  cursor?: Prisma.user_petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_pets.
   */
  distinct?: Prisma.User_petScalarFieldEnum | Prisma.User_petScalarFieldEnum[]
}

/**
 * user_pet findFirstOrThrow
 */
export type user_petFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter, which user_pet to fetch.
   */
  where?: Prisma.user_petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_pets to fetch.
   */
  orderBy?: Prisma.user_petOrderByWithRelationInput | Prisma.user_petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_pets.
   */
  cursor?: Prisma.user_petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_pets.
   */
  distinct?: Prisma.User_petScalarFieldEnum | Prisma.User_petScalarFieldEnum[]
}

/**
 * user_pet findMany
 */
export type user_petFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter, which user_pets to fetch.
   */
  where?: Prisma.user_petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_pets to fetch.
   */
  orderBy?: Prisma.user_petOrderByWithRelationInput | Prisma.user_petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_pets.
   */
  cursor?: Prisma.user_petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_pets.
   */
  skip?: number
  distinct?: Prisma.User_petScalarFieldEnum | Prisma.User_petScalarFieldEnum[]
}

/**
 * user_pet create
 */
export type user_petCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * The data needed to create a user_pet.
   */
  data: Prisma.XOR<Prisma.user_petCreateInput, Prisma.user_petUncheckedCreateInput>
}

/**
 * user_pet createMany
 */
export type user_petCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_pets.
   */
  data: Prisma.user_petCreateManyInput | Prisma.user_petCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_pet update
 */
export type user_petUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * The data needed to update a user_pet.
   */
  data: Prisma.XOR<Prisma.user_petUpdateInput, Prisma.user_petUncheckedUpdateInput>
  /**
   * Choose, which user_pet to update.
   */
  where: Prisma.user_petWhereUniqueInput
}

/**
 * user_pet updateMany
 */
export type user_petUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_pets.
   */
  data: Prisma.XOR<Prisma.user_petUpdateManyMutationInput, Prisma.user_petUncheckedUpdateManyInput>
  /**
   * Filter which user_pets to update
   */
  where?: Prisma.user_petWhereInput
  /**
   * Limit how many user_pets to update.
   */
  limit?: number
}

/**
 * user_pet upsert
 */
export type user_petUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * The filter to search for the user_pet to update in case it exists.
   */
  where: Prisma.user_petWhereUniqueInput
  /**
   * In case the user_pet found by the `where` argument doesn't exist, create a new user_pet with this data.
   */
  create: Prisma.XOR<Prisma.user_petCreateInput, Prisma.user_petUncheckedCreateInput>
  /**
   * In case the user_pet was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_petUpdateInput, Prisma.user_petUncheckedUpdateInput>
}

/**
 * user_pet delete
 */
export type user_petDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  /**
   * Filter which user_pet to delete.
   */
  where: Prisma.user_petWhereUniqueInput
}

/**
 * user_pet deleteMany
 */
export type user_petDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_pets to delete
   */
  where?: Prisma.user_petWhereInput
  /**
   * Limit how many user_pets to delete.
   */
  limit?: number
}

/**
 * user_pet without action
 */
export type user_petDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
}
