
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `push_token` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model push_token
 * 
 */
export type push_tokenModel = runtime.Types.Result.DefaultSelection<Prisma.$push_tokenPayload>

export type AggregatePush_token = {
  _count: Push_tokenCountAggregateOutputType | null
  _avg: Push_tokenAvgAggregateOutputType | null
  _sum: Push_tokenSumAggregateOutputType | null
  _min: Push_tokenMinAggregateOutputType | null
  _max: Push_tokenMaxAggregateOutputType | null
}

export type Push_tokenAvgAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type Push_tokenSumAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type Push_tokenMinAggregateOutputType = {
  id: number | null
  token: string | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type Push_tokenMaxAggregateOutputType = {
  id: number | null
  token: string | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type Push_tokenCountAggregateOutputType = {
  id: number
  token: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type Push_tokenAvgAggregateInputType = {
  id?: true
  userId?: true
}

export type Push_tokenSumAggregateInputType = {
  id?: true
  userId?: true
}

export type Push_tokenMinAggregateInputType = {
  id?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type Push_tokenMaxAggregateInputType = {
  id?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type Push_tokenCountAggregateInputType = {
  id?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type Push_tokenAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which push_token to aggregate.
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of push_tokens to fetch.
   */
  orderBy?: Prisma.push_tokenOrderByWithRelationInput | Prisma.push_tokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.push_tokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` push_tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` push_tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned push_tokens
  **/
  _count?: true | Push_tokenCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Push_tokenAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Push_tokenSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Push_tokenMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Push_tokenMaxAggregateInputType
}

export type GetPush_tokenAggregateType<T extends Push_tokenAggregateArgs> = {
      [P in keyof T & keyof AggregatePush_token]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePush_token[P]>
    : Prisma.GetScalarType<T[P], AggregatePush_token[P]>
}




export type push_tokenGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.push_tokenWhereInput
  orderBy?: Prisma.push_tokenOrderByWithAggregationInput | Prisma.push_tokenOrderByWithAggregationInput[]
  by: Prisma.Push_tokenScalarFieldEnum[] | Prisma.Push_tokenScalarFieldEnum
  having?: Prisma.push_tokenScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Push_tokenCountAggregateInputType | true
  _avg?: Push_tokenAvgAggregateInputType
  _sum?: Push_tokenSumAggregateInputType
  _min?: Push_tokenMinAggregateInputType
  _max?: Push_tokenMaxAggregateInputType
}

export type Push_tokenGroupByOutputType = {
  id: number
  token: string
  createdAt: Date
  updatedAt: Date
  userId: number | null
  _count: Push_tokenCountAggregateOutputType | null
  _avg: Push_tokenAvgAggregateOutputType | null
  _sum: Push_tokenSumAggregateOutputType | null
  _min: Push_tokenMinAggregateOutputType | null
  _max: Push_tokenMaxAggregateOutputType | null
}

type GetPush_tokenGroupByPayload<T extends push_tokenGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Push_tokenGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Push_tokenGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Push_tokenGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Push_tokenGroupByOutputType[P]>
      }
    >
  >



export type push_tokenWhereInput = {
  AND?: Prisma.push_tokenWhereInput | Prisma.push_tokenWhereInput[]
  OR?: Prisma.push_tokenWhereInput[]
  NOT?: Prisma.push_tokenWhereInput | Prisma.push_tokenWhereInput[]
  id?: Prisma.IntFilter<"push_token"> | number
  token?: Prisma.StringFilter<"push_token"> | string
  createdAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  userId?: Prisma.IntNullableFilter<"push_token"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type push_tokenOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.push_tokenOrderByRelevanceInput
}

export type push_tokenWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  token?: string
  AND?: Prisma.push_tokenWhereInput | Prisma.push_tokenWhereInput[]
  OR?: Prisma.push_tokenWhereInput[]
  NOT?: Prisma.push_tokenWhereInput | Prisma.push_tokenWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  userId?: Prisma.IntNullableFilter<"push_token"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id" | "token">

export type push_tokenOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.push_tokenCountOrderByAggregateInput
  _avg?: Prisma.push_tokenAvgOrderByAggregateInput
  _max?: Prisma.push_tokenMaxOrderByAggregateInput
  _min?: Prisma.push_tokenMinOrderByAggregateInput
  _sum?: Prisma.push_tokenSumOrderByAggregateInput
}

export type push_tokenScalarWhereWithAggregatesInput = {
  AND?: Prisma.push_tokenScalarWhereWithAggregatesInput | Prisma.push_tokenScalarWhereWithAggregatesInput[]
  OR?: Prisma.push_tokenScalarWhereWithAggregatesInput[]
  NOT?: Prisma.push_tokenScalarWhereWithAggregatesInput | Prisma.push_tokenScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"push_token"> | number
  token?: Prisma.StringWithAggregatesFilter<"push_token"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"push_token"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"push_token"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"push_token"> | number | null
}

export type push_tokenCreateInput = {
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutPush_tokenInput
}

export type push_tokenUncheckedCreateInput = {
  id?: number
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type push_tokenUpdateInput = {
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutPush_tokenNestedInput
}

export type push_tokenUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type push_tokenCreateManyInput = {
  id?: number
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type push_tokenUpdateManyMutationInput = {
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type push_tokenUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type push_tokenOrderByRelevanceInput = {
  fields: Prisma.push_tokenOrderByRelevanceFieldEnum | Prisma.push_tokenOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type push_tokenCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type push_tokenAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type push_tokenMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type push_tokenMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type push_tokenSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type Push_tokenListRelationFilter = {
  every?: Prisma.push_tokenWhereInput
  some?: Prisma.push_tokenWhereInput
  none?: Prisma.push_tokenWhereInput
}

export type push_tokenOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type push_tokenCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput> | Prisma.push_tokenCreateWithoutUserInput[] | Prisma.push_tokenUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.push_tokenCreateOrConnectWithoutUserInput | Prisma.push_tokenCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.push_tokenCreateManyUserInputEnvelope
  connect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
}

export type push_tokenUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput> | Prisma.push_tokenCreateWithoutUserInput[] | Prisma.push_tokenUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.push_tokenCreateOrConnectWithoutUserInput | Prisma.push_tokenCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.push_tokenCreateManyUserInputEnvelope
  connect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
}

export type push_tokenUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput> | Prisma.push_tokenCreateWithoutUserInput[] | Prisma.push_tokenUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.push_tokenCreateOrConnectWithoutUserInput | Prisma.push_tokenCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.push_tokenUpsertWithWhereUniqueWithoutUserInput | Prisma.push_tokenUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.push_tokenCreateManyUserInputEnvelope
  set?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  disconnect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  delete?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  connect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  update?: Prisma.push_tokenUpdateWithWhereUniqueWithoutUserInput | Prisma.push_tokenUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.push_tokenUpdateManyWithWhereWithoutUserInput | Prisma.push_tokenUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.push_tokenScalarWhereInput | Prisma.push_tokenScalarWhereInput[]
}

export type push_tokenUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput> | Prisma.push_tokenCreateWithoutUserInput[] | Prisma.push_tokenUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.push_tokenCreateOrConnectWithoutUserInput | Prisma.push_tokenCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.push_tokenUpsertWithWhereUniqueWithoutUserInput | Prisma.push_tokenUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.push_tokenCreateManyUserInputEnvelope
  set?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  disconnect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  delete?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  connect?: Prisma.push_tokenWhereUniqueInput | Prisma.push_tokenWhereUniqueInput[]
  update?: Prisma.push_tokenUpdateWithWhereUniqueWithoutUserInput | Prisma.push_tokenUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.push_tokenUpdateManyWithWhereWithoutUserInput | Prisma.push_tokenUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.push_tokenScalarWhereInput | Prisma.push_tokenScalarWhereInput[]
}

export type push_tokenCreateWithoutUserInput = {
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type push_tokenUncheckedCreateWithoutUserInput = {
  id?: number
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type push_tokenCreateOrConnectWithoutUserInput = {
  where: Prisma.push_tokenWhereUniqueInput
  create: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput>
}

export type push_tokenCreateManyUserInputEnvelope = {
  data: Prisma.push_tokenCreateManyUserInput | Prisma.push_tokenCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type push_tokenUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.push_tokenWhereUniqueInput
  update: Prisma.XOR<Prisma.push_tokenUpdateWithoutUserInput, Prisma.push_tokenUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.push_tokenCreateWithoutUserInput, Prisma.push_tokenUncheckedCreateWithoutUserInput>
}

export type push_tokenUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.push_tokenWhereUniqueInput
  data: Prisma.XOR<Prisma.push_tokenUpdateWithoutUserInput, Prisma.push_tokenUncheckedUpdateWithoutUserInput>
}

export type push_tokenUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.push_tokenScalarWhereInput
  data: Prisma.XOR<Prisma.push_tokenUpdateManyMutationInput, Prisma.push_tokenUncheckedUpdateManyWithoutUserInput>
}

export type push_tokenScalarWhereInput = {
  AND?: Prisma.push_tokenScalarWhereInput | Prisma.push_tokenScalarWhereInput[]
  OR?: Prisma.push_tokenScalarWhereInput[]
  NOT?: Prisma.push_tokenScalarWhereInput | Prisma.push_tokenScalarWhereInput[]
  id?: Prisma.IntFilter<"push_token"> | number
  token?: Prisma.StringFilter<"push_token"> | string
  createdAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"push_token"> | Date | string
  userId?: Prisma.IntNullableFilter<"push_token"> | number | null
}

export type push_tokenCreateManyUserInput = {
  id?: number
  token: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type push_tokenUpdateWithoutUserInput = {
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type push_tokenUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type push_tokenUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type push_tokenSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.push_token$userArgs<ExtArgs>
}, ExtArgs["result"]["push_token"]>



export type push_tokenSelectScalar = {
  id?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type push_tokenOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "token" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["push_token"]>
export type push_tokenInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.push_token$userArgs<ExtArgs>
}

export type $push_tokenPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "push_token"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    token: string
    createdAt: Date
    updatedAt: Date
    userId: number | null
  }, ExtArgs["result"]["push_token"]>
  composites: {}
}

export type push_tokenGetPayload<S extends boolean | null | undefined | push_tokenDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$push_tokenPayload, S>

export type push_tokenCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<push_tokenFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Push_tokenCountAggregateInputType | true
  }

export interface push_tokenDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['push_token'], meta: { name: 'push_token' } }
  /**
   * Find zero or one Push_token that matches the filter.
   * @param {push_tokenFindUniqueArgs} args - Arguments to find a Push_token
   * @example
   * // Get one Push_token
   * const push_token = await prisma.push_token.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends push_tokenFindUniqueArgs>(args: Prisma.SelectSubset<T, push_tokenFindUniqueArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Push_token that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {push_tokenFindUniqueOrThrowArgs} args - Arguments to find a Push_token
   * @example
   * // Get one Push_token
   * const push_token = await prisma.push_token.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends push_tokenFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, push_tokenFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Push_token that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenFindFirstArgs} args - Arguments to find a Push_token
   * @example
   * // Get one Push_token
   * const push_token = await prisma.push_token.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends push_tokenFindFirstArgs>(args?: Prisma.SelectSubset<T, push_tokenFindFirstArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Push_token that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenFindFirstOrThrowArgs} args - Arguments to find a Push_token
   * @example
   * // Get one Push_token
   * const push_token = await prisma.push_token.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends push_tokenFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, push_tokenFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Push_tokens that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Push_tokens
   * const push_tokens = await prisma.push_token.findMany()
   * 
   * // Get first 10 Push_tokens
   * const push_tokens = await prisma.push_token.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const push_tokenWithIdOnly = await prisma.push_token.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends push_tokenFindManyArgs>(args?: Prisma.SelectSubset<T, push_tokenFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Push_token.
   * @param {push_tokenCreateArgs} args - Arguments to create a Push_token.
   * @example
   * // Create one Push_token
   * const Push_token = await prisma.push_token.create({
   *   data: {
   *     // ... data to create a Push_token
   *   }
   * })
   * 
   */
  create<T extends push_tokenCreateArgs>(args: Prisma.SelectSubset<T, push_tokenCreateArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Push_tokens.
   * @param {push_tokenCreateManyArgs} args - Arguments to create many Push_tokens.
   * @example
   * // Create many Push_tokens
   * const push_token = await prisma.push_token.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends push_tokenCreateManyArgs>(args?: Prisma.SelectSubset<T, push_tokenCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Push_token.
   * @param {push_tokenDeleteArgs} args - Arguments to delete one Push_token.
   * @example
   * // Delete one Push_token
   * const Push_token = await prisma.push_token.delete({
   *   where: {
   *     // ... filter to delete one Push_token
   *   }
   * })
   * 
   */
  delete<T extends push_tokenDeleteArgs>(args: Prisma.SelectSubset<T, push_tokenDeleteArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Push_token.
   * @param {push_tokenUpdateArgs} args - Arguments to update one Push_token.
   * @example
   * // Update one Push_token
   * const push_token = await prisma.push_token.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends push_tokenUpdateArgs>(args: Prisma.SelectSubset<T, push_tokenUpdateArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Push_tokens.
   * @param {push_tokenDeleteManyArgs} args - Arguments to filter Push_tokens to delete.
   * @example
   * // Delete a few Push_tokens
   * const { count } = await prisma.push_token.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends push_tokenDeleteManyArgs>(args?: Prisma.SelectSubset<T, push_tokenDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Push_tokens.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Push_tokens
   * const push_token = await prisma.push_token.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends push_tokenUpdateManyArgs>(args: Prisma.SelectSubset<T, push_tokenUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Push_token.
   * @param {push_tokenUpsertArgs} args - Arguments to update or create a Push_token.
   * @example
   * // Update or create a Push_token
   * const push_token = await prisma.push_token.upsert({
   *   create: {
   *     // ... data to create a Push_token
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Push_token we want to update
   *   }
   * })
   */
  upsert<T extends push_tokenUpsertArgs>(args: Prisma.SelectSubset<T, push_tokenUpsertArgs<ExtArgs>>): Prisma.Prisma__push_tokenClient<runtime.Types.Result.GetResult<Prisma.$push_tokenPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Push_tokens.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenCountArgs} args - Arguments to filter Push_tokens to count.
   * @example
   * // Count the number of Push_tokens
   * const count = await prisma.push_token.count({
   *   where: {
   *     // ... the filter for the Push_tokens we want to count
   *   }
   * })
  **/
  count<T extends push_tokenCountArgs>(
    args?: Prisma.Subset<T, push_tokenCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Push_tokenCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Push_token.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Push_tokenAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Push_tokenAggregateArgs>(args: Prisma.Subset<T, Push_tokenAggregateArgs>): Prisma.PrismaPromise<GetPush_tokenAggregateType<T>>

  /**
   * Group by Push_token.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {push_tokenGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends push_tokenGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: push_tokenGroupByArgs['orderBy'] }
      : { orderBy?: push_tokenGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, push_tokenGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPush_tokenGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the push_token model
 */
readonly fields: push_tokenFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for push_token.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__push_tokenClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.push_token$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.push_token$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the push_token model
 */
export interface push_tokenFieldRefs {
  readonly id: Prisma.FieldRef<"push_token", 'Int'>
  readonly token: Prisma.FieldRef<"push_token", 'String'>
  readonly createdAt: Prisma.FieldRef<"push_token", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"push_token", 'DateTime'>
  readonly userId: Prisma.FieldRef<"push_token", 'Int'>
}
    

// Custom InputTypes
/**
 * push_token findUnique
 */
export type push_tokenFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter, which push_token to fetch.
   */
  where: Prisma.push_tokenWhereUniqueInput
}

/**
 * push_token findUniqueOrThrow
 */
export type push_tokenFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter, which push_token to fetch.
   */
  where: Prisma.push_tokenWhereUniqueInput
}

/**
 * push_token findFirst
 */
export type push_tokenFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter, which push_token to fetch.
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of push_tokens to fetch.
   */
  orderBy?: Prisma.push_tokenOrderByWithRelationInput | Prisma.push_tokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for push_tokens.
   */
  cursor?: Prisma.push_tokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` push_tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` push_tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of push_tokens.
   */
  distinct?: Prisma.Push_tokenScalarFieldEnum | Prisma.Push_tokenScalarFieldEnum[]
}

/**
 * push_token findFirstOrThrow
 */
export type push_tokenFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter, which push_token to fetch.
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of push_tokens to fetch.
   */
  orderBy?: Prisma.push_tokenOrderByWithRelationInput | Prisma.push_tokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for push_tokens.
   */
  cursor?: Prisma.push_tokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` push_tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` push_tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of push_tokens.
   */
  distinct?: Prisma.Push_tokenScalarFieldEnum | Prisma.Push_tokenScalarFieldEnum[]
}

/**
 * push_token findMany
 */
export type push_tokenFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter, which push_tokens to fetch.
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of push_tokens to fetch.
   */
  orderBy?: Prisma.push_tokenOrderByWithRelationInput | Prisma.push_tokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing push_tokens.
   */
  cursor?: Prisma.push_tokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` push_tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` push_tokens.
   */
  skip?: number
  distinct?: Prisma.Push_tokenScalarFieldEnum | Prisma.Push_tokenScalarFieldEnum[]
}

/**
 * push_token create
 */
export type push_tokenCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * The data needed to create a push_token.
   */
  data: Prisma.XOR<Prisma.push_tokenCreateInput, Prisma.push_tokenUncheckedCreateInput>
}

/**
 * push_token createMany
 */
export type push_tokenCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many push_tokens.
   */
  data: Prisma.push_tokenCreateManyInput | Prisma.push_tokenCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * push_token update
 */
export type push_tokenUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * The data needed to update a push_token.
   */
  data: Prisma.XOR<Prisma.push_tokenUpdateInput, Prisma.push_tokenUncheckedUpdateInput>
  /**
   * Choose, which push_token to update.
   */
  where: Prisma.push_tokenWhereUniqueInput
}

/**
 * push_token updateMany
 */
export type push_tokenUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update push_tokens.
   */
  data: Prisma.XOR<Prisma.push_tokenUpdateManyMutationInput, Prisma.push_tokenUncheckedUpdateManyInput>
  /**
   * Filter which push_tokens to update
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * Limit how many push_tokens to update.
   */
  limit?: number
}

/**
 * push_token upsert
 */
export type push_tokenUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * The filter to search for the push_token to update in case it exists.
   */
  where: Prisma.push_tokenWhereUniqueInput
  /**
   * In case the push_token found by the `where` argument doesn't exist, create a new push_token with this data.
   */
  create: Prisma.XOR<Prisma.push_tokenCreateInput, Prisma.push_tokenUncheckedCreateInput>
  /**
   * In case the push_token was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.push_tokenUpdateInput, Prisma.push_tokenUncheckedUpdateInput>
}

/**
 * push_token delete
 */
export type push_tokenDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
  /**
   * Filter which push_token to delete.
   */
  where: Prisma.push_tokenWhereUniqueInput
}

/**
 * push_token deleteMany
 */
export type push_tokenDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which push_tokens to delete
   */
  where?: Prisma.push_tokenWhereInput
  /**
   * Limit how many push_tokens to delete.
   */
  limit?: number
}

/**
 * push_token.user
 */
export type push_token$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * push_token without action
 */
export type push_tokenDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the push_token
   */
  select?: Prisma.push_tokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the push_token
   */
  omit?: Prisma.push_tokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.push_tokenInclude<ExtArgs> | null
}
