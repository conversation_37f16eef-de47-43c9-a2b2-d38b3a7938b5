
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `equipped_item` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model equipped_item
 * 
 */
export type equipped_itemModel = runtime.Types.Result.DefaultSelection<Prisma.$equipped_itemPayload>

export type AggregateEquipped_item = {
  _count: Equipped_itemCountAggregateOutputType | null
  _avg: Equipped_itemAvgAggregateOutputType | null
  _sum: Equipped_itemSumAggregateOutputType | null
  _min: Equipped_itemMinAggregateOutputType | null
  _max: Equipped_itemMaxAggregateOutputType | null
}

export type Equipped_itemAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  userItemId: number | null
}

export type Equipped_itemSumAggregateOutputType = {
  id: number | null
  userId: number | null
  userItemId: number | null
}

export type Equipped_itemMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  slot: $Enums.EquipSlots | null
  userItemId: number | null
}

export type Equipped_itemMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  slot: $Enums.EquipSlots | null
  userItemId: number | null
}

export type Equipped_itemCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  userId: number
  slot: number
  userItemId: number
  _all: number
}


export type Equipped_itemAvgAggregateInputType = {
  id?: true
  userId?: true
  userItemId?: true
}

export type Equipped_itemSumAggregateInputType = {
  id?: true
  userId?: true
  userItemId?: true
}

export type Equipped_itemMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  slot?: true
  userItemId?: true
}

export type Equipped_itemMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  slot?: true
  userItemId?: true
}

export type Equipped_itemCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  slot?: true
  userItemId?: true
  _all?: true
}

export type Equipped_itemAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which equipped_item to aggregate.
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of equipped_items to fetch.
   */
  orderBy?: Prisma.equipped_itemOrderByWithRelationInput | Prisma.equipped_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.equipped_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` equipped_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` equipped_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned equipped_items
  **/
  _count?: true | Equipped_itemCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Equipped_itemAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Equipped_itemSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Equipped_itemMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Equipped_itemMaxAggregateInputType
}

export type GetEquipped_itemAggregateType<T extends Equipped_itemAggregateArgs> = {
      [P in keyof T & keyof AggregateEquipped_item]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateEquipped_item[P]>
    : Prisma.GetScalarType<T[P], AggregateEquipped_item[P]>
}




export type equipped_itemGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.equipped_itemWhereInput
  orderBy?: Prisma.equipped_itemOrderByWithAggregationInput | Prisma.equipped_itemOrderByWithAggregationInput[]
  by: Prisma.Equipped_itemScalarFieldEnum[] | Prisma.Equipped_itemScalarFieldEnum
  having?: Prisma.equipped_itemScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Equipped_itemCountAggregateInputType | true
  _avg?: Equipped_itemAvgAggregateInputType
  _sum?: Equipped_itemSumAggregateInputType
  _min?: Equipped_itemMinAggregateInputType
  _max?: Equipped_itemMaxAggregateInputType
}

export type Equipped_itemGroupByOutputType = {
  id: number
  createdAt: Date
  updatedAt: Date
  userId: number | null
  slot: $Enums.EquipSlots
  userItemId: number | null
  _count: Equipped_itemCountAggregateOutputType | null
  _avg: Equipped_itemAvgAggregateOutputType | null
  _sum: Equipped_itemSumAggregateOutputType | null
  _min: Equipped_itemMinAggregateOutputType | null
  _max: Equipped_itemMaxAggregateOutputType | null
}

type GetEquipped_itemGroupByPayload<T extends equipped_itemGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Equipped_itemGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Equipped_itemGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Equipped_itemGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Equipped_itemGroupByOutputType[P]>
      }
    >
  >



export type equipped_itemWhereInput = {
  AND?: Prisma.equipped_itemWhereInput | Prisma.equipped_itemWhereInput[]
  OR?: Prisma.equipped_itemWhereInput[]
  NOT?: Prisma.equipped_itemWhereInput | Prisma.equipped_itemWhereInput[]
  id?: Prisma.IntFilter<"equipped_item"> | number
  createdAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
  slot?: Prisma.EnumEquipSlotsFilter<"equipped_item"> | $Enums.EquipSlots
  userItemId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_item?: Prisma.XOR<Prisma.User_itemNullableScalarRelationFilter, Prisma.user_itemWhereInput> | null
}

export type equipped_itemOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  slot?: Prisma.SortOrder
  userItemId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  user_item?: Prisma.user_itemOrderByWithRelationInput
}

export type equipped_itemWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  userId_slot?: Prisma.equipped_itemUserIdSlotCompoundUniqueInput
  AND?: Prisma.equipped_itemWhereInput | Prisma.equipped_itemWhereInput[]
  OR?: Prisma.equipped_itemWhereInput[]
  NOT?: Prisma.equipped_itemWhereInput | Prisma.equipped_itemWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
  slot?: Prisma.EnumEquipSlotsFilter<"equipped_item"> | $Enums.EquipSlots
  userItemId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_item?: Prisma.XOR<Prisma.User_itemNullableScalarRelationFilter, Prisma.user_itemWhereInput> | null
}, "id" | "userId_slot">

export type equipped_itemOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  slot?: Prisma.SortOrder
  userItemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.equipped_itemCountOrderByAggregateInput
  _avg?: Prisma.equipped_itemAvgOrderByAggregateInput
  _max?: Prisma.equipped_itemMaxOrderByAggregateInput
  _min?: Prisma.equipped_itemMinOrderByAggregateInput
  _sum?: Prisma.equipped_itemSumOrderByAggregateInput
}

export type equipped_itemScalarWhereWithAggregatesInput = {
  AND?: Prisma.equipped_itemScalarWhereWithAggregatesInput | Prisma.equipped_itemScalarWhereWithAggregatesInput[]
  OR?: Prisma.equipped_itemScalarWhereWithAggregatesInput[]
  NOT?: Prisma.equipped_itemScalarWhereWithAggregatesInput | Prisma.equipped_itemScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"equipped_item"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"equipped_item"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"equipped_item"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"equipped_item"> | number | null
  slot?: Prisma.EnumEquipSlotsWithAggregatesFilter<"equipped_item"> | $Enums.EquipSlots
  userItemId?: Prisma.IntNullableWithAggregatesFilter<"equipped_item"> | number | null
}

export type equipped_itemCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  slot: $Enums.EquipSlots
  user?: Prisma.userCreateNestedOneWithoutEquipped_itemInput
  user_item?: Prisma.user_itemCreateNestedOneWithoutEquipped_itemsInput
}

export type equipped_itemUncheckedCreateInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  slot: $Enums.EquipSlots
  userItemId?: number | null
}

export type equipped_itemUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  user?: Prisma.userUpdateOneWithoutEquipped_itemNestedInput
  user_item?: Prisma.user_itemUpdateOneWithoutEquipped_itemsNestedInput
}

export type equipped_itemUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  userItemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type equipped_itemCreateManyInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  slot: $Enums.EquipSlots
  userItemId?: number | null
}

export type equipped_itemUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
}

export type equipped_itemUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  userItemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type equipped_itemUserIdSlotCompoundUniqueInput = {
  userId: number
  slot: $Enums.EquipSlots
}

export type equipped_itemCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  slot?: Prisma.SortOrder
  userItemId?: Prisma.SortOrder
}

export type equipped_itemAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  userItemId?: Prisma.SortOrder
}

export type equipped_itemMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  slot?: Prisma.SortOrder
  userItemId?: Prisma.SortOrder
}

export type equipped_itemMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  slot?: Prisma.SortOrder
  userItemId?: Prisma.SortOrder
}

export type equipped_itemSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  userItemId?: Prisma.SortOrder
}

export type Equipped_itemListRelationFilter = {
  every?: Prisma.equipped_itemWhereInput
  some?: Prisma.equipped_itemWhereInput
  none?: Prisma.equipped_itemWhereInput
}

export type equipped_itemOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EnumEquipSlotsFieldUpdateOperationsInput = {
  set?: $Enums.EquipSlots
}

export type equipped_itemCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput> | Prisma.equipped_itemCreateWithoutUserInput[] | Prisma.equipped_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUserInput | Prisma.equipped_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.equipped_itemCreateManyUserInputEnvelope
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
}

export type equipped_itemUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput> | Prisma.equipped_itemCreateWithoutUserInput[] | Prisma.equipped_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUserInput | Prisma.equipped_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.equipped_itemCreateManyUserInputEnvelope
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
}

export type equipped_itemUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput> | Prisma.equipped_itemCreateWithoutUserInput[] | Prisma.equipped_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUserInput | Prisma.equipped_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.equipped_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.equipped_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.equipped_itemCreateManyUserInputEnvelope
  set?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  disconnect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  delete?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  update?: Prisma.equipped_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.equipped_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.equipped_itemUpdateManyWithWhereWithoutUserInput | Prisma.equipped_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
}

export type equipped_itemUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput> | Prisma.equipped_itemCreateWithoutUserInput[] | Prisma.equipped_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUserInput | Prisma.equipped_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.equipped_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.equipped_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.equipped_itemCreateManyUserInputEnvelope
  set?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  disconnect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  delete?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  update?: Prisma.equipped_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.equipped_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.equipped_itemUpdateManyWithWhereWithoutUserInput | Prisma.equipped_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
}

export type equipped_itemCreateNestedManyWithoutUser_itemInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput> | Prisma.equipped_itemCreateWithoutUser_itemInput[] | Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput | Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput[]
  createMany?: Prisma.equipped_itemCreateManyUser_itemInputEnvelope
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
}

export type equipped_itemUncheckedCreateNestedManyWithoutUser_itemInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput> | Prisma.equipped_itemCreateWithoutUser_itemInput[] | Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput | Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput[]
  createMany?: Prisma.equipped_itemCreateManyUser_itemInputEnvelope
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
}

export type equipped_itemUpdateManyWithoutUser_itemNestedInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput> | Prisma.equipped_itemCreateWithoutUser_itemInput[] | Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput | Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput[]
  upsert?: Prisma.equipped_itemUpsertWithWhereUniqueWithoutUser_itemInput | Prisma.equipped_itemUpsertWithWhereUniqueWithoutUser_itemInput[]
  createMany?: Prisma.equipped_itemCreateManyUser_itemInputEnvelope
  set?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  disconnect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  delete?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  update?: Prisma.equipped_itemUpdateWithWhereUniqueWithoutUser_itemInput | Prisma.equipped_itemUpdateWithWhereUniqueWithoutUser_itemInput[]
  updateMany?: Prisma.equipped_itemUpdateManyWithWhereWithoutUser_itemInput | Prisma.equipped_itemUpdateManyWithWhereWithoutUser_itemInput[]
  deleteMany?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
}

export type equipped_itemUncheckedUpdateManyWithoutUser_itemNestedInput = {
  create?: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput> | Prisma.equipped_itemCreateWithoutUser_itemInput[] | Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput[]
  connectOrCreate?: Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput | Prisma.equipped_itemCreateOrConnectWithoutUser_itemInput[]
  upsert?: Prisma.equipped_itemUpsertWithWhereUniqueWithoutUser_itemInput | Prisma.equipped_itemUpsertWithWhereUniqueWithoutUser_itemInput[]
  createMany?: Prisma.equipped_itemCreateManyUser_itemInputEnvelope
  set?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  disconnect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  delete?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  connect?: Prisma.equipped_itemWhereUniqueInput | Prisma.equipped_itemWhereUniqueInput[]
  update?: Prisma.equipped_itemUpdateWithWhereUniqueWithoutUser_itemInput | Prisma.equipped_itemUpdateWithWhereUniqueWithoutUser_itemInput[]
  updateMany?: Prisma.equipped_itemUpdateManyWithWhereWithoutUser_itemInput | Prisma.equipped_itemUpdateManyWithWhereWithoutUser_itemInput[]
  deleteMany?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
}

export type equipped_itemCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  slot: $Enums.EquipSlots
  user_item?: Prisma.user_itemCreateNestedOneWithoutEquipped_itemsInput
}

export type equipped_itemUncheckedCreateWithoutUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  slot: $Enums.EquipSlots
  userItemId?: number | null
}

export type equipped_itemCreateOrConnectWithoutUserInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput>
}

export type equipped_itemCreateManyUserInputEnvelope = {
  data: Prisma.equipped_itemCreateManyUserInput | Prisma.equipped_itemCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type equipped_itemUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.equipped_itemUpdateWithoutUserInput, Prisma.equipped_itemUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.equipped_itemCreateWithoutUserInput, Prisma.equipped_itemUncheckedCreateWithoutUserInput>
}

export type equipped_itemUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.equipped_itemUpdateWithoutUserInput, Prisma.equipped_itemUncheckedUpdateWithoutUserInput>
}

export type equipped_itemUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.equipped_itemScalarWhereInput
  data: Prisma.XOR<Prisma.equipped_itemUpdateManyMutationInput, Prisma.equipped_itemUncheckedUpdateManyWithoutUserInput>
}

export type equipped_itemScalarWhereInput = {
  AND?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
  OR?: Prisma.equipped_itemScalarWhereInput[]
  NOT?: Prisma.equipped_itemScalarWhereInput | Prisma.equipped_itemScalarWhereInput[]
  id?: Prisma.IntFilter<"equipped_item"> | number
  createdAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"equipped_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
  slot?: Prisma.EnumEquipSlotsFilter<"equipped_item"> | $Enums.EquipSlots
  userItemId?: Prisma.IntNullableFilter<"equipped_item"> | number | null
}

export type equipped_itemCreateWithoutUser_itemInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  slot: $Enums.EquipSlots
  user?: Prisma.userCreateNestedOneWithoutEquipped_itemInput
}

export type equipped_itemUncheckedCreateWithoutUser_itemInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  slot: $Enums.EquipSlots
}

export type equipped_itemCreateOrConnectWithoutUser_itemInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput>
}

export type equipped_itemCreateManyUser_itemInputEnvelope = {
  data: Prisma.equipped_itemCreateManyUser_itemInput | Prisma.equipped_itemCreateManyUser_itemInput[]
  skipDuplicates?: boolean
}

export type equipped_itemUpsertWithWhereUniqueWithoutUser_itemInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.equipped_itemUpdateWithoutUser_itemInput, Prisma.equipped_itemUncheckedUpdateWithoutUser_itemInput>
  create: Prisma.XOR<Prisma.equipped_itemCreateWithoutUser_itemInput, Prisma.equipped_itemUncheckedCreateWithoutUser_itemInput>
}

export type equipped_itemUpdateWithWhereUniqueWithoutUser_itemInput = {
  where: Prisma.equipped_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.equipped_itemUpdateWithoutUser_itemInput, Prisma.equipped_itemUncheckedUpdateWithoutUser_itemInput>
}

export type equipped_itemUpdateManyWithWhereWithoutUser_itemInput = {
  where: Prisma.equipped_itemScalarWhereInput
  data: Prisma.XOR<Prisma.equipped_itemUpdateManyMutationInput, Prisma.equipped_itemUncheckedUpdateManyWithoutUser_itemInput>
}

export type equipped_itemCreateManyUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  slot: $Enums.EquipSlots
  userItemId?: number | null
}

export type equipped_itemUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  user_item?: Prisma.user_itemUpdateOneWithoutEquipped_itemsNestedInput
}

export type equipped_itemUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  userItemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type equipped_itemUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  userItemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type equipped_itemCreateManyUser_itemInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  slot: $Enums.EquipSlots
}

export type equipped_itemUpdateWithoutUser_itemInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
  user?: Prisma.userUpdateOneWithoutEquipped_itemNestedInput
}

export type equipped_itemUncheckedUpdateWithoutUser_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
}

export type equipped_itemUncheckedUpdateManyWithoutUser_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  slot?: Prisma.EnumEquipSlotsFieldUpdateOperationsInput | $Enums.EquipSlots
}



export type equipped_itemSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  slot?: boolean
  userItemId?: boolean
  user?: boolean | Prisma.equipped_item$userArgs<ExtArgs>
  user_item?: boolean | Prisma.equipped_item$user_itemArgs<ExtArgs>
}, ExtArgs["result"]["equipped_item"]>



export type equipped_itemSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  slot?: boolean
  userItemId?: boolean
}

export type equipped_itemOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "createdAt" | "updatedAt" | "userId" | "slot" | "userItemId", ExtArgs["result"]["equipped_item"]>
export type equipped_itemInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.equipped_item$userArgs<ExtArgs>
  user_item?: boolean | Prisma.equipped_item$user_itemArgs<ExtArgs>
}

export type $equipped_itemPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "equipped_item"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    user_item: Prisma.$user_itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    createdAt: Date
    updatedAt: Date
    userId: number | null
    slot: $Enums.EquipSlots
    userItemId: number | null
  }, ExtArgs["result"]["equipped_item"]>
  composites: {}
}

export type equipped_itemGetPayload<S extends boolean | null | undefined | equipped_itemDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload, S>

export type equipped_itemCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<equipped_itemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Equipped_itemCountAggregateInputType | true
  }

export interface equipped_itemDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['equipped_item'], meta: { name: 'equipped_item' } }
  /**
   * Find zero or one Equipped_item that matches the filter.
   * @param {equipped_itemFindUniqueArgs} args - Arguments to find a Equipped_item
   * @example
   * // Get one Equipped_item
   * const equipped_item = await prisma.equipped_item.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends equipped_itemFindUniqueArgs>(args: Prisma.SelectSubset<T, equipped_itemFindUniqueArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Equipped_item that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {equipped_itemFindUniqueOrThrowArgs} args - Arguments to find a Equipped_item
   * @example
   * // Get one Equipped_item
   * const equipped_item = await prisma.equipped_item.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends equipped_itemFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, equipped_itemFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Equipped_item that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemFindFirstArgs} args - Arguments to find a Equipped_item
   * @example
   * // Get one Equipped_item
   * const equipped_item = await prisma.equipped_item.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends equipped_itemFindFirstArgs>(args?: Prisma.SelectSubset<T, equipped_itemFindFirstArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Equipped_item that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemFindFirstOrThrowArgs} args - Arguments to find a Equipped_item
   * @example
   * // Get one Equipped_item
   * const equipped_item = await prisma.equipped_item.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends equipped_itemFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, equipped_itemFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Equipped_items that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Equipped_items
   * const equipped_items = await prisma.equipped_item.findMany()
   * 
   * // Get first 10 Equipped_items
   * const equipped_items = await prisma.equipped_item.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const equipped_itemWithIdOnly = await prisma.equipped_item.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends equipped_itemFindManyArgs>(args?: Prisma.SelectSubset<T, equipped_itemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Equipped_item.
   * @param {equipped_itemCreateArgs} args - Arguments to create a Equipped_item.
   * @example
   * // Create one Equipped_item
   * const Equipped_item = await prisma.equipped_item.create({
   *   data: {
   *     // ... data to create a Equipped_item
   *   }
   * })
   * 
   */
  create<T extends equipped_itemCreateArgs>(args: Prisma.SelectSubset<T, equipped_itemCreateArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Equipped_items.
   * @param {equipped_itemCreateManyArgs} args - Arguments to create many Equipped_items.
   * @example
   * // Create many Equipped_items
   * const equipped_item = await prisma.equipped_item.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends equipped_itemCreateManyArgs>(args?: Prisma.SelectSubset<T, equipped_itemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Equipped_item.
   * @param {equipped_itemDeleteArgs} args - Arguments to delete one Equipped_item.
   * @example
   * // Delete one Equipped_item
   * const Equipped_item = await prisma.equipped_item.delete({
   *   where: {
   *     // ... filter to delete one Equipped_item
   *   }
   * })
   * 
   */
  delete<T extends equipped_itemDeleteArgs>(args: Prisma.SelectSubset<T, equipped_itemDeleteArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Equipped_item.
   * @param {equipped_itemUpdateArgs} args - Arguments to update one Equipped_item.
   * @example
   * // Update one Equipped_item
   * const equipped_item = await prisma.equipped_item.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends equipped_itemUpdateArgs>(args: Prisma.SelectSubset<T, equipped_itemUpdateArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Equipped_items.
   * @param {equipped_itemDeleteManyArgs} args - Arguments to filter Equipped_items to delete.
   * @example
   * // Delete a few Equipped_items
   * const { count } = await prisma.equipped_item.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends equipped_itemDeleteManyArgs>(args?: Prisma.SelectSubset<T, equipped_itemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Equipped_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Equipped_items
   * const equipped_item = await prisma.equipped_item.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends equipped_itemUpdateManyArgs>(args: Prisma.SelectSubset<T, equipped_itemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Equipped_item.
   * @param {equipped_itemUpsertArgs} args - Arguments to update or create a Equipped_item.
   * @example
   * // Update or create a Equipped_item
   * const equipped_item = await prisma.equipped_item.upsert({
   *   create: {
   *     // ... data to create a Equipped_item
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Equipped_item we want to update
   *   }
   * })
   */
  upsert<T extends equipped_itemUpsertArgs>(args: Prisma.SelectSubset<T, equipped_itemUpsertArgs<ExtArgs>>): Prisma.Prisma__equipped_itemClient<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Equipped_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemCountArgs} args - Arguments to filter Equipped_items to count.
   * @example
   * // Count the number of Equipped_items
   * const count = await prisma.equipped_item.count({
   *   where: {
   *     // ... the filter for the Equipped_items we want to count
   *   }
   * })
  **/
  count<T extends equipped_itemCountArgs>(
    args?: Prisma.Subset<T, equipped_itemCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Equipped_itemCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Equipped_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Equipped_itemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Equipped_itemAggregateArgs>(args: Prisma.Subset<T, Equipped_itemAggregateArgs>): Prisma.PrismaPromise<GetEquipped_itemAggregateType<T>>

  /**
   * Group by Equipped_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {equipped_itemGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends equipped_itemGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: equipped_itemGroupByArgs['orderBy'] }
      : { orderBy?: equipped_itemGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, equipped_itemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetEquipped_itemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the equipped_item model
 */
readonly fields: equipped_itemFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for equipped_item.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__equipped_itemClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.equipped_item$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.equipped_item$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_item<T extends Prisma.equipped_item$user_itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.equipped_item$user_itemArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the equipped_item model
 */
export interface equipped_itemFieldRefs {
  readonly id: Prisma.FieldRef<"equipped_item", 'Int'>
  readonly createdAt: Prisma.FieldRef<"equipped_item", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"equipped_item", 'DateTime'>
  readonly userId: Prisma.FieldRef<"equipped_item", 'Int'>
  readonly slot: Prisma.FieldRef<"equipped_item", 'EquipSlots'>
  readonly userItemId: Prisma.FieldRef<"equipped_item", 'Int'>
}
    

// Custom InputTypes
/**
 * equipped_item findUnique
 */
export type equipped_itemFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter, which equipped_item to fetch.
   */
  where: Prisma.equipped_itemWhereUniqueInput
}

/**
 * equipped_item findUniqueOrThrow
 */
export type equipped_itemFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter, which equipped_item to fetch.
   */
  where: Prisma.equipped_itemWhereUniqueInput
}

/**
 * equipped_item findFirst
 */
export type equipped_itemFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter, which equipped_item to fetch.
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of equipped_items to fetch.
   */
  orderBy?: Prisma.equipped_itemOrderByWithRelationInput | Prisma.equipped_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for equipped_items.
   */
  cursor?: Prisma.equipped_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` equipped_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` equipped_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of equipped_items.
   */
  distinct?: Prisma.Equipped_itemScalarFieldEnum | Prisma.Equipped_itemScalarFieldEnum[]
}

/**
 * equipped_item findFirstOrThrow
 */
export type equipped_itemFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter, which equipped_item to fetch.
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of equipped_items to fetch.
   */
  orderBy?: Prisma.equipped_itemOrderByWithRelationInput | Prisma.equipped_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for equipped_items.
   */
  cursor?: Prisma.equipped_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` equipped_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` equipped_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of equipped_items.
   */
  distinct?: Prisma.Equipped_itemScalarFieldEnum | Prisma.Equipped_itemScalarFieldEnum[]
}

/**
 * equipped_item findMany
 */
export type equipped_itemFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter, which equipped_items to fetch.
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of equipped_items to fetch.
   */
  orderBy?: Prisma.equipped_itemOrderByWithRelationInput | Prisma.equipped_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing equipped_items.
   */
  cursor?: Prisma.equipped_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` equipped_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` equipped_items.
   */
  skip?: number
  distinct?: Prisma.Equipped_itemScalarFieldEnum | Prisma.Equipped_itemScalarFieldEnum[]
}

/**
 * equipped_item create
 */
export type equipped_itemCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * The data needed to create a equipped_item.
   */
  data: Prisma.XOR<Prisma.equipped_itemCreateInput, Prisma.equipped_itemUncheckedCreateInput>
}

/**
 * equipped_item createMany
 */
export type equipped_itemCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many equipped_items.
   */
  data: Prisma.equipped_itemCreateManyInput | Prisma.equipped_itemCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * equipped_item update
 */
export type equipped_itemUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * The data needed to update a equipped_item.
   */
  data: Prisma.XOR<Prisma.equipped_itemUpdateInput, Prisma.equipped_itemUncheckedUpdateInput>
  /**
   * Choose, which equipped_item to update.
   */
  where: Prisma.equipped_itemWhereUniqueInput
}

/**
 * equipped_item updateMany
 */
export type equipped_itemUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update equipped_items.
   */
  data: Prisma.XOR<Prisma.equipped_itemUpdateManyMutationInput, Prisma.equipped_itemUncheckedUpdateManyInput>
  /**
   * Filter which equipped_items to update
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * Limit how many equipped_items to update.
   */
  limit?: number
}

/**
 * equipped_item upsert
 */
export type equipped_itemUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * The filter to search for the equipped_item to update in case it exists.
   */
  where: Prisma.equipped_itemWhereUniqueInput
  /**
   * In case the equipped_item found by the `where` argument doesn't exist, create a new equipped_item with this data.
   */
  create: Prisma.XOR<Prisma.equipped_itemCreateInput, Prisma.equipped_itemUncheckedCreateInput>
  /**
   * In case the equipped_item was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.equipped_itemUpdateInput, Prisma.equipped_itemUncheckedUpdateInput>
}

/**
 * equipped_item delete
 */
export type equipped_itemDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  /**
   * Filter which equipped_item to delete.
   */
  where: Prisma.equipped_itemWhereUniqueInput
}

/**
 * equipped_item deleteMany
 */
export type equipped_itemDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which equipped_items to delete
   */
  where?: Prisma.equipped_itemWhereInput
  /**
   * Limit how many equipped_items to delete.
   */
  limit?: number
}

/**
 * equipped_item.user
 */
export type equipped_item$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * equipped_item.user_item
 */
export type equipped_item$user_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  where?: Prisma.user_itemWhereInput
}

/**
 * equipped_item without action
 */
export type equipped_itemDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
}
