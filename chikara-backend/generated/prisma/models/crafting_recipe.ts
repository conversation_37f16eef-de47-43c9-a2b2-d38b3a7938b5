
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `crafting_recipe` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model crafting_recipe
 * 
 */
export type crafting_recipeModel = runtime.Types.Result.DefaultSelection<Prisma.$crafting_recipePayload>

export type AggregateCrafting_recipe = {
  _count: Crafting_recipeCountAggregateOutputType | null
  _avg: Crafting_recipeAvgAggregateOutputType | null
  _sum: Crafting_recipeSumAggregateOutputType | null
  _min: Crafting_recipeMinAggregateOutputType | null
  _max: Crafting_recipeMaxAggregateOutputType | null
}

export type Crafting_recipeAvgAggregateOutputType = {
  id: number | null
  cost: number | null
  craftTime: number | null
  requiredSkillLevel: number | null
}

export type Crafting_recipeSumAggregateOutputType = {
  id: number | null
  cost: number | null
  craftTime: number | null
  requiredSkillLevel: number | null
}

export type Crafting_recipeMinAggregateOutputType = {
  id: number | null
  cost: number | null
  craftTime: number | null
  isUnlockable: boolean | null
  requiredSkillType: $Enums.CraftingSkills | null
  requiredSkillLevel: number | null
}

export type Crafting_recipeMaxAggregateOutputType = {
  id: number | null
  cost: number | null
  craftTime: number | null
  isUnlockable: boolean | null
  requiredSkillType: $Enums.CraftingSkills | null
  requiredSkillLevel: number | null
}

export type Crafting_recipeCountAggregateOutputType = {
  id: number
  cost: number
  craftTime: number
  isUnlockable: number
  requiredSkillType: number
  requiredSkillLevel: number
  _all: number
}


export type Crafting_recipeAvgAggregateInputType = {
  id?: true
  cost?: true
  craftTime?: true
  requiredSkillLevel?: true
}

export type Crafting_recipeSumAggregateInputType = {
  id?: true
  cost?: true
  craftTime?: true
  requiredSkillLevel?: true
}

export type Crafting_recipeMinAggregateInputType = {
  id?: true
  cost?: true
  craftTime?: true
  isUnlockable?: true
  requiredSkillType?: true
  requiredSkillLevel?: true
}

export type Crafting_recipeMaxAggregateInputType = {
  id?: true
  cost?: true
  craftTime?: true
  isUnlockable?: true
  requiredSkillType?: true
  requiredSkillLevel?: true
}

export type Crafting_recipeCountAggregateInputType = {
  id?: true
  cost?: true
  craftTime?: true
  isUnlockable?: true
  requiredSkillType?: true
  requiredSkillLevel?: true
  _all?: true
}

export type Crafting_recipeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which crafting_recipe to aggregate.
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of crafting_recipes to fetch.
   */
  orderBy?: Prisma.crafting_recipeOrderByWithRelationInput | Prisma.crafting_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.crafting_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` crafting_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` crafting_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned crafting_recipes
  **/
  _count?: true | Crafting_recipeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Crafting_recipeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Crafting_recipeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Crafting_recipeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Crafting_recipeMaxAggregateInputType
}

export type GetCrafting_recipeAggregateType<T extends Crafting_recipeAggregateArgs> = {
      [P in keyof T & keyof AggregateCrafting_recipe]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateCrafting_recipe[P]>
    : Prisma.GetScalarType<T[P], AggregateCrafting_recipe[P]>
}




export type crafting_recipeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.crafting_recipeWhereInput
  orderBy?: Prisma.crafting_recipeOrderByWithAggregationInput | Prisma.crafting_recipeOrderByWithAggregationInput[]
  by: Prisma.Crafting_recipeScalarFieldEnum[] | Prisma.Crafting_recipeScalarFieldEnum
  having?: Prisma.crafting_recipeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Crafting_recipeCountAggregateInputType | true
  _avg?: Crafting_recipeAvgAggregateInputType
  _sum?: Crafting_recipeSumAggregateInputType
  _min?: Crafting_recipeMinAggregateInputType
  _max?: Crafting_recipeMaxAggregateInputType
}

export type Crafting_recipeGroupByOutputType = {
  id: number
  cost: number
  craftTime: number
  isUnlockable: boolean
  requiredSkillType: $Enums.CraftingSkills | null
  requiredSkillLevel: number
  _count: Crafting_recipeCountAggregateOutputType | null
  _avg: Crafting_recipeAvgAggregateOutputType | null
  _sum: Crafting_recipeSumAggregateOutputType | null
  _min: Crafting_recipeMinAggregateOutputType | null
  _max: Crafting_recipeMaxAggregateOutputType | null
}

type GetCrafting_recipeGroupByPayload<T extends crafting_recipeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Crafting_recipeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Crafting_recipeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Crafting_recipeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Crafting_recipeGroupByOutputType[P]>
      }
    >
  >



export type crafting_recipeWhereInput = {
  AND?: Prisma.crafting_recipeWhereInput | Prisma.crafting_recipeWhereInput[]
  OR?: Prisma.crafting_recipeWhereInput[]
  NOT?: Prisma.crafting_recipeWhereInput | Prisma.crafting_recipeWhereInput[]
  id?: Prisma.IntFilter<"crafting_recipe"> | number
  cost?: Prisma.IntFilter<"crafting_recipe"> | number
  craftTime?: Prisma.IntFilter<"crafting_recipe"> | number
  isUnlockable?: Prisma.BoolFilter<"crafting_recipe"> | boolean
  requiredSkillType?: Prisma.EnumCraftingSkillsNullableFilter<"crafting_recipe"> | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFilter<"crafting_recipe"> | number
  item?: Prisma.ItemListRelationFilter
  recipe_item?: Prisma.Recipe_itemListRelationFilter
  user_crafting_queue?: Prisma.User_crafting_queueListRelationFilter
  user_recipe?: Prisma.User_recipeListRelationFilter
}

export type crafting_recipeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  isUnlockable?: Prisma.SortOrder
  requiredSkillType?: Prisma.SortOrderInput | Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
  item?: Prisma.itemOrderByRelationAggregateInput
  recipe_item?: Prisma.recipe_itemOrderByRelationAggregateInput
  user_crafting_queue?: Prisma.user_crafting_queueOrderByRelationAggregateInput
  user_recipe?: Prisma.user_recipeOrderByRelationAggregateInput
}

export type crafting_recipeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.crafting_recipeWhereInput | Prisma.crafting_recipeWhereInput[]
  OR?: Prisma.crafting_recipeWhereInput[]
  NOT?: Prisma.crafting_recipeWhereInput | Prisma.crafting_recipeWhereInput[]
  cost?: Prisma.IntFilter<"crafting_recipe"> | number
  craftTime?: Prisma.IntFilter<"crafting_recipe"> | number
  isUnlockable?: Prisma.BoolFilter<"crafting_recipe"> | boolean
  requiredSkillType?: Prisma.EnumCraftingSkillsNullableFilter<"crafting_recipe"> | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFilter<"crafting_recipe"> | number
  item?: Prisma.ItemListRelationFilter
  recipe_item?: Prisma.Recipe_itemListRelationFilter
  user_crafting_queue?: Prisma.User_crafting_queueListRelationFilter
  user_recipe?: Prisma.User_recipeListRelationFilter
}, "id">

export type crafting_recipeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  isUnlockable?: Prisma.SortOrder
  requiredSkillType?: Prisma.SortOrderInput | Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
  _count?: Prisma.crafting_recipeCountOrderByAggregateInput
  _avg?: Prisma.crafting_recipeAvgOrderByAggregateInput
  _max?: Prisma.crafting_recipeMaxOrderByAggregateInput
  _min?: Prisma.crafting_recipeMinOrderByAggregateInput
  _sum?: Prisma.crafting_recipeSumOrderByAggregateInput
}

export type crafting_recipeScalarWhereWithAggregatesInput = {
  AND?: Prisma.crafting_recipeScalarWhereWithAggregatesInput | Prisma.crafting_recipeScalarWhereWithAggregatesInput[]
  OR?: Prisma.crafting_recipeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.crafting_recipeScalarWhereWithAggregatesInput | Prisma.crafting_recipeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"crafting_recipe"> | number
  cost?: Prisma.IntWithAggregatesFilter<"crafting_recipe"> | number
  craftTime?: Prisma.IntWithAggregatesFilter<"crafting_recipe"> | number
  isUnlockable?: Prisma.BoolWithAggregatesFilter<"crafting_recipe"> | boolean
  requiredSkillType?: Prisma.EnumCraftingSkillsNullableWithAggregatesFilter<"crafting_recipe"> | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntWithAggregatesFilter<"crafting_recipe"> | number
}

export type crafting_recipeCreateInput = {
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUncheckedCreateInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeUncheckedCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUpdateInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUncheckedUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeCreateManyInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
}

export type crafting_recipeUpdateManyMutationInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
}

export type crafting_recipeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
}

export type crafting_recipeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  isUnlockable?: Prisma.SortOrder
  requiredSkillType?: Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
}

export type crafting_recipeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
}

export type crafting_recipeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  isUnlockable?: Prisma.SortOrder
  requiredSkillType?: Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
}

export type crafting_recipeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  isUnlockable?: Prisma.SortOrder
  requiredSkillType?: Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
}

export type crafting_recipeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  craftTime?: Prisma.SortOrder
  requiredSkillLevel?: Prisma.SortOrder
}

export type Crafting_recipeNullableScalarRelationFilter = {
  is?: Prisma.crafting_recipeWhereInput | null
  isNot?: Prisma.crafting_recipeWhereInput | null
}

export type Crafting_recipeScalarRelationFilter = {
  is?: Prisma.crafting_recipeWhereInput
  isNot?: Prisma.crafting_recipeWhereInput
}

export type NullableEnumCraftingSkillsFieldUpdateOperationsInput = {
  set?: $Enums.CraftingSkills | null
}

export type crafting_recipeCreateNestedOneWithoutItemInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutItemInput, Prisma.crafting_recipeUncheckedCreateWithoutItemInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutItemInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
}

export type crafting_recipeUpdateOneWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutItemInput, Prisma.crafting_recipeUncheckedCreateWithoutItemInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutItemInput
  upsert?: Prisma.crafting_recipeUpsertWithoutItemInput
  disconnect?: Prisma.crafting_recipeWhereInput | boolean
  delete?: Prisma.crafting_recipeWhereInput | boolean
  connect?: Prisma.crafting_recipeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.crafting_recipeUpdateToOneWithWhereWithoutItemInput, Prisma.crafting_recipeUpdateWithoutItemInput>, Prisma.crafting_recipeUncheckedUpdateWithoutItemInput>
}

export type crafting_recipeCreateNestedOneWithoutRecipe_itemInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedCreateWithoutRecipe_itemInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutRecipe_itemInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
}

export type crafting_recipeUpdateOneRequiredWithoutRecipe_itemNestedInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedCreateWithoutRecipe_itemInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutRecipe_itemInput
  upsert?: Prisma.crafting_recipeUpsertWithoutRecipe_itemInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.crafting_recipeUpdateToOneWithWhereWithoutRecipe_itemInput, Prisma.crafting_recipeUpdateWithoutRecipe_itemInput>, Prisma.crafting_recipeUncheckedUpdateWithoutRecipe_itemInput>
}

export type crafting_recipeCreateNestedOneWithoutUser_recipeInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_recipeInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutUser_recipeInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
}

export type crafting_recipeUpdateOneRequiredWithoutUser_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_recipeInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutUser_recipeInput
  upsert?: Prisma.crafting_recipeUpsertWithoutUser_recipeInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.crafting_recipeUpdateToOneWithWhereWithoutUser_recipeInput, Prisma.crafting_recipeUpdateWithoutUser_recipeInput>, Prisma.crafting_recipeUncheckedUpdateWithoutUser_recipeInput>
}

export type crafting_recipeCreateNestedOneWithoutUser_crafting_queueInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_crafting_queueInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutUser_crafting_queueInput
  connect?: Prisma.crafting_recipeWhereUniqueInput
}

export type crafting_recipeUpdateOneWithoutUser_crafting_queueNestedInput = {
  create?: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_crafting_queueInput>
  connectOrCreate?: Prisma.crafting_recipeCreateOrConnectWithoutUser_crafting_queueInput
  upsert?: Prisma.crafting_recipeUpsertWithoutUser_crafting_queueInput
  disconnect?: Prisma.crafting_recipeWhereInput | boolean
  delete?: Prisma.crafting_recipeWhereInput | boolean
  connect?: Prisma.crafting_recipeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.crafting_recipeUpdateToOneWithWhereWithoutUser_crafting_queueInput, Prisma.crafting_recipeUpdateWithoutUser_crafting_queueInput>, Prisma.crafting_recipeUncheckedUpdateWithoutUser_crafting_queueInput>
}

export type crafting_recipeCreateWithoutItemInput = {
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUncheckedCreateWithoutItemInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeUncheckedCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeCreateOrConnectWithoutItemInput = {
  where: Prisma.crafting_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutItemInput, Prisma.crafting_recipeUncheckedCreateWithoutItemInput>
}

export type crafting_recipeUpsertWithoutItemInput = {
  update: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutItemInput, Prisma.crafting_recipeUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutItemInput, Prisma.crafting_recipeUncheckedCreateWithoutItemInput>
  where?: Prisma.crafting_recipeWhereInput
}

export type crafting_recipeUpdateToOneWithWhereWithoutItemInput = {
  where?: Prisma.crafting_recipeWhereInput
  data: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutItemInput, Prisma.crafting_recipeUncheckedUpdateWithoutItemInput>
}

export type crafting_recipeUpdateWithoutItemInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUncheckedUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeCreateWithoutRecipe_itemInput = {
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUncheckedCreateWithoutRecipe_itemInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeUncheckedCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeCreateOrConnectWithoutRecipe_itemInput = {
  where: Prisma.crafting_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedCreateWithoutRecipe_itemInput>
}

export type crafting_recipeUpsertWithoutRecipe_itemInput = {
  update: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedUpdateWithoutRecipe_itemInput>
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedCreateWithoutRecipe_itemInput>
  where?: Prisma.crafting_recipeWhereInput
}

export type crafting_recipeUpdateToOneWithWhereWithoutRecipe_itemInput = {
  where?: Prisma.crafting_recipeWhereInput
  data: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutRecipe_itemInput, Prisma.crafting_recipeUncheckedUpdateWithoutRecipe_itemInput>
}

export type crafting_recipeUpdateWithoutRecipe_itemInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeUncheckedUpdateWithoutRecipe_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUncheckedUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeCreateWithoutUser_recipeInput = {
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUncheckedCreateWithoutUser_recipeInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeCreateOrConnectWithoutUser_recipeInput = {
  where: Prisma.crafting_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_recipeInput>
}

export type crafting_recipeUpsertWithoutUser_recipeInput = {
  update: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedUpdateWithoutUser_recipeInput>
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_recipeInput>
  where?: Prisma.crafting_recipeWhereInput
}

export type crafting_recipeUpdateToOneWithWhereWithoutUser_recipeInput = {
  where?: Prisma.crafting_recipeWhereInput
  data: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutUser_recipeInput, Prisma.crafting_recipeUncheckedUpdateWithoutUser_recipeInput>
}

export type crafting_recipeUpdateWithoutUser_recipeInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeUncheckedUpdateWithoutUser_recipeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_crafting_queue?: Prisma.user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeCreateWithoutUser_crafting_queueInput = {
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeUncheckedCreateWithoutUser_crafting_queueInput = {
  id?: number
  cost: number
  craftTime: number
  isUnlockable?: boolean
  requiredSkillType?: $Enums.CraftingSkills | null
  requiredSkillLevel?: number
  item?: Prisma.itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutCrafting_recipeInput
  user_recipe?: Prisma.user_recipeUncheckedCreateNestedManyWithoutCrafting_recipeInput
}

export type crafting_recipeCreateOrConnectWithoutUser_crafting_queueInput = {
  where: Prisma.crafting_recipeWhereUniqueInput
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_crafting_queueInput>
}

export type crafting_recipeUpsertWithoutUser_crafting_queueInput = {
  update: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedUpdateWithoutUser_crafting_queueInput>
  create: Prisma.XOR<Prisma.crafting_recipeCreateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedCreateWithoutUser_crafting_queueInput>
  where?: Prisma.crafting_recipeWhereInput
}

export type crafting_recipeUpdateToOneWithWhereWithoutUser_crafting_queueInput = {
  where?: Prisma.crafting_recipeWhereInput
  data: Prisma.XOR<Prisma.crafting_recipeUpdateWithoutUser_crafting_queueInput, Prisma.crafting_recipeUncheckedUpdateWithoutUser_crafting_queueInput>
}

export type crafting_recipeUpdateWithoutUser_crafting_queueInput = {
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUpdateManyWithoutCrafting_recipeNestedInput
}

export type crafting_recipeUncheckedUpdateWithoutUser_crafting_queueInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  craftTime?: Prisma.IntFieldUpdateOperationsInput | number
  isUnlockable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  requiredSkillType?: Prisma.NullableEnumCraftingSkillsFieldUpdateOperationsInput | $Enums.CraftingSkills | null
  requiredSkillLevel?: Prisma.IntFieldUpdateOperationsInput | number
  item?: Prisma.itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput
  user_recipe?: Prisma.user_recipeUncheckedUpdateManyWithoutCrafting_recipeNestedInput
}


/**
 * Count Type Crafting_recipeCountOutputType
 */

export type Crafting_recipeCountOutputType = {
  item: number
  recipe_item: number
  user_crafting_queue: number
  user_recipe: number
}

export type Crafting_recipeCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Crafting_recipeCountOutputTypeCountItemArgs
  recipe_item?: boolean | Crafting_recipeCountOutputTypeCountRecipe_itemArgs
  user_crafting_queue?: boolean | Crafting_recipeCountOutputTypeCountUser_crafting_queueArgs
  user_recipe?: boolean | Crafting_recipeCountOutputTypeCountUser_recipeArgs
}

/**
 * Crafting_recipeCountOutputType without action
 */
export type Crafting_recipeCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Crafting_recipeCountOutputType
   */
  select?: Prisma.Crafting_recipeCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Crafting_recipeCountOutputType without action
 */
export type Crafting_recipeCountOutputTypeCountItemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.itemWhereInput
}

/**
 * Crafting_recipeCountOutputType without action
 */
export type Crafting_recipeCountOutputTypeCountRecipe_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.recipe_itemWhereInput
}

/**
 * Crafting_recipeCountOutputType without action
 */
export type Crafting_recipeCountOutputTypeCountUser_crafting_queueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_crafting_queueWhereInput
}

/**
 * Crafting_recipeCountOutputType without action
 */
export type Crafting_recipeCountOutputTypeCountUser_recipeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_recipeWhereInput
}


export type crafting_recipeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  cost?: boolean
  craftTime?: boolean
  isUnlockable?: boolean
  requiredSkillType?: boolean
  requiredSkillLevel?: boolean
  item?: boolean | Prisma.crafting_recipe$itemArgs<ExtArgs>
  recipe_item?: boolean | Prisma.crafting_recipe$recipe_itemArgs<ExtArgs>
  user_crafting_queue?: boolean | Prisma.crafting_recipe$user_crafting_queueArgs<ExtArgs>
  user_recipe?: boolean | Prisma.crafting_recipe$user_recipeArgs<ExtArgs>
  _count?: boolean | Prisma.Crafting_recipeCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["crafting_recipe"]>



export type crafting_recipeSelectScalar = {
  id?: boolean
  cost?: boolean
  craftTime?: boolean
  isUnlockable?: boolean
  requiredSkillType?: boolean
  requiredSkillLevel?: boolean
}

export type crafting_recipeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "cost" | "craftTime" | "isUnlockable" | "requiredSkillType" | "requiredSkillLevel", ExtArgs["result"]["crafting_recipe"]>
export type crafting_recipeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Prisma.crafting_recipe$itemArgs<ExtArgs>
  recipe_item?: boolean | Prisma.crafting_recipe$recipe_itemArgs<ExtArgs>
  user_crafting_queue?: boolean | Prisma.crafting_recipe$user_crafting_queueArgs<ExtArgs>
  user_recipe?: boolean | Prisma.crafting_recipe$user_recipeArgs<ExtArgs>
  _count?: boolean | Prisma.Crafting_recipeCountOutputTypeDefaultArgs<ExtArgs>
}

export type $crafting_recipePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "crafting_recipe"
  objects: {
    item: Prisma.$itemPayload<ExtArgs>[]
    recipe_item: Prisma.$recipe_itemPayload<ExtArgs>[]
    user_crafting_queue: Prisma.$user_crafting_queuePayload<ExtArgs>[]
    user_recipe: Prisma.$user_recipePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    cost: number
    craftTime: number
    isUnlockable: boolean
    requiredSkillType: $Enums.CraftingSkills | null
    requiredSkillLevel: number
  }, ExtArgs["result"]["crafting_recipe"]>
  composites: {}
}

export type crafting_recipeGetPayload<S extends boolean | null | undefined | crafting_recipeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload, S>

export type crafting_recipeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<crafting_recipeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Crafting_recipeCountAggregateInputType | true
  }

export interface crafting_recipeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['crafting_recipe'], meta: { name: 'crafting_recipe' } }
  /**
   * Find zero or one Crafting_recipe that matches the filter.
   * @param {crafting_recipeFindUniqueArgs} args - Arguments to find a Crafting_recipe
   * @example
   * // Get one Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends crafting_recipeFindUniqueArgs>(args: Prisma.SelectSubset<T, crafting_recipeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Crafting_recipe that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {crafting_recipeFindUniqueOrThrowArgs} args - Arguments to find a Crafting_recipe
   * @example
   * // Get one Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends crafting_recipeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, crafting_recipeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Crafting_recipe that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeFindFirstArgs} args - Arguments to find a Crafting_recipe
   * @example
   * // Get one Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends crafting_recipeFindFirstArgs>(args?: Prisma.SelectSubset<T, crafting_recipeFindFirstArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Crafting_recipe that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeFindFirstOrThrowArgs} args - Arguments to find a Crafting_recipe
   * @example
   * // Get one Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends crafting_recipeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, crafting_recipeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Crafting_recipes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Crafting_recipes
   * const crafting_recipes = await prisma.crafting_recipe.findMany()
   * 
   * // Get first 10 Crafting_recipes
   * const crafting_recipes = await prisma.crafting_recipe.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const crafting_recipeWithIdOnly = await prisma.crafting_recipe.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends crafting_recipeFindManyArgs>(args?: Prisma.SelectSubset<T, crafting_recipeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Crafting_recipe.
   * @param {crafting_recipeCreateArgs} args - Arguments to create a Crafting_recipe.
   * @example
   * // Create one Crafting_recipe
   * const Crafting_recipe = await prisma.crafting_recipe.create({
   *   data: {
   *     // ... data to create a Crafting_recipe
   *   }
   * })
   * 
   */
  create<T extends crafting_recipeCreateArgs>(args: Prisma.SelectSubset<T, crafting_recipeCreateArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Crafting_recipes.
   * @param {crafting_recipeCreateManyArgs} args - Arguments to create many Crafting_recipes.
   * @example
   * // Create many Crafting_recipes
   * const crafting_recipe = await prisma.crafting_recipe.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends crafting_recipeCreateManyArgs>(args?: Prisma.SelectSubset<T, crafting_recipeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Crafting_recipe.
   * @param {crafting_recipeDeleteArgs} args - Arguments to delete one Crafting_recipe.
   * @example
   * // Delete one Crafting_recipe
   * const Crafting_recipe = await prisma.crafting_recipe.delete({
   *   where: {
   *     // ... filter to delete one Crafting_recipe
   *   }
   * })
   * 
   */
  delete<T extends crafting_recipeDeleteArgs>(args: Prisma.SelectSubset<T, crafting_recipeDeleteArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Crafting_recipe.
   * @param {crafting_recipeUpdateArgs} args - Arguments to update one Crafting_recipe.
   * @example
   * // Update one Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends crafting_recipeUpdateArgs>(args: Prisma.SelectSubset<T, crafting_recipeUpdateArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Crafting_recipes.
   * @param {crafting_recipeDeleteManyArgs} args - Arguments to filter Crafting_recipes to delete.
   * @example
   * // Delete a few Crafting_recipes
   * const { count } = await prisma.crafting_recipe.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends crafting_recipeDeleteManyArgs>(args?: Prisma.SelectSubset<T, crafting_recipeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Crafting_recipes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Crafting_recipes
   * const crafting_recipe = await prisma.crafting_recipe.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends crafting_recipeUpdateManyArgs>(args: Prisma.SelectSubset<T, crafting_recipeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Crafting_recipe.
   * @param {crafting_recipeUpsertArgs} args - Arguments to update or create a Crafting_recipe.
   * @example
   * // Update or create a Crafting_recipe
   * const crafting_recipe = await prisma.crafting_recipe.upsert({
   *   create: {
   *     // ... data to create a Crafting_recipe
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Crafting_recipe we want to update
   *   }
   * })
   */
  upsert<T extends crafting_recipeUpsertArgs>(args: Prisma.SelectSubset<T, crafting_recipeUpsertArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Crafting_recipes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeCountArgs} args - Arguments to filter Crafting_recipes to count.
   * @example
   * // Count the number of Crafting_recipes
   * const count = await prisma.crafting_recipe.count({
   *   where: {
   *     // ... the filter for the Crafting_recipes we want to count
   *   }
   * })
  **/
  count<T extends crafting_recipeCountArgs>(
    args?: Prisma.Subset<T, crafting_recipeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Crafting_recipeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Crafting_recipe.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Crafting_recipeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Crafting_recipeAggregateArgs>(args: Prisma.Subset<T, Crafting_recipeAggregateArgs>): Prisma.PrismaPromise<GetCrafting_recipeAggregateType<T>>

  /**
   * Group by Crafting_recipe.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {crafting_recipeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends crafting_recipeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: crafting_recipeGroupByArgs['orderBy'] }
      : { orderBy?: crafting_recipeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, crafting_recipeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCrafting_recipeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the crafting_recipe model
 */
readonly fields: crafting_recipeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for crafting_recipe.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__crafting_recipeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  item<T extends Prisma.crafting_recipe$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipe$itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  recipe_item<T extends Prisma.crafting_recipe$recipe_itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipe$recipe_itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user_crafting_queue<T extends Prisma.crafting_recipe$user_crafting_queueArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipe$user_crafting_queueArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user_recipe<T extends Prisma.crafting_recipe$user_recipeArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipe$user_recipeArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_recipePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the crafting_recipe model
 */
export interface crafting_recipeFieldRefs {
  readonly id: Prisma.FieldRef<"crafting_recipe", 'Int'>
  readonly cost: Prisma.FieldRef<"crafting_recipe", 'Int'>
  readonly craftTime: Prisma.FieldRef<"crafting_recipe", 'Int'>
  readonly isUnlockable: Prisma.FieldRef<"crafting_recipe", 'Boolean'>
  readonly requiredSkillType: Prisma.FieldRef<"crafting_recipe", 'CraftingSkills'>
  readonly requiredSkillLevel: Prisma.FieldRef<"crafting_recipe", 'Int'>
}
    

// Custom InputTypes
/**
 * crafting_recipe findUnique
 */
export type crafting_recipeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter, which crafting_recipe to fetch.
   */
  where: Prisma.crafting_recipeWhereUniqueInput
}

/**
 * crafting_recipe findUniqueOrThrow
 */
export type crafting_recipeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter, which crafting_recipe to fetch.
   */
  where: Prisma.crafting_recipeWhereUniqueInput
}

/**
 * crafting_recipe findFirst
 */
export type crafting_recipeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter, which crafting_recipe to fetch.
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of crafting_recipes to fetch.
   */
  orderBy?: Prisma.crafting_recipeOrderByWithRelationInput | Prisma.crafting_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for crafting_recipes.
   */
  cursor?: Prisma.crafting_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` crafting_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` crafting_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of crafting_recipes.
   */
  distinct?: Prisma.Crafting_recipeScalarFieldEnum | Prisma.Crafting_recipeScalarFieldEnum[]
}

/**
 * crafting_recipe findFirstOrThrow
 */
export type crafting_recipeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter, which crafting_recipe to fetch.
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of crafting_recipes to fetch.
   */
  orderBy?: Prisma.crafting_recipeOrderByWithRelationInput | Prisma.crafting_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for crafting_recipes.
   */
  cursor?: Prisma.crafting_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` crafting_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` crafting_recipes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of crafting_recipes.
   */
  distinct?: Prisma.Crafting_recipeScalarFieldEnum | Prisma.Crafting_recipeScalarFieldEnum[]
}

/**
 * crafting_recipe findMany
 */
export type crafting_recipeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter, which crafting_recipes to fetch.
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of crafting_recipes to fetch.
   */
  orderBy?: Prisma.crafting_recipeOrderByWithRelationInput | Prisma.crafting_recipeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing crafting_recipes.
   */
  cursor?: Prisma.crafting_recipeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` crafting_recipes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` crafting_recipes.
   */
  skip?: number
  distinct?: Prisma.Crafting_recipeScalarFieldEnum | Prisma.Crafting_recipeScalarFieldEnum[]
}

/**
 * crafting_recipe create
 */
export type crafting_recipeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * The data needed to create a crafting_recipe.
   */
  data: Prisma.XOR<Prisma.crafting_recipeCreateInput, Prisma.crafting_recipeUncheckedCreateInput>
}

/**
 * crafting_recipe createMany
 */
export type crafting_recipeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many crafting_recipes.
   */
  data: Prisma.crafting_recipeCreateManyInput | Prisma.crafting_recipeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * crafting_recipe update
 */
export type crafting_recipeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * The data needed to update a crafting_recipe.
   */
  data: Prisma.XOR<Prisma.crafting_recipeUpdateInput, Prisma.crafting_recipeUncheckedUpdateInput>
  /**
   * Choose, which crafting_recipe to update.
   */
  where: Prisma.crafting_recipeWhereUniqueInput
}

/**
 * crafting_recipe updateMany
 */
export type crafting_recipeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update crafting_recipes.
   */
  data: Prisma.XOR<Prisma.crafting_recipeUpdateManyMutationInput, Prisma.crafting_recipeUncheckedUpdateManyInput>
  /**
   * Filter which crafting_recipes to update
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * Limit how many crafting_recipes to update.
   */
  limit?: number
}

/**
 * crafting_recipe upsert
 */
export type crafting_recipeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * The filter to search for the crafting_recipe to update in case it exists.
   */
  where: Prisma.crafting_recipeWhereUniqueInput
  /**
   * In case the crafting_recipe found by the `where` argument doesn't exist, create a new crafting_recipe with this data.
   */
  create: Prisma.XOR<Prisma.crafting_recipeCreateInput, Prisma.crafting_recipeUncheckedCreateInput>
  /**
   * In case the crafting_recipe was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.crafting_recipeUpdateInput, Prisma.crafting_recipeUncheckedUpdateInput>
}

/**
 * crafting_recipe delete
 */
export type crafting_recipeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  /**
   * Filter which crafting_recipe to delete.
   */
  where: Prisma.crafting_recipeWhereUniqueInput
}

/**
 * crafting_recipe deleteMany
 */
export type crafting_recipeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which crafting_recipes to delete
   */
  where?: Prisma.crafting_recipeWhereInput
  /**
   * Limit how many crafting_recipes to delete.
   */
  limit?: number
}

/**
 * crafting_recipe.item
 */
export type crafting_recipe$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  cursor?: Prisma.itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ItemScalarFieldEnum | Prisma.ItemScalarFieldEnum[]
}

/**
 * crafting_recipe.recipe_item
 */
export type crafting_recipe$recipe_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  where?: Prisma.recipe_itemWhereInput
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  cursor?: Prisma.recipe_itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Recipe_itemScalarFieldEnum | Prisma.Recipe_itemScalarFieldEnum[]
}

/**
 * crafting_recipe.user_crafting_queue
 */
export type crafting_recipe$user_crafting_queueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  where?: Prisma.user_crafting_queueWhereInput
  orderBy?: Prisma.user_crafting_queueOrderByWithRelationInput | Prisma.user_crafting_queueOrderByWithRelationInput[]
  cursor?: Prisma.user_crafting_queueWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_crafting_queueScalarFieldEnum | Prisma.User_crafting_queueScalarFieldEnum[]
}

/**
 * crafting_recipe.user_recipe
 */
export type crafting_recipe$user_recipeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_recipe
   */
  select?: Prisma.user_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_recipe
   */
  omit?: Prisma.user_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_recipeInclude<ExtArgs> | null
  where?: Prisma.user_recipeWhereInput
  orderBy?: Prisma.user_recipeOrderByWithRelationInput | Prisma.user_recipeOrderByWithRelationInput[]
  cursor?: Prisma.user_recipeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_recipeScalarFieldEnum | Prisma.User_recipeScalarFieldEnum[]
}

/**
 * crafting_recipe without action
 */
export type crafting_recipeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
}
