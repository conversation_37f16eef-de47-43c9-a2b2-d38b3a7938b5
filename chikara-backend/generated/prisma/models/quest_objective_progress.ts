
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `quest_objective_progress` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model quest_objective_progress
 * 
 */
export type quest_objective_progressModel = runtime.Types.Result.DefaultSelection<Prisma.$quest_objective_progressPayload>

export type AggregateQuest_objective_progress = {
  _count: Quest_objective_progressCountAggregateOutputType | null
  _avg: Quest_objective_progressAvgAggregateOutputType | null
  _sum: Quest_objective_progressSumAggregateOutputType | null
  _min: Quest_objective_progressMinAggregateOutputType | null
  _max: Quest_objective_progressMaxAggregateOutputType | null
}

export type Quest_objective_progressAvgAggregateOutputType = {
  id: number | null
  count: number | null
  userId: number | null
  questObjectiveId: number | null
}

export type Quest_objective_progressSumAggregateOutputType = {
  id: number | null
  count: number | null
  userId: number | null
  questObjectiveId: number | null
}

export type Quest_objective_progressMinAggregateOutputType = {
  id: number | null
  count: number | null
  status: $Enums.QuestProgressStatus | null
  userId: number | null
  createdAt: Date | null
  updatedAt: Date | null
  questObjectiveId: number | null
}

export type Quest_objective_progressMaxAggregateOutputType = {
  id: number | null
  count: number | null
  status: $Enums.QuestProgressStatus | null
  userId: number | null
  createdAt: Date | null
  updatedAt: Date | null
  questObjectiveId: number | null
}

export type Quest_objective_progressCountAggregateOutputType = {
  id: number
  count: number
  status: number
  userId: number
  createdAt: number
  updatedAt: number
  questObjectiveId: number
  _all: number
}


export type Quest_objective_progressAvgAggregateInputType = {
  id?: true
  count?: true
  userId?: true
  questObjectiveId?: true
}

export type Quest_objective_progressSumAggregateInputType = {
  id?: true
  count?: true
  userId?: true
  questObjectiveId?: true
}

export type Quest_objective_progressMinAggregateInputType = {
  id?: true
  count?: true
  status?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  questObjectiveId?: true
}

export type Quest_objective_progressMaxAggregateInputType = {
  id?: true
  count?: true
  status?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  questObjectiveId?: true
}

export type Quest_objective_progressCountAggregateInputType = {
  id?: true
  count?: true
  status?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  questObjectiveId?: true
  _all?: true
}

export type Quest_objective_progressAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_objective_progress to aggregate.
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objective_progresses to fetch.
   */
  orderBy?: Prisma.quest_objective_progressOrderByWithRelationInput | Prisma.quest_objective_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.quest_objective_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objective_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objective_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned quest_objective_progresses
  **/
  _count?: true | Quest_objective_progressCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Quest_objective_progressAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Quest_objective_progressSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Quest_objective_progressMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Quest_objective_progressMaxAggregateInputType
}

export type GetQuest_objective_progressAggregateType<T extends Quest_objective_progressAggregateArgs> = {
      [P in keyof T & keyof AggregateQuest_objective_progress]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateQuest_objective_progress[P]>
    : Prisma.GetScalarType<T[P], AggregateQuest_objective_progress[P]>
}




export type quest_objective_progressGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objective_progressWhereInput
  orderBy?: Prisma.quest_objective_progressOrderByWithAggregationInput | Prisma.quest_objective_progressOrderByWithAggregationInput[]
  by: Prisma.Quest_objective_progressScalarFieldEnum[] | Prisma.Quest_objective_progressScalarFieldEnum
  having?: Prisma.quest_objective_progressScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Quest_objective_progressCountAggregateInputType | true
  _avg?: Quest_objective_progressAvgAggregateInputType
  _sum?: Quest_objective_progressSumAggregateInputType
  _min?: Quest_objective_progressMinAggregateInputType
  _max?: Quest_objective_progressMaxAggregateInputType
}

export type Quest_objective_progressGroupByOutputType = {
  id: number
  count: number
  status: $Enums.QuestProgressStatus
  userId: number
  createdAt: Date
  updatedAt: Date
  questObjectiveId: number
  _count: Quest_objective_progressCountAggregateOutputType | null
  _avg: Quest_objective_progressAvgAggregateOutputType | null
  _sum: Quest_objective_progressSumAggregateOutputType | null
  _min: Quest_objective_progressMinAggregateOutputType | null
  _max: Quest_objective_progressMaxAggregateOutputType | null
}

type GetQuest_objective_progressGroupByPayload<T extends quest_objective_progressGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Quest_objective_progressGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Quest_objective_progressGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Quest_objective_progressGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Quest_objective_progressGroupByOutputType[P]>
      }
    >
  >



export type quest_objective_progressWhereInput = {
  AND?: Prisma.quest_objective_progressWhereInput | Prisma.quest_objective_progressWhereInput[]
  OR?: Prisma.quest_objective_progressWhereInput[]
  NOT?: Prisma.quest_objective_progressWhereInput | Prisma.quest_objective_progressWhereInput[]
  id?: Prisma.IntFilter<"quest_objective_progress"> | number
  count?: Prisma.IntFilter<"quest_objective_progress"> | number
  status?: Prisma.EnumQuestProgressStatusFilter<"quest_objective_progress"> | $Enums.QuestProgressStatus
  userId?: Prisma.IntFilter<"quest_objective_progress"> | number
  createdAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  questObjectiveId?: Prisma.IntFilter<"quest_objective_progress"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  quest_objective?: Prisma.XOR<Prisma.Quest_objectiveScalarRelationFilter, Prisma.quest_objectiveWhereInput>
}

export type quest_objective_progressOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  status?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  quest_objective?: Prisma.quest_objectiveOrderByWithRelationInput
}

export type quest_objective_progressWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  userId_questObjectiveId?: Prisma.quest_objective_progressUserIdQuestObjectiveIdCompoundUniqueInput
  AND?: Prisma.quest_objective_progressWhereInput | Prisma.quest_objective_progressWhereInput[]
  OR?: Prisma.quest_objective_progressWhereInput[]
  NOT?: Prisma.quest_objective_progressWhereInput | Prisma.quest_objective_progressWhereInput[]
  count?: Prisma.IntFilter<"quest_objective_progress"> | number
  status?: Prisma.EnumQuestProgressStatusFilter<"quest_objective_progress"> | $Enums.QuestProgressStatus
  userId?: Prisma.IntFilter<"quest_objective_progress"> | number
  createdAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  questObjectiveId?: Prisma.IntFilter<"quest_objective_progress"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  quest_objective?: Prisma.XOR<Prisma.Quest_objectiveScalarRelationFilter, Prisma.quest_objectiveWhereInput>
}, "id" | "userId_questObjectiveId">

export type quest_objective_progressOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  status?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
  _count?: Prisma.quest_objective_progressCountOrderByAggregateInput
  _avg?: Prisma.quest_objective_progressAvgOrderByAggregateInput
  _max?: Prisma.quest_objective_progressMaxOrderByAggregateInput
  _min?: Prisma.quest_objective_progressMinOrderByAggregateInput
  _sum?: Prisma.quest_objective_progressSumOrderByAggregateInput
}

export type quest_objective_progressScalarWhereWithAggregatesInput = {
  AND?: Prisma.quest_objective_progressScalarWhereWithAggregatesInput | Prisma.quest_objective_progressScalarWhereWithAggregatesInput[]
  OR?: Prisma.quest_objective_progressScalarWhereWithAggregatesInput[]
  NOT?: Prisma.quest_objective_progressScalarWhereWithAggregatesInput | Prisma.quest_objective_progressScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"quest_objective_progress"> | number
  count?: Prisma.IntWithAggregatesFilter<"quest_objective_progress"> | number
  status?: Prisma.EnumQuestProgressStatusWithAggregatesFilter<"quest_objective_progress"> | $Enums.QuestProgressStatus
  userId?: Prisma.IntWithAggregatesFilter<"quest_objective_progress"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"quest_objective_progress"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"quest_objective_progress"> | Date | string
  questObjectiveId?: Prisma.IntWithAggregatesFilter<"quest_objective_progress"> | number
}

export type quest_objective_progressCreateInput = {
  count?: number
  status?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutQuest_objective_progressInput
  quest_objective: Prisma.quest_objectiveCreateNestedOneWithoutQuest_objective_progressInput
}

export type quest_objective_progressUncheckedCreateInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
  questObjectiveId: number
}

export type quest_objective_progressUpdateInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutQuest_objective_progressNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateOneRequiredWithoutQuest_objective_progressNestedInput
}

export type quest_objective_progressUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questObjectiveId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type quest_objective_progressCreateManyInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
  questObjectiveId: number
}

export type quest_objective_progressUpdateManyMutationInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type quest_objective_progressUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questObjectiveId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type Quest_objective_progressListRelationFilter = {
  every?: Prisma.quest_objective_progressWhereInput
  some?: Prisma.quest_objective_progressWhereInput
  none?: Prisma.quest_objective_progressWhereInput
}

export type quest_objective_progressOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type quest_objective_progressUserIdQuestObjectiveIdCompoundUniqueInput = {
  userId: number
  questObjectiveId: number
}

export type quest_objective_progressCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  status?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
}

export type quest_objective_progressAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
}

export type quest_objective_progressMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  status?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
}

export type quest_objective_progressMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  status?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
}

export type quest_objective_progressSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  questObjectiveId?: Prisma.SortOrder
}

export type quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput> | Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput | Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput[]
  createMany?: Prisma.quest_objective_progressCreateManyQuest_objectiveInputEnvelope
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
}

export type quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput> | Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput | Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput[]
  createMany?: Prisma.quest_objective_progressCreateManyQuest_objectiveInputEnvelope
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
}

export type quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput> | Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput | Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput[]
  upsert?: Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutQuest_objectiveInput[]
  createMany?: Prisma.quest_objective_progressCreateManyQuest_objectiveInputEnvelope
  set?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  delete?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  update?: Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutQuest_objectiveInput[]
  updateMany?: Prisma.quest_objective_progressUpdateManyWithWhereWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpdateManyWithWhereWithoutQuest_objectiveInput[]
  deleteMany?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
}

export type quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput> | Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput | Prisma.quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput[]
  upsert?: Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutQuest_objectiveInput[]
  createMany?: Prisma.quest_objective_progressCreateManyQuest_objectiveInputEnvelope
  set?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  delete?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  update?: Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutQuest_objectiveInput[]
  updateMany?: Prisma.quest_objective_progressUpdateManyWithWhereWithoutQuest_objectiveInput | Prisma.quest_objective_progressUpdateManyWithWhereWithoutQuest_objectiveInput[]
  deleteMany?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
}

export type quest_objective_progressCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput> | Prisma.quest_objective_progressCreateWithoutUserInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutUserInput | Prisma.quest_objective_progressCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.quest_objective_progressCreateManyUserInputEnvelope
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
}

export type quest_objective_progressUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput> | Prisma.quest_objective_progressCreateWithoutUserInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutUserInput | Prisma.quest_objective_progressCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.quest_objective_progressCreateManyUserInputEnvelope
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
}

export type quest_objective_progressUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput> | Prisma.quest_objective_progressCreateWithoutUserInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutUserInput | Prisma.quest_objective_progressCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutUserInput | Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.quest_objective_progressCreateManyUserInputEnvelope
  set?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  delete?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  update?: Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutUserInput | Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.quest_objective_progressUpdateManyWithWhereWithoutUserInput | Prisma.quest_objective_progressUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
}

export type quest_objective_progressUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput> | Prisma.quest_objective_progressCreateWithoutUserInput[] | Prisma.quest_objective_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_objective_progressCreateOrConnectWithoutUserInput | Prisma.quest_objective_progressCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutUserInput | Prisma.quest_objective_progressUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.quest_objective_progressCreateManyUserInputEnvelope
  set?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  delete?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  connect?: Prisma.quest_objective_progressWhereUniqueInput | Prisma.quest_objective_progressWhereUniqueInput[]
  update?: Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutUserInput | Prisma.quest_objective_progressUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.quest_objective_progressUpdateManyWithWhereWithoutUserInput | Prisma.quest_objective_progressUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
}

export type quest_objective_progressCreateWithoutQuest_objectiveInput = {
  count?: number
  status?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutQuest_objective_progressInput
}

export type quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type quest_objective_progressCreateOrConnectWithoutQuest_objectiveInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput>
}

export type quest_objective_progressCreateManyQuest_objectiveInputEnvelope = {
  data: Prisma.quest_objective_progressCreateManyQuest_objectiveInput | Prisma.quest_objective_progressCreateManyQuest_objectiveInput[]
  skipDuplicates?: boolean
}

export type quest_objective_progressUpsertWithWhereUniqueWithoutQuest_objectiveInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_objective_progressUpdateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedUpdateWithoutQuest_objectiveInput>
  create: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedCreateWithoutQuest_objectiveInput>
}

export type quest_objective_progressUpdateWithWhereUniqueWithoutQuest_objectiveInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateWithoutQuest_objectiveInput, Prisma.quest_objective_progressUncheckedUpdateWithoutQuest_objectiveInput>
}

export type quest_objective_progressUpdateManyWithWhereWithoutQuest_objectiveInput = {
  where: Prisma.quest_objective_progressScalarWhereInput
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateManyMutationInput, Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveInput>
}

export type quest_objective_progressScalarWhereInput = {
  AND?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
  OR?: Prisma.quest_objective_progressScalarWhereInput[]
  NOT?: Prisma.quest_objective_progressScalarWhereInput | Prisma.quest_objective_progressScalarWhereInput[]
  id?: Prisma.IntFilter<"quest_objective_progress"> | number
  count?: Prisma.IntFilter<"quest_objective_progress"> | number
  status?: Prisma.EnumQuestProgressStatusFilter<"quest_objective_progress"> | $Enums.QuestProgressStatus
  userId?: Prisma.IntFilter<"quest_objective_progress"> | number
  createdAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_objective_progress"> | Date | string
  questObjectiveId?: Prisma.IntFilter<"quest_objective_progress"> | number
}

export type quest_objective_progressCreateWithoutUserInput = {
  count?: number
  status?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  quest_objective: Prisma.quest_objectiveCreateNestedOneWithoutQuest_objective_progressInput
}

export type quest_objective_progressUncheckedCreateWithoutUserInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questObjectiveId: number
}

export type quest_objective_progressCreateOrConnectWithoutUserInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput>
}

export type quest_objective_progressCreateManyUserInputEnvelope = {
  data: Prisma.quest_objective_progressCreateManyUserInput | Prisma.quest_objective_progressCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type quest_objective_progressUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_objective_progressUpdateWithoutUserInput, Prisma.quest_objective_progressUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.quest_objective_progressCreateWithoutUserInput, Prisma.quest_objective_progressUncheckedCreateWithoutUserInput>
}

export type quest_objective_progressUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.quest_objective_progressWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateWithoutUserInput, Prisma.quest_objective_progressUncheckedUpdateWithoutUserInput>
}

export type quest_objective_progressUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.quest_objective_progressScalarWhereInput
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateManyMutationInput, Prisma.quest_objective_progressUncheckedUpdateManyWithoutUserInput>
}

export type quest_objective_progressCreateManyQuest_objectiveInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  userId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type quest_objective_progressUpdateWithoutQuest_objectiveInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutQuest_objective_progressNestedInput
}

export type quest_objective_progressUncheckedUpdateWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type quest_objective_progressCreateManyUserInput = {
  id?: number
  count?: number
  status?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questObjectiveId: number
}

export type quest_objective_progressUpdateWithoutUserInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest_objective?: Prisma.quest_objectiveUpdateOneRequiredWithoutQuest_objective_progressNestedInput
}

export type quest_objective_progressUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questObjectiveId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type quest_objective_progressUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questObjectiveId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type quest_objective_progressSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  count?: boolean
  status?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questObjectiveId?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  quest_objective?: boolean | Prisma.quest_objectiveDefaultArgs<ExtArgs>
}, ExtArgs["result"]["quest_objective_progress"]>



export type quest_objective_progressSelectScalar = {
  id?: boolean
  count?: boolean
  status?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questObjectiveId?: boolean
}

export type quest_objective_progressOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "count" | "status" | "userId" | "createdAt" | "updatedAt" | "questObjectiveId", ExtArgs["result"]["quest_objective_progress"]>
export type quest_objective_progressInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  quest_objective?: boolean | Prisma.quest_objectiveDefaultArgs<ExtArgs>
}

export type $quest_objective_progressPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "quest_objective_progress"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    quest_objective: Prisma.$quest_objectivePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    count: number
    status: $Enums.QuestProgressStatus
    userId: number
    createdAt: Date
    updatedAt: Date
    questObjectiveId: number
  }, ExtArgs["result"]["quest_objective_progress"]>
  composites: {}
}

export type quest_objective_progressGetPayload<S extends boolean | null | undefined | quest_objective_progressDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload, S>

export type quest_objective_progressCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<quest_objective_progressFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Quest_objective_progressCountAggregateInputType | true
  }

export interface quest_objective_progressDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['quest_objective_progress'], meta: { name: 'quest_objective_progress' } }
  /**
   * Find zero or one Quest_objective_progress that matches the filter.
   * @param {quest_objective_progressFindUniqueArgs} args - Arguments to find a Quest_objective_progress
   * @example
   * // Get one Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends quest_objective_progressFindUniqueArgs>(args: Prisma.SelectSubset<T, quest_objective_progressFindUniqueArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Quest_objective_progress that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {quest_objective_progressFindUniqueOrThrowArgs} args - Arguments to find a Quest_objective_progress
   * @example
   * // Get one Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends quest_objective_progressFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, quest_objective_progressFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_objective_progress that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressFindFirstArgs} args - Arguments to find a Quest_objective_progress
   * @example
   * // Get one Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends quest_objective_progressFindFirstArgs>(args?: Prisma.SelectSubset<T, quest_objective_progressFindFirstArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_objective_progress that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressFindFirstOrThrowArgs} args - Arguments to find a Quest_objective_progress
   * @example
   * // Get one Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends quest_objective_progressFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, quest_objective_progressFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Quest_objective_progresses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Quest_objective_progresses
   * const quest_objective_progresses = await prisma.quest_objective_progress.findMany()
   * 
   * // Get first 10 Quest_objective_progresses
   * const quest_objective_progresses = await prisma.quest_objective_progress.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const quest_objective_progressWithIdOnly = await prisma.quest_objective_progress.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends quest_objective_progressFindManyArgs>(args?: Prisma.SelectSubset<T, quest_objective_progressFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Quest_objective_progress.
   * @param {quest_objective_progressCreateArgs} args - Arguments to create a Quest_objective_progress.
   * @example
   * // Create one Quest_objective_progress
   * const Quest_objective_progress = await prisma.quest_objective_progress.create({
   *   data: {
   *     // ... data to create a Quest_objective_progress
   *   }
   * })
   * 
   */
  create<T extends quest_objective_progressCreateArgs>(args: Prisma.SelectSubset<T, quest_objective_progressCreateArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Quest_objective_progresses.
   * @param {quest_objective_progressCreateManyArgs} args - Arguments to create many Quest_objective_progresses.
   * @example
   * // Create many Quest_objective_progresses
   * const quest_objective_progress = await prisma.quest_objective_progress.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends quest_objective_progressCreateManyArgs>(args?: Prisma.SelectSubset<T, quest_objective_progressCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Quest_objective_progress.
   * @param {quest_objective_progressDeleteArgs} args - Arguments to delete one Quest_objective_progress.
   * @example
   * // Delete one Quest_objective_progress
   * const Quest_objective_progress = await prisma.quest_objective_progress.delete({
   *   where: {
   *     // ... filter to delete one Quest_objective_progress
   *   }
   * })
   * 
   */
  delete<T extends quest_objective_progressDeleteArgs>(args: Prisma.SelectSubset<T, quest_objective_progressDeleteArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Quest_objective_progress.
   * @param {quest_objective_progressUpdateArgs} args - Arguments to update one Quest_objective_progress.
   * @example
   * // Update one Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends quest_objective_progressUpdateArgs>(args: Prisma.SelectSubset<T, quest_objective_progressUpdateArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Quest_objective_progresses.
   * @param {quest_objective_progressDeleteManyArgs} args - Arguments to filter Quest_objective_progresses to delete.
   * @example
   * // Delete a few Quest_objective_progresses
   * const { count } = await prisma.quest_objective_progress.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends quest_objective_progressDeleteManyArgs>(args?: Prisma.SelectSubset<T, quest_objective_progressDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Quest_objective_progresses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Quest_objective_progresses
   * const quest_objective_progress = await prisma.quest_objective_progress.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends quest_objective_progressUpdateManyArgs>(args: Prisma.SelectSubset<T, quest_objective_progressUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Quest_objective_progress.
   * @param {quest_objective_progressUpsertArgs} args - Arguments to update or create a Quest_objective_progress.
   * @example
   * // Update or create a Quest_objective_progress
   * const quest_objective_progress = await prisma.quest_objective_progress.upsert({
   *   create: {
   *     // ... data to create a Quest_objective_progress
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Quest_objective_progress we want to update
   *   }
   * })
   */
  upsert<T extends quest_objective_progressUpsertArgs>(args: Prisma.SelectSubset<T, quest_objective_progressUpsertArgs<ExtArgs>>): Prisma.Prisma__quest_objective_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Quest_objective_progresses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressCountArgs} args - Arguments to filter Quest_objective_progresses to count.
   * @example
   * // Count the number of Quest_objective_progresses
   * const count = await prisma.quest_objective_progress.count({
   *   where: {
   *     // ... the filter for the Quest_objective_progresses we want to count
   *   }
   * })
  **/
  count<T extends quest_objective_progressCountArgs>(
    args?: Prisma.Subset<T, quest_objective_progressCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Quest_objective_progressCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Quest_objective_progress.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Quest_objective_progressAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Quest_objective_progressAggregateArgs>(args: Prisma.Subset<T, Quest_objective_progressAggregateArgs>): Prisma.PrismaPromise<GetQuest_objective_progressAggregateType<T>>

  /**
   * Group by Quest_objective_progress.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objective_progressGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends quest_objective_progressGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: quest_objective_progressGroupByArgs['orderBy'] }
      : { orderBy?: quest_objective_progressGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, quest_objective_progressGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQuest_objective_progressGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the quest_objective_progress model
 */
readonly fields: quest_objective_progressFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for quest_objective_progress.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__quest_objective_progressClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  quest_objective<T extends Prisma.quest_objectiveDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objectiveDefaultArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the quest_objective_progress model
 */
export interface quest_objective_progressFieldRefs {
  readonly id: Prisma.FieldRef<"quest_objective_progress", 'Int'>
  readonly count: Prisma.FieldRef<"quest_objective_progress", 'Int'>
  readonly status: Prisma.FieldRef<"quest_objective_progress", 'QuestProgressStatus'>
  readonly userId: Prisma.FieldRef<"quest_objective_progress", 'Int'>
  readonly createdAt: Prisma.FieldRef<"quest_objective_progress", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"quest_objective_progress", 'DateTime'>
  readonly questObjectiveId: Prisma.FieldRef<"quest_objective_progress", 'Int'>
}
    

// Custom InputTypes
/**
 * quest_objective_progress findUnique
 */
export type quest_objective_progressFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective_progress to fetch.
   */
  where: Prisma.quest_objective_progressWhereUniqueInput
}

/**
 * quest_objective_progress findUniqueOrThrow
 */
export type quest_objective_progressFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective_progress to fetch.
   */
  where: Prisma.quest_objective_progressWhereUniqueInput
}

/**
 * quest_objective_progress findFirst
 */
export type quest_objective_progressFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective_progress to fetch.
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objective_progresses to fetch.
   */
  orderBy?: Prisma.quest_objective_progressOrderByWithRelationInput | Prisma.quest_objective_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_objective_progresses.
   */
  cursor?: Prisma.quest_objective_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objective_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objective_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_objective_progresses.
   */
  distinct?: Prisma.Quest_objective_progressScalarFieldEnum | Prisma.Quest_objective_progressScalarFieldEnum[]
}

/**
 * quest_objective_progress findFirstOrThrow
 */
export type quest_objective_progressFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective_progress to fetch.
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objective_progresses to fetch.
   */
  orderBy?: Prisma.quest_objective_progressOrderByWithRelationInput | Prisma.quest_objective_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_objective_progresses.
   */
  cursor?: Prisma.quest_objective_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objective_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objective_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_objective_progresses.
   */
  distinct?: Prisma.Quest_objective_progressScalarFieldEnum | Prisma.Quest_objective_progressScalarFieldEnum[]
}

/**
 * quest_objective_progress findMany
 */
export type quest_objective_progressFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective_progresses to fetch.
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objective_progresses to fetch.
   */
  orderBy?: Prisma.quest_objective_progressOrderByWithRelationInput | Prisma.quest_objective_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing quest_objective_progresses.
   */
  cursor?: Prisma.quest_objective_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objective_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objective_progresses.
   */
  skip?: number
  distinct?: Prisma.Quest_objective_progressScalarFieldEnum | Prisma.Quest_objective_progressScalarFieldEnum[]
}

/**
 * quest_objective_progress create
 */
export type quest_objective_progressCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * The data needed to create a quest_objective_progress.
   */
  data: Prisma.XOR<Prisma.quest_objective_progressCreateInput, Prisma.quest_objective_progressUncheckedCreateInput>
}

/**
 * quest_objective_progress createMany
 */
export type quest_objective_progressCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many quest_objective_progresses.
   */
  data: Prisma.quest_objective_progressCreateManyInput | Prisma.quest_objective_progressCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * quest_objective_progress update
 */
export type quest_objective_progressUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * The data needed to update a quest_objective_progress.
   */
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateInput, Prisma.quest_objective_progressUncheckedUpdateInput>
  /**
   * Choose, which quest_objective_progress to update.
   */
  where: Prisma.quest_objective_progressWhereUniqueInput
}

/**
 * quest_objective_progress updateMany
 */
export type quest_objective_progressUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update quest_objective_progresses.
   */
  data: Prisma.XOR<Prisma.quest_objective_progressUpdateManyMutationInput, Prisma.quest_objective_progressUncheckedUpdateManyInput>
  /**
   * Filter which quest_objective_progresses to update
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * Limit how many quest_objective_progresses to update.
   */
  limit?: number
}

/**
 * quest_objective_progress upsert
 */
export type quest_objective_progressUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * The filter to search for the quest_objective_progress to update in case it exists.
   */
  where: Prisma.quest_objective_progressWhereUniqueInput
  /**
   * In case the quest_objective_progress found by the `where` argument doesn't exist, create a new quest_objective_progress with this data.
   */
  create: Prisma.XOR<Prisma.quest_objective_progressCreateInput, Prisma.quest_objective_progressUncheckedCreateInput>
  /**
   * In case the quest_objective_progress was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.quest_objective_progressUpdateInput, Prisma.quest_objective_progressUncheckedUpdateInput>
}

/**
 * quest_objective_progress delete
 */
export type quest_objective_progressDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  /**
   * Filter which quest_objective_progress to delete.
   */
  where: Prisma.quest_objective_progressWhereUniqueInput
}

/**
 * quest_objective_progress deleteMany
 */
export type quest_objective_progressDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_objective_progresses to delete
   */
  where?: Prisma.quest_objective_progressWhereInput
  /**
   * Limit how many quest_objective_progresses to delete.
   */
  limit?: number
}

/**
 * quest_objective_progress without action
 */
export type quest_objective_progressDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
}
