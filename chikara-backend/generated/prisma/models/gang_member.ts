
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `gang_member` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model gang_member
 * 
 */
export type gang_memberModel = runtime.Types.Result.DefaultSelection<Prisma.$gang_memberPayload>

export type AggregateGang_member = {
  _count: Gang_memberCountAggregateOutputType | null
  _avg: Gang_memberAvgAggregateOutputType | null
  _sum: Gang_memberSumAggregateOutputType | null
  _min: Gang_memberMinAggregateOutputType | null
  _max: Gang_memberMaxAggregateOutputType | null
}

export type Gang_memberAvgAggregateOutputType = {
  id: number | null
  rank: number | null
  payoutShare: number | null
  weeklyMaterials: number | null
  weeklyEssence: number | null
  weeklyTools: number | null
  weeklyRespect: number | null
  totalContribution: number | null
  gangId: number | null
  userId: number | null
}

export type Gang_memberSumAggregateOutputType = {
  id: number | null
  rank: number | null
  payoutShare: number | null
  weeklyMaterials: number | null
  weeklyEssence: number | null
  weeklyTools: number | null
  weeklyRespect: number | null
  totalContribution: number | null
  gangId: number | null
  userId: number | null
}

export type Gang_memberMinAggregateOutputType = {
  id: number | null
  rank: number | null
  payoutShare: number | null
  weeklyMaterials: number | null
  weeklyEssence: number | null
  weeklyTools: number | null
  weeklyRespect: number | null
  totalContribution: number | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  userId: number | null
}

export type Gang_memberMaxAggregateOutputType = {
  id: number | null
  rank: number | null
  payoutShare: number | null
  weeklyMaterials: number | null
  weeklyEssence: number | null
  weeklyTools: number | null
  weeklyRespect: number | null
  totalContribution: number | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  userId: number | null
}

export type Gang_memberCountAggregateOutputType = {
  id: number
  rank: number
  payoutShare: number
  weeklyMaterials: number
  weeklyEssence: number
  weeklyTools: number
  weeklyRespect: number
  totalContribution: number
  createdAt: number
  updatedAt: number
  gangId: number
  userId: number
  _all: number
}


export type Gang_memberAvgAggregateInputType = {
  id?: true
  rank?: true
  payoutShare?: true
  weeklyMaterials?: true
  weeklyEssence?: true
  weeklyTools?: true
  weeklyRespect?: true
  totalContribution?: true
  gangId?: true
  userId?: true
}

export type Gang_memberSumAggregateInputType = {
  id?: true
  rank?: true
  payoutShare?: true
  weeklyMaterials?: true
  weeklyEssence?: true
  weeklyTools?: true
  weeklyRespect?: true
  totalContribution?: true
  gangId?: true
  userId?: true
}

export type Gang_memberMinAggregateInputType = {
  id?: true
  rank?: true
  payoutShare?: true
  weeklyMaterials?: true
  weeklyEssence?: true
  weeklyTools?: true
  weeklyRespect?: true
  totalContribution?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  userId?: true
}

export type Gang_memberMaxAggregateInputType = {
  id?: true
  rank?: true
  payoutShare?: true
  weeklyMaterials?: true
  weeklyEssence?: true
  weeklyTools?: true
  weeklyRespect?: true
  totalContribution?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  userId?: true
}

export type Gang_memberCountAggregateInputType = {
  id?: true
  rank?: true
  payoutShare?: true
  weeklyMaterials?: true
  weeklyEssence?: true
  weeklyTools?: true
  weeklyRespect?: true
  totalContribution?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  userId?: true
  _all?: true
}

export type Gang_memberAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_member to aggregate.
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_members to fetch.
   */
  orderBy?: Prisma.gang_memberOrderByWithRelationInput | Prisma.gang_memberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.gang_memberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned gang_members
  **/
  _count?: true | Gang_memberCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Gang_memberAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Gang_memberSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Gang_memberMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Gang_memberMaxAggregateInputType
}

export type GetGang_memberAggregateType<T extends Gang_memberAggregateArgs> = {
      [P in keyof T & keyof AggregateGang_member]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGang_member[P]>
    : Prisma.GetScalarType<T[P], AggregateGang_member[P]>
}




export type gang_memberGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_memberWhereInput
  orderBy?: Prisma.gang_memberOrderByWithAggregationInput | Prisma.gang_memberOrderByWithAggregationInput[]
  by: Prisma.Gang_memberScalarFieldEnum[] | Prisma.Gang_memberScalarFieldEnum
  having?: Prisma.gang_memberScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Gang_memberCountAggregateInputType | true
  _avg?: Gang_memberAvgAggregateInputType
  _sum?: Gang_memberSumAggregateInputType
  _min?: Gang_memberMinAggregateInputType
  _max?: Gang_memberMaxAggregateInputType
}

export type Gang_memberGroupByOutputType = {
  id: number
  rank: number
  payoutShare: number
  weeklyMaterials: number | null
  weeklyEssence: number | null
  weeklyTools: number | null
  weeklyRespect: number | null
  totalContribution: number | null
  createdAt: Date
  updatedAt: Date
  gangId: number | null
  userId: number | null
  _count: Gang_memberCountAggregateOutputType | null
  _avg: Gang_memberAvgAggregateOutputType | null
  _sum: Gang_memberSumAggregateOutputType | null
  _min: Gang_memberMinAggregateOutputType | null
  _max: Gang_memberMaxAggregateOutputType | null
}

type GetGang_memberGroupByPayload<T extends gang_memberGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Gang_memberGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Gang_memberGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Gang_memberGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Gang_memberGroupByOutputType[P]>
      }
    >
  >



export type gang_memberWhereInput = {
  AND?: Prisma.gang_memberWhereInput | Prisma.gang_memberWhereInput[]
  OR?: Prisma.gang_memberWhereInput[]
  NOT?: Prisma.gang_memberWhereInput | Prisma.gang_memberWhereInput[]
  id?: Prisma.IntFilter<"gang_member"> | number
  rank?: Prisma.IntFilter<"gang_member"> | number
  payoutShare?: Prisma.FloatFilter<"gang_member"> | number
  weeklyMaterials?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyEssence?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyTools?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang_member"> | number | null
  totalContribution?: Prisma.IntNullableFilter<"gang_member"> | number | null
  createdAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_member"> | number | null
  userId?: Prisma.IntNullableFilter<"gang_member"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type gang_memberOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyTools?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  totalContribution?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  gang?: Prisma.gangOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type gang_memberWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.gang_memberWhereInput | Prisma.gang_memberWhereInput[]
  OR?: Prisma.gang_memberWhereInput[]
  NOT?: Prisma.gang_memberWhereInput | Prisma.gang_memberWhereInput[]
  rank?: Prisma.IntFilter<"gang_member"> | number
  payoutShare?: Prisma.FloatFilter<"gang_member"> | number
  weeklyMaterials?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyEssence?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyTools?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang_member"> | number | null
  totalContribution?: Prisma.IntNullableFilter<"gang_member"> | number | null
  createdAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_member"> | number | null
  userId?: Prisma.IntNullableFilter<"gang_member"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type gang_memberOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyTools?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  totalContribution?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.gang_memberCountOrderByAggregateInput
  _avg?: Prisma.gang_memberAvgOrderByAggregateInput
  _max?: Prisma.gang_memberMaxOrderByAggregateInput
  _min?: Prisma.gang_memberMinOrderByAggregateInput
  _sum?: Prisma.gang_memberSumOrderByAggregateInput
}

export type gang_memberScalarWhereWithAggregatesInput = {
  AND?: Prisma.gang_memberScalarWhereWithAggregatesInput | Prisma.gang_memberScalarWhereWithAggregatesInput[]
  OR?: Prisma.gang_memberScalarWhereWithAggregatesInput[]
  NOT?: Prisma.gang_memberScalarWhereWithAggregatesInput | Prisma.gang_memberScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"gang_member"> | number
  rank?: Prisma.IntWithAggregatesFilter<"gang_member"> | number
  payoutShare?: Prisma.FloatWithAggregatesFilter<"gang_member"> | number
  weeklyMaterials?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  weeklyEssence?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  weeklyTools?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  weeklyRespect?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  totalContribution?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"gang_member"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"gang_member"> | Date | string
  gangId?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"gang_member"> | number | null
}

export type gang_memberCreateInput = {
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_memberInput
  user?: Prisma.userCreateNestedOneWithoutGang_memberInput
}

export type gang_memberUncheckedCreateInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  userId?: number | null
}

export type gang_memberUpdateInput = {
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_memberNestedInput
  user?: Prisma.userUpdateOneWithoutGang_memberNestedInput
}

export type gang_memberUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_memberCreateManyInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  userId?: number | null
}

export type gang_memberUpdateManyMutationInput = {
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type gang_memberUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Gang_memberListRelationFilter = {
  every?: Prisma.gang_memberWhereInput
  some?: Prisma.gang_memberWhereInput
  none?: Prisma.gang_memberWhereInput
}

export type gang_memberOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type gang_memberCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrder
  weeklyTools?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalContribution?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type gang_memberAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrder
  weeklyTools?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalContribution?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type gang_memberMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrder
  weeklyTools?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalContribution?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type gang_memberMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrder
  weeklyTools?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalContribution?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type gang_memberSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rank?: Prisma.SortOrder
  payoutShare?: Prisma.SortOrder
  weeklyMaterials?: Prisma.SortOrder
  weeklyEssence?: Prisma.SortOrder
  weeklyTools?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalContribution?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type gang_memberCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput> | Prisma.gang_memberCreateWithoutGangInput[] | Prisma.gang_memberUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutGangInput | Prisma.gang_memberCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_memberCreateManyGangInputEnvelope
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
}

export type gang_memberUncheckedCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput> | Prisma.gang_memberCreateWithoutGangInput[] | Prisma.gang_memberUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutGangInput | Prisma.gang_memberCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_memberCreateManyGangInputEnvelope
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
}

export type gang_memberUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput> | Prisma.gang_memberCreateWithoutGangInput[] | Prisma.gang_memberUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutGangInput | Prisma.gang_memberCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_memberUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_memberUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_memberCreateManyGangInputEnvelope
  set?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  disconnect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  delete?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  update?: Prisma.gang_memberUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_memberUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_memberUpdateManyWithWhereWithoutGangInput | Prisma.gang_memberUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
}

export type gang_memberUncheckedUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput> | Prisma.gang_memberCreateWithoutGangInput[] | Prisma.gang_memberUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutGangInput | Prisma.gang_memberCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_memberUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_memberUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_memberCreateManyGangInputEnvelope
  set?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  disconnect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  delete?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  update?: Prisma.gang_memberUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_memberUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_memberUpdateManyWithWhereWithoutGangInput | Prisma.gang_memberUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
}

export type gang_memberCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput> | Prisma.gang_memberCreateWithoutUserInput[] | Prisma.gang_memberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutUserInput | Prisma.gang_memberCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.gang_memberCreateManyUserInputEnvelope
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
}

export type gang_memberUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput> | Prisma.gang_memberCreateWithoutUserInput[] | Prisma.gang_memberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutUserInput | Prisma.gang_memberCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.gang_memberCreateManyUserInputEnvelope
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
}

export type gang_memberUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput> | Prisma.gang_memberCreateWithoutUserInput[] | Prisma.gang_memberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutUserInput | Prisma.gang_memberCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.gang_memberUpsertWithWhereUniqueWithoutUserInput | Prisma.gang_memberUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.gang_memberCreateManyUserInputEnvelope
  set?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  disconnect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  delete?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  update?: Prisma.gang_memberUpdateWithWhereUniqueWithoutUserInput | Prisma.gang_memberUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.gang_memberUpdateManyWithWhereWithoutUserInput | Prisma.gang_memberUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
}

export type gang_memberUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput> | Prisma.gang_memberCreateWithoutUserInput[] | Prisma.gang_memberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gang_memberCreateOrConnectWithoutUserInput | Prisma.gang_memberCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.gang_memberUpsertWithWhereUniqueWithoutUserInput | Prisma.gang_memberUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.gang_memberCreateManyUserInputEnvelope
  set?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  disconnect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  delete?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  connect?: Prisma.gang_memberWhereUniqueInput | Prisma.gang_memberWhereUniqueInput[]
  update?: Prisma.gang_memberUpdateWithWhereUniqueWithoutUserInput | Prisma.gang_memberUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.gang_memberUpdateManyWithWhereWithoutUserInput | Prisma.gang_memberUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
}

export type gang_memberCreateWithoutGangInput = {
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutGang_memberInput
}

export type gang_memberUncheckedCreateWithoutGangInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type gang_memberCreateOrConnectWithoutGangInput = {
  where: Prisma.gang_memberWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput>
}

export type gang_memberCreateManyGangInputEnvelope = {
  data: Prisma.gang_memberCreateManyGangInput | Prisma.gang_memberCreateManyGangInput[]
  skipDuplicates?: boolean
}

export type gang_memberUpsertWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_memberWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_memberUpdateWithoutGangInput, Prisma.gang_memberUncheckedUpdateWithoutGangInput>
  create: Prisma.XOR<Prisma.gang_memberCreateWithoutGangInput, Prisma.gang_memberUncheckedCreateWithoutGangInput>
}

export type gang_memberUpdateWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_memberWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_memberUpdateWithoutGangInput, Prisma.gang_memberUncheckedUpdateWithoutGangInput>
}

export type gang_memberUpdateManyWithWhereWithoutGangInput = {
  where: Prisma.gang_memberScalarWhereInput
  data: Prisma.XOR<Prisma.gang_memberUpdateManyMutationInput, Prisma.gang_memberUncheckedUpdateManyWithoutGangInput>
}

export type gang_memberScalarWhereInput = {
  AND?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
  OR?: Prisma.gang_memberScalarWhereInput[]
  NOT?: Prisma.gang_memberScalarWhereInput | Prisma.gang_memberScalarWhereInput[]
  id?: Prisma.IntFilter<"gang_member"> | number
  rank?: Prisma.IntFilter<"gang_member"> | number
  payoutShare?: Prisma.FloatFilter<"gang_member"> | number
  weeklyMaterials?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyEssence?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyTools?: Prisma.IntNullableFilter<"gang_member"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang_member"> | number | null
  totalContribution?: Prisma.IntNullableFilter<"gang_member"> | number | null
  createdAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_member"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_member"> | number | null
  userId?: Prisma.IntNullableFilter<"gang_member"> | number | null
}

export type gang_memberCreateWithoutUserInput = {
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_memberInput
}

export type gang_memberUncheckedCreateWithoutUserInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
}

export type gang_memberCreateOrConnectWithoutUserInput = {
  where: Prisma.gang_memberWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput>
}

export type gang_memberCreateManyUserInputEnvelope = {
  data: Prisma.gang_memberCreateManyUserInput | Prisma.gang_memberCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type gang_memberUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.gang_memberWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_memberUpdateWithoutUserInput, Prisma.gang_memberUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.gang_memberCreateWithoutUserInput, Prisma.gang_memberUncheckedCreateWithoutUserInput>
}

export type gang_memberUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.gang_memberWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_memberUpdateWithoutUserInput, Prisma.gang_memberUncheckedUpdateWithoutUserInput>
}

export type gang_memberUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.gang_memberScalarWhereInput
  data: Prisma.XOR<Prisma.gang_memberUpdateManyMutationInput, Prisma.gang_memberUncheckedUpdateManyWithoutUserInput>
}

export type gang_memberCreateManyGangInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type gang_memberUpdateWithoutGangInput = {
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutGang_memberNestedInput
}

export type gang_memberUncheckedUpdateWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_memberUncheckedUpdateManyWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_memberCreateManyUserInput = {
  id?: number
  rank?: number
  payoutShare?: number
  weeklyMaterials?: number | null
  weeklyEssence?: number | null
  weeklyTools?: number | null
  weeklyRespect?: number | null
  totalContribution?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
}

export type gang_memberUpdateWithoutUserInput = {
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_memberNestedInput
}

export type gang_memberUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_memberUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rank?: Prisma.IntFieldUpdateOperationsInput | number
  payoutShare?: Prisma.FloatFieldUpdateOperationsInput | number
  weeklyMaterials?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyEssence?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyTools?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalContribution?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type gang_memberSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  rank?: boolean
  payoutShare?: boolean
  weeklyMaterials?: boolean
  weeklyEssence?: boolean
  weeklyTools?: boolean
  weeklyRespect?: boolean
  totalContribution?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  userId?: boolean
  gang?: boolean | Prisma.gang_member$gangArgs<ExtArgs>
  user?: boolean | Prisma.gang_member$userArgs<ExtArgs>
}, ExtArgs["result"]["gang_member"]>



export type gang_memberSelectScalar = {
  id?: boolean
  rank?: boolean
  payoutShare?: boolean
  weeklyMaterials?: boolean
  weeklyEssence?: boolean
  weeklyTools?: boolean
  weeklyRespect?: boolean
  totalContribution?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  userId?: boolean
}

export type gang_memberOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "rank" | "payoutShare" | "weeklyMaterials" | "weeklyEssence" | "weeklyTools" | "weeklyRespect" | "totalContribution" | "createdAt" | "updatedAt" | "gangId" | "userId", ExtArgs["result"]["gang_member"]>
export type gang_memberInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  gang?: boolean | Prisma.gang_member$gangArgs<ExtArgs>
  user?: boolean | Prisma.gang_member$userArgs<ExtArgs>
}

export type $gang_memberPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "gang_member"
  objects: {
    gang: Prisma.$gangPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    rank: number
    payoutShare: number
    weeklyMaterials: number | null
    weeklyEssence: number | null
    weeklyTools: number | null
    weeklyRespect: number | null
    totalContribution: number | null
    createdAt: Date
    updatedAt: Date
    gangId: number | null
    userId: number | null
  }, ExtArgs["result"]["gang_member"]>
  composites: {}
}

export type gang_memberGetPayload<S extends boolean | null | undefined | gang_memberDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$gang_memberPayload, S>

export type gang_memberCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<gang_memberFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Gang_memberCountAggregateInputType | true
  }

export interface gang_memberDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['gang_member'], meta: { name: 'gang_member' } }
  /**
   * Find zero or one Gang_member that matches the filter.
   * @param {gang_memberFindUniqueArgs} args - Arguments to find a Gang_member
   * @example
   * // Get one Gang_member
   * const gang_member = await prisma.gang_member.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends gang_memberFindUniqueArgs>(args: Prisma.SelectSubset<T, gang_memberFindUniqueArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Gang_member that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {gang_memberFindUniqueOrThrowArgs} args - Arguments to find a Gang_member
   * @example
   * // Get one Gang_member
   * const gang_member = await prisma.gang_member.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends gang_memberFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, gang_memberFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_member that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberFindFirstArgs} args - Arguments to find a Gang_member
   * @example
   * // Get one Gang_member
   * const gang_member = await prisma.gang_member.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends gang_memberFindFirstArgs>(args?: Prisma.SelectSubset<T, gang_memberFindFirstArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_member that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberFindFirstOrThrowArgs} args - Arguments to find a Gang_member
   * @example
   * // Get one Gang_member
   * const gang_member = await prisma.gang_member.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends gang_memberFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, gang_memberFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Gang_members that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Gang_members
   * const gang_members = await prisma.gang_member.findMany()
   * 
   * // Get first 10 Gang_members
   * const gang_members = await prisma.gang_member.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const gang_memberWithIdOnly = await prisma.gang_member.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends gang_memberFindManyArgs>(args?: Prisma.SelectSubset<T, gang_memberFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Gang_member.
   * @param {gang_memberCreateArgs} args - Arguments to create a Gang_member.
   * @example
   * // Create one Gang_member
   * const Gang_member = await prisma.gang_member.create({
   *   data: {
   *     // ... data to create a Gang_member
   *   }
   * })
   * 
   */
  create<T extends gang_memberCreateArgs>(args: Prisma.SelectSubset<T, gang_memberCreateArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Gang_members.
   * @param {gang_memberCreateManyArgs} args - Arguments to create many Gang_members.
   * @example
   * // Create many Gang_members
   * const gang_member = await prisma.gang_member.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends gang_memberCreateManyArgs>(args?: Prisma.SelectSubset<T, gang_memberCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Gang_member.
   * @param {gang_memberDeleteArgs} args - Arguments to delete one Gang_member.
   * @example
   * // Delete one Gang_member
   * const Gang_member = await prisma.gang_member.delete({
   *   where: {
   *     // ... filter to delete one Gang_member
   *   }
   * })
   * 
   */
  delete<T extends gang_memberDeleteArgs>(args: Prisma.SelectSubset<T, gang_memberDeleteArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Gang_member.
   * @param {gang_memberUpdateArgs} args - Arguments to update one Gang_member.
   * @example
   * // Update one Gang_member
   * const gang_member = await prisma.gang_member.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends gang_memberUpdateArgs>(args: Prisma.SelectSubset<T, gang_memberUpdateArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Gang_members.
   * @param {gang_memberDeleteManyArgs} args - Arguments to filter Gang_members to delete.
   * @example
   * // Delete a few Gang_members
   * const { count } = await prisma.gang_member.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends gang_memberDeleteManyArgs>(args?: Prisma.SelectSubset<T, gang_memberDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Gang_members.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Gang_members
   * const gang_member = await prisma.gang_member.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends gang_memberUpdateManyArgs>(args: Prisma.SelectSubset<T, gang_memberUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Gang_member.
   * @param {gang_memberUpsertArgs} args - Arguments to update or create a Gang_member.
   * @example
   * // Update or create a Gang_member
   * const gang_member = await prisma.gang_member.upsert({
   *   create: {
   *     // ... data to create a Gang_member
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Gang_member we want to update
   *   }
   * })
   */
  upsert<T extends gang_memberUpsertArgs>(args: Prisma.SelectSubset<T, gang_memberUpsertArgs<ExtArgs>>): Prisma.Prisma__gang_memberClient<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Gang_members.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberCountArgs} args - Arguments to filter Gang_members to count.
   * @example
   * // Count the number of Gang_members
   * const count = await prisma.gang_member.count({
   *   where: {
   *     // ... the filter for the Gang_members we want to count
   *   }
   * })
  **/
  count<T extends gang_memberCountArgs>(
    args?: Prisma.Subset<T, gang_memberCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Gang_memberCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Gang_member.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Gang_memberAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Gang_memberAggregateArgs>(args: Prisma.Subset<T, Gang_memberAggregateArgs>): Prisma.PrismaPromise<GetGang_memberAggregateType<T>>

  /**
   * Group by Gang_member.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_memberGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends gang_memberGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: gang_memberGroupByArgs['orderBy'] }
      : { orderBy?: gang_memberGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, gang_memberGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGang_memberGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the gang_member model
 */
readonly fields: gang_memberFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for gang_member.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__gang_memberClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  gang<T extends Prisma.gang_member$gangArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_member$gangArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.gang_member$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_member$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the gang_member model
 */
export interface gang_memberFieldRefs {
  readonly id: Prisma.FieldRef<"gang_member", 'Int'>
  readonly rank: Prisma.FieldRef<"gang_member", 'Int'>
  readonly payoutShare: Prisma.FieldRef<"gang_member", 'Float'>
  readonly weeklyMaterials: Prisma.FieldRef<"gang_member", 'Int'>
  readonly weeklyEssence: Prisma.FieldRef<"gang_member", 'Int'>
  readonly weeklyTools: Prisma.FieldRef<"gang_member", 'Int'>
  readonly weeklyRespect: Prisma.FieldRef<"gang_member", 'Int'>
  readonly totalContribution: Prisma.FieldRef<"gang_member", 'Int'>
  readonly createdAt: Prisma.FieldRef<"gang_member", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"gang_member", 'DateTime'>
  readonly gangId: Prisma.FieldRef<"gang_member", 'Int'>
  readonly userId: Prisma.FieldRef<"gang_member", 'Int'>
}
    

// Custom InputTypes
/**
 * gang_member findUnique
 */
export type gang_memberFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter, which gang_member to fetch.
   */
  where: Prisma.gang_memberWhereUniqueInput
}

/**
 * gang_member findUniqueOrThrow
 */
export type gang_memberFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter, which gang_member to fetch.
   */
  where: Prisma.gang_memberWhereUniqueInput
}

/**
 * gang_member findFirst
 */
export type gang_memberFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter, which gang_member to fetch.
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_members to fetch.
   */
  orderBy?: Prisma.gang_memberOrderByWithRelationInput | Prisma.gang_memberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_members.
   */
  cursor?: Prisma.gang_memberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_members.
   */
  distinct?: Prisma.Gang_memberScalarFieldEnum | Prisma.Gang_memberScalarFieldEnum[]
}

/**
 * gang_member findFirstOrThrow
 */
export type gang_memberFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter, which gang_member to fetch.
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_members to fetch.
   */
  orderBy?: Prisma.gang_memberOrderByWithRelationInput | Prisma.gang_memberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_members.
   */
  cursor?: Prisma.gang_memberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_members.
   */
  distinct?: Prisma.Gang_memberScalarFieldEnum | Prisma.Gang_memberScalarFieldEnum[]
}

/**
 * gang_member findMany
 */
export type gang_memberFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter, which gang_members to fetch.
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_members to fetch.
   */
  orderBy?: Prisma.gang_memberOrderByWithRelationInput | Prisma.gang_memberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing gang_members.
   */
  cursor?: Prisma.gang_memberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_members.
   */
  skip?: number
  distinct?: Prisma.Gang_memberScalarFieldEnum | Prisma.Gang_memberScalarFieldEnum[]
}

/**
 * gang_member create
 */
export type gang_memberCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * The data needed to create a gang_member.
   */
  data: Prisma.XOR<Prisma.gang_memberCreateInput, Prisma.gang_memberUncheckedCreateInput>
}

/**
 * gang_member createMany
 */
export type gang_memberCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many gang_members.
   */
  data: Prisma.gang_memberCreateManyInput | Prisma.gang_memberCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * gang_member update
 */
export type gang_memberUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * The data needed to update a gang_member.
   */
  data: Prisma.XOR<Prisma.gang_memberUpdateInput, Prisma.gang_memberUncheckedUpdateInput>
  /**
   * Choose, which gang_member to update.
   */
  where: Prisma.gang_memberWhereUniqueInput
}

/**
 * gang_member updateMany
 */
export type gang_memberUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update gang_members.
   */
  data: Prisma.XOR<Prisma.gang_memberUpdateManyMutationInput, Prisma.gang_memberUncheckedUpdateManyInput>
  /**
   * Filter which gang_members to update
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * Limit how many gang_members to update.
   */
  limit?: number
}

/**
 * gang_member upsert
 */
export type gang_memberUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * The filter to search for the gang_member to update in case it exists.
   */
  where: Prisma.gang_memberWhereUniqueInput
  /**
   * In case the gang_member found by the `where` argument doesn't exist, create a new gang_member with this data.
   */
  create: Prisma.XOR<Prisma.gang_memberCreateInput, Prisma.gang_memberUncheckedCreateInput>
  /**
   * In case the gang_member was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.gang_memberUpdateInput, Prisma.gang_memberUncheckedUpdateInput>
}

/**
 * gang_member delete
 */
export type gang_memberDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  /**
   * Filter which gang_member to delete.
   */
  where: Prisma.gang_memberWhereUniqueInput
}

/**
 * gang_member deleteMany
 */
export type gang_memberDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_members to delete
   */
  where?: Prisma.gang_memberWhereInput
  /**
   * Limit how many gang_members to delete.
   */
  limit?: number
}

/**
 * gang_member.gang
 */
export type gang_member$gangArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  where?: Prisma.gangWhereInput
}

/**
 * gang_member.user
 */
export type gang_member$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang_member without action
 */
export type gang_memberDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
}
