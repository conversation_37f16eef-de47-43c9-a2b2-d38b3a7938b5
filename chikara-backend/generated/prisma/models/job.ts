
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `job` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model job
 * 
 */
export type jobModel = runtime.Types.Result.DefaultSelection<Prisma.$jobPayload>

export type AggregateJob = {
  _count: JobCountAggregateOutputType | null
  _avg: JobAvgAggregateOutputType | null
  _sum: JobSumAggregateOutputType | null
  _min: JobMinAggregateOutputType | null
  _max: JobMaxAggregateOutputType | null
}

export type JobAvgAggregateOutputType = {
  id: number | null
}

export type JobSumAggregateOutputType = {
  id: number | null
}

export type JobMinAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  avatar: string | null
  payFormula: string | null
  strengthFormula: string | null
  intelligenceFormula: string | null
  dexterityFormula: string | null
  defenceFormula: string | null
  enduranceFormula: string | null
  vitalityFormula: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type JobMaxAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  avatar: string | null
  payFormula: string | null
  strengthFormula: string | null
  intelligenceFormula: string | null
  dexterityFormula: string | null
  defenceFormula: string | null
  enduranceFormula: string | null
  vitalityFormula: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type JobCountAggregateOutputType = {
  id: number
  name: number
  description: number
  avatar: number
  payFormula: number
  strengthFormula: number
  intelligenceFormula: number
  dexterityFormula: number
  defenceFormula: number
  enduranceFormula: number
  vitalityFormula: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type JobAvgAggregateInputType = {
  id?: true
}

export type JobSumAggregateInputType = {
  id?: true
}

export type JobMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  avatar?: true
  payFormula?: true
  strengthFormula?: true
  intelligenceFormula?: true
  dexterityFormula?: true
  defenceFormula?: true
  enduranceFormula?: true
  vitalityFormula?: true
  createdAt?: true
  updatedAt?: true
}

export type JobMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  avatar?: true
  payFormula?: true
  strengthFormula?: true
  intelligenceFormula?: true
  dexterityFormula?: true
  defenceFormula?: true
  enduranceFormula?: true
  vitalityFormula?: true
  createdAt?: true
  updatedAt?: true
}

export type JobCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  avatar?: true
  payFormula?: true
  strengthFormula?: true
  intelligenceFormula?: true
  dexterityFormula?: true
  defenceFormula?: true
  enduranceFormula?: true
  vitalityFormula?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type JobAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which job to aggregate.
   */
  where?: Prisma.jobWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of jobs to fetch.
   */
  orderBy?: Prisma.jobOrderByWithRelationInput | Prisma.jobOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.jobWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` jobs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` jobs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned jobs
  **/
  _count?: true | JobCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: JobAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: JobSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: JobMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: JobMaxAggregateInputType
}

export type GetJobAggregateType<T extends JobAggregateArgs> = {
      [P in keyof T & keyof AggregateJob]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateJob[P]>
    : Prisma.GetScalarType<T[P], AggregateJob[P]>
}




export type jobGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.jobWhereInput
  orderBy?: Prisma.jobOrderByWithAggregationInput | Prisma.jobOrderByWithAggregationInput[]
  by: Prisma.JobScalarFieldEnum[] | Prisma.JobScalarFieldEnum
  having?: Prisma.jobScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: JobCountAggregateInputType | true
  _avg?: JobAvgAggregateInputType
  _sum?: JobSumAggregateInputType
  _min?: JobMinAggregateInputType
  _max?: JobMaxAggregateInputType
}

export type JobGroupByOutputType = {
  id: number
  name: string
  description: string | null
  avatar: string | null
  payFormula: string | null
  strengthFormula: string | null
  intelligenceFormula: string | null
  dexterityFormula: string | null
  defenceFormula: string | null
  enduranceFormula: string | null
  vitalityFormula: string | null
  createdAt: Date
  updatedAt: Date
  _count: JobCountAggregateOutputType | null
  _avg: JobAvgAggregateOutputType | null
  _sum: JobSumAggregateOutputType | null
  _min: JobMinAggregateOutputType | null
  _max: JobMaxAggregateOutputType | null
}

type GetJobGroupByPayload<T extends jobGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<JobGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof JobGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], JobGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], JobGroupByOutputType[P]>
      }
    >
  >



export type jobWhereInput = {
  AND?: Prisma.jobWhereInput | Prisma.jobWhereInput[]
  OR?: Prisma.jobWhereInput[]
  NOT?: Prisma.jobWhereInput | Prisma.jobWhereInput[]
  id?: Prisma.IntFilter<"job"> | number
  name?: Prisma.StringFilter<"job"> | string
  description?: Prisma.StringNullableFilter<"job"> | string | null
  avatar?: Prisma.StringNullableFilter<"job"> | string | null
  payFormula?: Prisma.StringNullableFilter<"job"> | string | null
  strengthFormula?: Prisma.StringNullableFilter<"job"> | string | null
  intelligenceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  dexterityFormula?: Prisma.StringNullableFilter<"job"> | string | null
  defenceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  enduranceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  vitalityFormula?: Prisma.StringNullableFilter<"job"> | string | null
  createdAt?: Prisma.DateTimeFilter<"job"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"job"> | Date | string
  user?: Prisma.UserListRelationFilter
}

export type jobOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  payFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  strengthFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  intelligenceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  dexterityFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  defenceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  enduranceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  vitalityFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.userOrderByRelationAggregateInput
  _relevance?: Prisma.jobOrderByRelevanceInput
}

export type jobWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.jobWhereInput | Prisma.jobWhereInput[]
  OR?: Prisma.jobWhereInput[]
  NOT?: Prisma.jobWhereInput | Prisma.jobWhereInput[]
  name?: Prisma.StringFilter<"job"> | string
  description?: Prisma.StringNullableFilter<"job"> | string | null
  avatar?: Prisma.StringNullableFilter<"job"> | string | null
  payFormula?: Prisma.StringNullableFilter<"job"> | string | null
  strengthFormula?: Prisma.StringNullableFilter<"job"> | string | null
  intelligenceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  dexterityFormula?: Prisma.StringNullableFilter<"job"> | string | null
  defenceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  enduranceFormula?: Prisma.StringNullableFilter<"job"> | string | null
  vitalityFormula?: Prisma.StringNullableFilter<"job"> | string | null
  createdAt?: Prisma.DateTimeFilter<"job"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"job"> | Date | string
  user?: Prisma.UserListRelationFilter
}, "id">

export type jobOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  payFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  strengthFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  intelligenceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  dexterityFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  defenceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  enduranceFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  vitalityFormula?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.jobCountOrderByAggregateInput
  _avg?: Prisma.jobAvgOrderByAggregateInput
  _max?: Prisma.jobMaxOrderByAggregateInput
  _min?: Prisma.jobMinOrderByAggregateInput
  _sum?: Prisma.jobSumOrderByAggregateInput
}

export type jobScalarWhereWithAggregatesInput = {
  AND?: Prisma.jobScalarWhereWithAggregatesInput | Prisma.jobScalarWhereWithAggregatesInput[]
  OR?: Prisma.jobScalarWhereWithAggregatesInput[]
  NOT?: Prisma.jobScalarWhereWithAggregatesInput | Prisma.jobScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"job"> | number
  name?: Prisma.StringWithAggregatesFilter<"job"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  avatar?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  payFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  strengthFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  intelligenceFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  dexterityFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  defenceFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  enduranceFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  vitalityFormula?: Prisma.StringNullableWithAggregatesFilter<"job"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"job"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"job"> | Date | string
}

export type jobCreateInput = {
  name: string
  description?: string | null
  avatar?: string | null
  payFormula?: string | null
  strengthFormula?: string | null
  intelligenceFormula?: string | null
  dexterityFormula?: string | null
  defenceFormula?: string | null
  enduranceFormula?: string | null
  vitalityFormula?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedManyWithoutJobInput
}

export type jobUncheckedCreateInput = {
  id?: number
  name: string
  description?: string | null
  avatar?: string | null
  payFormula?: string | null
  strengthFormula?: string | null
  intelligenceFormula?: string | null
  dexterityFormula?: string | null
  defenceFormula?: string | null
  enduranceFormula?: string | null
  vitalityFormula?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userUncheckedCreateNestedManyWithoutJobInput
}

export type jobUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateManyWithoutJobNestedInput
}

export type jobUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUncheckedUpdateManyWithoutJobNestedInput
}

export type jobCreateManyInput = {
  id?: number
  name: string
  description?: string | null
  avatar?: string | null
  payFormula?: string | null
  strengthFormula?: string | null
  intelligenceFormula?: string | null
  dexterityFormula?: string | null
  defenceFormula?: string | null
  enduranceFormula?: string | null
  vitalityFormula?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type jobUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type jobUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type jobOrderByRelevanceInput = {
  fields: Prisma.jobOrderByRelevanceFieldEnum | Prisma.jobOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type jobCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  payFormula?: Prisma.SortOrder
  strengthFormula?: Prisma.SortOrder
  intelligenceFormula?: Prisma.SortOrder
  dexterityFormula?: Prisma.SortOrder
  defenceFormula?: Prisma.SortOrder
  enduranceFormula?: Prisma.SortOrder
  vitalityFormula?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type jobAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type jobMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  payFormula?: Prisma.SortOrder
  strengthFormula?: Prisma.SortOrder
  intelligenceFormula?: Prisma.SortOrder
  dexterityFormula?: Prisma.SortOrder
  defenceFormula?: Prisma.SortOrder
  enduranceFormula?: Prisma.SortOrder
  vitalityFormula?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type jobMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  payFormula?: Prisma.SortOrder
  strengthFormula?: Prisma.SortOrder
  intelligenceFormula?: Prisma.SortOrder
  dexterityFormula?: Prisma.SortOrder
  defenceFormula?: Prisma.SortOrder
  enduranceFormula?: Prisma.SortOrder
  vitalityFormula?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type jobSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type JobNullableScalarRelationFilter = {
  is?: Prisma.jobWhereInput | null
  isNot?: Prisma.jobWhereInput | null
}

export type jobCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.jobCreateWithoutUserInput, Prisma.jobUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.jobCreateOrConnectWithoutUserInput
  connect?: Prisma.jobWhereUniqueInput
}

export type jobUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.jobCreateWithoutUserInput, Prisma.jobUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.jobCreateOrConnectWithoutUserInput
  upsert?: Prisma.jobUpsertWithoutUserInput
  disconnect?: Prisma.jobWhereInput | boolean
  delete?: Prisma.jobWhereInput | boolean
  connect?: Prisma.jobWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.jobUpdateToOneWithWhereWithoutUserInput, Prisma.jobUpdateWithoutUserInput>, Prisma.jobUncheckedUpdateWithoutUserInput>
}

export type jobCreateWithoutUserInput = {
  name: string
  description?: string | null
  avatar?: string | null
  payFormula?: string | null
  strengthFormula?: string | null
  intelligenceFormula?: string | null
  dexterityFormula?: string | null
  defenceFormula?: string | null
  enduranceFormula?: string | null
  vitalityFormula?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type jobUncheckedCreateWithoutUserInput = {
  id?: number
  name: string
  description?: string | null
  avatar?: string | null
  payFormula?: string | null
  strengthFormula?: string | null
  intelligenceFormula?: string | null
  dexterityFormula?: string | null
  defenceFormula?: string | null
  enduranceFormula?: string | null
  vitalityFormula?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type jobCreateOrConnectWithoutUserInput = {
  where: Prisma.jobWhereUniqueInput
  create: Prisma.XOR<Prisma.jobCreateWithoutUserInput, Prisma.jobUncheckedCreateWithoutUserInput>
}

export type jobUpsertWithoutUserInput = {
  update: Prisma.XOR<Prisma.jobUpdateWithoutUserInput, Prisma.jobUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.jobCreateWithoutUserInput, Prisma.jobUncheckedCreateWithoutUserInput>
  where?: Prisma.jobWhereInput
}

export type jobUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.jobWhereInput
  data: Prisma.XOR<Prisma.jobUpdateWithoutUserInput, Prisma.jobUncheckedUpdateWithoutUserInput>
}

export type jobUpdateWithoutUserInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type jobUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  payFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengthFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intelligenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dexterityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  defenceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enduranceFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  vitalityFormula?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type JobCountOutputType
 */

export type JobCountOutputType = {
  user: number
}

export type JobCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | JobCountOutputTypeCountUserArgs
}

/**
 * JobCountOutputType without action
 */
export type JobCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the JobCountOutputType
   */
  select?: Prisma.JobCountOutputTypeSelect<ExtArgs> | null
}

/**
 * JobCountOutputType without action
 */
export type JobCountOutputTypeCountUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.userWhereInput
}


export type jobSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  avatar?: boolean
  payFormula?: boolean
  strengthFormula?: boolean
  intelligenceFormula?: boolean
  dexterityFormula?: boolean
  defenceFormula?: boolean
  enduranceFormula?: boolean
  vitalityFormula?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.job$userArgs<ExtArgs>
  _count?: boolean | Prisma.JobCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["job"]>



export type jobSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  avatar?: boolean
  payFormula?: boolean
  strengthFormula?: boolean
  intelligenceFormula?: boolean
  dexterityFormula?: boolean
  defenceFormula?: boolean
  enduranceFormula?: boolean
  vitalityFormula?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type jobOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "avatar" | "payFormula" | "strengthFormula" | "intelligenceFormula" | "dexterityFormula" | "defenceFormula" | "enduranceFormula" | "vitalityFormula" | "createdAt" | "updatedAt", ExtArgs["result"]["job"]>
export type jobInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.job$userArgs<ExtArgs>
  _count?: boolean | Prisma.JobCountOutputTypeDefaultArgs<ExtArgs>
}

export type $jobPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "job"
  objects: {
    user: Prisma.$userPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    description: string | null
    avatar: string | null
    payFormula: string | null
    strengthFormula: string | null
    intelligenceFormula: string | null
    dexterityFormula: string | null
    defenceFormula: string | null
    enduranceFormula: string | null
    vitalityFormula: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["job"]>
  composites: {}
}

export type jobGetPayload<S extends boolean | null | undefined | jobDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$jobPayload, S>

export type jobCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<jobFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: JobCountAggregateInputType | true
  }

export interface jobDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['job'], meta: { name: 'job' } }
  /**
   * Find zero or one Job that matches the filter.
   * @param {jobFindUniqueArgs} args - Arguments to find a Job
   * @example
   * // Get one Job
   * const job = await prisma.job.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends jobFindUniqueArgs>(args: Prisma.SelectSubset<T, jobFindUniqueArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Job that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {jobFindUniqueOrThrowArgs} args - Arguments to find a Job
   * @example
   * // Get one Job
   * const job = await prisma.job.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends jobFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, jobFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Job that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobFindFirstArgs} args - Arguments to find a Job
   * @example
   * // Get one Job
   * const job = await prisma.job.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends jobFindFirstArgs>(args?: Prisma.SelectSubset<T, jobFindFirstArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Job that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobFindFirstOrThrowArgs} args - Arguments to find a Job
   * @example
   * // Get one Job
   * const job = await prisma.job.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends jobFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, jobFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Jobs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Jobs
   * const jobs = await prisma.job.findMany()
   * 
   * // Get first 10 Jobs
   * const jobs = await prisma.job.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const jobWithIdOnly = await prisma.job.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends jobFindManyArgs>(args?: Prisma.SelectSubset<T, jobFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Job.
   * @param {jobCreateArgs} args - Arguments to create a Job.
   * @example
   * // Create one Job
   * const Job = await prisma.job.create({
   *   data: {
   *     // ... data to create a Job
   *   }
   * })
   * 
   */
  create<T extends jobCreateArgs>(args: Prisma.SelectSubset<T, jobCreateArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Jobs.
   * @param {jobCreateManyArgs} args - Arguments to create many Jobs.
   * @example
   * // Create many Jobs
   * const job = await prisma.job.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends jobCreateManyArgs>(args?: Prisma.SelectSubset<T, jobCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Job.
   * @param {jobDeleteArgs} args - Arguments to delete one Job.
   * @example
   * // Delete one Job
   * const Job = await prisma.job.delete({
   *   where: {
   *     // ... filter to delete one Job
   *   }
   * })
   * 
   */
  delete<T extends jobDeleteArgs>(args: Prisma.SelectSubset<T, jobDeleteArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Job.
   * @param {jobUpdateArgs} args - Arguments to update one Job.
   * @example
   * // Update one Job
   * const job = await prisma.job.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends jobUpdateArgs>(args: Prisma.SelectSubset<T, jobUpdateArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Jobs.
   * @param {jobDeleteManyArgs} args - Arguments to filter Jobs to delete.
   * @example
   * // Delete a few Jobs
   * const { count } = await prisma.job.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends jobDeleteManyArgs>(args?: Prisma.SelectSubset<T, jobDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Jobs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Jobs
   * const job = await prisma.job.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends jobUpdateManyArgs>(args: Prisma.SelectSubset<T, jobUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Job.
   * @param {jobUpsertArgs} args - Arguments to update or create a Job.
   * @example
   * // Update or create a Job
   * const job = await prisma.job.upsert({
   *   create: {
   *     // ... data to create a Job
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Job we want to update
   *   }
   * })
   */
  upsert<T extends jobUpsertArgs>(args: Prisma.SelectSubset<T, jobUpsertArgs<ExtArgs>>): Prisma.Prisma__jobClient<runtime.Types.Result.GetResult<Prisma.$jobPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Jobs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobCountArgs} args - Arguments to filter Jobs to count.
   * @example
   * // Count the number of Jobs
   * const count = await prisma.job.count({
   *   where: {
   *     // ... the filter for the Jobs we want to count
   *   }
   * })
  **/
  count<T extends jobCountArgs>(
    args?: Prisma.Subset<T, jobCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], JobCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Job.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {JobAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends JobAggregateArgs>(args: Prisma.Subset<T, JobAggregateArgs>): Prisma.PrismaPromise<GetJobAggregateType<T>>

  /**
   * Group by Job.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {jobGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends jobGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: jobGroupByArgs['orderBy'] }
      : { orderBy?: jobGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, jobGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetJobGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the job model
 */
readonly fields: jobFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for job.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__jobClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.job$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.job$userArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the job model
 */
export interface jobFieldRefs {
  readonly id: Prisma.FieldRef<"job", 'Int'>
  readonly name: Prisma.FieldRef<"job", 'String'>
  readonly description: Prisma.FieldRef<"job", 'String'>
  readonly avatar: Prisma.FieldRef<"job", 'String'>
  readonly payFormula: Prisma.FieldRef<"job", 'String'>
  readonly strengthFormula: Prisma.FieldRef<"job", 'String'>
  readonly intelligenceFormula: Prisma.FieldRef<"job", 'String'>
  readonly dexterityFormula: Prisma.FieldRef<"job", 'String'>
  readonly defenceFormula: Prisma.FieldRef<"job", 'String'>
  readonly enduranceFormula: Prisma.FieldRef<"job", 'String'>
  readonly vitalityFormula: Prisma.FieldRef<"job", 'String'>
  readonly createdAt: Prisma.FieldRef<"job", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"job", 'DateTime'>
}
    

// Custom InputTypes
/**
 * job findUnique
 */
export type jobFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter, which job to fetch.
   */
  where: Prisma.jobWhereUniqueInput
}

/**
 * job findUniqueOrThrow
 */
export type jobFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter, which job to fetch.
   */
  where: Prisma.jobWhereUniqueInput
}

/**
 * job findFirst
 */
export type jobFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter, which job to fetch.
   */
  where?: Prisma.jobWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of jobs to fetch.
   */
  orderBy?: Prisma.jobOrderByWithRelationInput | Prisma.jobOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for jobs.
   */
  cursor?: Prisma.jobWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` jobs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` jobs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of jobs.
   */
  distinct?: Prisma.JobScalarFieldEnum | Prisma.JobScalarFieldEnum[]
}

/**
 * job findFirstOrThrow
 */
export type jobFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter, which job to fetch.
   */
  where?: Prisma.jobWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of jobs to fetch.
   */
  orderBy?: Prisma.jobOrderByWithRelationInput | Prisma.jobOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for jobs.
   */
  cursor?: Prisma.jobWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` jobs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` jobs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of jobs.
   */
  distinct?: Prisma.JobScalarFieldEnum | Prisma.JobScalarFieldEnum[]
}

/**
 * job findMany
 */
export type jobFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter, which jobs to fetch.
   */
  where?: Prisma.jobWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of jobs to fetch.
   */
  orderBy?: Prisma.jobOrderByWithRelationInput | Prisma.jobOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing jobs.
   */
  cursor?: Prisma.jobWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` jobs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` jobs.
   */
  skip?: number
  distinct?: Prisma.JobScalarFieldEnum | Prisma.JobScalarFieldEnum[]
}

/**
 * job create
 */
export type jobCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * The data needed to create a job.
   */
  data: Prisma.XOR<Prisma.jobCreateInput, Prisma.jobUncheckedCreateInput>
}

/**
 * job createMany
 */
export type jobCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many jobs.
   */
  data: Prisma.jobCreateManyInput | Prisma.jobCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * job update
 */
export type jobUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * The data needed to update a job.
   */
  data: Prisma.XOR<Prisma.jobUpdateInput, Prisma.jobUncheckedUpdateInput>
  /**
   * Choose, which job to update.
   */
  where: Prisma.jobWhereUniqueInput
}

/**
 * job updateMany
 */
export type jobUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update jobs.
   */
  data: Prisma.XOR<Prisma.jobUpdateManyMutationInput, Prisma.jobUncheckedUpdateManyInput>
  /**
   * Filter which jobs to update
   */
  where?: Prisma.jobWhereInput
  /**
   * Limit how many jobs to update.
   */
  limit?: number
}

/**
 * job upsert
 */
export type jobUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * The filter to search for the job to update in case it exists.
   */
  where: Prisma.jobWhereUniqueInput
  /**
   * In case the job found by the `where` argument doesn't exist, create a new job with this data.
   */
  create: Prisma.XOR<Prisma.jobCreateInput, Prisma.jobUncheckedCreateInput>
  /**
   * In case the job was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.jobUpdateInput, Prisma.jobUncheckedUpdateInput>
}

/**
 * job delete
 */
export type jobDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
  /**
   * Filter which job to delete.
   */
  where: Prisma.jobWhereUniqueInput
}

/**
 * job deleteMany
 */
export type jobDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which jobs to delete
   */
  where?: Prisma.jobWhereInput
  /**
   * Limit how many jobs to delete.
   */
  limit?: number
}

/**
 * job.user
 */
export type job$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
  orderBy?: Prisma.userOrderByWithRelationInput | Prisma.userOrderByWithRelationInput[]
  cursor?: Prisma.userWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * job without action
 */
export type jobDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the job
   */
  select?: Prisma.jobSelect<ExtArgs> | null
  /**
   * Omit specific fields from the job
   */
  omit?: Prisma.jobOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.jobInclude<ExtArgs> | null
}
