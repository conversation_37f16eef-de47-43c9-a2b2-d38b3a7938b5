
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `profile_comment` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model profile_comment
 * 
 */
export type profile_commentModel = runtime.Types.Result.DefaultSelection<Prisma.$profile_commentPayload>

export type AggregateProfile_comment = {
  _count: Profile_commentCountAggregateOutputType | null
  _avg: Profile_commentAvgAggregateOutputType | null
  _sum: Profile_commentSumAggregateOutputType | null
  _min: Profile_commentMinAggregateOutputType | null
  _max: Profile_commentMaxAggregateOutputType | null
}

export type Profile_commentAvgAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Profile_commentSumAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Profile_commentMinAggregateOutputType = {
  id: number | null
  message: string | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Profile_commentMaxAggregateOutputType = {
  id: number | null
  message: string | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Profile_commentCountAggregateOutputType = {
  id: number
  message: number
  createdAt: number
  updatedAt: number
  senderId: number
  receiverId: number
  _all: number
}


export type Profile_commentAvgAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Profile_commentSumAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Profile_commentMinAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Profile_commentMaxAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Profile_commentCountAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
  _all?: true
}

export type Profile_commentAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which profile_comment to aggregate.
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of profile_comments to fetch.
   */
  orderBy?: Prisma.profile_commentOrderByWithRelationInput | Prisma.profile_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.profile_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` profile_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` profile_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned profile_comments
  **/
  _count?: true | Profile_commentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Profile_commentAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Profile_commentSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Profile_commentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Profile_commentMaxAggregateInputType
}

export type GetProfile_commentAggregateType<T extends Profile_commentAggregateArgs> = {
      [P in keyof T & keyof AggregateProfile_comment]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProfile_comment[P]>
    : Prisma.GetScalarType<T[P], AggregateProfile_comment[P]>
}




export type profile_commentGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.profile_commentWhereInput
  orderBy?: Prisma.profile_commentOrderByWithAggregationInput | Prisma.profile_commentOrderByWithAggregationInput[]
  by: Prisma.Profile_commentScalarFieldEnum[] | Prisma.Profile_commentScalarFieldEnum
  having?: Prisma.profile_commentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Profile_commentCountAggregateInputType | true
  _avg?: Profile_commentAvgAggregateInputType
  _sum?: Profile_commentSumAggregateInputType
  _min?: Profile_commentMinAggregateInputType
  _max?: Profile_commentMaxAggregateInputType
}

export type Profile_commentGroupByOutputType = {
  id: number
  message: string
  createdAt: Date
  updatedAt: Date
  senderId: number | null
  receiverId: number | null
  _count: Profile_commentCountAggregateOutputType | null
  _avg: Profile_commentAvgAggregateOutputType | null
  _sum: Profile_commentSumAggregateOutputType | null
  _min: Profile_commentMinAggregateOutputType | null
  _max: Profile_commentMaxAggregateOutputType | null
}

type GetProfile_commentGroupByPayload<T extends profile_commentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Profile_commentGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Profile_commentGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Profile_commentGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Profile_commentGroupByOutputType[P]>
      }
    >
  >



export type profile_commentWhereInput = {
  AND?: Prisma.profile_commentWhereInput | Prisma.profile_commentWhereInput[]
  OR?: Prisma.profile_commentWhereInput[]
  NOT?: Prisma.profile_commentWhereInput | Prisma.profile_commentWhereInput[]
  id?: Prisma.IntFilter<"profile_comment"> | number
  message?: Prisma.StringFilter<"profile_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  senderId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
  receiverId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
  user_profile_comment_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_profile_comment_receiverIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type profile_commentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  receiverId?: Prisma.SortOrderInput | Prisma.SortOrder
  user_profile_comment_senderIdTouser?: Prisma.userOrderByWithRelationInput
  user_profile_comment_receiverIdTouser?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.profile_commentOrderByRelevanceInput
}

export type profile_commentWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.profile_commentWhereInput | Prisma.profile_commentWhereInput[]
  OR?: Prisma.profile_commentWhereInput[]
  NOT?: Prisma.profile_commentWhereInput | Prisma.profile_commentWhereInput[]
  message?: Prisma.StringFilter<"profile_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  senderId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
  receiverId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
  user_profile_comment_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_profile_comment_receiverIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type profile_commentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  receiverId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.profile_commentCountOrderByAggregateInput
  _avg?: Prisma.profile_commentAvgOrderByAggregateInput
  _max?: Prisma.profile_commentMaxOrderByAggregateInput
  _min?: Prisma.profile_commentMinOrderByAggregateInput
  _sum?: Prisma.profile_commentSumOrderByAggregateInput
}

export type profile_commentScalarWhereWithAggregatesInput = {
  AND?: Prisma.profile_commentScalarWhereWithAggregatesInput | Prisma.profile_commentScalarWhereWithAggregatesInput[]
  OR?: Prisma.profile_commentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.profile_commentScalarWhereWithAggregatesInput | Prisma.profile_commentScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"profile_comment"> | number
  message?: Prisma.StringWithAggregatesFilter<"profile_comment"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"profile_comment"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"profile_comment"> | Date | string
  senderId?: Prisma.IntNullableWithAggregatesFilter<"profile_comment"> | number | null
  receiverId?: Prisma.IntNullableWithAggregatesFilter<"profile_comment"> | number | null
}

export type profile_commentCreateInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user_profile_comment_senderIdTouser?: Prisma.userCreateNestedOneWithoutProfile_comment_profile_comment_senderIdTouserInput
  user_profile_comment_receiverIdTouser?: Prisma.userCreateNestedOneWithoutProfile_comment_profile_comment_receiverIdTouserInput
}

export type profile_commentUncheckedCreateInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  receiverId?: number | null
}

export type profile_commentUpdateInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_profile_comment_senderIdTouser?: Prisma.userUpdateOneWithoutProfile_comment_profile_comment_senderIdTouserNestedInput
  user_profile_comment_receiverIdTouser?: Prisma.userUpdateOneWithoutProfile_comment_profile_comment_receiverIdTouserNestedInput
}

export type profile_commentUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type profile_commentCreateManyInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  receiverId?: number | null
}

export type profile_commentUpdateManyMutationInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type profile_commentUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type profile_commentOrderByRelevanceInput = {
  fields: Prisma.profile_commentOrderByRelevanceFieldEnum | Prisma.profile_commentOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type profile_commentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type profile_commentAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type profile_commentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type profile_commentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type profile_commentSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type Profile_commentListRelationFilter = {
  every?: Prisma.profile_commentWhereInput
  some?: Prisma.profile_commentWhereInput
  none?: Prisma.profile_commentWhereInput
}

export type profile_commentOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type profile_commentCreateNestedManyWithoutUser_profile_comment_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInputEnvelope
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
}

export type profile_commentCreateNestedManyWithoutUser_profile_comment_receiverIdTouserInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInputEnvelope
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
}

export type profile_commentUncheckedCreateNestedManyWithoutUser_profile_comment_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInputEnvelope
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
}

export type profile_commentUncheckedCreateNestedManyWithoutUser_profile_comment_receiverIdTouserInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInputEnvelope
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
}

export type profile_commentUpdateManyWithoutUser_profile_comment_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput[]
  upsert?: Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInputEnvelope
  set?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  disconnect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  delete?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  update?: Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput[]
  updateMany?: Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_senderIdTouserInput[]
  deleteMany?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
}

export type profile_commentUpdateManyWithoutUser_profile_comment_receiverIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput[]
  upsert?: Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInputEnvelope
  set?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  disconnect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  delete?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  update?: Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput[]
  updateMany?: Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_receiverIdTouserInput[]
  deleteMany?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
}

export type profile_commentUncheckedUpdateManyWithoutUser_profile_comment_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput[]
  upsert?: Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInputEnvelope
  set?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  disconnect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  delete?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  update?: Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput[]
  updateMany?: Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_senderIdTouserInput | Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_senderIdTouserInput[]
  deleteMany?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
}

export type profile_commentUncheckedUpdateManyWithoutUser_profile_comment_receiverIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput> | Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput[] | Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput[]
  connectOrCreate?: Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput[]
  upsert?: Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput[]
  createMany?: Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInputEnvelope
  set?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  disconnect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  delete?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  connect?: Prisma.profile_commentWhereUniqueInput | Prisma.profile_commentWhereUniqueInput[]
  update?: Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput[]
  updateMany?: Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentUpdateManyWithWhereWithoutUser_profile_comment_receiverIdTouserInput[]
  deleteMany?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
}

export type profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user_profile_comment_receiverIdTouser?: Prisma.userCreateNestedOneWithoutProfile_comment_profile_comment_receiverIdTouserInput
}

export type profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId?: number | null
}

export type profile_commentCreateOrConnectWithoutUser_profile_comment_senderIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  create: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput>
}

export type profile_commentCreateManyUser_profile_comment_senderIdTouserInputEnvelope = {
  data: Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInput | Prisma.profile_commentCreateManyUser_profile_comment_senderIdTouserInput[]
  skipDuplicates?: boolean
}

export type profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user_profile_comment_senderIdTouser?: Prisma.userCreateNestedOneWithoutProfile_comment_profile_comment_senderIdTouserInput
}

export type profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
}

export type profile_commentCreateOrConnectWithoutUser_profile_comment_receiverIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  create: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput>
}

export type profile_commentCreateManyUser_profile_comment_receiverIdTouserInputEnvelope = {
  data: Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInput | Prisma.profile_commentCreateManyUser_profile_comment_receiverIdTouserInput[]
  skipDuplicates?: boolean
}

export type profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  update: Prisma.XOR<Prisma.profile_commentUpdateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedUpdateWithoutUser_profile_comment_senderIdTouserInput>
  create: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_senderIdTouserInput>
}

export type profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_senderIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  data: Prisma.XOR<Prisma.profile_commentUpdateWithoutUser_profile_comment_senderIdTouserInput, Prisma.profile_commentUncheckedUpdateWithoutUser_profile_comment_senderIdTouserInput>
}

export type profile_commentUpdateManyWithWhereWithoutUser_profile_comment_senderIdTouserInput = {
  where: Prisma.profile_commentScalarWhereInput
  data: Prisma.XOR<Prisma.profile_commentUpdateManyMutationInput, Prisma.profile_commentUncheckedUpdateManyWithoutUser_profile_comment_senderIdTouserInput>
}

export type profile_commentScalarWhereInput = {
  AND?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
  OR?: Prisma.profile_commentScalarWhereInput[]
  NOT?: Prisma.profile_commentScalarWhereInput | Prisma.profile_commentScalarWhereInput[]
  id?: Prisma.IntFilter<"profile_comment"> | number
  message?: Prisma.StringFilter<"profile_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"profile_comment"> | Date | string
  senderId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
  receiverId?: Prisma.IntNullableFilter<"profile_comment"> | number | null
}

export type profile_commentUpsertWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  update: Prisma.XOR<Prisma.profile_commentUpdateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedUpdateWithoutUser_profile_comment_receiverIdTouserInput>
  create: Prisma.XOR<Prisma.profile_commentCreateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedCreateWithoutUser_profile_comment_receiverIdTouserInput>
}

export type profile_commentUpdateWithWhereUniqueWithoutUser_profile_comment_receiverIdTouserInput = {
  where: Prisma.profile_commentWhereUniqueInput
  data: Prisma.XOR<Prisma.profile_commentUpdateWithoutUser_profile_comment_receiverIdTouserInput, Prisma.profile_commentUncheckedUpdateWithoutUser_profile_comment_receiverIdTouserInput>
}

export type profile_commentUpdateManyWithWhereWithoutUser_profile_comment_receiverIdTouserInput = {
  where: Prisma.profile_commentScalarWhereInput
  data: Prisma.XOR<Prisma.profile_commentUpdateManyMutationInput, Prisma.profile_commentUncheckedUpdateManyWithoutUser_profile_comment_receiverIdTouserInput>
}

export type profile_commentCreateManyUser_profile_comment_senderIdTouserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId?: number | null
}

export type profile_commentCreateManyUser_profile_comment_receiverIdTouserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
}

export type profile_commentUpdateWithoutUser_profile_comment_senderIdTouserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_profile_comment_receiverIdTouser?: Prisma.userUpdateOneWithoutProfile_comment_profile_comment_receiverIdTouserNestedInput
}

export type profile_commentUncheckedUpdateWithoutUser_profile_comment_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type profile_commentUncheckedUpdateManyWithoutUser_profile_comment_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type profile_commentUpdateWithoutUser_profile_comment_receiverIdTouserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_profile_comment_senderIdTouser?: Prisma.userUpdateOneWithoutProfile_comment_profile_comment_senderIdTouserNestedInput
}

export type profile_commentUncheckedUpdateWithoutUser_profile_comment_receiverIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type profile_commentUncheckedUpdateManyWithoutUser_profile_comment_receiverIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type profile_commentSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  message?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
  user_profile_comment_senderIdTouser?: boolean | Prisma.profile_comment$user_profile_comment_senderIdTouserArgs<ExtArgs>
  user_profile_comment_receiverIdTouser?: boolean | Prisma.profile_comment$user_profile_comment_receiverIdTouserArgs<ExtArgs>
}, ExtArgs["result"]["profile_comment"]>



export type profile_commentSelectScalar = {
  id?: boolean
  message?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
}

export type profile_commentOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "message" | "createdAt" | "updatedAt" | "senderId" | "receiverId", ExtArgs["result"]["profile_comment"]>
export type profile_commentInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_profile_comment_senderIdTouser?: boolean | Prisma.profile_comment$user_profile_comment_senderIdTouserArgs<ExtArgs>
  user_profile_comment_receiverIdTouser?: boolean | Prisma.profile_comment$user_profile_comment_receiverIdTouserArgs<ExtArgs>
}

export type $profile_commentPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "profile_comment"
  objects: {
    user_profile_comment_senderIdTouser: Prisma.$userPayload<ExtArgs> | null
    user_profile_comment_receiverIdTouser: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    message: string
    createdAt: Date
    updatedAt: Date
    senderId: number | null
    receiverId: number | null
  }, ExtArgs["result"]["profile_comment"]>
  composites: {}
}

export type profile_commentGetPayload<S extends boolean | null | undefined | profile_commentDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$profile_commentPayload, S>

export type profile_commentCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<profile_commentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Profile_commentCountAggregateInputType | true
  }

export interface profile_commentDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['profile_comment'], meta: { name: 'profile_comment' } }
  /**
   * Find zero or one Profile_comment that matches the filter.
   * @param {profile_commentFindUniqueArgs} args - Arguments to find a Profile_comment
   * @example
   * // Get one Profile_comment
   * const profile_comment = await prisma.profile_comment.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends profile_commentFindUniqueArgs>(args: Prisma.SelectSubset<T, profile_commentFindUniqueArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Profile_comment that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {profile_commentFindUniqueOrThrowArgs} args - Arguments to find a Profile_comment
   * @example
   * // Get one Profile_comment
   * const profile_comment = await prisma.profile_comment.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends profile_commentFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, profile_commentFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Profile_comment that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentFindFirstArgs} args - Arguments to find a Profile_comment
   * @example
   * // Get one Profile_comment
   * const profile_comment = await prisma.profile_comment.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends profile_commentFindFirstArgs>(args?: Prisma.SelectSubset<T, profile_commentFindFirstArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Profile_comment that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentFindFirstOrThrowArgs} args - Arguments to find a Profile_comment
   * @example
   * // Get one Profile_comment
   * const profile_comment = await prisma.profile_comment.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends profile_commentFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, profile_commentFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Profile_comments that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Profile_comments
   * const profile_comments = await prisma.profile_comment.findMany()
   * 
   * // Get first 10 Profile_comments
   * const profile_comments = await prisma.profile_comment.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const profile_commentWithIdOnly = await prisma.profile_comment.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends profile_commentFindManyArgs>(args?: Prisma.SelectSubset<T, profile_commentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Profile_comment.
   * @param {profile_commentCreateArgs} args - Arguments to create a Profile_comment.
   * @example
   * // Create one Profile_comment
   * const Profile_comment = await prisma.profile_comment.create({
   *   data: {
   *     // ... data to create a Profile_comment
   *   }
   * })
   * 
   */
  create<T extends profile_commentCreateArgs>(args: Prisma.SelectSubset<T, profile_commentCreateArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Profile_comments.
   * @param {profile_commentCreateManyArgs} args - Arguments to create many Profile_comments.
   * @example
   * // Create many Profile_comments
   * const profile_comment = await prisma.profile_comment.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends profile_commentCreateManyArgs>(args?: Prisma.SelectSubset<T, profile_commentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Profile_comment.
   * @param {profile_commentDeleteArgs} args - Arguments to delete one Profile_comment.
   * @example
   * // Delete one Profile_comment
   * const Profile_comment = await prisma.profile_comment.delete({
   *   where: {
   *     // ... filter to delete one Profile_comment
   *   }
   * })
   * 
   */
  delete<T extends profile_commentDeleteArgs>(args: Prisma.SelectSubset<T, profile_commentDeleteArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Profile_comment.
   * @param {profile_commentUpdateArgs} args - Arguments to update one Profile_comment.
   * @example
   * // Update one Profile_comment
   * const profile_comment = await prisma.profile_comment.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends profile_commentUpdateArgs>(args: Prisma.SelectSubset<T, profile_commentUpdateArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Profile_comments.
   * @param {profile_commentDeleteManyArgs} args - Arguments to filter Profile_comments to delete.
   * @example
   * // Delete a few Profile_comments
   * const { count } = await prisma.profile_comment.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends profile_commentDeleteManyArgs>(args?: Prisma.SelectSubset<T, profile_commentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Profile_comments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Profile_comments
   * const profile_comment = await prisma.profile_comment.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends profile_commentUpdateManyArgs>(args: Prisma.SelectSubset<T, profile_commentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Profile_comment.
   * @param {profile_commentUpsertArgs} args - Arguments to update or create a Profile_comment.
   * @example
   * // Update or create a Profile_comment
   * const profile_comment = await prisma.profile_comment.upsert({
   *   create: {
   *     // ... data to create a Profile_comment
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Profile_comment we want to update
   *   }
   * })
   */
  upsert<T extends profile_commentUpsertArgs>(args: Prisma.SelectSubset<T, profile_commentUpsertArgs<ExtArgs>>): Prisma.Prisma__profile_commentClient<runtime.Types.Result.GetResult<Prisma.$profile_commentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Profile_comments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentCountArgs} args - Arguments to filter Profile_comments to count.
   * @example
   * // Count the number of Profile_comments
   * const count = await prisma.profile_comment.count({
   *   where: {
   *     // ... the filter for the Profile_comments we want to count
   *   }
   * })
  **/
  count<T extends profile_commentCountArgs>(
    args?: Prisma.Subset<T, profile_commentCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Profile_commentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Profile_comment.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Profile_commentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Profile_commentAggregateArgs>(args: Prisma.Subset<T, Profile_commentAggregateArgs>): Prisma.PrismaPromise<GetProfile_commentAggregateType<T>>

  /**
   * Group by Profile_comment.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {profile_commentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends profile_commentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: profile_commentGroupByArgs['orderBy'] }
      : { orderBy?: profile_commentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, profile_commentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProfile_commentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the profile_comment model
 */
readonly fields: profile_commentFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for profile_comment.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__profile_commentClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_profile_comment_senderIdTouser<T extends Prisma.profile_comment$user_profile_comment_senderIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.profile_comment$user_profile_comment_senderIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_profile_comment_receiverIdTouser<T extends Prisma.profile_comment$user_profile_comment_receiverIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.profile_comment$user_profile_comment_receiverIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the profile_comment model
 */
export interface profile_commentFieldRefs {
  readonly id: Prisma.FieldRef<"profile_comment", 'Int'>
  readonly message: Prisma.FieldRef<"profile_comment", 'String'>
  readonly createdAt: Prisma.FieldRef<"profile_comment", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"profile_comment", 'DateTime'>
  readonly senderId: Prisma.FieldRef<"profile_comment", 'Int'>
  readonly receiverId: Prisma.FieldRef<"profile_comment", 'Int'>
}
    

// Custom InputTypes
/**
 * profile_comment findUnique
 */
export type profile_commentFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter, which profile_comment to fetch.
   */
  where: Prisma.profile_commentWhereUniqueInput
}

/**
 * profile_comment findUniqueOrThrow
 */
export type profile_commentFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter, which profile_comment to fetch.
   */
  where: Prisma.profile_commentWhereUniqueInput
}

/**
 * profile_comment findFirst
 */
export type profile_commentFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter, which profile_comment to fetch.
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of profile_comments to fetch.
   */
  orderBy?: Prisma.profile_commentOrderByWithRelationInput | Prisma.profile_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for profile_comments.
   */
  cursor?: Prisma.profile_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` profile_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` profile_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of profile_comments.
   */
  distinct?: Prisma.Profile_commentScalarFieldEnum | Prisma.Profile_commentScalarFieldEnum[]
}

/**
 * profile_comment findFirstOrThrow
 */
export type profile_commentFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter, which profile_comment to fetch.
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of profile_comments to fetch.
   */
  orderBy?: Prisma.profile_commentOrderByWithRelationInput | Prisma.profile_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for profile_comments.
   */
  cursor?: Prisma.profile_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` profile_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` profile_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of profile_comments.
   */
  distinct?: Prisma.Profile_commentScalarFieldEnum | Prisma.Profile_commentScalarFieldEnum[]
}

/**
 * profile_comment findMany
 */
export type profile_commentFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter, which profile_comments to fetch.
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of profile_comments to fetch.
   */
  orderBy?: Prisma.profile_commentOrderByWithRelationInput | Prisma.profile_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing profile_comments.
   */
  cursor?: Prisma.profile_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` profile_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` profile_comments.
   */
  skip?: number
  distinct?: Prisma.Profile_commentScalarFieldEnum | Prisma.Profile_commentScalarFieldEnum[]
}

/**
 * profile_comment create
 */
export type profile_commentCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * The data needed to create a profile_comment.
   */
  data: Prisma.XOR<Prisma.profile_commentCreateInput, Prisma.profile_commentUncheckedCreateInput>
}

/**
 * profile_comment createMany
 */
export type profile_commentCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many profile_comments.
   */
  data: Prisma.profile_commentCreateManyInput | Prisma.profile_commentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * profile_comment update
 */
export type profile_commentUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * The data needed to update a profile_comment.
   */
  data: Prisma.XOR<Prisma.profile_commentUpdateInput, Prisma.profile_commentUncheckedUpdateInput>
  /**
   * Choose, which profile_comment to update.
   */
  where: Prisma.profile_commentWhereUniqueInput
}

/**
 * profile_comment updateMany
 */
export type profile_commentUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update profile_comments.
   */
  data: Prisma.XOR<Prisma.profile_commentUpdateManyMutationInput, Prisma.profile_commentUncheckedUpdateManyInput>
  /**
   * Filter which profile_comments to update
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * Limit how many profile_comments to update.
   */
  limit?: number
}

/**
 * profile_comment upsert
 */
export type profile_commentUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * The filter to search for the profile_comment to update in case it exists.
   */
  where: Prisma.profile_commentWhereUniqueInput
  /**
   * In case the profile_comment found by the `where` argument doesn't exist, create a new profile_comment with this data.
   */
  create: Prisma.XOR<Prisma.profile_commentCreateInput, Prisma.profile_commentUncheckedCreateInput>
  /**
   * In case the profile_comment was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.profile_commentUpdateInput, Prisma.profile_commentUncheckedUpdateInput>
}

/**
 * profile_comment delete
 */
export type profile_commentDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
  /**
   * Filter which profile_comment to delete.
   */
  where: Prisma.profile_commentWhereUniqueInput
}

/**
 * profile_comment deleteMany
 */
export type profile_commentDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which profile_comments to delete
   */
  where?: Prisma.profile_commentWhereInput
  /**
   * Limit how many profile_comments to delete.
   */
  limit?: number
}

/**
 * profile_comment.user_profile_comment_senderIdTouser
 */
export type profile_comment$user_profile_comment_senderIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * profile_comment.user_profile_comment_receiverIdTouser
 */
export type profile_comment$user_profile_comment_receiverIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * profile_comment without action
 */
export type profile_commentDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the profile_comment
   */
  select?: Prisma.profile_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the profile_comment
   */
  omit?: Prisma.profile_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.profile_commentInclude<ExtArgs> | null
}
