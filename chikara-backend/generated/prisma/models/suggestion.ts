
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `suggestion` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model suggestion
 * 
 */
export type suggestionModel = runtime.Types.Result.DefaultSelection<Prisma.$suggestionPayload>

export type AggregateSuggestion = {
  _count: SuggestionCountAggregateOutputType | null
  _avg: SuggestionAvgAggregateOutputType | null
  _sum: SuggestionSumAggregateOutputType | null
  _min: SuggestionMinAggregateOutputType | null
  _max: SuggestionMaxAggregateOutputType | null
}

export type SuggestionAvgAggregateOutputType = {
  id: number | null
  upvotes: number | null
  downvotes: number | null
  totalComments: number | null
  userId: number | null
}

export type SuggestionSumAggregateOutputType = {
  id: number | null
  upvotes: number | null
  downvotes: number | null
  totalComments: number | null
  userId: number | null
}

export type SuggestionMinAggregateOutputType = {
  id: number | null
  title: string | null
  content: string | null
  state: $Enums.SuggestionStates | null
  upvotes: number | null
  downvotes: number | null
  totalComments: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type SuggestionMaxAggregateOutputType = {
  id: number | null
  title: string | null
  content: string | null
  state: $Enums.SuggestionStates | null
  upvotes: number | null
  downvotes: number | null
  totalComments: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type SuggestionCountAggregateOutputType = {
  id: number
  title: number
  content: number
  state: number
  upvotes: number
  downvotes: number
  totalComments: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type SuggestionAvgAggregateInputType = {
  id?: true
  upvotes?: true
  downvotes?: true
  totalComments?: true
  userId?: true
}

export type SuggestionSumAggregateInputType = {
  id?: true
  upvotes?: true
  downvotes?: true
  totalComments?: true
  userId?: true
}

export type SuggestionMinAggregateInputType = {
  id?: true
  title?: true
  content?: true
  state?: true
  upvotes?: true
  downvotes?: true
  totalComments?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type SuggestionMaxAggregateInputType = {
  id?: true
  title?: true
  content?: true
  state?: true
  upvotes?: true
  downvotes?: true
  totalComments?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type SuggestionCountAggregateInputType = {
  id?: true
  title?: true
  content?: true
  state?: true
  upvotes?: true
  downvotes?: true
  totalComments?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type SuggestionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestion to aggregate.
   */
  where?: Prisma.suggestionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestions to fetch.
   */
  orderBy?: Prisma.suggestionOrderByWithRelationInput | Prisma.suggestionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.suggestionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned suggestions
  **/
  _count?: true | SuggestionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: SuggestionAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: SuggestionSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SuggestionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SuggestionMaxAggregateInputType
}

export type GetSuggestionAggregateType<T extends SuggestionAggregateArgs> = {
      [P in keyof T & keyof AggregateSuggestion]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSuggestion[P]>
    : Prisma.GetScalarType<T[P], AggregateSuggestion[P]>
}




export type suggestionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.suggestionWhereInput
  orderBy?: Prisma.suggestionOrderByWithAggregationInput | Prisma.suggestionOrderByWithAggregationInput[]
  by: Prisma.SuggestionScalarFieldEnum[] | Prisma.SuggestionScalarFieldEnum
  having?: Prisma.suggestionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SuggestionCountAggregateInputType | true
  _avg?: SuggestionAvgAggregateInputType
  _sum?: SuggestionSumAggregateInputType
  _min?: SuggestionMinAggregateInputType
  _max?: SuggestionMaxAggregateInputType
}

export type SuggestionGroupByOutputType = {
  id: number
  title: string
  content: string
  state: $Enums.SuggestionStates | null
  upvotes: number | null
  downvotes: number | null
  totalComments: number | null
  createdAt: Date
  updatedAt: Date
  userId: number | null
  _count: SuggestionCountAggregateOutputType | null
  _avg: SuggestionAvgAggregateOutputType | null
  _sum: SuggestionSumAggregateOutputType | null
  _min: SuggestionMinAggregateOutputType | null
  _max: SuggestionMaxAggregateOutputType | null
}

type GetSuggestionGroupByPayload<T extends suggestionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SuggestionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SuggestionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SuggestionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SuggestionGroupByOutputType[P]>
      }
    >
  >



export type suggestionWhereInput = {
  AND?: Prisma.suggestionWhereInput | Prisma.suggestionWhereInput[]
  OR?: Prisma.suggestionWhereInput[]
  NOT?: Prisma.suggestionWhereInput | Prisma.suggestionWhereInput[]
  id?: Prisma.IntFilter<"suggestion"> | number
  title?: Prisma.StringFilter<"suggestion"> | string
  content?: Prisma.StringFilter<"suggestion"> | string
  state?: Prisma.EnumSuggestionStatesNullableFilter<"suggestion"> | $Enums.SuggestionStates | null
  upvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  downvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  totalComments?: Prisma.IntNullableFilter<"suggestion"> | number | null
  createdAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion_comment?: Prisma.Suggestion_commentListRelationFilter
  suggestion_vote?: Prisma.Suggestion_voteListRelationFilter
}

export type suggestionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  state?: Prisma.SortOrderInput | Prisma.SortOrder
  upvotes?: Prisma.SortOrderInput | Prisma.SortOrder
  downvotes?: Prisma.SortOrderInput | Prisma.SortOrder
  totalComments?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  suggestion_comment?: Prisma.suggestion_commentOrderByRelationAggregateInput
  suggestion_vote?: Prisma.suggestion_voteOrderByRelationAggregateInput
  _relevance?: Prisma.suggestionOrderByRelevanceInput
}

export type suggestionWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.suggestionWhereInput | Prisma.suggestionWhereInput[]
  OR?: Prisma.suggestionWhereInput[]
  NOT?: Prisma.suggestionWhereInput | Prisma.suggestionWhereInput[]
  title?: Prisma.StringFilter<"suggestion"> | string
  content?: Prisma.StringFilter<"suggestion"> | string
  state?: Prisma.EnumSuggestionStatesNullableFilter<"suggestion"> | $Enums.SuggestionStates | null
  upvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  downvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  totalComments?: Prisma.IntNullableFilter<"suggestion"> | number | null
  createdAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion_comment?: Prisma.Suggestion_commentListRelationFilter
  suggestion_vote?: Prisma.Suggestion_voteListRelationFilter
}, "id">

export type suggestionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  state?: Prisma.SortOrderInput | Prisma.SortOrder
  upvotes?: Prisma.SortOrderInput | Prisma.SortOrder
  downvotes?: Prisma.SortOrderInput | Prisma.SortOrder
  totalComments?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.suggestionCountOrderByAggregateInput
  _avg?: Prisma.suggestionAvgOrderByAggregateInput
  _max?: Prisma.suggestionMaxOrderByAggregateInput
  _min?: Prisma.suggestionMinOrderByAggregateInput
  _sum?: Prisma.suggestionSumOrderByAggregateInput
}

export type suggestionScalarWhereWithAggregatesInput = {
  AND?: Prisma.suggestionScalarWhereWithAggregatesInput | Prisma.suggestionScalarWhereWithAggregatesInput[]
  OR?: Prisma.suggestionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.suggestionScalarWhereWithAggregatesInput | Prisma.suggestionScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"suggestion"> | number
  title?: Prisma.StringWithAggregatesFilter<"suggestion"> | string
  content?: Prisma.StringWithAggregatesFilter<"suggestion"> | string
  state?: Prisma.EnumSuggestionStatesNullableWithAggregatesFilter<"suggestion"> | $Enums.SuggestionStates | null
  upvotes?: Prisma.IntNullableWithAggregatesFilter<"suggestion"> | number | null
  downvotes?: Prisma.IntNullableWithAggregatesFilter<"suggestion"> | number | null
  totalComments?: Prisma.IntNullableWithAggregatesFilter<"suggestion"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"suggestion"> | number | null
}

export type suggestionCreateInput = {
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestionInput
  suggestion_comment?: Prisma.suggestion_commentCreateNestedManyWithoutSuggestionInput
  suggestion_vote?: Prisma.suggestion_voteCreateNestedManyWithoutSuggestionInput
}

export type suggestionUncheckedCreateInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestion_comment?: Prisma.suggestion_commentUncheckedCreateNestedManyWithoutSuggestionInput
  suggestion_vote?: Prisma.suggestion_voteUncheckedCreateNestedManyWithoutSuggestionInput
}

export type suggestionUpdateInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestionNestedInput
  suggestion_comment?: Prisma.suggestion_commentUpdateManyWithoutSuggestionNestedInput
  suggestion_vote?: Prisma.suggestion_voteUpdateManyWithoutSuggestionNestedInput
}

export type suggestionUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestion_comment?: Prisma.suggestion_commentUncheckedUpdateManyWithoutSuggestionNestedInput
  suggestion_vote?: Prisma.suggestion_voteUncheckedUpdateManyWithoutSuggestionNestedInput
}

export type suggestionCreateManyInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type suggestionUpdateManyMutationInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type suggestionUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestionOrderByRelevanceInput = {
  fields: Prisma.suggestionOrderByRelevanceFieldEnum | Prisma.suggestionOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type suggestionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  state?: Prisma.SortOrder
  upvotes?: Prisma.SortOrder
  downvotes?: Prisma.SortOrder
  totalComments?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type suggestionAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  upvotes?: Prisma.SortOrder
  downvotes?: Prisma.SortOrder
  totalComments?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type suggestionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  state?: Prisma.SortOrder
  upvotes?: Prisma.SortOrder
  downvotes?: Prisma.SortOrder
  totalComments?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type suggestionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  state?: Prisma.SortOrder
  upvotes?: Prisma.SortOrder
  downvotes?: Prisma.SortOrder
  totalComments?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type suggestionSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  upvotes?: Prisma.SortOrder
  downvotes?: Prisma.SortOrder
  totalComments?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SuggestionNullableScalarRelationFilter = {
  is?: Prisma.suggestionWhereInput | null
  isNot?: Prisma.suggestionWhereInput | null
}

export type SuggestionListRelationFilter = {
  every?: Prisma.suggestionWhereInput
  some?: Prisma.suggestionWhereInput
  none?: Prisma.suggestionWhereInput
}

export type suggestionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NullableEnumSuggestionStatesFieldUpdateOperationsInput = {
  set?: $Enums.SuggestionStates | null
}

export type suggestionCreateNestedOneWithoutSuggestion_commentInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_commentInput>
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutSuggestion_commentInput
  connect?: Prisma.suggestionWhereUniqueInput
}

export type suggestionUpdateOneWithoutSuggestion_commentNestedInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_commentInput>
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutSuggestion_commentInput
  upsert?: Prisma.suggestionUpsertWithoutSuggestion_commentInput
  disconnect?: Prisma.suggestionWhereInput | boolean
  delete?: Prisma.suggestionWhereInput | boolean
  connect?: Prisma.suggestionWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.suggestionUpdateToOneWithWhereWithoutSuggestion_commentInput, Prisma.suggestionUpdateWithoutSuggestion_commentInput>, Prisma.suggestionUncheckedUpdateWithoutSuggestion_commentInput>
}

export type suggestionCreateNestedOneWithoutSuggestion_voteInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_voteInput>
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutSuggestion_voteInput
  connect?: Prisma.suggestionWhereUniqueInput
}

export type suggestionUpdateOneWithoutSuggestion_voteNestedInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_voteInput>
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutSuggestion_voteInput
  upsert?: Prisma.suggestionUpsertWithoutSuggestion_voteInput
  disconnect?: Prisma.suggestionWhereInput | boolean
  delete?: Prisma.suggestionWhereInput | boolean
  connect?: Prisma.suggestionWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.suggestionUpdateToOneWithWhereWithoutSuggestion_voteInput, Prisma.suggestionUpdateWithoutSuggestion_voteInput>, Prisma.suggestionUncheckedUpdateWithoutSuggestion_voteInput>
}

export type suggestionCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput> | Prisma.suggestionCreateWithoutUserInput[] | Prisma.suggestionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutUserInput | Prisma.suggestionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestionCreateManyUserInputEnvelope
  connect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
}

export type suggestionUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput> | Prisma.suggestionCreateWithoutUserInput[] | Prisma.suggestionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutUserInput | Prisma.suggestionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestionCreateManyUserInputEnvelope
  connect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
}

export type suggestionUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput> | Prisma.suggestionCreateWithoutUserInput[] | Prisma.suggestionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutUserInput | Prisma.suggestionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestionUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestionCreateManyUserInputEnvelope
  set?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  disconnect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  delete?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  connect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  update?: Prisma.suggestionUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestionUpdateManyWithWhereWithoutUserInput | Prisma.suggestionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestionScalarWhereInput | Prisma.suggestionScalarWhereInput[]
}

export type suggestionUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput> | Prisma.suggestionCreateWithoutUserInput[] | Prisma.suggestionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestionCreateOrConnectWithoutUserInput | Prisma.suggestionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestionUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestionCreateManyUserInputEnvelope
  set?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  disconnect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  delete?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  connect?: Prisma.suggestionWhereUniqueInput | Prisma.suggestionWhereUniqueInput[]
  update?: Prisma.suggestionUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestionUpdateManyWithWhereWithoutUserInput | Prisma.suggestionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestionScalarWhereInput | Prisma.suggestionScalarWhereInput[]
}

export type suggestionCreateWithoutSuggestion_commentInput = {
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestionInput
  suggestion_vote?: Prisma.suggestion_voteCreateNestedManyWithoutSuggestionInput
}

export type suggestionUncheckedCreateWithoutSuggestion_commentInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestion_vote?: Prisma.suggestion_voteUncheckedCreateNestedManyWithoutSuggestionInput
}

export type suggestionCreateOrConnectWithoutSuggestion_commentInput = {
  where: Prisma.suggestionWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_commentInput>
}

export type suggestionUpsertWithoutSuggestion_commentInput = {
  update: Prisma.XOR<Prisma.suggestionUpdateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedUpdateWithoutSuggestion_commentInput>
  create: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_commentInput>
  where?: Prisma.suggestionWhereInput
}

export type suggestionUpdateToOneWithWhereWithoutSuggestion_commentInput = {
  where?: Prisma.suggestionWhereInput
  data: Prisma.XOR<Prisma.suggestionUpdateWithoutSuggestion_commentInput, Prisma.suggestionUncheckedUpdateWithoutSuggestion_commentInput>
}

export type suggestionUpdateWithoutSuggestion_commentInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestionNestedInput
  suggestion_vote?: Prisma.suggestion_voteUpdateManyWithoutSuggestionNestedInput
}

export type suggestionUncheckedUpdateWithoutSuggestion_commentInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestion_vote?: Prisma.suggestion_voteUncheckedUpdateManyWithoutSuggestionNestedInput
}

export type suggestionCreateWithoutSuggestion_voteInput = {
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestionInput
  suggestion_comment?: Prisma.suggestion_commentCreateNestedManyWithoutSuggestionInput
}

export type suggestionUncheckedCreateWithoutSuggestion_voteInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestion_comment?: Prisma.suggestion_commentUncheckedCreateNestedManyWithoutSuggestionInput
}

export type suggestionCreateOrConnectWithoutSuggestion_voteInput = {
  where: Prisma.suggestionWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_voteInput>
}

export type suggestionUpsertWithoutSuggestion_voteInput = {
  update: Prisma.XOR<Prisma.suggestionUpdateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedUpdateWithoutSuggestion_voteInput>
  create: Prisma.XOR<Prisma.suggestionCreateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedCreateWithoutSuggestion_voteInput>
  where?: Prisma.suggestionWhereInput
}

export type suggestionUpdateToOneWithWhereWithoutSuggestion_voteInput = {
  where?: Prisma.suggestionWhereInput
  data: Prisma.XOR<Prisma.suggestionUpdateWithoutSuggestion_voteInput, Prisma.suggestionUncheckedUpdateWithoutSuggestion_voteInput>
}

export type suggestionUpdateWithoutSuggestion_voteInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestionNestedInput
  suggestion_comment?: Prisma.suggestion_commentUpdateManyWithoutSuggestionNestedInput
}

export type suggestionUncheckedUpdateWithoutSuggestion_voteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestion_comment?: Prisma.suggestion_commentUncheckedUpdateManyWithoutSuggestionNestedInput
}

export type suggestionCreateWithoutUserInput = {
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestion_comment?: Prisma.suggestion_commentCreateNestedManyWithoutSuggestionInput
  suggestion_vote?: Prisma.suggestion_voteCreateNestedManyWithoutSuggestionInput
}

export type suggestionUncheckedCreateWithoutUserInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestion_comment?: Prisma.suggestion_commentUncheckedCreateNestedManyWithoutSuggestionInput
  suggestion_vote?: Prisma.suggestion_voteUncheckedCreateNestedManyWithoutSuggestionInput
}

export type suggestionCreateOrConnectWithoutUserInput = {
  where: Prisma.suggestionWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput>
}

export type suggestionCreateManyUserInputEnvelope = {
  data: Prisma.suggestionCreateManyUserInput | Prisma.suggestionCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type suggestionUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestionWhereUniqueInput
  update: Prisma.XOR<Prisma.suggestionUpdateWithoutUserInput, Prisma.suggestionUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.suggestionCreateWithoutUserInput, Prisma.suggestionUncheckedCreateWithoutUserInput>
}

export type suggestionUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestionWhereUniqueInput
  data: Prisma.XOR<Prisma.suggestionUpdateWithoutUserInput, Prisma.suggestionUncheckedUpdateWithoutUserInput>
}

export type suggestionUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.suggestionScalarWhereInput
  data: Prisma.XOR<Prisma.suggestionUpdateManyMutationInput, Prisma.suggestionUncheckedUpdateManyWithoutUserInput>
}

export type suggestionScalarWhereInput = {
  AND?: Prisma.suggestionScalarWhereInput | Prisma.suggestionScalarWhereInput[]
  OR?: Prisma.suggestionScalarWhereInput[]
  NOT?: Prisma.suggestionScalarWhereInput | Prisma.suggestionScalarWhereInput[]
  id?: Prisma.IntFilter<"suggestion"> | number
  title?: Prisma.StringFilter<"suggestion"> | string
  content?: Prisma.StringFilter<"suggestion"> | string
  state?: Prisma.EnumSuggestionStatesNullableFilter<"suggestion"> | $Enums.SuggestionStates | null
  upvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  downvotes?: Prisma.IntNullableFilter<"suggestion"> | number | null
  totalComments?: Prisma.IntNullableFilter<"suggestion"> | number | null
  createdAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion"> | number | null
}

export type suggestionCreateManyUserInput = {
  id?: number
  title: string
  content: string
  state?: $Enums.SuggestionStates | null
  upvotes?: number | null
  downvotes?: number | null
  totalComments?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type suggestionUpdateWithoutUserInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestion_comment?: Prisma.suggestion_commentUpdateManyWithoutSuggestionNestedInput
  suggestion_vote?: Prisma.suggestion_voteUpdateManyWithoutSuggestionNestedInput
}

export type suggestionUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestion_comment?: Prisma.suggestion_commentUncheckedUpdateManyWithoutSuggestionNestedInput
  suggestion_vote?: Prisma.suggestion_voteUncheckedUpdateManyWithoutSuggestionNestedInput
}

export type suggestionUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  state?: Prisma.NullableEnumSuggestionStatesFieldUpdateOperationsInput | $Enums.SuggestionStates | null
  upvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  downvotes?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalComments?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type SuggestionCountOutputType
 */

export type SuggestionCountOutputType = {
  suggestion_comment: number
  suggestion_vote: number
}

export type SuggestionCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  suggestion_comment?: boolean | SuggestionCountOutputTypeCountSuggestion_commentArgs
  suggestion_vote?: boolean | SuggestionCountOutputTypeCountSuggestion_voteArgs
}

/**
 * SuggestionCountOutputType without action
 */
export type SuggestionCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SuggestionCountOutputType
   */
  select?: Prisma.SuggestionCountOutputTypeSelect<ExtArgs> | null
}

/**
 * SuggestionCountOutputType without action
 */
export type SuggestionCountOutputTypeCountSuggestion_commentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.suggestion_commentWhereInput
}

/**
 * SuggestionCountOutputType without action
 */
export type SuggestionCountOutputTypeCountSuggestion_voteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.suggestion_voteWhereInput
}


export type suggestionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  content?: boolean
  state?: boolean
  upvotes?: boolean
  downvotes?: boolean
  totalComments?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.suggestion$userArgs<ExtArgs>
  suggestion_comment?: boolean | Prisma.suggestion$suggestion_commentArgs<ExtArgs>
  suggestion_vote?: boolean | Prisma.suggestion$suggestion_voteArgs<ExtArgs>
  _count?: boolean | Prisma.SuggestionCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["suggestion"]>



export type suggestionSelectScalar = {
  id?: boolean
  title?: boolean
  content?: boolean
  state?: boolean
  upvotes?: boolean
  downvotes?: boolean
  totalComments?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type suggestionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "content" | "state" | "upvotes" | "downvotes" | "totalComments" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["suggestion"]>
export type suggestionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.suggestion$userArgs<ExtArgs>
  suggestion_comment?: boolean | Prisma.suggestion$suggestion_commentArgs<ExtArgs>
  suggestion_vote?: boolean | Prisma.suggestion$suggestion_voteArgs<ExtArgs>
  _count?: boolean | Prisma.SuggestionCountOutputTypeDefaultArgs<ExtArgs>
}

export type $suggestionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "suggestion"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    suggestion_comment: Prisma.$suggestion_commentPayload<ExtArgs>[]
    suggestion_vote: Prisma.$suggestion_votePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    title: string
    content: string
    state: $Enums.SuggestionStates | null
    upvotes: number | null
    downvotes: number | null
    totalComments: number | null
    createdAt: Date
    updatedAt: Date
    userId: number | null
  }, ExtArgs["result"]["suggestion"]>
  composites: {}
}

export type suggestionGetPayload<S extends boolean | null | undefined | suggestionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$suggestionPayload, S>

export type suggestionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<suggestionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SuggestionCountAggregateInputType | true
  }

export interface suggestionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['suggestion'], meta: { name: 'suggestion' } }
  /**
   * Find zero or one Suggestion that matches the filter.
   * @param {suggestionFindUniqueArgs} args - Arguments to find a Suggestion
   * @example
   * // Get one Suggestion
   * const suggestion = await prisma.suggestion.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends suggestionFindUniqueArgs>(args: Prisma.SelectSubset<T, suggestionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Suggestion that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {suggestionFindUniqueOrThrowArgs} args - Arguments to find a Suggestion
   * @example
   * // Get one Suggestion
   * const suggestion = await prisma.suggestion.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends suggestionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, suggestionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionFindFirstArgs} args - Arguments to find a Suggestion
   * @example
   * // Get one Suggestion
   * const suggestion = await prisma.suggestion.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends suggestionFindFirstArgs>(args?: Prisma.SelectSubset<T, suggestionFindFirstArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionFindFirstOrThrowArgs} args - Arguments to find a Suggestion
   * @example
   * // Get one Suggestion
   * const suggestion = await prisma.suggestion.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends suggestionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, suggestionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Suggestions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Suggestions
   * const suggestions = await prisma.suggestion.findMany()
   * 
   * // Get first 10 Suggestions
   * const suggestions = await prisma.suggestion.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const suggestionWithIdOnly = await prisma.suggestion.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends suggestionFindManyArgs>(args?: Prisma.SelectSubset<T, suggestionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Suggestion.
   * @param {suggestionCreateArgs} args - Arguments to create a Suggestion.
   * @example
   * // Create one Suggestion
   * const Suggestion = await prisma.suggestion.create({
   *   data: {
   *     // ... data to create a Suggestion
   *   }
   * })
   * 
   */
  create<T extends suggestionCreateArgs>(args: Prisma.SelectSubset<T, suggestionCreateArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Suggestions.
   * @param {suggestionCreateManyArgs} args - Arguments to create many Suggestions.
   * @example
   * // Create many Suggestions
   * const suggestion = await prisma.suggestion.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends suggestionCreateManyArgs>(args?: Prisma.SelectSubset<T, suggestionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Suggestion.
   * @param {suggestionDeleteArgs} args - Arguments to delete one Suggestion.
   * @example
   * // Delete one Suggestion
   * const Suggestion = await prisma.suggestion.delete({
   *   where: {
   *     // ... filter to delete one Suggestion
   *   }
   * })
   * 
   */
  delete<T extends suggestionDeleteArgs>(args: Prisma.SelectSubset<T, suggestionDeleteArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Suggestion.
   * @param {suggestionUpdateArgs} args - Arguments to update one Suggestion.
   * @example
   * // Update one Suggestion
   * const suggestion = await prisma.suggestion.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends suggestionUpdateArgs>(args: Prisma.SelectSubset<T, suggestionUpdateArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Suggestions.
   * @param {suggestionDeleteManyArgs} args - Arguments to filter Suggestions to delete.
   * @example
   * // Delete a few Suggestions
   * const { count } = await prisma.suggestion.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends suggestionDeleteManyArgs>(args?: Prisma.SelectSubset<T, suggestionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Suggestions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Suggestions
   * const suggestion = await prisma.suggestion.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends suggestionUpdateManyArgs>(args: Prisma.SelectSubset<T, suggestionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Suggestion.
   * @param {suggestionUpsertArgs} args - Arguments to update or create a Suggestion.
   * @example
   * // Update or create a Suggestion
   * const suggestion = await prisma.suggestion.upsert({
   *   create: {
   *     // ... data to create a Suggestion
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Suggestion we want to update
   *   }
   * })
   */
  upsert<T extends suggestionUpsertArgs>(args: Prisma.SelectSubset<T, suggestionUpsertArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Suggestions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionCountArgs} args - Arguments to filter Suggestions to count.
   * @example
   * // Count the number of Suggestions
   * const count = await prisma.suggestion.count({
   *   where: {
   *     // ... the filter for the Suggestions we want to count
   *   }
   * })
  **/
  count<T extends suggestionCountArgs>(
    args?: Prisma.Subset<T, suggestionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SuggestionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Suggestion.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SuggestionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SuggestionAggregateArgs>(args: Prisma.Subset<T, SuggestionAggregateArgs>): Prisma.PrismaPromise<GetSuggestionAggregateType<T>>

  /**
   * Group by Suggestion.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends suggestionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: suggestionGroupByArgs['orderBy'] }
      : { orderBy?: suggestionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, suggestionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSuggestionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the suggestion model
 */
readonly fields: suggestionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for suggestion.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__suggestionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.suggestion$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  suggestion_comment<T extends Prisma.suggestion$suggestion_commentArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion$suggestion_commentArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  suggestion_vote<T extends Prisma.suggestion$suggestion_voteArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion$suggestion_voteArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$suggestion_votePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the suggestion model
 */
export interface suggestionFieldRefs {
  readonly id: Prisma.FieldRef<"suggestion", 'Int'>
  readonly title: Prisma.FieldRef<"suggestion", 'String'>
  readonly content: Prisma.FieldRef<"suggestion", 'String'>
  readonly state: Prisma.FieldRef<"suggestion", 'SuggestionStates'>
  readonly upvotes: Prisma.FieldRef<"suggestion", 'Int'>
  readonly downvotes: Prisma.FieldRef<"suggestion", 'Int'>
  readonly totalComments: Prisma.FieldRef<"suggestion", 'Int'>
  readonly createdAt: Prisma.FieldRef<"suggestion", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"suggestion", 'DateTime'>
  readonly userId: Prisma.FieldRef<"suggestion", 'Int'>
}
    

// Custom InputTypes
/**
 * suggestion findUnique
 */
export type suggestionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter, which suggestion to fetch.
   */
  where: Prisma.suggestionWhereUniqueInput
}

/**
 * suggestion findUniqueOrThrow
 */
export type suggestionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter, which suggestion to fetch.
   */
  where: Prisma.suggestionWhereUniqueInput
}

/**
 * suggestion findFirst
 */
export type suggestionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter, which suggestion to fetch.
   */
  where?: Prisma.suggestionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestions to fetch.
   */
  orderBy?: Prisma.suggestionOrderByWithRelationInput | Prisma.suggestionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestions.
   */
  cursor?: Prisma.suggestionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestions.
   */
  distinct?: Prisma.SuggestionScalarFieldEnum | Prisma.SuggestionScalarFieldEnum[]
}

/**
 * suggestion findFirstOrThrow
 */
export type suggestionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter, which suggestion to fetch.
   */
  where?: Prisma.suggestionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestions to fetch.
   */
  orderBy?: Prisma.suggestionOrderByWithRelationInput | Prisma.suggestionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestions.
   */
  cursor?: Prisma.suggestionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestions.
   */
  distinct?: Prisma.SuggestionScalarFieldEnum | Prisma.SuggestionScalarFieldEnum[]
}

/**
 * suggestion findMany
 */
export type suggestionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter, which suggestions to fetch.
   */
  where?: Prisma.suggestionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestions to fetch.
   */
  orderBy?: Prisma.suggestionOrderByWithRelationInput | Prisma.suggestionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing suggestions.
   */
  cursor?: Prisma.suggestionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestions.
   */
  skip?: number
  distinct?: Prisma.SuggestionScalarFieldEnum | Prisma.SuggestionScalarFieldEnum[]
}

/**
 * suggestion create
 */
export type suggestionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * The data needed to create a suggestion.
   */
  data: Prisma.XOR<Prisma.suggestionCreateInput, Prisma.suggestionUncheckedCreateInput>
}

/**
 * suggestion createMany
 */
export type suggestionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many suggestions.
   */
  data: Prisma.suggestionCreateManyInput | Prisma.suggestionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * suggestion update
 */
export type suggestionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * The data needed to update a suggestion.
   */
  data: Prisma.XOR<Prisma.suggestionUpdateInput, Prisma.suggestionUncheckedUpdateInput>
  /**
   * Choose, which suggestion to update.
   */
  where: Prisma.suggestionWhereUniqueInput
}

/**
 * suggestion updateMany
 */
export type suggestionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update suggestions.
   */
  data: Prisma.XOR<Prisma.suggestionUpdateManyMutationInput, Prisma.suggestionUncheckedUpdateManyInput>
  /**
   * Filter which suggestions to update
   */
  where?: Prisma.suggestionWhereInput
  /**
   * Limit how many suggestions to update.
   */
  limit?: number
}

/**
 * suggestion upsert
 */
export type suggestionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * The filter to search for the suggestion to update in case it exists.
   */
  where: Prisma.suggestionWhereUniqueInput
  /**
   * In case the suggestion found by the `where` argument doesn't exist, create a new suggestion with this data.
   */
  create: Prisma.XOR<Prisma.suggestionCreateInput, Prisma.suggestionUncheckedCreateInput>
  /**
   * In case the suggestion was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.suggestionUpdateInput, Prisma.suggestionUncheckedUpdateInput>
}

/**
 * suggestion delete
 */
export type suggestionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  /**
   * Filter which suggestion to delete.
   */
  where: Prisma.suggestionWhereUniqueInput
}

/**
 * suggestion deleteMany
 */
export type suggestionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestions to delete
   */
  where?: Prisma.suggestionWhereInput
  /**
   * Limit how many suggestions to delete.
   */
  limit?: number
}

/**
 * suggestion.user
 */
export type suggestion$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * suggestion.suggestion_comment
 */
export type suggestion$suggestion_commentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  where?: Prisma.suggestion_commentWhereInput
  orderBy?: Prisma.suggestion_commentOrderByWithRelationInput | Prisma.suggestion_commentOrderByWithRelationInput[]
  cursor?: Prisma.suggestion_commentWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Suggestion_commentScalarFieldEnum | Prisma.Suggestion_commentScalarFieldEnum[]
}

/**
 * suggestion.suggestion_vote
 */
export type suggestion$suggestion_voteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_vote
   */
  select?: Prisma.suggestion_voteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_vote
   */
  omit?: Prisma.suggestion_voteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_voteInclude<ExtArgs> | null
  where?: Prisma.suggestion_voteWhereInput
  orderBy?: Prisma.suggestion_voteOrderByWithRelationInput | Prisma.suggestion_voteOrderByWithRelationInput[]
  cursor?: Prisma.suggestion_voteWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Suggestion_voteScalarFieldEnum | Prisma.Suggestion_voteScalarFieldEnum[]
}

/**
 * suggestion without action
 */
export type suggestionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
}
