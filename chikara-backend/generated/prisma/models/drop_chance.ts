
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `drop_chance` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model drop_chance
 * 
 */
export type drop_chanceModel = runtime.Types.Result.DefaultSelection<Prisma.$drop_chancePayload>

export type AggregateDrop_chance = {
  _count: Drop_chanceCountAggregateOutputType | null
  _avg: Drop_chanceAvgAggregateOutputType | null
  _sum: Drop_chanceSumAggregateOutputType | null
  _min: Drop_chanceMinAggregateOutputType | null
  _max: Drop_chanceMaxAggregateOutputType | null
}

export type Drop_chanceAvgAggregateOutputType = {
  id: number | null
  dropRate: number | null
  quantity: number | null
  minLevel: number | null
  maxLevel: number | null
  itemId: number | null
}

export type Drop_chanceSumAggregateOutputType = {
  id: number | null
  dropRate: number | null
  quantity: number | null
  minLevel: number | null
  maxLevel: number | null
  itemId: number | null
}

export type Drop_chanceMinAggregateOutputType = {
  id: number | null
  dropRate: number | null
  dropChanceType: $Enums.DropChanceTypes | null
  quantity: number | null
  location: $Enums.LocationTypes | null
  minLevel: number | null
  maxLevel: number | null
  scavengeType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  itemId: number | null
}

export type Drop_chanceMaxAggregateOutputType = {
  id: number | null
  dropRate: number | null
  dropChanceType: $Enums.DropChanceTypes | null
  quantity: number | null
  location: $Enums.LocationTypes | null
  minLevel: number | null
  maxLevel: number | null
  scavengeType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  itemId: number | null
}

export type Drop_chanceCountAggregateOutputType = {
  id: number
  dropRate: number
  dropChanceType: number
  quantity: number
  location: number
  minLevel: number
  maxLevel: number
  scavengeType: number
  createdAt: number
  updatedAt: number
  itemId: number
  _all: number
}


export type Drop_chanceAvgAggregateInputType = {
  id?: true
  dropRate?: true
  quantity?: true
  minLevel?: true
  maxLevel?: true
  itemId?: true
}

export type Drop_chanceSumAggregateInputType = {
  id?: true
  dropRate?: true
  quantity?: true
  minLevel?: true
  maxLevel?: true
  itemId?: true
}

export type Drop_chanceMinAggregateInputType = {
  id?: true
  dropRate?: true
  dropChanceType?: true
  quantity?: true
  location?: true
  minLevel?: true
  maxLevel?: true
  scavengeType?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
}

export type Drop_chanceMaxAggregateInputType = {
  id?: true
  dropRate?: true
  dropChanceType?: true
  quantity?: true
  location?: true
  minLevel?: true
  maxLevel?: true
  scavengeType?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
}

export type Drop_chanceCountAggregateInputType = {
  id?: true
  dropRate?: true
  dropChanceType?: true
  quantity?: true
  location?: true
  minLevel?: true
  maxLevel?: true
  scavengeType?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
  _all?: true
}

export type Drop_chanceAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which drop_chance to aggregate.
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of drop_chances to fetch.
   */
  orderBy?: Prisma.drop_chanceOrderByWithRelationInput | Prisma.drop_chanceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.drop_chanceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` drop_chances from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` drop_chances.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned drop_chances
  **/
  _count?: true | Drop_chanceCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Drop_chanceAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Drop_chanceSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Drop_chanceMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Drop_chanceMaxAggregateInputType
}

export type GetDrop_chanceAggregateType<T extends Drop_chanceAggregateArgs> = {
      [P in keyof T & keyof AggregateDrop_chance]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDrop_chance[P]>
    : Prisma.GetScalarType<T[P], AggregateDrop_chance[P]>
}




export type drop_chanceGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.drop_chanceWhereInput
  orderBy?: Prisma.drop_chanceOrderByWithAggregationInput | Prisma.drop_chanceOrderByWithAggregationInput[]
  by: Prisma.Drop_chanceScalarFieldEnum[] | Prisma.Drop_chanceScalarFieldEnum
  having?: Prisma.drop_chanceScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Drop_chanceCountAggregateInputType | true
  _avg?: Drop_chanceAvgAggregateInputType
  _sum?: Drop_chanceSumAggregateInputType
  _min?: Drop_chanceMinAggregateInputType
  _max?: Drop_chanceMaxAggregateInputType
}

export type Drop_chanceGroupByOutputType = {
  id: number
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity: number
  location: $Enums.LocationTypes | null
  minLevel: number | null
  maxLevel: number | null
  scavengeType: string | null
  createdAt: Date
  updatedAt: Date
  itemId: number | null
  _count: Drop_chanceCountAggregateOutputType | null
  _avg: Drop_chanceAvgAggregateOutputType | null
  _sum: Drop_chanceSumAggregateOutputType | null
  _min: Drop_chanceMinAggregateOutputType | null
  _max: Drop_chanceMaxAggregateOutputType | null
}

type GetDrop_chanceGroupByPayload<T extends drop_chanceGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Drop_chanceGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Drop_chanceGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Drop_chanceGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Drop_chanceGroupByOutputType[P]>
      }
    >
  >



export type drop_chanceWhereInput = {
  AND?: Prisma.drop_chanceWhereInput | Prisma.drop_chanceWhereInput[]
  OR?: Prisma.drop_chanceWhereInput[]
  NOT?: Prisma.drop_chanceWhereInput | Prisma.drop_chanceWhereInput[]
  id?: Prisma.IntFilter<"drop_chance"> | number
  dropRate?: Prisma.FloatFilter<"drop_chance"> | number
  dropChanceType?: Prisma.EnumDropChanceTypesFilter<"drop_chance"> | $Enums.DropChanceTypes
  quantity?: Prisma.IntFilter<"drop_chance"> | number
  location?: Prisma.EnumLocationTypesNullableFilter<"drop_chance"> | $Enums.LocationTypes | null
  minLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  maxLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  scavengeType?: Prisma.StringNullableFilter<"drop_chance"> | string | null
  createdAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  itemId?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}

export type drop_chanceOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  dropChanceType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  minLevel?: Prisma.SortOrderInput | Prisma.SortOrder
  maxLevel?: Prisma.SortOrderInput | Prisma.SortOrder
  scavengeType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  item?: Prisma.itemOrderByWithRelationInput
  _relevance?: Prisma.drop_chanceOrderByRelevanceInput
}

export type drop_chanceWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.drop_chanceWhereInput | Prisma.drop_chanceWhereInput[]
  OR?: Prisma.drop_chanceWhereInput[]
  NOT?: Prisma.drop_chanceWhereInput | Prisma.drop_chanceWhereInput[]
  dropRate?: Prisma.FloatFilter<"drop_chance"> | number
  dropChanceType?: Prisma.EnumDropChanceTypesFilter<"drop_chance"> | $Enums.DropChanceTypes
  quantity?: Prisma.IntFilter<"drop_chance"> | number
  location?: Prisma.EnumLocationTypesNullableFilter<"drop_chance"> | $Enums.LocationTypes | null
  minLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  maxLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  scavengeType?: Prisma.StringNullableFilter<"drop_chance"> | string | null
  createdAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  itemId?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}, "id">

export type drop_chanceOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  dropChanceType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  minLevel?: Prisma.SortOrderInput | Prisma.SortOrder
  maxLevel?: Prisma.SortOrderInput | Prisma.SortOrder
  scavengeType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.drop_chanceCountOrderByAggregateInput
  _avg?: Prisma.drop_chanceAvgOrderByAggregateInput
  _max?: Prisma.drop_chanceMaxOrderByAggregateInput
  _min?: Prisma.drop_chanceMinOrderByAggregateInput
  _sum?: Prisma.drop_chanceSumOrderByAggregateInput
}

export type drop_chanceScalarWhereWithAggregatesInput = {
  AND?: Prisma.drop_chanceScalarWhereWithAggregatesInput | Prisma.drop_chanceScalarWhereWithAggregatesInput[]
  OR?: Prisma.drop_chanceScalarWhereWithAggregatesInput[]
  NOT?: Prisma.drop_chanceScalarWhereWithAggregatesInput | Prisma.drop_chanceScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"drop_chance"> | number
  dropRate?: Prisma.FloatWithAggregatesFilter<"drop_chance"> | number
  dropChanceType?: Prisma.EnumDropChanceTypesWithAggregatesFilter<"drop_chance"> | $Enums.DropChanceTypes
  quantity?: Prisma.IntWithAggregatesFilter<"drop_chance"> | number
  location?: Prisma.EnumLocationTypesNullableWithAggregatesFilter<"drop_chance"> | $Enums.LocationTypes | null
  minLevel?: Prisma.IntNullableWithAggregatesFilter<"drop_chance"> | number | null
  maxLevel?: Prisma.IntNullableWithAggregatesFilter<"drop_chance"> | number | null
  scavengeType?: Prisma.StringNullableWithAggregatesFilter<"drop_chance"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"drop_chance"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"drop_chance"> | Date | string
  itemId?: Prisma.IntNullableWithAggregatesFilter<"drop_chance"> | number | null
}

export type drop_chanceCreateInput = {
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutDrop_chanceInput
}

export type drop_chanceUncheckedCreateInput = {
  id?: number
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
}

export type drop_chanceUpdateInput = {
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutDrop_chanceNestedInput
}

export type drop_chanceUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type drop_chanceCreateManyInput = {
  id?: number
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
}

export type drop_chanceUpdateManyMutationInput = {
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type drop_chanceUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type drop_chanceOrderByRelevanceInput = {
  fields: Prisma.drop_chanceOrderByRelevanceFieldEnum | Prisma.drop_chanceOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type drop_chanceCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  dropChanceType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  minLevel?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  scavengeType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type drop_chanceAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  minLevel?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type drop_chanceMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  dropChanceType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  minLevel?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  scavengeType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type drop_chanceMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  dropChanceType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  minLevel?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  scavengeType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type drop_chanceSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  dropRate?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  minLevel?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type Drop_chanceListRelationFilter = {
  every?: Prisma.drop_chanceWhereInput
  some?: Prisma.drop_chanceWhereInput
  none?: Prisma.drop_chanceWhereInput
}

export type drop_chanceOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type FloatFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type EnumDropChanceTypesFieldUpdateOperationsInput = {
  set?: $Enums.DropChanceTypes
}

export type drop_chanceCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput> | Prisma.drop_chanceCreateWithoutItemInput[] | Prisma.drop_chanceUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.drop_chanceCreateOrConnectWithoutItemInput | Prisma.drop_chanceCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.drop_chanceCreateManyItemInputEnvelope
  connect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
}

export type drop_chanceUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput> | Prisma.drop_chanceCreateWithoutItemInput[] | Prisma.drop_chanceUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.drop_chanceCreateOrConnectWithoutItemInput | Prisma.drop_chanceCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.drop_chanceCreateManyItemInputEnvelope
  connect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
}

export type drop_chanceUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput> | Prisma.drop_chanceCreateWithoutItemInput[] | Prisma.drop_chanceUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.drop_chanceCreateOrConnectWithoutItemInput | Prisma.drop_chanceCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.drop_chanceUpsertWithWhereUniqueWithoutItemInput | Prisma.drop_chanceUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.drop_chanceCreateManyItemInputEnvelope
  set?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  disconnect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  delete?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  connect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  update?: Prisma.drop_chanceUpdateWithWhereUniqueWithoutItemInput | Prisma.drop_chanceUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.drop_chanceUpdateManyWithWhereWithoutItemInput | Prisma.drop_chanceUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.drop_chanceScalarWhereInput | Prisma.drop_chanceScalarWhereInput[]
}

export type drop_chanceUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput> | Prisma.drop_chanceCreateWithoutItemInput[] | Prisma.drop_chanceUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.drop_chanceCreateOrConnectWithoutItemInput | Prisma.drop_chanceCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.drop_chanceUpsertWithWhereUniqueWithoutItemInput | Prisma.drop_chanceUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.drop_chanceCreateManyItemInputEnvelope
  set?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  disconnect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  delete?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  connect?: Prisma.drop_chanceWhereUniqueInput | Prisma.drop_chanceWhereUniqueInput[]
  update?: Prisma.drop_chanceUpdateWithWhereUniqueWithoutItemInput | Prisma.drop_chanceUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.drop_chanceUpdateManyWithWhereWithoutItemInput | Prisma.drop_chanceUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.drop_chanceScalarWhereInput | Prisma.drop_chanceScalarWhereInput[]
}

export type drop_chanceCreateWithoutItemInput = {
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type drop_chanceUncheckedCreateWithoutItemInput = {
  id?: number
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type drop_chanceCreateOrConnectWithoutItemInput = {
  where: Prisma.drop_chanceWhereUniqueInput
  create: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput>
}

export type drop_chanceCreateManyItemInputEnvelope = {
  data: Prisma.drop_chanceCreateManyItemInput | Prisma.drop_chanceCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type drop_chanceUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.drop_chanceWhereUniqueInput
  update: Prisma.XOR<Prisma.drop_chanceUpdateWithoutItemInput, Prisma.drop_chanceUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.drop_chanceCreateWithoutItemInput, Prisma.drop_chanceUncheckedCreateWithoutItemInput>
}

export type drop_chanceUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.drop_chanceWhereUniqueInput
  data: Prisma.XOR<Prisma.drop_chanceUpdateWithoutItemInput, Prisma.drop_chanceUncheckedUpdateWithoutItemInput>
}

export type drop_chanceUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.drop_chanceScalarWhereInput
  data: Prisma.XOR<Prisma.drop_chanceUpdateManyMutationInput, Prisma.drop_chanceUncheckedUpdateManyWithoutItemInput>
}

export type drop_chanceScalarWhereInput = {
  AND?: Prisma.drop_chanceScalarWhereInput | Prisma.drop_chanceScalarWhereInput[]
  OR?: Prisma.drop_chanceScalarWhereInput[]
  NOT?: Prisma.drop_chanceScalarWhereInput | Prisma.drop_chanceScalarWhereInput[]
  id?: Prisma.IntFilter<"drop_chance"> | number
  dropRate?: Prisma.FloatFilter<"drop_chance"> | number
  dropChanceType?: Prisma.EnumDropChanceTypesFilter<"drop_chance"> | $Enums.DropChanceTypes
  quantity?: Prisma.IntFilter<"drop_chance"> | number
  location?: Prisma.EnumLocationTypesNullableFilter<"drop_chance"> | $Enums.LocationTypes | null
  minLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  maxLevel?: Prisma.IntNullableFilter<"drop_chance"> | number | null
  scavengeType?: Prisma.StringNullableFilter<"drop_chance"> | string | null
  createdAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"drop_chance"> | Date | string
  itemId?: Prisma.IntNullableFilter<"drop_chance"> | number | null
}

export type drop_chanceCreateManyItemInput = {
  id?: number
  dropRate: number
  dropChanceType: $Enums.DropChanceTypes
  quantity?: number
  location?: $Enums.LocationTypes | null
  minLevel?: number | null
  maxLevel?: number | null
  scavengeType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type drop_chanceUpdateWithoutItemInput = {
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type drop_chanceUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type drop_chanceUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  dropRate?: Prisma.FloatFieldUpdateOperationsInput | number
  dropChanceType?: Prisma.EnumDropChanceTypesFieldUpdateOperationsInput | $Enums.DropChanceTypes
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  minLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  maxLevel?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  scavengeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type drop_chanceSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  dropRate?: boolean
  dropChanceType?: boolean
  quantity?: boolean
  location?: boolean
  minLevel?: boolean
  maxLevel?: boolean
  scavengeType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemId?: boolean
  item?: boolean | Prisma.drop_chance$itemArgs<ExtArgs>
}, ExtArgs["result"]["drop_chance"]>



export type drop_chanceSelectScalar = {
  id?: boolean
  dropRate?: boolean
  dropChanceType?: boolean
  quantity?: boolean
  location?: boolean
  minLevel?: boolean
  maxLevel?: boolean
  scavengeType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemId?: boolean
}

export type drop_chanceOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "dropRate" | "dropChanceType" | "quantity" | "location" | "minLevel" | "maxLevel" | "scavengeType" | "createdAt" | "updatedAt" | "itemId", ExtArgs["result"]["drop_chance"]>
export type drop_chanceInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Prisma.drop_chance$itemArgs<ExtArgs>
}

export type $drop_chancePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "drop_chance"
  objects: {
    item: Prisma.$itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    dropRate: number
    dropChanceType: $Enums.DropChanceTypes
    quantity: number
    location: $Enums.LocationTypes | null
    minLevel: number | null
    maxLevel: number | null
    scavengeType: string | null
    createdAt: Date
    updatedAt: Date
    itemId: number | null
  }, ExtArgs["result"]["drop_chance"]>
  composites: {}
}

export type drop_chanceGetPayload<S extends boolean | null | undefined | drop_chanceDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$drop_chancePayload, S>

export type drop_chanceCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<drop_chanceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Drop_chanceCountAggregateInputType | true
  }

export interface drop_chanceDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['drop_chance'], meta: { name: 'drop_chance' } }
  /**
   * Find zero or one Drop_chance that matches the filter.
   * @param {drop_chanceFindUniqueArgs} args - Arguments to find a Drop_chance
   * @example
   * // Get one Drop_chance
   * const drop_chance = await prisma.drop_chance.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends drop_chanceFindUniqueArgs>(args: Prisma.SelectSubset<T, drop_chanceFindUniqueArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Drop_chance that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {drop_chanceFindUniqueOrThrowArgs} args - Arguments to find a Drop_chance
   * @example
   * // Get one Drop_chance
   * const drop_chance = await prisma.drop_chance.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends drop_chanceFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, drop_chanceFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Drop_chance that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceFindFirstArgs} args - Arguments to find a Drop_chance
   * @example
   * // Get one Drop_chance
   * const drop_chance = await prisma.drop_chance.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends drop_chanceFindFirstArgs>(args?: Prisma.SelectSubset<T, drop_chanceFindFirstArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Drop_chance that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceFindFirstOrThrowArgs} args - Arguments to find a Drop_chance
   * @example
   * // Get one Drop_chance
   * const drop_chance = await prisma.drop_chance.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends drop_chanceFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, drop_chanceFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Drop_chances that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Drop_chances
   * const drop_chances = await prisma.drop_chance.findMany()
   * 
   * // Get first 10 Drop_chances
   * const drop_chances = await prisma.drop_chance.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const drop_chanceWithIdOnly = await prisma.drop_chance.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends drop_chanceFindManyArgs>(args?: Prisma.SelectSubset<T, drop_chanceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Drop_chance.
   * @param {drop_chanceCreateArgs} args - Arguments to create a Drop_chance.
   * @example
   * // Create one Drop_chance
   * const Drop_chance = await prisma.drop_chance.create({
   *   data: {
   *     // ... data to create a Drop_chance
   *   }
   * })
   * 
   */
  create<T extends drop_chanceCreateArgs>(args: Prisma.SelectSubset<T, drop_chanceCreateArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Drop_chances.
   * @param {drop_chanceCreateManyArgs} args - Arguments to create many Drop_chances.
   * @example
   * // Create many Drop_chances
   * const drop_chance = await prisma.drop_chance.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends drop_chanceCreateManyArgs>(args?: Prisma.SelectSubset<T, drop_chanceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Drop_chance.
   * @param {drop_chanceDeleteArgs} args - Arguments to delete one Drop_chance.
   * @example
   * // Delete one Drop_chance
   * const Drop_chance = await prisma.drop_chance.delete({
   *   where: {
   *     // ... filter to delete one Drop_chance
   *   }
   * })
   * 
   */
  delete<T extends drop_chanceDeleteArgs>(args: Prisma.SelectSubset<T, drop_chanceDeleteArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Drop_chance.
   * @param {drop_chanceUpdateArgs} args - Arguments to update one Drop_chance.
   * @example
   * // Update one Drop_chance
   * const drop_chance = await prisma.drop_chance.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends drop_chanceUpdateArgs>(args: Prisma.SelectSubset<T, drop_chanceUpdateArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Drop_chances.
   * @param {drop_chanceDeleteManyArgs} args - Arguments to filter Drop_chances to delete.
   * @example
   * // Delete a few Drop_chances
   * const { count } = await prisma.drop_chance.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends drop_chanceDeleteManyArgs>(args?: Prisma.SelectSubset<T, drop_chanceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Drop_chances.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Drop_chances
   * const drop_chance = await prisma.drop_chance.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends drop_chanceUpdateManyArgs>(args: Prisma.SelectSubset<T, drop_chanceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Drop_chance.
   * @param {drop_chanceUpsertArgs} args - Arguments to update or create a Drop_chance.
   * @example
   * // Update or create a Drop_chance
   * const drop_chance = await prisma.drop_chance.upsert({
   *   create: {
   *     // ... data to create a Drop_chance
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Drop_chance we want to update
   *   }
   * })
   */
  upsert<T extends drop_chanceUpsertArgs>(args: Prisma.SelectSubset<T, drop_chanceUpsertArgs<ExtArgs>>): Prisma.Prisma__drop_chanceClient<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Drop_chances.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceCountArgs} args - Arguments to filter Drop_chances to count.
   * @example
   * // Count the number of Drop_chances
   * const count = await prisma.drop_chance.count({
   *   where: {
   *     // ... the filter for the Drop_chances we want to count
   *   }
   * })
  **/
  count<T extends drop_chanceCountArgs>(
    args?: Prisma.Subset<T, drop_chanceCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Drop_chanceCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Drop_chance.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Drop_chanceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Drop_chanceAggregateArgs>(args: Prisma.Subset<T, Drop_chanceAggregateArgs>): Prisma.PrismaPromise<GetDrop_chanceAggregateType<T>>

  /**
   * Group by Drop_chance.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {drop_chanceGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends drop_chanceGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: drop_chanceGroupByArgs['orderBy'] }
      : { orderBy?: drop_chanceGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, drop_chanceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDrop_chanceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the drop_chance model
 */
readonly fields: drop_chanceFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for drop_chance.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__drop_chanceClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  item<T extends Prisma.drop_chance$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.drop_chance$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the drop_chance model
 */
export interface drop_chanceFieldRefs {
  readonly id: Prisma.FieldRef<"drop_chance", 'Int'>
  readonly dropRate: Prisma.FieldRef<"drop_chance", 'Float'>
  readonly dropChanceType: Prisma.FieldRef<"drop_chance", 'DropChanceTypes'>
  readonly quantity: Prisma.FieldRef<"drop_chance", 'Int'>
  readonly location: Prisma.FieldRef<"drop_chance", 'LocationTypes'>
  readonly minLevel: Prisma.FieldRef<"drop_chance", 'Int'>
  readonly maxLevel: Prisma.FieldRef<"drop_chance", 'Int'>
  readonly scavengeType: Prisma.FieldRef<"drop_chance", 'String'>
  readonly createdAt: Prisma.FieldRef<"drop_chance", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"drop_chance", 'DateTime'>
  readonly itemId: Prisma.FieldRef<"drop_chance", 'Int'>
}
    

// Custom InputTypes
/**
 * drop_chance findUnique
 */
export type drop_chanceFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter, which drop_chance to fetch.
   */
  where: Prisma.drop_chanceWhereUniqueInput
}

/**
 * drop_chance findUniqueOrThrow
 */
export type drop_chanceFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter, which drop_chance to fetch.
   */
  where: Prisma.drop_chanceWhereUniqueInput
}

/**
 * drop_chance findFirst
 */
export type drop_chanceFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter, which drop_chance to fetch.
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of drop_chances to fetch.
   */
  orderBy?: Prisma.drop_chanceOrderByWithRelationInput | Prisma.drop_chanceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for drop_chances.
   */
  cursor?: Prisma.drop_chanceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` drop_chances from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` drop_chances.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of drop_chances.
   */
  distinct?: Prisma.Drop_chanceScalarFieldEnum | Prisma.Drop_chanceScalarFieldEnum[]
}

/**
 * drop_chance findFirstOrThrow
 */
export type drop_chanceFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter, which drop_chance to fetch.
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of drop_chances to fetch.
   */
  orderBy?: Prisma.drop_chanceOrderByWithRelationInput | Prisma.drop_chanceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for drop_chances.
   */
  cursor?: Prisma.drop_chanceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` drop_chances from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` drop_chances.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of drop_chances.
   */
  distinct?: Prisma.Drop_chanceScalarFieldEnum | Prisma.Drop_chanceScalarFieldEnum[]
}

/**
 * drop_chance findMany
 */
export type drop_chanceFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter, which drop_chances to fetch.
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of drop_chances to fetch.
   */
  orderBy?: Prisma.drop_chanceOrderByWithRelationInput | Prisma.drop_chanceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing drop_chances.
   */
  cursor?: Prisma.drop_chanceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` drop_chances from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` drop_chances.
   */
  skip?: number
  distinct?: Prisma.Drop_chanceScalarFieldEnum | Prisma.Drop_chanceScalarFieldEnum[]
}

/**
 * drop_chance create
 */
export type drop_chanceCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * The data needed to create a drop_chance.
   */
  data: Prisma.XOR<Prisma.drop_chanceCreateInput, Prisma.drop_chanceUncheckedCreateInput>
}

/**
 * drop_chance createMany
 */
export type drop_chanceCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many drop_chances.
   */
  data: Prisma.drop_chanceCreateManyInput | Prisma.drop_chanceCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * drop_chance update
 */
export type drop_chanceUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * The data needed to update a drop_chance.
   */
  data: Prisma.XOR<Prisma.drop_chanceUpdateInput, Prisma.drop_chanceUncheckedUpdateInput>
  /**
   * Choose, which drop_chance to update.
   */
  where: Prisma.drop_chanceWhereUniqueInput
}

/**
 * drop_chance updateMany
 */
export type drop_chanceUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update drop_chances.
   */
  data: Prisma.XOR<Prisma.drop_chanceUpdateManyMutationInput, Prisma.drop_chanceUncheckedUpdateManyInput>
  /**
   * Filter which drop_chances to update
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * Limit how many drop_chances to update.
   */
  limit?: number
}

/**
 * drop_chance upsert
 */
export type drop_chanceUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * The filter to search for the drop_chance to update in case it exists.
   */
  where: Prisma.drop_chanceWhereUniqueInput
  /**
   * In case the drop_chance found by the `where` argument doesn't exist, create a new drop_chance with this data.
   */
  create: Prisma.XOR<Prisma.drop_chanceCreateInput, Prisma.drop_chanceUncheckedCreateInput>
  /**
   * In case the drop_chance was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.drop_chanceUpdateInput, Prisma.drop_chanceUncheckedUpdateInput>
}

/**
 * drop_chance delete
 */
export type drop_chanceDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  /**
   * Filter which drop_chance to delete.
   */
  where: Prisma.drop_chanceWhereUniqueInput
}

/**
 * drop_chance deleteMany
 */
export type drop_chanceDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which drop_chances to delete
   */
  where?: Prisma.drop_chanceWhereInput
  /**
   * Limit how many drop_chances to delete.
   */
  limit?: number
}

/**
 * drop_chance.item
 */
export type drop_chance$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * drop_chance without action
 */
export type drop_chanceDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
}
