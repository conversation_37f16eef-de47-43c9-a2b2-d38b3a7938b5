
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `creature` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model creature
 * 
 */
export type creatureModel = runtime.Types.Result.DefaultSelection<Prisma.$creaturePayload>

export type AggregateCreature = {
  _count: CreatureCountAggregateOutputType | null
  _avg: CreatureAvgAggregateOutputType | null
  _sum: CreatureSumAggregateOutputType | null
  _min: CreatureMinAggregateOutputType | null
  _max: CreatureMaxAggregateOutputType | null
}

export type CreatureAvgAggregateOutputType = {
  id: number | null
  minFloor: number | null
  maxFloor: number | null
  health: number | null
  currentHealth: number | null
  strength: number | null
  defence: number | null
  weaponDamage: number | null
}

export type CreatureSumAggregateOutputType = {
  id: number | null
  minFloor: number | null
  maxFloor: number | null
  health: number | null
  currentHealth: number | null
  strength: number | null
  defence: number | null
  weaponDamage: number | null
}

export type CreatureMinAggregateOutputType = {
  id: number | null
  name: string | null
  image: string | null
  minFloor: number | null
  maxFloor: number | null
  boss: boolean | null
  health: number | null
  currentHealth: number | null
  strength: number | null
  defence: number | null
  weaponDamage: number | null
  location: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type CreatureMaxAggregateOutputType = {
  id: number | null
  name: string | null
  image: string | null
  minFloor: number | null
  maxFloor: number | null
  boss: boolean | null
  health: number | null
  currentHealth: number | null
  strength: number | null
  defence: number | null
  weaponDamage: number | null
  location: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type CreatureCountAggregateOutputType = {
  id: number
  name: number
  image: number
  minFloor: number
  maxFloor: number
  boss: number
  health: number
  currentHealth: number
  strength: number
  defence: number
  weaponDamage: number
  location: number
  statType: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type CreatureAvgAggregateInputType = {
  id?: true
  minFloor?: true
  maxFloor?: true
  health?: true
  currentHealth?: true
  strength?: true
  defence?: true
  weaponDamage?: true
}

export type CreatureSumAggregateInputType = {
  id?: true
  minFloor?: true
  maxFloor?: true
  health?: true
  currentHealth?: true
  strength?: true
  defence?: true
  weaponDamage?: true
}

export type CreatureMinAggregateInputType = {
  id?: true
  name?: true
  image?: true
  minFloor?: true
  maxFloor?: true
  boss?: true
  health?: true
  currentHealth?: true
  strength?: true
  defence?: true
  weaponDamage?: true
  location?: true
  statType?: true
  createdAt?: true
  updatedAt?: true
}

export type CreatureMaxAggregateInputType = {
  id?: true
  name?: true
  image?: true
  minFloor?: true
  maxFloor?: true
  boss?: true
  health?: true
  currentHealth?: true
  strength?: true
  defence?: true
  weaponDamage?: true
  location?: true
  statType?: true
  createdAt?: true
  updatedAt?: true
}

export type CreatureCountAggregateInputType = {
  id?: true
  name?: true
  image?: true
  minFloor?: true
  maxFloor?: true
  boss?: true
  health?: true
  currentHealth?: true
  strength?: true
  defence?: true
  weaponDamage?: true
  location?: true
  statType?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type CreatureAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which creature to aggregate.
   */
  where?: Prisma.creatureWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of creatures to fetch.
   */
  orderBy?: Prisma.creatureOrderByWithRelationInput | Prisma.creatureOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.creatureWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` creatures from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` creatures.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned creatures
  **/
  _count?: true | CreatureCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: CreatureAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: CreatureSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: CreatureMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: CreatureMaxAggregateInputType
}

export type GetCreatureAggregateType<T extends CreatureAggregateArgs> = {
      [P in keyof T & keyof AggregateCreature]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateCreature[P]>
    : Prisma.GetScalarType<T[P], AggregateCreature[P]>
}




export type creatureGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.creatureWhereInput
  orderBy?: Prisma.creatureOrderByWithAggregationInput | Prisma.creatureOrderByWithAggregationInput[]
  by: Prisma.CreatureScalarFieldEnum[] | Prisma.CreatureScalarFieldEnum
  having?: Prisma.creatureScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: CreatureCountAggregateInputType | true
  _avg?: CreatureAvgAggregateInputType
  _sum?: CreatureSumAggregateInputType
  _min?: CreatureMinAggregateInputType
  _max?: CreatureMaxAggregateInputType
}

export type CreatureGroupByOutputType = {
  id: number
  name: string
  image: string
  minFloor: number
  maxFloor: number
  boss: boolean
  health: number
  currentHealth: number | null
  strength: number
  defence: number
  weaponDamage: number
  location: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt: Date
  updatedAt: Date
  _count: CreatureCountAggregateOutputType | null
  _avg: CreatureAvgAggregateOutputType | null
  _sum: CreatureSumAggregateOutputType | null
  _min: CreatureMinAggregateOutputType | null
  _max: CreatureMaxAggregateOutputType | null
}

type GetCreatureGroupByPayload<T extends creatureGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<CreatureGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof CreatureGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], CreatureGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], CreatureGroupByOutputType[P]>
      }
    >
  >



export type creatureWhereInput = {
  AND?: Prisma.creatureWhereInput | Prisma.creatureWhereInput[]
  OR?: Prisma.creatureWhereInput[]
  NOT?: Prisma.creatureWhereInput | Prisma.creatureWhereInput[]
  id?: Prisma.IntFilter<"creature"> | number
  name?: Prisma.StringFilter<"creature"> | string
  image?: Prisma.StringFilter<"creature"> | string
  minFloor?: Prisma.IntFilter<"creature"> | number
  maxFloor?: Prisma.IntFilter<"creature"> | number
  boss?: Prisma.BoolFilter<"creature"> | boolean
  health?: Prisma.IntFilter<"creature"> | number
  currentHealth?: Prisma.IntNullableFilter<"creature"> | number | null
  strength?: Prisma.IntFilter<"creature"> | number
  defence?: Prisma.IntFilter<"creature"> | number
  weaponDamage?: Prisma.IntFilter<"creature"> | number
  location?: Prisma.EnumLocationTypesNullableFilter<"creature"> | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFilter<"creature"> | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFilter<"creature"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"creature"> | Date | string
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}

export type creatureOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  image?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  boss?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrderInput | Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  statType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  quest_objective?: Prisma.quest_objectiveOrderByRelationAggregateInput
  _relevance?: Prisma.creatureOrderByRelevanceInput
}

export type creatureWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.creatureWhereInput | Prisma.creatureWhereInput[]
  OR?: Prisma.creatureWhereInput[]
  NOT?: Prisma.creatureWhereInput | Prisma.creatureWhereInput[]
  name?: Prisma.StringFilter<"creature"> | string
  image?: Prisma.StringFilter<"creature"> | string
  minFloor?: Prisma.IntFilter<"creature"> | number
  maxFloor?: Prisma.IntFilter<"creature"> | number
  boss?: Prisma.BoolFilter<"creature"> | boolean
  health?: Prisma.IntFilter<"creature"> | number
  currentHealth?: Prisma.IntNullableFilter<"creature"> | number | null
  strength?: Prisma.IntFilter<"creature"> | number
  defence?: Prisma.IntFilter<"creature"> | number
  weaponDamage?: Prisma.IntFilter<"creature"> | number
  location?: Prisma.EnumLocationTypesNullableFilter<"creature"> | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFilter<"creature"> | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFilter<"creature"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"creature"> | Date | string
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}, "id">

export type creatureOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  image?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  boss?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrderInput | Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  statType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.creatureCountOrderByAggregateInput
  _avg?: Prisma.creatureAvgOrderByAggregateInput
  _max?: Prisma.creatureMaxOrderByAggregateInput
  _min?: Prisma.creatureMinOrderByAggregateInput
  _sum?: Prisma.creatureSumOrderByAggregateInput
}

export type creatureScalarWhereWithAggregatesInput = {
  AND?: Prisma.creatureScalarWhereWithAggregatesInput | Prisma.creatureScalarWhereWithAggregatesInput[]
  OR?: Prisma.creatureScalarWhereWithAggregatesInput[]
  NOT?: Prisma.creatureScalarWhereWithAggregatesInput | Prisma.creatureScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"creature"> | number
  name?: Prisma.StringWithAggregatesFilter<"creature"> | string
  image?: Prisma.StringWithAggregatesFilter<"creature"> | string
  minFloor?: Prisma.IntWithAggregatesFilter<"creature"> | number
  maxFloor?: Prisma.IntWithAggregatesFilter<"creature"> | number
  boss?: Prisma.BoolWithAggregatesFilter<"creature"> | boolean
  health?: Prisma.IntWithAggregatesFilter<"creature"> | number
  currentHealth?: Prisma.IntNullableWithAggregatesFilter<"creature"> | number | null
  strength?: Prisma.IntWithAggregatesFilter<"creature"> | number
  defence?: Prisma.IntWithAggregatesFilter<"creature"> | number
  weaponDamage?: Prisma.IntWithAggregatesFilter<"creature"> | number
  location?: Prisma.EnumLocationTypesNullableWithAggregatesFilter<"creature"> | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesWithAggregatesFilter<"creature"> | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"creature"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"creature"> | Date | string
}

export type creatureCreateInput = {
  name: string
  image: string
  minFloor?: number
  maxFloor?: number
  boss: boolean
  health?: number
  currentHealth?: number | null
  strength?: number
  defence?: number
  weaponDamage?: number
  location?: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutCreatureInput
}

export type creatureUncheckedCreateInput = {
  id?: number
  name: string
  image: string
  minFloor?: number
  maxFloor?: number
  boss: boolean
  health?: number
  currentHealth?: number | null
  strength?: number
  defence?: number
  weaponDamage?: number
  location?: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutCreatureInput
}

export type creatureUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutCreatureNestedInput
}

export type creatureUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutCreatureNestedInput
}

export type creatureCreateManyInput = {
  id?: number
  name: string
  image: string
  minFloor?: number
  maxFloor?: number
  boss: boolean
  health?: number
  currentHealth?: number | null
  strength?: number
  defence?: number
  weaponDamage?: number
  location?: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type creatureUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type creatureUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type creatureOrderByRelevanceInput = {
  fields: Prisma.creatureOrderByRelevanceFieldEnum | Prisma.creatureOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type creatureCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  image?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  boss?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
  location?: Prisma.SortOrder
  statType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type creatureAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
}

export type creatureMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  image?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  boss?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
  location?: Prisma.SortOrder
  statType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type creatureMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  image?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  boss?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
  location?: Prisma.SortOrder
  statType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type creatureSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  minFloor?: Prisma.SortOrder
  maxFloor?: Prisma.SortOrder
  health?: Prisma.SortOrder
  currentHealth?: Prisma.SortOrder
  strength?: Prisma.SortOrder
  defence?: Prisma.SortOrder
  weaponDamage?: Prisma.SortOrder
}

export type CreatureNullableScalarRelationFilter = {
  is?: Prisma.creatureWhereInput | null
  isNot?: Prisma.creatureWhereInput | null
}

export type NullableEnumLocationTypesFieldUpdateOperationsInput = {
  set?: $Enums.LocationTypes | null
}

export type EnumCreatureStatTypesFieldUpdateOperationsInput = {
  set?: $Enums.CreatureStatTypes
}

export type creatureCreateNestedOneWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.creatureCreateWithoutQuest_objectiveInput, Prisma.creatureUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.creatureCreateOrConnectWithoutQuest_objectiveInput
  connect?: Prisma.creatureWhereUniqueInput
}

export type creatureUpdateOneWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.creatureCreateWithoutQuest_objectiveInput, Prisma.creatureUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.creatureCreateOrConnectWithoutQuest_objectiveInput
  upsert?: Prisma.creatureUpsertWithoutQuest_objectiveInput
  disconnect?: Prisma.creatureWhereInput | boolean
  delete?: Prisma.creatureWhereInput | boolean
  connect?: Prisma.creatureWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.creatureUpdateToOneWithWhereWithoutQuest_objectiveInput, Prisma.creatureUpdateWithoutQuest_objectiveInput>, Prisma.creatureUncheckedUpdateWithoutQuest_objectiveInput>
}

export type creatureCreateWithoutQuest_objectiveInput = {
  name: string
  image: string
  minFloor?: number
  maxFloor?: number
  boss: boolean
  health?: number
  currentHealth?: number | null
  strength?: number
  defence?: number
  weaponDamage?: number
  location?: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type creatureUncheckedCreateWithoutQuest_objectiveInput = {
  id?: number
  name: string
  image: string
  minFloor?: number
  maxFloor?: number
  boss: boolean
  health?: number
  currentHealth?: number | null
  strength?: number
  defence?: number
  weaponDamage?: number
  location?: $Enums.LocationTypes | null
  statType: $Enums.CreatureStatTypes
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type creatureCreateOrConnectWithoutQuest_objectiveInput = {
  where: Prisma.creatureWhereUniqueInput
  create: Prisma.XOR<Prisma.creatureCreateWithoutQuest_objectiveInput, Prisma.creatureUncheckedCreateWithoutQuest_objectiveInput>
}

export type creatureUpsertWithoutQuest_objectiveInput = {
  update: Prisma.XOR<Prisma.creatureUpdateWithoutQuest_objectiveInput, Prisma.creatureUncheckedUpdateWithoutQuest_objectiveInput>
  create: Prisma.XOR<Prisma.creatureCreateWithoutQuest_objectiveInput, Prisma.creatureUncheckedCreateWithoutQuest_objectiveInput>
  where?: Prisma.creatureWhereInput
}

export type creatureUpdateToOneWithWhereWithoutQuest_objectiveInput = {
  where?: Prisma.creatureWhereInput
  data: Prisma.XOR<Prisma.creatureUpdateWithoutQuest_objectiveInput, Prisma.creatureUncheckedUpdateWithoutQuest_objectiveInput>
}

export type creatureUpdateWithoutQuest_objectiveInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type creatureUncheckedUpdateWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.StringFieldUpdateOperationsInput | string
  minFloor?: Prisma.IntFieldUpdateOperationsInput | number
  maxFloor?: Prisma.IntFieldUpdateOperationsInput | number
  boss?: Prisma.BoolFieldUpdateOperationsInput | boolean
  health?: Prisma.IntFieldUpdateOperationsInput | number
  currentHealth?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  strength?: Prisma.IntFieldUpdateOperationsInput | number
  defence?: Prisma.IntFieldUpdateOperationsInput | number
  weaponDamage?: Prisma.IntFieldUpdateOperationsInput | number
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  statType?: Prisma.EnumCreatureStatTypesFieldUpdateOperationsInput | $Enums.CreatureStatTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type CreatureCountOutputType
 */

export type CreatureCountOutputType = {
  quest_objective: number
}

export type CreatureCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest_objective?: boolean | CreatureCountOutputTypeCountQuest_objectiveArgs
}

/**
 * CreatureCountOutputType without action
 */
export type CreatureCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the CreatureCountOutputType
   */
  select?: Prisma.CreatureCountOutputTypeSelect<ExtArgs> | null
}

/**
 * CreatureCountOutputType without action
 */
export type CreatureCountOutputTypeCountQuest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objectiveWhereInput
}


export type creatureSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  image?: boolean
  minFloor?: boolean
  maxFloor?: boolean
  boss?: boolean
  health?: boolean
  currentHealth?: boolean
  strength?: boolean
  defence?: boolean
  weaponDamage?: boolean
  location?: boolean
  statType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  quest_objective?: boolean | Prisma.creature$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.CreatureCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["creature"]>



export type creatureSelectScalar = {
  id?: boolean
  name?: boolean
  image?: boolean
  minFloor?: boolean
  maxFloor?: boolean
  boss?: boolean
  health?: boolean
  currentHealth?: boolean
  strength?: boolean
  defence?: boolean
  weaponDamage?: boolean
  location?: boolean
  statType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type creatureOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "image" | "minFloor" | "maxFloor" | "boss" | "health" | "currentHealth" | "strength" | "defence" | "weaponDamage" | "location" | "statType" | "createdAt" | "updatedAt", ExtArgs["result"]["creature"]>
export type creatureInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest_objective?: boolean | Prisma.creature$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.CreatureCountOutputTypeDefaultArgs<ExtArgs>
}

export type $creaturePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "creature"
  objects: {
    quest_objective: Prisma.$quest_objectivePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    image: string
    minFloor: number
    maxFloor: number
    boss: boolean
    health: number
    currentHealth: number | null
    strength: number
    defence: number
    weaponDamage: number
    location: $Enums.LocationTypes | null
    statType: $Enums.CreatureStatTypes
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["creature"]>
  composites: {}
}

export type creatureGetPayload<S extends boolean | null | undefined | creatureDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$creaturePayload, S>

export type creatureCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<creatureFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: CreatureCountAggregateInputType | true
  }

export interface creatureDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['creature'], meta: { name: 'creature' } }
  /**
   * Find zero or one Creature that matches the filter.
   * @param {creatureFindUniqueArgs} args - Arguments to find a Creature
   * @example
   * // Get one Creature
   * const creature = await prisma.creature.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends creatureFindUniqueArgs>(args: Prisma.SelectSubset<T, creatureFindUniqueArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Creature that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {creatureFindUniqueOrThrowArgs} args - Arguments to find a Creature
   * @example
   * // Get one Creature
   * const creature = await prisma.creature.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends creatureFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, creatureFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Creature that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureFindFirstArgs} args - Arguments to find a Creature
   * @example
   * // Get one Creature
   * const creature = await prisma.creature.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends creatureFindFirstArgs>(args?: Prisma.SelectSubset<T, creatureFindFirstArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Creature that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureFindFirstOrThrowArgs} args - Arguments to find a Creature
   * @example
   * // Get one Creature
   * const creature = await prisma.creature.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends creatureFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, creatureFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Creatures that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Creatures
   * const creatures = await prisma.creature.findMany()
   * 
   * // Get first 10 Creatures
   * const creatures = await prisma.creature.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const creatureWithIdOnly = await prisma.creature.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends creatureFindManyArgs>(args?: Prisma.SelectSubset<T, creatureFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Creature.
   * @param {creatureCreateArgs} args - Arguments to create a Creature.
   * @example
   * // Create one Creature
   * const Creature = await prisma.creature.create({
   *   data: {
   *     // ... data to create a Creature
   *   }
   * })
   * 
   */
  create<T extends creatureCreateArgs>(args: Prisma.SelectSubset<T, creatureCreateArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Creatures.
   * @param {creatureCreateManyArgs} args - Arguments to create many Creatures.
   * @example
   * // Create many Creatures
   * const creature = await prisma.creature.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends creatureCreateManyArgs>(args?: Prisma.SelectSubset<T, creatureCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Creature.
   * @param {creatureDeleteArgs} args - Arguments to delete one Creature.
   * @example
   * // Delete one Creature
   * const Creature = await prisma.creature.delete({
   *   where: {
   *     // ... filter to delete one Creature
   *   }
   * })
   * 
   */
  delete<T extends creatureDeleteArgs>(args: Prisma.SelectSubset<T, creatureDeleteArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Creature.
   * @param {creatureUpdateArgs} args - Arguments to update one Creature.
   * @example
   * // Update one Creature
   * const creature = await prisma.creature.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends creatureUpdateArgs>(args: Prisma.SelectSubset<T, creatureUpdateArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Creatures.
   * @param {creatureDeleteManyArgs} args - Arguments to filter Creatures to delete.
   * @example
   * // Delete a few Creatures
   * const { count } = await prisma.creature.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends creatureDeleteManyArgs>(args?: Prisma.SelectSubset<T, creatureDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Creatures.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Creatures
   * const creature = await prisma.creature.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends creatureUpdateManyArgs>(args: Prisma.SelectSubset<T, creatureUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Creature.
   * @param {creatureUpsertArgs} args - Arguments to update or create a Creature.
   * @example
   * // Update or create a Creature
   * const creature = await prisma.creature.upsert({
   *   create: {
   *     // ... data to create a Creature
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Creature we want to update
   *   }
   * })
   */
  upsert<T extends creatureUpsertArgs>(args: Prisma.SelectSubset<T, creatureUpsertArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Creatures.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureCountArgs} args - Arguments to filter Creatures to count.
   * @example
   * // Count the number of Creatures
   * const count = await prisma.creature.count({
   *   where: {
   *     // ... the filter for the Creatures we want to count
   *   }
   * })
  **/
  count<T extends creatureCountArgs>(
    args?: Prisma.Subset<T, creatureCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], CreatureCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Creature.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {CreatureAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends CreatureAggregateArgs>(args: Prisma.Subset<T, CreatureAggregateArgs>): Prisma.PrismaPromise<GetCreatureAggregateType<T>>

  /**
   * Group by Creature.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {creatureGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends creatureGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: creatureGroupByArgs['orderBy'] }
      : { orderBy?: creatureGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, creatureGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCreatureGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the creature model
 */
readonly fields: creatureFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for creature.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__creatureClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest_objective<T extends Prisma.creature$quest_objectiveArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.creature$quest_objectiveArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the creature model
 */
export interface creatureFieldRefs {
  readonly id: Prisma.FieldRef<"creature", 'Int'>
  readonly name: Prisma.FieldRef<"creature", 'String'>
  readonly image: Prisma.FieldRef<"creature", 'String'>
  readonly minFloor: Prisma.FieldRef<"creature", 'Int'>
  readonly maxFloor: Prisma.FieldRef<"creature", 'Int'>
  readonly boss: Prisma.FieldRef<"creature", 'Boolean'>
  readonly health: Prisma.FieldRef<"creature", 'Int'>
  readonly currentHealth: Prisma.FieldRef<"creature", 'Int'>
  readonly strength: Prisma.FieldRef<"creature", 'Int'>
  readonly defence: Prisma.FieldRef<"creature", 'Int'>
  readonly weaponDamage: Prisma.FieldRef<"creature", 'Int'>
  readonly location: Prisma.FieldRef<"creature", 'LocationTypes'>
  readonly statType: Prisma.FieldRef<"creature", 'CreatureStatTypes'>
  readonly createdAt: Prisma.FieldRef<"creature", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"creature", 'DateTime'>
}
    

// Custom InputTypes
/**
 * creature findUnique
 */
export type creatureFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter, which creature to fetch.
   */
  where: Prisma.creatureWhereUniqueInput
}

/**
 * creature findUniqueOrThrow
 */
export type creatureFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter, which creature to fetch.
   */
  where: Prisma.creatureWhereUniqueInput
}

/**
 * creature findFirst
 */
export type creatureFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter, which creature to fetch.
   */
  where?: Prisma.creatureWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of creatures to fetch.
   */
  orderBy?: Prisma.creatureOrderByWithRelationInput | Prisma.creatureOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for creatures.
   */
  cursor?: Prisma.creatureWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` creatures from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` creatures.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of creatures.
   */
  distinct?: Prisma.CreatureScalarFieldEnum | Prisma.CreatureScalarFieldEnum[]
}

/**
 * creature findFirstOrThrow
 */
export type creatureFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter, which creature to fetch.
   */
  where?: Prisma.creatureWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of creatures to fetch.
   */
  orderBy?: Prisma.creatureOrderByWithRelationInput | Prisma.creatureOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for creatures.
   */
  cursor?: Prisma.creatureWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` creatures from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` creatures.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of creatures.
   */
  distinct?: Prisma.CreatureScalarFieldEnum | Prisma.CreatureScalarFieldEnum[]
}

/**
 * creature findMany
 */
export type creatureFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter, which creatures to fetch.
   */
  where?: Prisma.creatureWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of creatures to fetch.
   */
  orderBy?: Prisma.creatureOrderByWithRelationInput | Prisma.creatureOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing creatures.
   */
  cursor?: Prisma.creatureWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` creatures from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` creatures.
   */
  skip?: number
  distinct?: Prisma.CreatureScalarFieldEnum | Prisma.CreatureScalarFieldEnum[]
}

/**
 * creature create
 */
export type creatureCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * The data needed to create a creature.
   */
  data: Prisma.XOR<Prisma.creatureCreateInput, Prisma.creatureUncheckedCreateInput>
}

/**
 * creature createMany
 */
export type creatureCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many creatures.
   */
  data: Prisma.creatureCreateManyInput | Prisma.creatureCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * creature update
 */
export type creatureUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * The data needed to update a creature.
   */
  data: Prisma.XOR<Prisma.creatureUpdateInput, Prisma.creatureUncheckedUpdateInput>
  /**
   * Choose, which creature to update.
   */
  where: Prisma.creatureWhereUniqueInput
}

/**
 * creature updateMany
 */
export type creatureUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update creatures.
   */
  data: Prisma.XOR<Prisma.creatureUpdateManyMutationInput, Prisma.creatureUncheckedUpdateManyInput>
  /**
   * Filter which creatures to update
   */
  where?: Prisma.creatureWhereInput
  /**
   * Limit how many creatures to update.
   */
  limit?: number
}

/**
 * creature upsert
 */
export type creatureUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * The filter to search for the creature to update in case it exists.
   */
  where: Prisma.creatureWhereUniqueInput
  /**
   * In case the creature found by the `where` argument doesn't exist, create a new creature with this data.
   */
  create: Prisma.XOR<Prisma.creatureCreateInput, Prisma.creatureUncheckedCreateInput>
  /**
   * In case the creature was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.creatureUpdateInput, Prisma.creatureUncheckedUpdateInput>
}

/**
 * creature delete
 */
export type creatureDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  /**
   * Filter which creature to delete.
   */
  where: Prisma.creatureWhereUniqueInput
}

/**
 * creature deleteMany
 */
export type creatureDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which creatures to delete
   */
  where?: Prisma.creatureWhereInput
  /**
   * Limit how many creatures to delete.
   */
  limit?: number
}

/**
 * creature.quest_objective
 */
export type creature$quest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  where?: Prisma.quest_objectiveWhereInput
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * creature without action
 */
export type creatureDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
}
