
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `talent` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model talent
 * 
 */
export type talentModel = runtime.Types.Result.DefaultSelection<Prisma.$talentPayload>

export type AggregateTalent = {
  _count: TalentCountAggregateOutputType | null
  _avg: TalentAvgAggregateOutputType | null
  _sum: TalentSumAggregateOutputType | null
  _min: TalentMinAggregateOutputType | null
  _max: TalentMaxAggregateOutputType | null
}

export type TalentAvgAggregateOutputType = {
  id: number | null
  skillLevelRequired: number | null
  pointsInTreeRequired: number | null
  pointsCost: number | null
  maxPoints: number | null
  staminaCost: number | null
  tier1Modifier: number | null
  tier2Modifier: number | null
  tier3Modifier: number | null
  secondaryModifier: number | null
}

export type TalentSumAggregateOutputType = {
  id: number | null
  skillLevelRequired: number | null
  pointsInTreeRequired: number | null
  pointsCost: number | null
  maxPoints: number | null
  staminaCost: number | null
  tier1Modifier: number | null
  tier2Modifier: number | null
  tier3Modifier: number | null
  secondaryModifier: number | null
}

export type TalentMinAggregateOutputType = {
  id: number | null
  name: string | null
  displayName: string | null
  tree: $Enums.TalentTree | null
  talentType: $Enums.TalentType | null
  description: string | null
  skillLevelRequired: number | null
  pointsInTreeRequired: number | null
  pointsCost: number | null
  maxPoints: number | null
  staminaCost: number | null
  tier1Modifier: number | null
  tier2Modifier: number | null
  tier3Modifier: number | null
  secondaryModifier: number | null
}

export type TalentMaxAggregateOutputType = {
  id: number | null
  name: string | null
  displayName: string | null
  tree: $Enums.TalentTree | null
  talentType: $Enums.TalentType | null
  description: string | null
  skillLevelRequired: number | null
  pointsInTreeRequired: number | null
  pointsCost: number | null
  maxPoints: number | null
  staminaCost: number | null
  tier1Modifier: number | null
  tier2Modifier: number | null
  tier3Modifier: number | null
  secondaryModifier: number | null
}

export type TalentCountAggregateOutputType = {
  id: number
  name: number
  displayName: number
  tree: number
  talentType: number
  description: number
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost: number
  maxPoints: number
  staminaCost: number
  tier1Modifier: number
  tier2Modifier: number
  tier3Modifier: number
  secondaryModifier: number
  _all: number
}


export type TalentAvgAggregateInputType = {
  id?: true
  skillLevelRequired?: true
  pointsInTreeRequired?: true
  pointsCost?: true
  maxPoints?: true
  staminaCost?: true
  tier1Modifier?: true
  tier2Modifier?: true
  tier3Modifier?: true
  secondaryModifier?: true
}

export type TalentSumAggregateInputType = {
  id?: true
  skillLevelRequired?: true
  pointsInTreeRequired?: true
  pointsCost?: true
  maxPoints?: true
  staminaCost?: true
  tier1Modifier?: true
  tier2Modifier?: true
  tier3Modifier?: true
  secondaryModifier?: true
}

export type TalentMinAggregateInputType = {
  id?: true
  name?: true
  displayName?: true
  tree?: true
  talentType?: true
  description?: true
  skillLevelRequired?: true
  pointsInTreeRequired?: true
  pointsCost?: true
  maxPoints?: true
  staminaCost?: true
  tier1Modifier?: true
  tier2Modifier?: true
  tier3Modifier?: true
  secondaryModifier?: true
}

export type TalentMaxAggregateInputType = {
  id?: true
  name?: true
  displayName?: true
  tree?: true
  talentType?: true
  description?: true
  skillLevelRequired?: true
  pointsInTreeRequired?: true
  pointsCost?: true
  maxPoints?: true
  staminaCost?: true
  tier1Modifier?: true
  tier2Modifier?: true
  tier3Modifier?: true
  secondaryModifier?: true
}

export type TalentCountAggregateInputType = {
  id?: true
  name?: true
  displayName?: true
  tree?: true
  talentType?: true
  description?: true
  skillLevelRequired?: true
  pointsInTreeRequired?: true
  pointsCost?: true
  maxPoints?: true
  staminaCost?: true
  tier1Modifier?: true
  tier2Modifier?: true
  tier3Modifier?: true
  secondaryModifier?: true
  _all?: true
}

export type TalentAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which talent to aggregate.
   */
  where?: Prisma.talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of talents to fetch.
   */
  orderBy?: Prisma.talentOrderByWithRelationInput | Prisma.talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned talents
  **/
  _count?: true | TalentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TalentAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TalentSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TalentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TalentMaxAggregateInputType
}

export type GetTalentAggregateType<T extends TalentAggregateArgs> = {
      [P in keyof T & keyof AggregateTalent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTalent[P]>
    : Prisma.GetScalarType<T[P], AggregateTalent[P]>
}




export type talentGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.talentWhereInput
  orderBy?: Prisma.talentOrderByWithAggregationInput | Prisma.talentOrderByWithAggregationInput[]
  by: Prisma.TalentScalarFieldEnum[] | Prisma.TalentScalarFieldEnum
  having?: Prisma.talentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TalentCountAggregateInputType | true
  _avg?: TalentAvgAggregateInputType
  _sum?: TalentSumAggregateInputType
  _min?: TalentMinAggregateInputType
  _max?: TalentMaxAggregateInputType
}

export type TalentGroupByOutputType = {
  id: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType: $Enums.TalentType
  description: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost: number
  maxPoints: number
  staminaCost: number | null
  tier1Modifier: number | null
  tier2Modifier: number | null
  tier3Modifier: number | null
  secondaryModifier: number | null
  _count: TalentCountAggregateOutputType | null
  _avg: TalentAvgAggregateOutputType | null
  _sum: TalentSumAggregateOutputType | null
  _min: TalentMinAggregateOutputType | null
  _max: TalentMaxAggregateOutputType | null
}

type GetTalentGroupByPayload<T extends talentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TalentGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TalentGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TalentGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TalentGroupByOutputType[P]>
      }
    >
  >



export type talentWhereInput = {
  AND?: Prisma.talentWhereInput | Prisma.talentWhereInput[]
  OR?: Prisma.talentWhereInput[]
  NOT?: Prisma.talentWhereInput | Prisma.talentWhereInput[]
  id?: Prisma.IntFilter<"talent"> | number
  name?: Prisma.StringFilter<"talent"> | string
  displayName?: Prisma.StringFilter<"talent"> | string
  tree?: Prisma.EnumTalentTreeFilter<"talent"> | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFilter<"talent"> | $Enums.TalentType
  description?: Prisma.StringNullableFilter<"talent"> | string | null
  skillLevelRequired?: Prisma.IntFilter<"talent"> | number
  pointsInTreeRequired?: Prisma.IntFilter<"talent"> | number
  pointsCost?: Prisma.IntFilter<"talent"> | number
  maxPoints?: Prisma.IntFilter<"talent"> | number
  staminaCost?: Prisma.IntNullableFilter<"talent"> | number | null
  tier1Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  tier2Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  tier3Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  secondaryModifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  usersWithAbility1?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility2?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility3?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility4?: Prisma.User_equipped_abilitiesListRelationFilter
  user_talent?: Prisma.User_talentListRelationFilter
}

export type talentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  tree?: Prisma.SortOrder
  talentType?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrderInput | Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrderInput | Prisma.SortOrder
  usersWithAbility1?: Prisma.user_equipped_abilitiesOrderByRelationAggregateInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesOrderByRelationAggregateInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesOrderByRelationAggregateInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesOrderByRelationAggregateInput
  user_talent?: Prisma.user_talentOrderByRelationAggregateInput
  _relevance?: Prisma.talentOrderByRelevanceInput
}

export type talentWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.talentWhereInput | Prisma.talentWhereInput[]
  OR?: Prisma.talentWhereInput[]
  NOT?: Prisma.talentWhereInput | Prisma.talentWhereInput[]
  name?: Prisma.StringFilter<"talent"> | string
  displayName?: Prisma.StringFilter<"talent"> | string
  tree?: Prisma.EnumTalentTreeFilter<"talent"> | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFilter<"talent"> | $Enums.TalentType
  description?: Prisma.StringNullableFilter<"talent"> | string | null
  skillLevelRequired?: Prisma.IntFilter<"talent"> | number
  pointsInTreeRequired?: Prisma.IntFilter<"talent"> | number
  pointsCost?: Prisma.IntFilter<"talent"> | number
  maxPoints?: Prisma.IntFilter<"talent"> | number
  staminaCost?: Prisma.IntNullableFilter<"talent"> | number | null
  tier1Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  tier2Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  tier3Modifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  secondaryModifier?: Prisma.FloatNullableFilter<"talent"> | number | null
  usersWithAbility1?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility2?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility3?: Prisma.User_equipped_abilitiesListRelationFilter
  usersWithAbility4?: Prisma.User_equipped_abilitiesListRelationFilter
  user_talent?: Prisma.User_talentListRelationFilter
}, "id">

export type talentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  tree?: Prisma.SortOrder
  talentType?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrderInput | Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.talentCountOrderByAggregateInput
  _avg?: Prisma.talentAvgOrderByAggregateInput
  _max?: Prisma.talentMaxOrderByAggregateInput
  _min?: Prisma.talentMinOrderByAggregateInput
  _sum?: Prisma.talentSumOrderByAggregateInput
}

export type talentScalarWhereWithAggregatesInput = {
  AND?: Prisma.talentScalarWhereWithAggregatesInput | Prisma.talentScalarWhereWithAggregatesInput[]
  OR?: Prisma.talentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.talentScalarWhereWithAggregatesInput | Prisma.talentScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"talent"> | number
  name?: Prisma.StringWithAggregatesFilter<"talent"> | string
  displayName?: Prisma.StringWithAggregatesFilter<"talent"> | string
  tree?: Prisma.EnumTalentTreeWithAggregatesFilter<"talent"> | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeWithAggregatesFilter<"talent"> | $Enums.TalentType
  description?: Prisma.StringNullableWithAggregatesFilter<"talent"> | string | null
  skillLevelRequired?: Prisma.IntWithAggregatesFilter<"talent"> | number
  pointsInTreeRequired?: Prisma.IntWithAggregatesFilter<"talent"> | number
  pointsCost?: Prisma.IntWithAggregatesFilter<"talent"> | number
  maxPoints?: Prisma.IntWithAggregatesFilter<"talent"> | number
  staminaCost?: Prisma.IntNullableWithAggregatesFilter<"talent"> | number | null
  tier1Modifier?: Prisma.FloatNullableWithAggregatesFilter<"talent"> | number | null
  tier2Modifier?: Prisma.FloatNullableWithAggregatesFilter<"talent"> | number | null
  tier3Modifier?: Prisma.FloatNullableWithAggregatesFilter<"talent"> | number | null
  secondaryModifier?: Prisma.FloatNullableWithAggregatesFilter<"talent"> | number | null
}

export type talentCreateInput = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentCreateNestedManyWithoutTalentInput
}

export type talentUncheckedCreateInput = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentUncheckedCreateNestedManyWithoutTalentInput
}

export type talentUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUpdateManyWithoutTalentNestedInput
}

export type talentUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUncheckedUpdateManyWithoutTalentNestedInput
}

export type talentCreateManyInput = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
}

export type talentUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
}

export type talentUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
}

export type talentOrderByRelevanceInput = {
  fields: Prisma.talentOrderByRelevanceFieldEnum | Prisma.talentOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type talentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  tree?: Prisma.SortOrder
  talentType?: Prisma.SortOrder
  description?: Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrder
}

export type talentAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrder
}

export type talentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  tree?: Prisma.SortOrder
  talentType?: Prisma.SortOrder
  description?: Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrder
}

export type talentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  tree?: Prisma.SortOrder
  talentType?: Prisma.SortOrder
  description?: Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrder
}

export type talentSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  skillLevelRequired?: Prisma.SortOrder
  pointsInTreeRequired?: Prisma.SortOrder
  pointsCost?: Prisma.SortOrder
  maxPoints?: Prisma.SortOrder
  staminaCost?: Prisma.SortOrder
  tier1Modifier?: Prisma.SortOrder
  tier2Modifier?: Prisma.SortOrder
  tier3Modifier?: Prisma.SortOrder
  secondaryModifier?: Prisma.SortOrder
}

export type TalentNullableScalarRelationFilter = {
  is?: Prisma.talentWhereInput | null
  isNot?: Prisma.talentWhereInput | null
}

export type TalentScalarRelationFilter = {
  is?: Prisma.talentWhereInput
  isNot?: Prisma.talentWhereInput
}

export type EnumTalentTreeFieldUpdateOperationsInput = {
  set?: $Enums.TalentTree
}

export type EnumTalentTypeFieldUpdateOperationsInput = {
  set?: $Enums.TalentType
}

export type NullableFloatFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type talentCreateNestedOneWithoutUsersWithAbility1Input = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility1Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility1Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility1Input
  connect?: Prisma.talentWhereUniqueInput
}

export type talentCreateNestedOneWithoutUsersWithAbility2Input = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility2Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility2Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility2Input
  connect?: Prisma.talentWhereUniqueInput
}

export type talentCreateNestedOneWithoutUsersWithAbility3Input = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility3Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility3Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility3Input
  connect?: Prisma.talentWhereUniqueInput
}

export type talentCreateNestedOneWithoutUsersWithAbility4Input = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility4Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility4Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility4Input
  connect?: Prisma.talentWhereUniqueInput
}

export type talentUpdateOneWithoutUsersWithAbility1NestedInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility1Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility1Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility1Input
  upsert?: Prisma.talentUpsertWithoutUsersWithAbility1Input
  disconnect?: Prisma.talentWhereInput | boolean
  delete?: Prisma.talentWhereInput | boolean
  connect?: Prisma.talentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.talentUpdateToOneWithWhereWithoutUsersWithAbility1Input, Prisma.talentUpdateWithoutUsersWithAbility1Input>, Prisma.talentUncheckedUpdateWithoutUsersWithAbility1Input>
}

export type talentUpdateOneWithoutUsersWithAbility2NestedInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility2Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility2Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility2Input
  upsert?: Prisma.talentUpsertWithoutUsersWithAbility2Input
  disconnect?: Prisma.talentWhereInput | boolean
  delete?: Prisma.talentWhereInput | boolean
  connect?: Prisma.talentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.talentUpdateToOneWithWhereWithoutUsersWithAbility2Input, Prisma.talentUpdateWithoutUsersWithAbility2Input>, Prisma.talentUncheckedUpdateWithoutUsersWithAbility2Input>
}

export type talentUpdateOneWithoutUsersWithAbility3NestedInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility3Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility3Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility3Input
  upsert?: Prisma.talentUpsertWithoutUsersWithAbility3Input
  disconnect?: Prisma.talentWhereInput | boolean
  delete?: Prisma.talentWhereInput | boolean
  connect?: Prisma.talentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.talentUpdateToOneWithWhereWithoutUsersWithAbility3Input, Prisma.talentUpdateWithoutUsersWithAbility3Input>, Prisma.talentUncheckedUpdateWithoutUsersWithAbility3Input>
}

export type talentUpdateOneWithoutUsersWithAbility4NestedInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility4Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility4Input>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUsersWithAbility4Input
  upsert?: Prisma.talentUpsertWithoutUsersWithAbility4Input
  disconnect?: Prisma.talentWhereInput | boolean
  delete?: Prisma.talentWhereInput | boolean
  connect?: Prisma.talentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.talentUpdateToOneWithWhereWithoutUsersWithAbility4Input, Prisma.talentUpdateWithoutUsersWithAbility4Input>, Prisma.talentUncheckedUpdateWithoutUsersWithAbility4Input>
}

export type talentCreateNestedOneWithoutUser_talentInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUser_talentInput, Prisma.talentUncheckedCreateWithoutUser_talentInput>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUser_talentInput
  connect?: Prisma.talentWhereUniqueInput
}

export type talentUpdateOneRequiredWithoutUser_talentNestedInput = {
  create?: Prisma.XOR<Prisma.talentCreateWithoutUser_talentInput, Prisma.talentUncheckedCreateWithoutUser_talentInput>
  connectOrCreate?: Prisma.talentCreateOrConnectWithoutUser_talentInput
  upsert?: Prisma.talentUpsertWithoutUser_talentInput
  connect?: Prisma.talentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.talentUpdateToOneWithWhereWithoutUser_talentInput, Prisma.talentUpdateWithoutUser_talentInput>, Prisma.talentUncheckedUpdateWithoutUser_talentInput>
}

export type talentCreateWithoutUsersWithAbility1Input = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility2?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentCreateNestedManyWithoutTalentInput
}

export type talentUncheckedCreateWithoutUsersWithAbility1Input = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentUncheckedCreateNestedManyWithoutTalentInput
}

export type talentCreateOrConnectWithoutUsersWithAbility1Input = {
  where: Prisma.talentWhereUniqueInput
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility1Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility1Input>
}

export type talentCreateWithoutUsersWithAbility2Input = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentCreateNestedManyWithoutTalentInput
}

export type talentUncheckedCreateWithoutUsersWithAbility2Input = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentUncheckedCreateNestedManyWithoutTalentInput
}

export type talentCreateOrConnectWithoutUsersWithAbility2Input = {
  where: Prisma.talentWhereUniqueInput
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility2Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility2Input>
}

export type talentCreateWithoutUsersWithAbility3Input = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentCreateNestedManyWithoutTalentInput
}

export type talentUncheckedCreateWithoutUsersWithAbility3Input = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input
  user_talent?: Prisma.user_talentUncheckedCreateNestedManyWithoutTalentInput
}

export type talentCreateOrConnectWithoutUsersWithAbility3Input = {
  where: Prisma.talentWhereUniqueInput
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility3Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility3Input>
}

export type talentCreateWithoutUsersWithAbility4Input = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input
  user_talent?: Prisma.user_talentCreateNestedManyWithoutTalentInput
}

export type talentUncheckedCreateWithoutUsersWithAbility4Input = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input
  user_talent?: Prisma.user_talentUncheckedCreateNestedManyWithoutTalentInput
}

export type talentCreateOrConnectWithoutUsersWithAbility4Input = {
  where: Prisma.talentWhereUniqueInput
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility4Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility4Input>
}

export type talentUpsertWithoutUsersWithAbility1Input = {
  update: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility1Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility1Input>
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility1Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility1Input>
  where?: Prisma.talentWhereInput
}

export type talentUpdateToOneWithWhereWithoutUsersWithAbility1Input = {
  where?: Prisma.talentWhereInput
  data: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility1Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility1Input>
}

export type talentUpdateWithoutUsersWithAbility1Input = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility2?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUpdateManyWithoutTalentNestedInput
}

export type talentUncheckedUpdateWithoutUsersWithAbility1Input = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUncheckedUpdateManyWithoutTalentNestedInput
}

export type talentUpsertWithoutUsersWithAbility2Input = {
  update: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility2Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility2Input>
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility2Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility2Input>
  where?: Prisma.talentWhereInput
}

export type talentUpdateToOneWithWhereWithoutUsersWithAbility2Input = {
  where?: Prisma.talentWhereInput
  data: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility2Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility2Input>
}

export type talentUpdateWithoutUsersWithAbility2Input = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUpdateManyWithoutTalentNestedInput
}

export type talentUncheckedUpdateWithoutUsersWithAbility2Input = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUncheckedUpdateManyWithoutTalentNestedInput
}

export type talentUpsertWithoutUsersWithAbility3Input = {
  update: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility3Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility3Input>
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility3Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility3Input>
  where?: Prisma.talentWhereInput
}

export type talentUpdateToOneWithWhereWithoutUsersWithAbility3Input = {
  where?: Prisma.talentWhereInput
  data: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility3Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility3Input>
}

export type talentUpdateWithoutUsersWithAbility3Input = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUpdateManyWithoutTalentNestedInput
}

export type talentUncheckedUpdateWithoutUsersWithAbility3Input = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput
  user_talent?: Prisma.user_talentUncheckedUpdateManyWithoutTalentNestedInput
}

export type talentUpsertWithoutUsersWithAbility4Input = {
  update: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility4Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility4Input>
  create: Prisma.XOR<Prisma.talentCreateWithoutUsersWithAbility4Input, Prisma.talentUncheckedCreateWithoutUsersWithAbility4Input>
  where?: Prisma.talentWhereInput
}

export type talentUpdateToOneWithWhereWithoutUsersWithAbility4Input = {
  where?: Prisma.talentWhereInput
  data: Prisma.XOR<Prisma.talentUpdateWithoutUsersWithAbility4Input, Prisma.talentUncheckedUpdateWithoutUsersWithAbility4Input>
}

export type talentUpdateWithoutUsersWithAbility4Input = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput
  user_talent?: Prisma.user_talentUpdateManyWithoutTalentNestedInput
}

export type talentUncheckedUpdateWithoutUsersWithAbility4Input = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput
  user_talent?: Prisma.user_talentUncheckedUpdateManyWithoutTalentNestedInput
}

export type talentCreateWithoutUser_talentInput = {
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesCreateNestedManyWithoutTalent_equippedAbility4Input
}

export type talentUncheckedCreateWithoutUser_talentInput = {
  id?: number
  name: string
  displayName: string
  tree: $Enums.TalentTree
  talentType?: $Enums.TalentType
  description?: string | null
  skillLevelRequired: number
  pointsInTreeRequired: number
  pointsCost?: number
  maxPoints: number
  staminaCost?: number | null
  tier1Modifier?: number | null
  tier2Modifier?: number | null
  tier3Modifier?: number | null
  secondaryModifier?: number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility1Input
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility2Input
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility3Input
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedCreateNestedManyWithoutTalent_equippedAbility4Input
}

export type talentCreateOrConnectWithoutUser_talentInput = {
  where: Prisma.talentWhereUniqueInput
  create: Prisma.XOR<Prisma.talentCreateWithoutUser_talentInput, Prisma.talentUncheckedCreateWithoutUser_talentInput>
}

export type talentUpsertWithoutUser_talentInput = {
  update: Prisma.XOR<Prisma.talentUpdateWithoutUser_talentInput, Prisma.talentUncheckedUpdateWithoutUser_talentInput>
  create: Prisma.XOR<Prisma.talentCreateWithoutUser_talentInput, Prisma.talentUncheckedCreateWithoutUser_talentInput>
  where?: Prisma.talentWhereInput
}

export type talentUpdateToOneWithWhereWithoutUser_talentInput = {
  where?: Prisma.talentWhereInput
  data: Prisma.XOR<Prisma.talentUpdateWithoutUser_talentInput, Prisma.talentUncheckedUpdateWithoutUser_talentInput>
}

export type talentUpdateWithoutUser_talentInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUpdateManyWithoutTalent_equippedAbility4NestedInput
}

export type talentUncheckedUpdateWithoutUser_talentInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.StringFieldUpdateOperationsInput | string
  tree?: Prisma.EnumTalentTreeFieldUpdateOperationsInput | $Enums.TalentTree
  talentType?: Prisma.EnumTalentTypeFieldUpdateOperationsInput | $Enums.TalentType
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillLevelRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsInTreeRequired?: Prisma.IntFieldUpdateOperationsInput | number
  pointsCost?: Prisma.IntFieldUpdateOperationsInput | number
  maxPoints?: Prisma.IntFieldUpdateOperationsInput | number
  staminaCost?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  tier1Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier2Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  tier3Modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  secondaryModifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  usersWithAbility1?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility1NestedInput
  usersWithAbility2?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility2NestedInput
  usersWithAbility3?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility3NestedInput
  usersWithAbility4?: Prisma.user_equipped_abilitiesUncheckedUpdateManyWithoutTalent_equippedAbility4NestedInput
}


/**
 * Count Type TalentCountOutputType
 */

export type TalentCountOutputType = {
  usersWithAbility1: number
  usersWithAbility2: number
  usersWithAbility3: number
  usersWithAbility4: number
  user_talent: number
}

export type TalentCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usersWithAbility1?: boolean | TalentCountOutputTypeCountUsersWithAbility1Args
  usersWithAbility2?: boolean | TalentCountOutputTypeCountUsersWithAbility2Args
  usersWithAbility3?: boolean | TalentCountOutputTypeCountUsersWithAbility3Args
  usersWithAbility4?: boolean | TalentCountOutputTypeCountUsersWithAbility4Args
  user_talent?: boolean | TalentCountOutputTypeCountUser_talentArgs
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TalentCountOutputType
   */
  select?: Prisma.TalentCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeCountUsersWithAbility1Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_equipped_abilitiesWhereInput
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeCountUsersWithAbility2Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_equipped_abilitiesWhereInput
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeCountUsersWithAbility3Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_equipped_abilitiesWhereInput
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeCountUsersWithAbility4Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_equipped_abilitiesWhereInput
}

/**
 * TalentCountOutputType without action
 */
export type TalentCountOutputTypeCountUser_talentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_talentWhereInput
}


export type talentSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  displayName?: boolean
  tree?: boolean
  talentType?: boolean
  description?: boolean
  skillLevelRequired?: boolean
  pointsInTreeRequired?: boolean
  pointsCost?: boolean
  maxPoints?: boolean
  staminaCost?: boolean
  tier1Modifier?: boolean
  tier2Modifier?: boolean
  tier3Modifier?: boolean
  secondaryModifier?: boolean
  usersWithAbility1?: boolean | Prisma.talent$usersWithAbility1Args<ExtArgs>
  usersWithAbility2?: boolean | Prisma.talent$usersWithAbility2Args<ExtArgs>
  usersWithAbility3?: boolean | Prisma.talent$usersWithAbility3Args<ExtArgs>
  usersWithAbility4?: boolean | Prisma.talent$usersWithAbility4Args<ExtArgs>
  user_talent?: boolean | Prisma.talent$user_talentArgs<ExtArgs>
  _count?: boolean | Prisma.TalentCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["talent"]>



export type talentSelectScalar = {
  id?: boolean
  name?: boolean
  displayName?: boolean
  tree?: boolean
  talentType?: boolean
  description?: boolean
  skillLevelRequired?: boolean
  pointsInTreeRequired?: boolean
  pointsCost?: boolean
  maxPoints?: boolean
  staminaCost?: boolean
  tier1Modifier?: boolean
  tier2Modifier?: boolean
  tier3Modifier?: boolean
  secondaryModifier?: boolean
}

export type talentOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "displayName" | "tree" | "talentType" | "description" | "skillLevelRequired" | "pointsInTreeRequired" | "pointsCost" | "maxPoints" | "staminaCost" | "tier1Modifier" | "tier2Modifier" | "tier3Modifier" | "secondaryModifier", ExtArgs["result"]["talent"]>
export type talentInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usersWithAbility1?: boolean | Prisma.talent$usersWithAbility1Args<ExtArgs>
  usersWithAbility2?: boolean | Prisma.talent$usersWithAbility2Args<ExtArgs>
  usersWithAbility3?: boolean | Prisma.talent$usersWithAbility3Args<ExtArgs>
  usersWithAbility4?: boolean | Prisma.talent$usersWithAbility4Args<ExtArgs>
  user_talent?: boolean | Prisma.talent$user_talentArgs<ExtArgs>
  _count?: boolean | Prisma.TalentCountOutputTypeDefaultArgs<ExtArgs>
}

export type $talentPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "talent"
  objects: {
    usersWithAbility1: Prisma.$user_equipped_abilitiesPayload<ExtArgs>[]
    usersWithAbility2: Prisma.$user_equipped_abilitiesPayload<ExtArgs>[]
    usersWithAbility3: Prisma.$user_equipped_abilitiesPayload<ExtArgs>[]
    usersWithAbility4: Prisma.$user_equipped_abilitiesPayload<ExtArgs>[]
    user_talent: Prisma.$user_talentPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    displayName: string
    tree: $Enums.TalentTree
    talentType: $Enums.TalentType
    description: string | null
    skillLevelRequired: number
    pointsInTreeRequired: number
    pointsCost: number
    maxPoints: number
    staminaCost: number | null
    tier1Modifier: number | null
    tier2Modifier: number | null
    tier3Modifier: number | null
    secondaryModifier: number | null
  }, ExtArgs["result"]["talent"]>
  composites: {}
}

export type talentGetPayload<S extends boolean | null | undefined | talentDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$talentPayload, S>

export type talentCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<talentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TalentCountAggregateInputType | true
  }

export interface talentDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['talent'], meta: { name: 'talent' } }
  /**
   * Find zero or one Talent that matches the filter.
   * @param {talentFindUniqueArgs} args - Arguments to find a Talent
   * @example
   * // Get one Talent
   * const talent = await prisma.talent.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends talentFindUniqueArgs>(args: Prisma.SelectSubset<T, talentFindUniqueArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Talent that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {talentFindUniqueOrThrowArgs} args - Arguments to find a Talent
   * @example
   * // Get one Talent
   * const talent = await prisma.talent.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends talentFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, talentFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Talent that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentFindFirstArgs} args - Arguments to find a Talent
   * @example
   * // Get one Talent
   * const talent = await prisma.talent.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends talentFindFirstArgs>(args?: Prisma.SelectSubset<T, talentFindFirstArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Talent that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentFindFirstOrThrowArgs} args - Arguments to find a Talent
   * @example
   * // Get one Talent
   * const talent = await prisma.talent.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends talentFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, talentFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Talents that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Talents
   * const talents = await prisma.talent.findMany()
   * 
   * // Get first 10 Talents
   * const talents = await prisma.talent.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const talentWithIdOnly = await prisma.talent.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends talentFindManyArgs>(args?: Prisma.SelectSubset<T, talentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Talent.
   * @param {talentCreateArgs} args - Arguments to create a Talent.
   * @example
   * // Create one Talent
   * const Talent = await prisma.talent.create({
   *   data: {
   *     // ... data to create a Talent
   *   }
   * })
   * 
   */
  create<T extends talentCreateArgs>(args: Prisma.SelectSubset<T, talentCreateArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Talents.
   * @param {talentCreateManyArgs} args - Arguments to create many Talents.
   * @example
   * // Create many Talents
   * const talent = await prisma.talent.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends talentCreateManyArgs>(args?: Prisma.SelectSubset<T, talentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Talent.
   * @param {talentDeleteArgs} args - Arguments to delete one Talent.
   * @example
   * // Delete one Talent
   * const Talent = await prisma.talent.delete({
   *   where: {
   *     // ... filter to delete one Talent
   *   }
   * })
   * 
   */
  delete<T extends talentDeleteArgs>(args: Prisma.SelectSubset<T, talentDeleteArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Talent.
   * @param {talentUpdateArgs} args - Arguments to update one Talent.
   * @example
   * // Update one Talent
   * const talent = await prisma.talent.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends talentUpdateArgs>(args: Prisma.SelectSubset<T, talentUpdateArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Talents.
   * @param {talentDeleteManyArgs} args - Arguments to filter Talents to delete.
   * @example
   * // Delete a few Talents
   * const { count } = await prisma.talent.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends talentDeleteManyArgs>(args?: Prisma.SelectSubset<T, talentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Talents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Talents
   * const talent = await prisma.talent.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends talentUpdateManyArgs>(args: Prisma.SelectSubset<T, talentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Talent.
   * @param {talentUpsertArgs} args - Arguments to update or create a Talent.
   * @example
   * // Update or create a Talent
   * const talent = await prisma.talent.upsert({
   *   create: {
   *     // ... data to create a Talent
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Talent we want to update
   *   }
   * })
   */
  upsert<T extends talentUpsertArgs>(args: Prisma.SelectSubset<T, talentUpsertArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Talents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentCountArgs} args - Arguments to filter Talents to count.
   * @example
   * // Count the number of Talents
   * const count = await prisma.talent.count({
   *   where: {
   *     // ... the filter for the Talents we want to count
   *   }
   * })
  **/
  count<T extends talentCountArgs>(
    args?: Prisma.Subset<T, talentCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TalentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Talent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TalentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TalentAggregateArgs>(args: Prisma.Subset<T, TalentAggregateArgs>): Prisma.PrismaPromise<GetTalentAggregateType<T>>

  /**
   * Group by Talent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {talentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends talentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: talentGroupByArgs['orderBy'] }
      : { orderBy?: talentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, talentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTalentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the talent model
 */
readonly fields: talentFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for talent.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__talentClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  usersWithAbility1<T extends Prisma.talent$usersWithAbility1Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talent$usersWithAbility1Args<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  usersWithAbility2<T extends Prisma.talent$usersWithAbility2Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talent$usersWithAbility2Args<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  usersWithAbility3<T extends Prisma.talent$usersWithAbility3Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talent$usersWithAbility3Args<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  usersWithAbility4<T extends Prisma.talent$usersWithAbility4Args<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talent$usersWithAbility4Args<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_equipped_abilitiesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user_talent<T extends Prisma.talent$user_talentArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talent$user_talentArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the talent model
 */
export interface talentFieldRefs {
  readonly id: Prisma.FieldRef<"talent", 'Int'>
  readonly name: Prisma.FieldRef<"talent", 'String'>
  readonly displayName: Prisma.FieldRef<"talent", 'String'>
  readonly tree: Prisma.FieldRef<"talent", 'TalentTree'>
  readonly talentType: Prisma.FieldRef<"talent", 'TalentType'>
  readonly description: Prisma.FieldRef<"talent", 'String'>
  readonly skillLevelRequired: Prisma.FieldRef<"talent", 'Int'>
  readonly pointsInTreeRequired: Prisma.FieldRef<"talent", 'Int'>
  readonly pointsCost: Prisma.FieldRef<"talent", 'Int'>
  readonly maxPoints: Prisma.FieldRef<"talent", 'Int'>
  readonly staminaCost: Prisma.FieldRef<"talent", 'Int'>
  readonly tier1Modifier: Prisma.FieldRef<"talent", 'Float'>
  readonly tier2Modifier: Prisma.FieldRef<"talent", 'Float'>
  readonly tier3Modifier: Prisma.FieldRef<"talent", 'Float'>
  readonly secondaryModifier: Prisma.FieldRef<"talent", 'Float'>
}
    

// Custom InputTypes
/**
 * talent findUnique
 */
export type talentFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter, which talent to fetch.
   */
  where: Prisma.talentWhereUniqueInput
}

/**
 * talent findUniqueOrThrow
 */
export type talentFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter, which talent to fetch.
   */
  where: Prisma.talentWhereUniqueInput
}

/**
 * talent findFirst
 */
export type talentFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter, which talent to fetch.
   */
  where?: Prisma.talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of talents to fetch.
   */
  orderBy?: Prisma.talentOrderByWithRelationInput | Prisma.talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for talents.
   */
  cursor?: Prisma.talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of talents.
   */
  distinct?: Prisma.TalentScalarFieldEnum | Prisma.TalentScalarFieldEnum[]
}

/**
 * talent findFirstOrThrow
 */
export type talentFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter, which talent to fetch.
   */
  where?: Prisma.talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of talents to fetch.
   */
  orderBy?: Prisma.talentOrderByWithRelationInput | Prisma.talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for talents.
   */
  cursor?: Prisma.talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of talents.
   */
  distinct?: Prisma.TalentScalarFieldEnum | Prisma.TalentScalarFieldEnum[]
}

/**
 * talent findMany
 */
export type talentFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter, which talents to fetch.
   */
  where?: Prisma.talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of talents to fetch.
   */
  orderBy?: Prisma.talentOrderByWithRelationInput | Prisma.talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing talents.
   */
  cursor?: Prisma.talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` talents.
   */
  skip?: number
  distinct?: Prisma.TalentScalarFieldEnum | Prisma.TalentScalarFieldEnum[]
}

/**
 * talent create
 */
export type talentCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * The data needed to create a talent.
   */
  data: Prisma.XOR<Prisma.talentCreateInput, Prisma.talentUncheckedCreateInput>
}

/**
 * talent createMany
 */
export type talentCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many talents.
   */
  data: Prisma.talentCreateManyInput | Prisma.talentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * talent update
 */
export type talentUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * The data needed to update a talent.
   */
  data: Prisma.XOR<Prisma.talentUpdateInput, Prisma.talentUncheckedUpdateInput>
  /**
   * Choose, which talent to update.
   */
  where: Prisma.talentWhereUniqueInput
}

/**
 * talent updateMany
 */
export type talentUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update talents.
   */
  data: Prisma.XOR<Prisma.talentUpdateManyMutationInput, Prisma.talentUncheckedUpdateManyInput>
  /**
   * Filter which talents to update
   */
  where?: Prisma.talentWhereInput
  /**
   * Limit how many talents to update.
   */
  limit?: number
}

/**
 * talent upsert
 */
export type talentUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * The filter to search for the talent to update in case it exists.
   */
  where: Prisma.talentWhereUniqueInput
  /**
   * In case the talent found by the `where` argument doesn't exist, create a new talent with this data.
   */
  create: Prisma.XOR<Prisma.talentCreateInput, Prisma.talentUncheckedCreateInput>
  /**
   * In case the talent was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.talentUpdateInput, Prisma.talentUncheckedUpdateInput>
}

/**
 * talent delete
 */
export type talentDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
  /**
   * Filter which talent to delete.
   */
  where: Prisma.talentWhereUniqueInput
}

/**
 * talent deleteMany
 */
export type talentDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which talents to delete
   */
  where?: Prisma.talentWhereInput
  /**
   * Limit how many talents to delete.
   */
  limit?: number
}

/**
 * talent.usersWithAbility1
 */
export type talent$usersWithAbility1Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  where?: Prisma.user_equipped_abilitiesWhereInput
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * talent.usersWithAbility2
 */
export type talent$usersWithAbility2Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  where?: Prisma.user_equipped_abilitiesWhereInput
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * talent.usersWithAbility3
 */
export type talent$usersWithAbility3Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  where?: Prisma.user_equipped_abilitiesWhereInput
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * talent.usersWithAbility4
 */
export type talent$usersWithAbility4Args<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_equipped_abilities
   */
  select?: Prisma.user_equipped_abilitiesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_equipped_abilities
   */
  omit?: Prisma.user_equipped_abilitiesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_equipped_abilitiesInclude<ExtArgs> | null
  where?: Prisma.user_equipped_abilitiesWhereInput
  orderBy?: Prisma.user_equipped_abilitiesOrderByWithRelationInput | Prisma.user_equipped_abilitiesOrderByWithRelationInput[]
  cursor?: Prisma.user_equipped_abilitiesWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_equipped_abilitiesScalarFieldEnum | Prisma.User_equipped_abilitiesScalarFieldEnum[]
}

/**
 * talent.user_talent
 */
export type talent$user_talentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  where?: Prisma.user_talentWhereInput
  orderBy?: Prisma.user_talentOrderByWithRelationInput | Prisma.user_talentOrderByWithRelationInput[]
  cursor?: Prisma.user_talentWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_talentScalarFieldEnum | Prisma.User_talentScalarFieldEnum[]
}

/**
 * talent without action
 */
export type talentDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the talent
   */
  select?: Prisma.talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the talent
   */
  omit?: Prisma.talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.talentInclude<ExtArgs> | null
}
