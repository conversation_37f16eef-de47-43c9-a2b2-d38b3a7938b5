
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `notification` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model notification
 * 
 */
export type notificationModel = runtime.Types.Result.DefaultSelection<Prisma.$notificationPayload>

export type AggregateNotification = {
  _count: NotificationCountAggregateOutputType | null
  _avg: NotificationAvgAggregateOutputType | null
  _sum: NotificationSumAggregateOutputType | null
  _min: NotificationMinAggregateOutputType | null
  _max: NotificationMaxAggregateOutputType | null
}

export type NotificationAvgAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type NotificationSumAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type NotificationMinAggregateOutputType = {
  id: number | null
  notificationType: string | null
  details: string | null
  read: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type NotificationMaxAggregateOutputType = {
  id: number | null
  notificationType: string | null
  details: string | null
  read: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type NotificationCountAggregateOutputType = {
  id: number
  notificationType: number
  details: number
  read: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type NotificationAvgAggregateInputType = {
  id?: true
  userId?: true
}

export type NotificationSumAggregateInputType = {
  id?: true
  userId?: true
}

export type NotificationMinAggregateInputType = {
  id?: true
  notificationType?: true
  details?: true
  read?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type NotificationMaxAggregateInputType = {
  id?: true
  notificationType?: true
  details?: true
  read?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type NotificationCountAggregateInputType = {
  id?: true
  notificationType?: true
  details?: true
  read?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type NotificationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which notification to aggregate.
   */
  where?: Prisma.notificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of notifications to fetch.
   */
  orderBy?: Prisma.notificationOrderByWithRelationInput | Prisma.notificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.notificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned notifications
  **/
  _count?: true | NotificationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: NotificationAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: NotificationSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: NotificationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: NotificationMaxAggregateInputType
}

export type GetNotificationAggregateType<T extends NotificationAggregateArgs> = {
      [P in keyof T & keyof AggregateNotification]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateNotification[P]>
    : Prisma.GetScalarType<T[P], AggregateNotification[P]>
}




export type notificationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.notificationWhereInput
  orderBy?: Prisma.notificationOrderByWithAggregationInput | Prisma.notificationOrderByWithAggregationInput[]
  by: Prisma.NotificationScalarFieldEnum[] | Prisma.NotificationScalarFieldEnum
  having?: Prisma.notificationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: NotificationCountAggregateInputType | true
  _avg?: NotificationAvgAggregateInputType
  _sum?: NotificationSumAggregateInputType
  _min?: NotificationMinAggregateInputType
  _max?: NotificationMaxAggregateInputType
}

export type NotificationGroupByOutputType = {
  id: number
  notificationType: string
  details: string | null
  read: boolean | null
  createdAt: Date
  updatedAt: Date
  userId: number | null
  _count: NotificationCountAggregateOutputType | null
  _avg: NotificationAvgAggregateOutputType | null
  _sum: NotificationSumAggregateOutputType | null
  _min: NotificationMinAggregateOutputType | null
  _max: NotificationMaxAggregateOutputType | null
}

type GetNotificationGroupByPayload<T extends notificationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<NotificationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof NotificationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], NotificationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], NotificationGroupByOutputType[P]>
      }
    >
  >



export type notificationWhereInput = {
  AND?: Prisma.notificationWhereInput | Prisma.notificationWhereInput[]
  OR?: Prisma.notificationWhereInput[]
  NOT?: Prisma.notificationWhereInput | Prisma.notificationWhereInput[]
  id?: Prisma.IntFilter<"notification"> | number
  notificationType?: Prisma.StringFilter<"notification"> | string
  details?: Prisma.StringNullableFilter<"notification"> | string | null
  read?: Prisma.BoolNullableFilter<"notification"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  userId?: Prisma.IntNullableFilter<"notification"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type notificationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  notificationType?: Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  read?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.notificationOrderByRelevanceInput
}

export type notificationWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.notificationWhereInput | Prisma.notificationWhereInput[]
  OR?: Prisma.notificationWhereInput[]
  NOT?: Prisma.notificationWhereInput | Prisma.notificationWhereInput[]
  notificationType?: Prisma.StringFilter<"notification"> | string
  details?: Prisma.StringNullableFilter<"notification"> | string | null
  read?: Prisma.BoolNullableFilter<"notification"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  userId?: Prisma.IntNullableFilter<"notification"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type notificationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  notificationType?: Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  read?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.notificationCountOrderByAggregateInput
  _avg?: Prisma.notificationAvgOrderByAggregateInput
  _max?: Prisma.notificationMaxOrderByAggregateInput
  _min?: Prisma.notificationMinOrderByAggregateInput
  _sum?: Prisma.notificationSumOrderByAggregateInput
}

export type notificationScalarWhereWithAggregatesInput = {
  AND?: Prisma.notificationScalarWhereWithAggregatesInput | Prisma.notificationScalarWhereWithAggregatesInput[]
  OR?: Prisma.notificationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.notificationScalarWhereWithAggregatesInput | Prisma.notificationScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"notification"> | number
  notificationType?: Prisma.StringWithAggregatesFilter<"notification"> | string
  details?: Prisma.StringNullableWithAggregatesFilter<"notification"> | string | null
  read?: Prisma.BoolNullableWithAggregatesFilter<"notification"> | boolean | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"notification"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"notification"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"notification"> | number | null
}

export type notificationCreateInput = {
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutNotificationInput
}

export type notificationUncheckedCreateInput = {
  id?: number
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type notificationUpdateInput = {
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutNotificationNestedInput
}

export type notificationUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type notificationCreateManyInput = {
  id?: number
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type notificationUpdateManyMutationInput = {
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type notificationUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type notificationOrderByRelevanceInput = {
  fields: Prisma.notificationOrderByRelevanceFieldEnum | Prisma.notificationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type notificationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  notificationType?: Prisma.SortOrder
  details?: Prisma.SortOrder
  read?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type notificationAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type notificationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  notificationType?: Prisma.SortOrder
  details?: Prisma.SortOrder
  read?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type notificationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  notificationType?: Prisma.SortOrder
  details?: Prisma.SortOrder
  read?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type notificationSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type NotificationListRelationFilter = {
  every?: Prisma.notificationWhereInput
  some?: Prisma.notificationWhereInput
  none?: Prisma.notificationWhereInput
}

export type notificationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type notificationCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput> | Prisma.notificationCreateWithoutUserInput[] | Prisma.notificationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.notificationCreateOrConnectWithoutUserInput | Prisma.notificationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.notificationCreateManyUserInputEnvelope
  connect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
}

export type notificationUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput> | Prisma.notificationCreateWithoutUserInput[] | Prisma.notificationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.notificationCreateOrConnectWithoutUserInput | Prisma.notificationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.notificationCreateManyUserInputEnvelope
  connect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
}

export type notificationUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput> | Prisma.notificationCreateWithoutUserInput[] | Prisma.notificationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.notificationCreateOrConnectWithoutUserInput | Prisma.notificationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.notificationUpsertWithWhereUniqueWithoutUserInput | Prisma.notificationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.notificationCreateManyUserInputEnvelope
  set?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  disconnect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  delete?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  connect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  update?: Prisma.notificationUpdateWithWhereUniqueWithoutUserInput | Prisma.notificationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.notificationUpdateManyWithWhereWithoutUserInput | Prisma.notificationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.notificationScalarWhereInput | Prisma.notificationScalarWhereInput[]
}

export type notificationUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput> | Prisma.notificationCreateWithoutUserInput[] | Prisma.notificationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.notificationCreateOrConnectWithoutUserInput | Prisma.notificationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.notificationUpsertWithWhereUniqueWithoutUserInput | Prisma.notificationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.notificationCreateManyUserInputEnvelope
  set?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  disconnect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  delete?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  connect?: Prisma.notificationWhereUniqueInput | Prisma.notificationWhereUniqueInput[]
  update?: Prisma.notificationUpdateWithWhereUniqueWithoutUserInput | Prisma.notificationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.notificationUpdateManyWithWhereWithoutUserInput | Prisma.notificationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.notificationScalarWhereInput | Prisma.notificationScalarWhereInput[]
}

export type notificationCreateWithoutUserInput = {
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type notificationUncheckedCreateWithoutUserInput = {
  id?: number
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type notificationCreateOrConnectWithoutUserInput = {
  where: Prisma.notificationWhereUniqueInput
  create: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput>
}

export type notificationCreateManyUserInputEnvelope = {
  data: Prisma.notificationCreateManyUserInput | Prisma.notificationCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type notificationUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.notificationWhereUniqueInput
  update: Prisma.XOR<Prisma.notificationUpdateWithoutUserInput, Prisma.notificationUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.notificationCreateWithoutUserInput, Prisma.notificationUncheckedCreateWithoutUserInput>
}

export type notificationUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.notificationWhereUniqueInput
  data: Prisma.XOR<Prisma.notificationUpdateWithoutUserInput, Prisma.notificationUncheckedUpdateWithoutUserInput>
}

export type notificationUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.notificationScalarWhereInput
  data: Prisma.XOR<Prisma.notificationUpdateManyMutationInput, Prisma.notificationUncheckedUpdateManyWithoutUserInput>
}

export type notificationScalarWhereInput = {
  AND?: Prisma.notificationScalarWhereInput | Prisma.notificationScalarWhereInput[]
  OR?: Prisma.notificationScalarWhereInput[]
  NOT?: Prisma.notificationScalarWhereInput | Prisma.notificationScalarWhereInput[]
  id?: Prisma.IntFilter<"notification"> | number
  notificationType?: Prisma.StringFilter<"notification"> | string
  details?: Prisma.StringNullableFilter<"notification"> | string | null
  read?: Prisma.BoolNullableFilter<"notification"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"notification"> | Date | string
  userId?: Prisma.IntNullableFilter<"notification"> | number | null
}

export type notificationCreateManyUserInput = {
  id?: number
  notificationType: string
  details?: string | null
  read?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type notificationUpdateWithoutUserInput = {
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type notificationUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type notificationUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  notificationType?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type notificationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  notificationType?: boolean
  details?: boolean
  read?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.notification$userArgs<ExtArgs>
}, ExtArgs["result"]["notification"]>



export type notificationSelectScalar = {
  id?: boolean
  notificationType?: boolean
  details?: boolean
  read?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type notificationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "notificationType" | "details" | "read" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["notification"]>
export type notificationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.notification$userArgs<ExtArgs>
}

export type $notificationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "notification"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    notificationType: string
    details: string | null
    read: boolean | null
    createdAt: Date
    updatedAt: Date
    userId: number | null
  }, ExtArgs["result"]["notification"]>
  composites: {}
}

export type notificationGetPayload<S extends boolean | null | undefined | notificationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$notificationPayload, S>

export type notificationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<notificationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: NotificationCountAggregateInputType | true
  }

export interface notificationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['notification'], meta: { name: 'notification' } }
  /**
   * Find zero or one Notification that matches the filter.
   * @param {notificationFindUniqueArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends notificationFindUniqueArgs>(args: Prisma.SelectSubset<T, notificationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Notification that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {notificationFindUniqueOrThrowArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends notificationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, notificationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Notification that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationFindFirstArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends notificationFindFirstArgs>(args?: Prisma.SelectSubset<T, notificationFindFirstArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Notification that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationFindFirstOrThrowArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends notificationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, notificationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Notifications that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Notifications
   * const notifications = await prisma.notification.findMany()
   * 
   * // Get first 10 Notifications
   * const notifications = await prisma.notification.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const notificationWithIdOnly = await prisma.notification.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends notificationFindManyArgs>(args?: Prisma.SelectSubset<T, notificationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Notification.
   * @param {notificationCreateArgs} args - Arguments to create a Notification.
   * @example
   * // Create one Notification
   * const Notification = await prisma.notification.create({
   *   data: {
   *     // ... data to create a Notification
   *   }
   * })
   * 
   */
  create<T extends notificationCreateArgs>(args: Prisma.SelectSubset<T, notificationCreateArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Notifications.
   * @param {notificationCreateManyArgs} args - Arguments to create many Notifications.
   * @example
   * // Create many Notifications
   * const notification = await prisma.notification.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends notificationCreateManyArgs>(args?: Prisma.SelectSubset<T, notificationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Notification.
   * @param {notificationDeleteArgs} args - Arguments to delete one Notification.
   * @example
   * // Delete one Notification
   * const Notification = await prisma.notification.delete({
   *   where: {
   *     // ... filter to delete one Notification
   *   }
   * })
   * 
   */
  delete<T extends notificationDeleteArgs>(args: Prisma.SelectSubset<T, notificationDeleteArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Notification.
   * @param {notificationUpdateArgs} args - Arguments to update one Notification.
   * @example
   * // Update one Notification
   * const notification = await prisma.notification.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends notificationUpdateArgs>(args: Prisma.SelectSubset<T, notificationUpdateArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Notifications.
   * @param {notificationDeleteManyArgs} args - Arguments to filter Notifications to delete.
   * @example
   * // Delete a few Notifications
   * const { count } = await prisma.notification.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends notificationDeleteManyArgs>(args?: Prisma.SelectSubset<T, notificationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Notifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Notifications
   * const notification = await prisma.notification.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends notificationUpdateManyArgs>(args: Prisma.SelectSubset<T, notificationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Notification.
   * @param {notificationUpsertArgs} args - Arguments to update or create a Notification.
   * @example
   * // Update or create a Notification
   * const notification = await prisma.notification.upsert({
   *   create: {
   *     // ... data to create a Notification
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Notification we want to update
   *   }
   * })
   */
  upsert<T extends notificationUpsertArgs>(args: Prisma.SelectSubset<T, notificationUpsertArgs<ExtArgs>>): Prisma.Prisma__notificationClient<runtime.Types.Result.GetResult<Prisma.$notificationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Notifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationCountArgs} args - Arguments to filter Notifications to count.
   * @example
   * // Count the number of Notifications
   * const count = await prisma.notification.count({
   *   where: {
   *     // ... the filter for the Notifications we want to count
   *   }
   * })
  **/
  count<T extends notificationCountArgs>(
    args?: Prisma.Subset<T, notificationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], NotificationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Notification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends NotificationAggregateArgs>(args: Prisma.Subset<T, NotificationAggregateArgs>): Prisma.PrismaPromise<GetNotificationAggregateType<T>>

  /**
   * Group by Notification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {notificationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends notificationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: notificationGroupByArgs['orderBy'] }
      : { orderBy?: notificationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, notificationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetNotificationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the notification model
 */
readonly fields: notificationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for notification.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__notificationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.notification$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.notification$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the notification model
 */
export interface notificationFieldRefs {
  readonly id: Prisma.FieldRef<"notification", 'Int'>
  readonly notificationType: Prisma.FieldRef<"notification", 'String'>
  readonly details: Prisma.FieldRef<"notification", 'String'>
  readonly read: Prisma.FieldRef<"notification", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"notification", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"notification", 'DateTime'>
  readonly userId: Prisma.FieldRef<"notification", 'Int'>
}
    

// Custom InputTypes
/**
 * notification findUnique
 */
export type notificationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter, which notification to fetch.
   */
  where: Prisma.notificationWhereUniqueInput
}

/**
 * notification findUniqueOrThrow
 */
export type notificationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter, which notification to fetch.
   */
  where: Prisma.notificationWhereUniqueInput
}

/**
 * notification findFirst
 */
export type notificationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter, which notification to fetch.
   */
  where?: Prisma.notificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of notifications to fetch.
   */
  orderBy?: Prisma.notificationOrderByWithRelationInput | Prisma.notificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for notifications.
   */
  cursor?: Prisma.notificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of notifications.
   */
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * notification findFirstOrThrow
 */
export type notificationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter, which notification to fetch.
   */
  where?: Prisma.notificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of notifications to fetch.
   */
  orderBy?: Prisma.notificationOrderByWithRelationInput | Prisma.notificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for notifications.
   */
  cursor?: Prisma.notificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of notifications.
   */
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * notification findMany
 */
export type notificationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter, which notifications to fetch.
   */
  where?: Prisma.notificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of notifications to fetch.
   */
  orderBy?: Prisma.notificationOrderByWithRelationInput | Prisma.notificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing notifications.
   */
  cursor?: Prisma.notificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` notifications.
   */
  skip?: number
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * notification create
 */
export type notificationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * The data needed to create a notification.
   */
  data: Prisma.XOR<Prisma.notificationCreateInput, Prisma.notificationUncheckedCreateInput>
}

/**
 * notification createMany
 */
export type notificationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many notifications.
   */
  data: Prisma.notificationCreateManyInput | Prisma.notificationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * notification update
 */
export type notificationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * The data needed to update a notification.
   */
  data: Prisma.XOR<Prisma.notificationUpdateInput, Prisma.notificationUncheckedUpdateInput>
  /**
   * Choose, which notification to update.
   */
  where: Prisma.notificationWhereUniqueInput
}

/**
 * notification updateMany
 */
export type notificationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update notifications.
   */
  data: Prisma.XOR<Prisma.notificationUpdateManyMutationInput, Prisma.notificationUncheckedUpdateManyInput>
  /**
   * Filter which notifications to update
   */
  where?: Prisma.notificationWhereInput
  /**
   * Limit how many notifications to update.
   */
  limit?: number
}

/**
 * notification upsert
 */
export type notificationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * The filter to search for the notification to update in case it exists.
   */
  where: Prisma.notificationWhereUniqueInput
  /**
   * In case the notification found by the `where` argument doesn't exist, create a new notification with this data.
   */
  create: Prisma.XOR<Prisma.notificationCreateInput, Prisma.notificationUncheckedCreateInput>
  /**
   * In case the notification was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.notificationUpdateInput, Prisma.notificationUncheckedUpdateInput>
}

/**
 * notification delete
 */
export type notificationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
  /**
   * Filter which notification to delete.
   */
  where: Prisma.notificationWhereUniqueInput
}

/**
 * notification deleteMany
 */
export type notificationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which notifications to delete
   */
  where?: Prisma.notificationWhereInput
  /**
   * Limit how many notifications to delete.
   */
  limit?: number
}

/**
 * notification.user
 */
export type notification$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * notification without action
 */
export type notificationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the notification
   */
  select?: Prisma.notificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the notification
   */
  omit?: Prisma.notificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.notificationInclude<ExtArgs> | null
}
