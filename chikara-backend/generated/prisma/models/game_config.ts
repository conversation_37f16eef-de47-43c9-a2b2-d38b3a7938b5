
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `game_config` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model game_config
 * 
 */
export type game_configModel = runtime.Types.Result.DefaultSelection<Prisma.$game_configPayload>

export type AggregateGame_config = {
  _count: Game_configCountAggregateOutputType | null
  _avg: Game_configAvgAggregateOutputType | null
  _sum: Game_configSumAggregateOutputType | null
  _min: Game_configMinAggregateOutputType | null
  _max: Game_configMaxAggregateOutputType | null
}

export type Game_configAvgAggregateOutputType = {
  id: number | null
}

export type Game_configSumAggregateOutputType = {
  id: number | null
}

export type Game_configMinAggregateOutputType = {
  id: number | null
  key: string | null
  category: string | null
  isPublic: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Game_configMaxAggregateOutputType = {
  id: number | null
  key: string | null
  category: string | null
  isPublic: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Game_configCountAggregateOutputType = {
  id: number
  key: number
  value: number
  category: number
  isPublic: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Game_configAvgAggregateInputType = {
  id?: true
}

export type Game_configSumAggregateInputType = {
  id?: true
}

export type Game_configMinAggregateInputType = {
  id?: true
  key?: true
  category?: true
  isPublic?: true
  createdAt?: true
  updatedAt?: true
}

export type Game_configMaxAggregateInputType = {
  id?: true
  key?: true
  category?: true
  isPublic?: true
  createdAt?: true
  updatedAt?: true
}

export type Game_configCountAggregateInputType = {
  id?: true
  key?: true
  value?: true
  category?: true
  isPublic?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Game_configAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which game_config to aggregate.
   */
  where?: Prisma.game_configWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_configs to fetch.
   */
  orderBy?: Prisma.game_configOrderByWithRelationInput | Prisma.game_configOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.game_configWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_configs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_configs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned game_configs
  **/
  _count?: true | Game_configCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Game_configAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Game_configSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Game_configMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Game_configMaxAggregateInputType
}

export type GetGame_configAggregateType<T extends Game_configAggregateArgs> = {
      [P in keyof T & keyof AggregateGame_config]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGame_config[P]>
    : Prisma.GetScalarType<T[P], AggregateGame_config[P]>
}




export type game_configGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.game_configWhereInput
  orderBy?: Prisma.game_configOrderByWithAggregationInput | Prisma.game_configOrderByWithAggregationInput[]
  by: Prisma.Game_configScalarFieldEnum[] | Prisma.Game_configScalarFieldEnum
  having?: Prisma.game_configScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Game_configCountAggregateInputType | true
  _avg?: Game_configAvgAggregateInputType
  _sum?: Game_configSumAggregateInputType
  _min?: Game_configMinAggregateInputType
  _max?: Game_configMaxAggregateInputType
}

export type Game_configGroupByOutputType = {
  id: number
  key: string
  value: runtime.JsonValue
  category: string | null
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
  _count: Game_configCountAggregateOutputType | null
  _avg: Game_configAvgAggregateOutputType | null
  _sum: Game_configSumAggregateOutputType | null
  _min: Game_configMinAggregateOutputType | null
  _max: Game_configMaxAggregateOutputType | null
}

type GetGame_configGroupByPayload<T extends game_configGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Game_configGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Game_configGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Game_configGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Game_configGroupByOutputType[P]>
      }
    >
  >



export type game_configWhereInput = {
  AND?: Prisma.game_configWhereInput | Prisma.game_configWhereInput[]
  OR?: Prisma.game_configWhereInput[]
  NOT?: Prisma.game_configWhereInput | Prisma.game_configWhereInput[]
  id?: Prisma.IntFilter<"game_config"> | number
  key?: Prisma.StringFilter<"game_config"> | string
  value?: Prisma.JsonFilter<"game_config">
  category?: Prisma.StringNullableFilter<"game_config"> | string | null
  isPublic?: Prisma.BoolFilter<"game_config"> | boolean
  createdAt?: Prisma.DateTimeFilter<"game_config"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"game_config"> | Date | string
}

export type game_configOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  key?: Prisma.SortOrder
  value?: Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  isPublic?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _relevance?: Prisma.game_configOrderByRelevanceInput
}

export type game_configWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  key?: string
  AND?: Prisma.game_configWhereInput | Prisma.game_configWhereInput[]
  OR?: Prisma.game_configWhereInput[]
  NOT?: Prisma.game_configWhereInput | Prisma.game_configWhereInput[]
  value?: Prisma.JsonFilter<"game_config">
  category?: Prisma.StringNullableFilter<"game_config"> | string | null
  isPublic?: Prisma.BoolFilter<"game_config"> | boolean
  createdAt?: Prisma.DateTimeFilter<"game_config"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"game_config"> | Date | string
}, "id" | "key">

export type game_configOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  key?: Prisma.SortOrder
  value?: Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  isPublic?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.game_configCountOrderByAggregateInput
  _avg?: Prisma.game_configAvgOrderByAggregateInput
  _max?: Prisma.game_configMaxOrderByAggregateInput
  _min?: Prisma.game_configMinOrderByAggregateInput
  _sum?: Prisma.game_configSumOrderByAggregateInput
}

export type game_configScalarWhereWithAggregatesInput = {
  AND?: Prisma.game_configScalarWhereWithAggregatesInput | Prisma.game_configScalarWhereWithAggregatesInput[]
  OR?: Prisma.game_configScalarWhereWithAggregatesInput[]
  NOT?: Prisma.game_configScalarWhereWithAggregatesInput | Prisma.game_configScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"game_config"> | number
  key?: Prisma.StringWithAggregatesFilter<"game_config"> | string
  value?: Prisma.JsonWithAggregatesFilter<"game_config">
  category?: Prisma.StringNullableWithAggregatesFilter<"game_config"> | string | null
  isPublic?: Prisma.BoolWithAggregatesFilter<"game_config"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"game_config"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"game_config"> | Date | string
}

export type game_configCreateInput = {
  key: string
  value:unknown
  category?: string | null
  isPublic?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_configUncheckedCreateInput = {
  id?: number
  key: string
  value:unknown
  category?: string | null
  isPublic?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_configUpdateInput = {
  key?: Prisma.StringFieldUpdateOperationsInput | string
  value?:unknown
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublic?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_configUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  key?: Prisma.StringFieldUpdateOperationsInput | string
  value?:unknown
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublic?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_configCreateManyInput = {
  id?: number
  key: string
  value:unknown
  category?: string | null
  isPublic?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_configUpdateManyMutationInput = {
  key?: Prisma.StringFieldUpdateOperationsInput | string
  value?:unknown
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublic?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_configUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  key?: Prisma.StringFieldUpdateOperationsInput | string
  value?:unknown
  category?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublic?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_configOrderByRelevanceInput = {
  fields: Prisma.game_configOrderByRelevanceFieldEnum | Prisma.game_configOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type game_configCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  key?: Prisma.SortOrder
  value?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublic?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type game_configAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type game_configMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  key?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublic?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type game_configMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  key?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublic?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type game_configSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}



export type game_configSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  key?: boolean
  value?: boolean
  category?: boolean
  isPublic?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["game_config"]>



export type game_configSelectScalar = {
  id?: boolean
  key?: boolean
  value?: boolean
  category?: boolean
  isPublic?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type game_configOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "key" | "value" | "category" | "isPublic" | "createdAt" | "updatedAt", ExtArgs["result"]["game_config"]>

export type $game_configPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "game_config"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    key: string
    value:unknown
    category: string | null
    isPublic: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["game_config"]>
  composites: {}
}

export type game_configGetPayload<S extends boolean | null | undefined | game_configDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$game_configPayload, S>

export type game_configCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<game_configFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Game_configCountAggregateInputType | true
  }

export interface game_configDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['game_config'], meta: { name: 'game_config' } }
  /**
   * Find zero or one Game_config that matches the filter.
   * @param {game_configFindUniqueArgs} args - Arguments to find a Game_config
   * @example
   * // Get one Game_config
   * const game_config = await prisma.game_config.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends game_configFindUniqueArgs>(args: Prisma.SelectSubset<T, game_configFindUniqueArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Game_config that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {game_configFindUniqueOrThrowArgs} args - Arguments to find a Game_config
   * @example
   * // Get one Game_config
   * const game_config = await prisma.game_config.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends game_configFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, game_configFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Game_config that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configFindFirstArgs} args - Arguments to find a Game_config
   * @example
   * // Get one Game_config
   * const game_config = await prisma.game_config.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends game_configFindFirstArgs>(args?: Prisma.SelectSubset<T, game_configFindFirstArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Game_config that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configFindFirstOrThrowArgs} args - Arguments to find a Game_config
   * @example
   * // Get one Game_config
   * const game_config = await prisma.game_config.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends game_configFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, game_configFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Game_configs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Game_configs
   * const game_configs = await prisma.game_config.findMany()
   * 
   * // Get first 10 Game_configs
   * const game_configs = await prisma.game_config.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const game_configWithIdOnly = await prisma.game_config.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends game_configFindManyArgs>(args?: Prisma.SelectSubset<T, game_configFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Game_config.
   * @param {game_configCreateArgs} args - Arguments to create a Game_config.
   * @example
   * // Create one Game_config
   * const Game_config = await prisma.game_config.create({
   *   data: {
   *     // ... data to create a Game_config
   *   }
   * })
   * 
   */
  create<T extends game_configCreateArgs>(args: Prisma.SelectSubset<T, game_configCreateArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Game_configs.
   * @param {game_configCreateManyArgs} args - Arguments to create many Game_configs.
   * @example
   * // Create many Game_configs
   * const game_config = await prisma.game_config.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends game_configCreateManyArgs>(args?: Prisma.SelectSubset<T, game_configCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Game_config.
   * @param {game_configDeleteArgs} args - Arguments to delete one Game_config.
   * @example
   * // Delete one Game_config
   * const Game_config = await prisma.game_config.delete({
   *   where: {
   *     // ... filter to delete one Game_config
   *   }
   * })
   * 
   */
  delete<T extends game_configDeleteArgs>(args: Prisma.SelectSubset<T, game_configDeleteArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Game_config.
   * @param {game_configUpdateArgs} args - Arguments to update one Game_config.
   * @example
   * // Update one Game_config
   * const game_config = await prisma.game_config.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends game_configUpdateArgs>(args: Prisma.SelectSubset<T, game_configUpdateArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Game_configs.
   * @param {game_configDeleteManyArgs} args - Arguments to filter Game_configs to delete.
   * @example
   * // Delete a few Game_configs
   * const { count } = await prisma.game_config.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends game_configDeleteManyArgs>(args?: Prisma.SelectSubset<T, game_configDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Game_configs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Game_configs
   * const game_config = await prisma.game_config.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends game_configUpdateManyArgs>(args: Prisma.SelectSubset<T, game_configUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Game_config.
   * @param {game_configUpsertArgs} args - Arguments to update or create a Game_config.
   * @example
   * // Update or create a Game_config
   * const game_config = await prisma.game_config.upsert({
   *   create: {
   *     // ... data to create a Game_config
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Game_config we want to update
   *   }
   * })
   */
  upsert<T extends game_configUpsertArgs>(args: Prisma.SelectSubset<T, game_configUpsertArgs<ExtArgs>>): Prisma.Prisma__game_configClient<runtime.Types.Result.GetResult<Prisma.$game_configPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Game_configs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configCountArgs} args - Arguments to filter Game_configs to count.
   * @example
   * // Count the number of Game_configs
   * const count = await prisma.game_config.count({
   *   where: {
   *     // ... the filter for the Game_configs we want to count
   *   }
   * })
  **/
  count<T extends game_configCountArgs>(
    args?: Prisma.Subset<T, game_configCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Game_configCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Game_config.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Game_configAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Game_configAggregateArgs>(args: Prisma.Subset<T, Game_configAggregateArgs>): Prisma.PrismaPromise<GetGame_configAggregateType<T>>

  /**
   * Group by Game_config.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_configGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends game_configGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: game_configGroupByArgs['orderBy'] }
      : { orderBy?: game_configGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, game_configGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGame_configGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the game_config model
 */
readonly fields: game_configFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for game_config.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__game_configClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the game_config model
 */
export interface game_configFieldRefs {
  readonly id: Prisma.FieldRef<"game_config", 'Int'>
  readonly key: Prisma.FieldRef<"game_config", 'String'>
  readonly value: Prisma.FieldRef<"game_config", 'Json'>
  readonly category: Prisma.FieldRef<"game_config", 'String'>
  readonly isPublic: Prisma.FieldRef<"game_config", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"game_config", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"game_config", 'DateTime'>
}
    

// Custom InputTypes
/**
 * game_config findUnique
 */
export type game_configFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter, which game_config to fetch.
   */
  where: Prisma.game_configWhereUniqueInput
}

/**
 * game_config findUniqueOrThrow
 */
export type game_configFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter, which game_config to fetch.
   */
  where: Prisma.game_configWhereUniqueInput
}

/**
 * game_config findFirst
 */
export type game_configFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter, which game_config to fetch.
   */
  where?: Prisma.game_configWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_configs to fetch.
   */
  orderBy?: Prisma.game_configOrderByWithRelationInput | Prisma.game_configOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for game_configs.
   */
  cursor?: Prisma.game_configWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_configs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_configs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of game_configs.
   */
  distinct?: Prisma.Game_configScalarFieldEnum | Prisma.Game_configScalarFieldEnum[]
}

/**
 * game_config findFirstOrThrow
 */
export type game_configFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter, which game_config to fetch.
   */
  where?: Prisma.game_configWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_configs to fetch.
   */
  orderBy?: Prisma.game_configOrderByWithRelationInput | Prisma.game_configOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for game_configs.
   */
  cursor?: Prisma.game_configWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_configs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_configs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of game_configs.
   */
  distinct?: Prisma.Game_configScalarFieldEnum | Prisma.Game_configScalarFieldEnum[]
}

/**
 * game_config findMany
 */
export type game_configFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter, which game_configs to fetch.
   */
  where?: Prisma.game_configWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_configs to fetch.
   */
  orderBy?: Prisma.game_configOrderByWithRelationInput | Prisma.game_configOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing game_configs.
   */
  cursor?: Prisma.game_configWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_configs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_configs.
   */
  skip?: number
  distinct?: Prisma.Game_configScalarFieldEnum | Prisma.Game_configScalarFieldEnum[]
}

/**
 * game_config create
 */
export type game_configCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * The data needed to create a game_config.
   */
  data: Prisma.XOR<Prisma.game_configCreateInput, Prisma.game_configUncheckedCreateInput>
}

/**
 * game_config createMany
 */
export type game_configCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many game_configs.
   */
  data: Prisma.game_configCreateManyInput | Prisma.game_configCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * game_config update
 */
export type game_configUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * The data needed to update a game_config.
   */
  data: Prisma.XOR<Prisma.game_configUpdateInput, Prisma.game_configUncheckedUpdateInput>
  /**
   * Choose, which game_config to update.
   */
  where: Prisma.game_configWhereUniqueInput
}

/**
 * game_config updateMany
 */
export type game_configUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update game_configs.
   */
  data: Prisma.XOR<Prisma.game_configUpdateManyMutationInput, Prisma.game_configUncheckedUpdateManyInput>
  /**
   * Filter which game_configs to update
   */
  where?: Prisma.game_configWhereInput
  /**
   * Limit how many game_configs to update.
   */
  limit?: number
}

/**
 * game_config upsert
 */
export type game_configUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * The filter to search for the game_config to update in case it exists.
   */
  where: Prisma.game_configWhereUniqueInput
  /**
   * In case the game_config found by the `where` argument doesn't exist, create a new game_config with this data.
   */
  create: Prisma.XOR<Prisma.game_configCreateInput, Prisma.game_configUncheckedCreateInput>
  /**
   * In case the game_config was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.game_configUpdateInput, Prisma.game_configUncheckedUpdateInput>
}

/**
 * game_config delete
 */
export type game_configDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
  /**
   * Filter which game_config to delete.
   */
  where: Prisma.game_configWhereUniqueInput
}

/**
 * game_config deleteMany
 */
export type game_configDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which game_configs to delete
   */
  where?: Prisma.game_configWhereInput
  /**
   * Limit how many game_configs to delete.
   */
  limit?: number
}

/**
 * game_config without action
 */
export type game_configDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_config
   */
  select?: Prisma.game_configSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_config
   */
  omit?: Prisma.game_configOmit<ExtArgs> | null
}
