
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `action_log` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model action_log
 * 
 */
export type action_logModel = runtime.Types.Result.DefaultSelection<Prisma.$action_logPayload>

export type AggregateAction_log = {
  _count: Action_logCountAggregateOutputType | null
  _avg: Action_logAvgAggregateOutputType | null
  _sum: Action_logSumAggregateOutputType | null
  _min: Action_logMinAggregateOutputType | null
  _max: Action_logMaxAggregateOutputType | null
}

export type Action_logAvgAggregateOutputType = {
  id: number | null
  playerId: number | null
  secondPartyId: number | null
}

export type Action_logSumAggregateOutputType = {
  id: number | null
  playerId: number | null
  secondPartyId: number | null
}

export type Action_logMinAggregateOutputType = {
  id: number | null
  logType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  playerId: number | null
  secondPartyId: number | null
}

export type Action_logMaxAggregateOutputType = {
  id: number | null
  logType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  playerId: number | null
  secondPartyId: number | null
}

export type Action_logCountAggregateOutputType = {
  id: number
  logType: number
  info: number
  createdAt: number
  updatedAt: number
  playerId: number
  secondPartyId: number
  _all: number
}


export type Action_logAvgAggregateInputType = {
  id?: true
  playerId?: true
  secondPartyId?: true
}

export type Action_logSumAggregateInputType = {
  id?: true
  playerId?: true
  secondPartyId?: true
}

export type Action_logMinAggregateInputType = {
  id?: true
  logType?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
  secondPartyId?: true
}

export type Action_logMaxAggregateInputType = {
  id?: true
  logType?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
  secondPartyId?: true
}

export type Action_logCountAggregateInputType = {
  id?: true
  logType?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
  secondPartyId?: true
  _all?: true
}

export type Action_logAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which action_log to aggregate.
   */
  where?: Prisma.action_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of action_logs to fetch.
   */
  orderBy?: Prisma.action_logOrderByWithRelationInput | Prisma.action_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.action_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` action_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` action_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned action_logs
  **/
  _count?: true | Action_logCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Action_logAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Action_logSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Action_logMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Action_logMaxAggregateInputType
}

export type GetAction_logAggregateType<T extends Action_logAggregateArgs> = {
      [P in keyof T & keyof AggregateAction_log]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAction_log[P]>
    : Prisma.GetScalarType<T[P], AggregateAction_log[P]>
}




export type action_logGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.action_logWhereInput
  orderBy?: Prisma.action_logOrderByWithAggregationInput | Prisma.action_logOrderByWithAggregationInput[]
  by: Prisma.Action_logScalarFieldEnum[] | Prisma.Action_logScalarFieldEnum
  having?: Prisma.action_logScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Action_logCountAggregateInputType | true
  _avg?: Action_logAvgAggregateInputType
  _sum?: Action_logSumAggregateInputType
  _min?: Action_logMinAggregateInputType
  _max?: Action_logMaxAggregateInputType
}

export type Action_logGroupByOutputType = {
  id: number
  logType: string | null
  info: runtime.JsonValue
  createdAt: Date
  updatedAt: Date
  playerId: number | null
  secondPartyId: number | null
  _count: Action_logCountAggregateOutputType | null
  _avg: Action_logAvgAggregateOutputType | null
  _sum: Action_logSumAggregateOutputType | null
  _min: Action_logMinAggregateOutputType | null
  _max: Action_logMaxAggregateOutputType | null
}

type GetAction_logGroupByPayload<T extends action_logGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Action_logGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Action_logGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Action_logGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Action_logGroupByOutputType[P]>
      }
    >
  >



export type action_logWhereInput = {
  AND?: Prisma.action_logWhereInput | Prisma.action_logWhereInput[]
  OR?: Prisma.action_logWhereInput[]
  NOT?: Prisma.action_logWhereInput | Prisma.action_logWhereInput[]
  id?: Prisma.IntFilter<"action_log"> | number
  logType?: Prisma.StringNullableFilter<"action_log"> | string | null
  info?: Prisma.JsonFilter<"action_log">
  createdAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  playerId?: Prisma.IntNullableFilter<"action_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"action_log"> | number | null
  player?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  secondParty?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type action_logOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  logType?: Prisma.SortOrderInput | Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  player?: Prisma.userOrderByWithRelationInput
  secondParty?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.action_logOrderByRelevanceInput
}

export type action_logWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.action_logWhereInput | Prisma.action_logWhereInput[]
  OR?: Prisma.action_logWhereInput[]
  NOT?: Prisma.action_logWhereInput | Prisma.action_logWhereInput[]
  logType?: Prisma.StringNullableFilter<"action_log"> | string | null
  info?: Prisma.JsonFilter<"action_log">
  createdAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  playerId?: Prisma.IntNullableFilter<"action_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"action_log"> | number | null
  player?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  secondParty?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type action_logOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  logType?: Prisma.SortOrderInput | Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.action_logCountOrderByAggregateInput
  _avg?: Prisma.action_logAvgOrderByAggregateInput
  _max?: Prisma.action_logMaxOrderByAggregateInput
  _min?: Prisma.action_logMinOrderByAggregateInput
  _sum?: Prisma.action_logSumOrderByAggregateInput
}

export type action_logScalarWhereWithAggregatesInput = {
  AND?: Prisma.action_logScalarWhereWithAggregatesInput | Prisma.action_logScalarWhereWithAggregatesInput[]
  OR?: Prisma.action_logScalarWhereWithAggregatesInput[]
  NOT?: Prisma.action_logScalarWhereWithAggregatesInput | Prisma.action_logScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"action_log"> | number
  logType?: Prisma.StringNullableWithAggregatesFilter<"action_log"> | string | null
  info?: Prisma.JsonWithAggregatesFilter<"action_log">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"action_log"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"action_log"> | Date | string
  playerId?: Prisma.IntNullableWithAggregatesFilter<"action_log"> | number | null
  secondPartyId?: Prisma.IntNullableWithAggregatesFilter<"action_log"> | number | null
}

export type action_logCreateInput = {
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  player?: Prisma.userCreateNestedOneWithoutActionsAsPlayerInput
  secondParty?: Prisma.userCreateNestedOneWithoutActionsAsSecondPartyInput
}

export type action_logUncheckedCreateInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
  secondPartyId?: number | null
}

export type action_logUpdateInput = {
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  player?: Prisma.userUpdateOneWithoutActionsAsPlayerNestedInput
  secondParty?: Prisma.userUpdateOneWithoutActionsAsSecondPartyNestedInput
}

export type action_logUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type action_logCreateManyInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
  secondPartyId?: number | null
}

export type action_logUpdateManyMutationInput = {
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type action_logUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type action_logOrderByRelevanceInput = {
  fields: Prisma.action_logOrderByRelevanceFieldEnum | Prisma.action_logOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type action_logCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  logType?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type action_logAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type action_logMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  logType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type action_logMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  logType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type action_logSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type Action_logListRelationFilter = {
  every?: Prisma.action_logWhereInput
  some?: Prisma.action_logWhereInput
  none?: Prisma.action_logWhereInput
}

export type action_logOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type action_logCreateNestedManyWithoutPlayerInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput> | Prisma.action_logCreateWithoutPlayerInput[] | Prisma.action_logUncheckedCreateWithoutPlayerInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutPlayerInput | Prisma.action_logCreateOrConnectWithoutPlayerInput[]
  createMany?: Prisma.action_logCreateManyPlayerInputEnvelope
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
}

export type action_logCreateNestedManyWithoutSecondPartyInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput> | Prisma.action_logCreateWithoutSecondPartyInput[] | Prisma.action_logUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutSecondPartyInput | Prisma.action_logCreateOrConnectWithoutSecondPartyInput[]
  createMany?: Prisma.action_logCreateManySecondPartyInputEnvelope
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
}

export type action_logUncheckedCreateNestedManyWithoutPlayerInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput> | Prisma.action_logCreateWithoutPlayerInput[] | Prisma.action_logUncheckedCreateWithoutPlayerInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutPlayerInput | Prisma.action_logCreateOrConnectWithoutPlayerInput[]
  createMany?: Prisma.action_logCreateManyPlayerInputEnvelope
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
}

export type action_logUncheckedCreateNestedManyWithoutSecondPartyInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput> | Prisma.action_logCreateWithoutSecondPartyInput[] | Prisma.action_logUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutSecondPartyInput | Prisma.action_logCreateOrConnectWithoutSecondPartyInput[]
  createMany?: Prisma.action_logCreateManySecondPartyInputEnvelope
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
}

export type action_logUpdateManyWithoutPlayerNestedInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput> | Prisma.action_logCreateWithoutPlayerInput[] | Prisma.action_logUncheckedCreateWithoutPlayerInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutPlayerInput | Prisma.action_logCreateOrConnectWithoutPlayerInput[]
  upsert?: Prisma.action_logUpsertWithWhereUniqueWithoutPlayerInput | Prisma.action_logUpsertWithWhereUniqueWithoutPlayerInput[]
  createMany?: Prisma.action_logCreateManyPlayerInputEnvelope
  set?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  disconnect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  delete?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  update?: Prisma.action_logUpdateWithWhereUniqueWithoutPlayerInput | Prisma.action_logUpdateWithWhereUniqueWithoutPlayerInput[]
  updateMany?: Prisma.action_logUpdateManyWithWhereWithoutPlayerInput | Prisma.action_logUpdateManyWithWhereWithoutPlayerInput[]
  deleteMany?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
}

export type action_logUpdateManyWithoutSecondPartyNestedInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput> | Prisma.action_logCreateWithoutSecondPartyInput[] | Prisma.action_logUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutSecondPartyInput | Prisma.action_logCreateOrConnectWithoutSecondPartyInput[]
  upsert?: Prisma.action_logUpsertWithWhereUniqueWithoutSecondPartyInput | Prisma.action_logUpsertWithWhereUniqueWithoutSecondPartyInput[]
  createMany?: Prisma.action_logCreateManySecondPartyInputEnvelope
  set?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  disconnect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  delete?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  update?: Prisma.action_logUpdateWithWhereUniqueWithoutSecondPartyInput | Prisma.action_logUpdateWithWhereUniqueWithoutSecondPartyInput[]
  updateMany?: Prisma.action_logUpdateManyWithWhereWithoutSecondPartyInput | Prisma.action_logUpdateManyWithWhereWithoutSecondPartyInput[]
  deleteMany?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
}

export type action_logUncheckedUpdateManyWithoutPlayerNestedInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput> | Prisma.action_logCreateWithoutPlayerInput[] | Prisma.action_logUncheckedCreateWithoutPlayerInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutPlayerInput | Prisma.action_logCreateOrConnectWithoutPlayerInput[]
  upsert?: Prisma.action_logUpsertWithWhereUniqueWithoutPlayerInput | Prisma.action_logUpsertWithWhereUniqueWithoutPlayerInput[]
  createMany?: Prisma.action_logCreateManyPlayerInputEnvelope
  set?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  disconnect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  delete?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  update?: Prisma.action_logUpdateWithWhereUniqueWithoutPlayerInput | Prisma.action_logUpdateWithWhereUniqueWithoutPlayerInput[]
  updateMany?: Prisma.action_logUpdateManyWithWhereWithoutPlayerInput | Prisma.action_logUpdateManyWithWhereWithoutPlayerInput[]
  deleteMany?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
}

export type action_logUncheckedUpdateManyWithoutSecondPartyNestedInput = {
  create?: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput> | Prisma.action_logCreateWithoutSecondPartyInput[] | Prisma.action_logUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.action_logCreateOrConnectWithoutSecondPartyInput | Prisma.action_logCreateOrConnectWithoutSecondPartyInput[]
  upsert?: Prisma.action_logUpsertWithWhereUniqueWithoutSecondPartyInput | Prisma.action_logUpsertWithWhereUniqueWithoutSecondPartyInput[]
  createMany?: Prisma.action_logCreateManySecondPartyInputEnvelope
  set?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  disconnect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  delete?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  connect?: Prisma.action_logWhereUniqueInput | Prisma.action_logWhereUniqueInput[]
  update?: Prisma.action_logUpdateWithWhereUniqueWithoutSecondPartyInput | Prisma.action_logUpdateWithWhereUniqueWithoutSecondPartyInput[]
  updateMany?: Prisma.action_logUpdateManyWithWhereWithoutSecondPartyInput | Prisma.action_logUpdateManyWithWhereWithoutSecondPartyInput[]
  deleteMany?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
}

export type action_logCreateWithoutPlayerInput = {
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  secondParty?: Prisma.userCreateNestedOneWithoutActionsAsSecondPartyInput
}

export type action_logUncheckedCreateWithoutPlayerInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  secondPartyId?: number | null
}

export type action_logCreateOrConnectWithoutPlayerInput = {
  where: Prisma.action_logWhereUniqueInput
  create: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput>
}

export type action_logCreateManyPlayerInputEnvelope = {
  data: Prisma.action_logCreateManyPlayerInput | Prisma.action_logCreateManyPlayerInput[]
  skipDuplicates?: boolean
}

export type action_logCreateWithoutSecondPartyInput = {
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  player?: Prisma.userCreateNestedOneWithoutActionsAsPlayerInput
}

export type action_logUncheckedCreateWithoutSecondPartyInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
}

export type action_logCreateOrConnectWithoutSecondPartyInput = {
  where: Prisma.action_logWhereUniqueInput
  create: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput>
}

export type action_logCreateManySecondPartyInputEnvelope = {
  data: Prisma.action_logCreateManySecondPartyInput | Prisma.action_logCreateManySecondPartyInput[]
  skipDuplicates?: boolean
}

export type action_logUpsertWithWhereUniqueWithoutPlayerInput = {
  where: Prisma.action_logWhereUniqueInput
  update: Prisma.XOR<Prisma.action_logUpdateWithoutPlayerInput, Prisma.action_logUncheckedUpdateWithoutPlayerInput>
  create: Prisma.XOR<Prisma.action_logCreateWithoutPlayerInput, Prisma.action_logUncheckedCreateWithoutPlayerInput>
}

export type action_logUpdateWithWhereUniqueWithoutPlayerInput = {
  where: Prisma.action_logWhereUniqueInput
  data: Prisma.XOR<Prisma.action_logUpdateWithoutPlayerInput, Prisma.action_logUncheckedUpdateWithoutPlayerInput>
}

export type action_logUpdateManyWithWhereWithoutPlayerInput = {
  where: Prisma.action_logScalarWhereInput
  data: Prisma.XOR<Prisma.action_logUpdateManyMutationInput, Prisma.action_logUncheckedUpdateManyWithoutPlayerInput>
}

export type action_logScalarWhereInput = {
  AND?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
  OR?: Prisma.action_logScalarWhereInput[]
  NOT?: Prisma.action_logScalarWhereInput | Prisma.action_logScalarWhereInput[]
  id?: Prisma.IntFilter<"action_log"> | number
  logType?: Prisma.StringNullableFilter<"action_log"> | string | null
  info?: Prisma.JsonFilter<"action_log">
  createdAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"action_log"> | Date | string
  playerId?: Prisma.IntNullableFilter<"action_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"action_log"> | number | null
}

export type action_logUpsertWithWhereUniqueWithoutSecondPartyInput = {
  where: Prisma.action_logWhereUniqueInput
  update: Prisma.XOR<Prisma.action_logUpdateWithoutSecondPartyInput, Prisma.action_logUncheckedUpdateWithoutSecondPartyInput>
  create: Prisma.XOR<Prisma.action_logCreateWithoutSecondPartyInput, Prisma.action_logUncheckedCreateWithoutSecondPartyInput>
}

export type action_logUpdateWithWhereUniqueWithoutSecondPartyInput = {
  where: Prisma.action_logWhereUniqueInput
  data: Prisma.XOR<Prisma.action_logUpdateWithoutSecondPartyInput, Prisma.action_logUncheckedUpdateWithoutSecondPartyInput>
}

export type action_logUpdateManyWithWhereWithoutSecondPartyInput = {
  where: Prisma.action_logScalarWhereInput
  data: Prisma.XOR<Prisma.action_logUpdateManyMutationInput, Prisma.action_logUncheckedUpdateManyWithoutSecondPartyInput>
}

export type action_logCreateManyPlayerInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  secondPartyId?: number | null
}

export type action_logCreateManySecondPartyInput = {
  id?: number
  logType?: string | null
  info?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
}

export type action_logUpdateWithoutPlayerInput = {
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondParty?: Prisma.userUpdateOneWithoutActionsAsSecondPartyNestedInput
}

export type action_logUncheckedUpdateWithoutPlayerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type action_logUncheckedUpdateManyWithoutPlayerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type action_logUpdateWithoutSecondPartyInput = {
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  player?: Prisma.userUpdateOneWithoutActionsAsPlayerNestedInput
}

export type action_logUncheckedUpdateWithoutSecondPartyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type action_logUncheckedUpdateManyWithoutSecondPartyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  logType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type action_logSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  logType?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  playerId?: boolean
  secondPartyId?: boolean
  player?: boolean | Prisma.action_log$playerArgs<ExtArgs>
  secondParty?: boolean | Prisma.action_log$secondPartyArgs<ExtArgs>
}, ExtArgs["result"]["action_log"]>



export type action_logSelectScalar = {
  id?: boolean
  logType?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  playerId?: boolean
  secondPartyId?: boolean
}

export type action_logOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "logType" | "info" | "createdAt" | "updatedAt" | "playerId" | "secondPartyId", ExtArgs["result"]["action_log"]>
export type action_logInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  player?: boolean | Prisma.action_log$playerArgs<ExtArgs>
  secondParty?: boolean | Prisma.action_log$secondPartyArgs<ExtArgs>
}

export type $action_logPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "action_log"
  objects: {
    player: Prisma.$userPayload<ExtArgs> | null
    secondParty: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    logType: string | null
    info:unknown
    createdAt: Date
    updatedAt: Date
    playerId: number | null
    secondPartyId: number | null
  }, ExtArgs["result"]["action_log"]>
  composites: {}
}

export type action_logGetPayload<S extends boolean | null | undefined | action_logDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$action_logPayload, S>

export type action_logCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<action_logFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Action_logCountAggregateInputType | true
  }

export interface action_logDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['action_log'], meta: { name: 'action_log' } }
  /**
   * Find zero or one Action_log that matches the filter.
   * @param {action_logFindUniqueArgs} args - Arguments to find a Action_log
   * @example
   * // Get one Action_log
   * const action_log = await prisma.action_log.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends action_logFindUniqueArgs>(args: Prisma.SelectSubset<T, action_logFindUniqueArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Action_log that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {action_logFindUniqueOrThrowArgs} args - Arguments to find a Action_log
   * @example
   * // Get one Action_log
   * const action_log = await prisma.action_log.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends action_logFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, action_logFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Action_log that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logFindFirstArgs} args - Arguments to find a Action_log
   * @example
   * // Get one Action_log
   * const action_log = await prisma.action_log.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends action_logFindFirstArgs>(args?: Prisma.SelectSubset<T, action_logFindFirstArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Action_log that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logFindFirstOrThrowArgs} args - Arguments to find a Action_log
   * @example
   * // Get one Action_log
   * const action_log = await prisma.action_log.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends action_logFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, action_logFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Action_logs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Action_logs
   * const action_logs = await prisma.action_log.findMany()
   * 
   * // Get first 10 Action_logs
   * const action_logs = await prisma.action_log.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const action_logWithIdOnly = await prisma.action_log.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends action_logFindManyArgs>(args?: Prisma.SelectSubset<T, action_logFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Action_log.
   * @param {action_logCreateArgs} args - Arguments to create a Action_log.
   * @example
   * // Create one Action_log
   * const Action_log = await prisma.action_log.create({
   *   data: {
   *     // ... data to create a Action_log
   *   }
   * })
   * 
   */
  create<T extends action_logCreateArgs>(args: Prisma.SelectSubset<T, action_logCreateArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Action_logs.
   * @param {action_logCreateManyArgs} args - Arguments to create many Action_logs.
   * @example
   * // Create many Action_logs
   * const action_log = await prisma.action_log.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends action_logCreateManyArgs>(args?: Prisma.SelectSubset<T, action_logCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Action_log.
   * @param {action_logDeleteArgs} args - Arguments to delete one Action_log.
   * @example
   * // Delete one Action_log
   * const Action_log = await prisma.action_log.delete({
   *   where: {
   *     // ... filter to delete one Action_log
   *   }
   * })
   * 
   */
  delete<T extends action_logDeleteArgs>(args: Prisma.SelectSubset<T, action_logDeleteArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Action_log.
   * @param {action_logUpdateArgs} args - Arguments to update one Action_log.
   * @example
   * // Update one Action_log
   * const action_log = await prisma.action_log.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends action_logUpdateArgs>(args: Prisma.SelectSubset<T, action_logUpdateArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Action_logs.
   * @param {action_logDeleteManyArgs} args - Arguments to filter Action_logs to delete.
   * @example
   * // Delete a few Action_logs
   * const { count } = await prisma.action_log.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends action_logDeleteManyArgs>(args?: Prisma.SelectSubset<T, action_logDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Action_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Action_logs
   * const action_log = await prisma.action_log.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends action_logUpdateManyArgs>(args: Prisma.SelectSubset<T, action_logUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Action_log.
   * @param {action_logUpsertArgs} args - Arguments to update or create a Action_log.
   * @example
   * // Update or create a Action_log
   * const action_log = await prisma.action_log.upsert({
   *   create: {
   *     // ... data to create a Action_log
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Action_log we want to update
   *   }
   * })
   */
  upsert<T extends action_logUpsertArgs>(args: Prisma.SelectSubset<T, action_logUpsertArgs<ExtArgs>>): Prisma.Prisma__action_logClient<runtime.Types.Result.GetResult<Prisma.$action_logPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Action_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logCountArgs} args - Arguments to filter Action_logs to count.
   * @example
   * // Count the number of Action_logs
   * const count = await prisma.action_log.count({
   *   where: {
   *     // ... the filter for the Action_logs we want to count
   *   }
   * })
  **/
  count<T extends action_logCountArgs>(
    args?: Prisma.Subset<T, action_logCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Action_logCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Action_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Action_logAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Action_logAggregateArgs>(args: Prisma.Subset<T, Action_logAggregateArgs>): Prisma.PrismaPromise<GetAction_logAggregateType<T>>

  /**
   * Group by Action_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {action_logGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends action_logGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: action_logGroupByArgs['orderBy'] }
      : { orderBy?: action_logGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, action_logGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAction_logGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the action_log model
 */
readonly fields: action_logFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for action_log.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__action_logClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  player<T extends Prisma.action_log$playerArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.action_log$playerArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  secondParty<T extends Prisma.action_log$secondPartyArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.action_log$secondPartyArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the action_log model
 */
export interface action_logFieldRefs {
  readonly id: Prisma.FieldRef<"action_log", 'Int'>
  readonly logType: Prisma.FieldRef<"action_log", 'String'>
  readonly info: Prisma.FieldRef<"action_log", 'Json'>
  readonly createdAt: Prisma.FieldRef<"action_log", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"action_log", 'DateTime'>
  readonly playerId: Prisma.FieldRef<"action_log", 'Int'>
  readonly secondPartyId: Prisma.FieldRef<"action_log", 'Int'>
}
    

// Custom InputTypes
/**
 * action_log findUnique
 */
export type action_logFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter, which action_log to fetch.
   */
  where: Prisma.action_logWhereUniqueInput
}

/**
 * action_log findUniqueOrThrow
 */
export type action_logFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter, which action_log to fetch.
   */
  where: Prisma.action_logWhereUniqueInput
}

/**
 * action_log findFirst
 */
export type action_logFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter, which action_log to fetch.
   */
  where?: Prisma.action_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of action_logs to fetch.
   */
  orderBy?: Prisma.action_logOrderByWithRelationInput | Prisma.action_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for action_logs.
   */
  cursor?: Prisma.action_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` action_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` action_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of action_logs.
   */
  distinct?: Prisma.Action_logScalarFieldEnum | Prisma.Action_logScalarFieldEnum[]
}

/**
 * action_log findFirstOrThrow
 */
export type action_logFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter, which action_log to fetch.
   */
  where?: Prisma.action_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of action_logs to fetch.
   */
  orderBy?: Prisma.action_logOrderByWithRelationInput | Prisma.action_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for action_logs.
   */
  cursor?: Prisma.action_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` action_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` action_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of action_logs.
   */
  distinct?: Prisma.Action_logScalarFieldEnum | Prisma.Action_logScalarFieldEnum[]
}

/**
 * action_log findMany
 */
export type action_logFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter, which action_logs to fetch.
   */
  where?: Prisma.action_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of action_logs to fetch.
   */
  orderBy?: Prisma.action_logOrderByWithRelationInput | Prisma.action_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing action_logs.
   */
  cursor?: Prisma.action_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` action_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` action_logs.
   */
  skip?: number
  distinct?: Prisma.Action_logScalarFieldEnum | Prisma.Action_logScalarFieldEnum[]
}

/**
 * action_log create
 */
export type action_logCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * The data needed to create a action_log.
   */
  data: Prisma.XOR<Prisma.action_logCreateInput, Prisma.action_logUncheckedCreateInput>
}

/**
 * action_log createMany
 */
export type action_logCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many action_logs.
   */
  data: Prisma.action_logCreateManyInput | Prisma.action_logCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * action_log update
 */
export type action_logUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * The data needed to update a action_log.
   */
  data: Prisma.XOR<Prisma.action_logUpdateInput, Prisma.action_logUncheckedUpdateInput>
  /**
   * Choose, which action_log to update.
   */
  where: Prisma.action_logWhereUniqueInput
}

/**
 * action_log updateMany
 */
export type action_logUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update action_logs.
   */
  data: Prisma.XOR<Prisma.action_logUpdateManyMutationInput, Prisma.action_logUncheckedUpdateManyInput>
  /**
   * Filter which action_logs to update
   */
  where?: Prisma.action_logWhereInput
  /**
   * Limit how many action_logs to update.
   */
  limit?: number
}

/**
 * action_log upsert
 */
export type action_logUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * The filter to search for the action_log to update in case it exists.
   */
  where: Prisma.action_logWhereUniqueInput
  /**
   * In case the action_log found by the `where` argument doesn't exist, create a new action_log with this data.
   */
  create: Prisma.XOR<Prisma.action_logCreateInput, Prisma.action_logUncheckedCreateInput>
  /**
   * In case the action_log was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.action_logUpdateInput, Prisma.action_logUncheckedUpdateInput>
}

/**
 * action_log delete
 */
export type action_logDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
  /**
   * Filter which action_log to delete.
   */
  where: Prisma.action_logWhereUniqueInput
}

/**
 * action_log deleteMany
 */
export type action_logDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which action_logs to delete
   */
  where?: Prisma.action_logWhereInput
  /**
   * Limit how many action_logs to delete.
   */
  limit?: number
}

/**
 * action_log.player
 */
export type action_log$playerArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * action_log.secondParty
 */
export type action_log$secondPartyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * action_log without action
 */
export type action_logDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the action_log
   */
  select?: Prisma.action_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the action_log
   */
  omit?: Prisma.action_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.action_logInclude<ExtArgs> | null
}
