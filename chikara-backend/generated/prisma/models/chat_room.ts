
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `chat_room` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model chat_room
 * 
 */
export type chat_roomModel = runtime.Types.Result.DefaultSelection<Prisma.$chat_roomPayload>

export type AggregateChat_room = {
  _count: Chat_roomCountAggregateOutputType | null
  _avg: Chat_roomAvgAggregateOutputType | null
  _sum: Chat_roomSumAggregateOutputType | null
  _min: Chat_roomMinAggregateOutputType | null
  _max: Chat_roomMaxAggregateOutputType | null
}

export type Chat_roomAvgAggregateOutputType = {
  id: number | null
  gangId: number | null
}

export type Chat_roomSumAggregateOutputType = {
  id: number | null
  gangId: number | null
}

export type Chat_roomMinAggregateOutputType = {
  id: number | null
  name: string | null
  gangId: number | null
}

export type Chat_roomMaxAggregateOutputType = {
  id: number | null
  name: string | null
  gangId: number | null
}

export type Chat_roomCountAggregateOutputType = {
  id: number
  name: number
  gangId: number
  _all: number
}


export type Chat_roomAvgAggregateInputType = {
  id?: true
  gangId?: true
}

export type Chat_roomSumAggregateInputType = {
  id?: true
  gangId?: true
}

export type Chat_roomMinAggregateInputType = {
  id?: true
  name?: true
  gangId?: true
}

export type Chat_roomMaxAggregateInputType = {
  id?: true
  name?: true
  gangId?: true
}

export type Chat_roomCountAggregateInputType = {
  id?: true
  name?: true
  gangId?: true
  _all?: true
}

export type Chat_roomAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which chat_room to aggregate.
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_rooms to fetch.
   */
  orderBy?: Prisma.chat_roomOrderByWithRelationInput | Prisma.chat_roomOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.chat_roomWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_rooms from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_rooms.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned chat_rooms
  **/
  _count?: true | Chat_roomCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Chat_roomAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Chat_roomSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Chat_roomMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Chat_roomMaxAggregateInputType
}

export type GetChat_roomAggregateType<T extends Chat_roomAggregateArgs> = {
      [P in keyof T & keyof AggregateChat_room]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateChat_room[P]>
    : Prisma.GetScalarType<T[P], AggregateChat_room[P]>
}




export type chat_roomGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.chat_roomWhereInput
  orderBy?: Prisma.chat_roomOrderByWithAggregationInput | Prisma.chat_roomOrderByWithAggregationInput[]
  by: Prisma.Chat_roomScalarFieldEnum[] | Prisma.Chat_roomScalarFieldEnum
  having?: Prisma.chat_roomScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Chat_roomCountAggregateInputType | true
  _avg?: Chat_roomAvgAggregateInputType
  _sum?: Chat_roomSumAggregateInputType
  _min?: Chat_roomMinAggregateInputType
  _max?: Chat_roomMaxAggregateInputType
}

export type Chat_roomGroupByOutputType = {
  id: number
  name: string
  gangId: number | null
  _count: Chat_roomCountAggregateOutputType | null
  _avg: Chat_roomAvgAggregateOutputType | null
  _sum: Chat_roomSumAggregateOutputType | null
  _min: Chat_roomMinAggregateOutputType | null
  _max: Chat_roomMaxAggregateOutputType | null
}

type GetChat_roomGroupByPayload<T extends chat_roomGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Chat_roomGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Chat_roomGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Chat_roomGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Chat_roomGroupByOutputType[P]>
      }
    >
  >



export type chat_roomWhereInput = {
  AND?: Prisma.chat_roomWhereInput | Prisma.chat_roomWhereInput[]
  OR?: Prisma.chat_roomWhereInput[]
  NOT?: Prisma.chat_roomWhereInput | Prisma.chat_roomWhereInput[]
  id?: Prisma.IntFilter<"chat_room"> | number
  name?: Prisma.StringFilter<"chat_room"> | string
  gangId?: Prisma.IntNullableFilter<"chat_room"> | number | null
  chat_message?: Prisma.Chat_messageListRelationFilter
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
}

export type chat_roomOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  chat_message?: Prisma.chat_messageOrderByRelationAggregateInput
  gang?: Prisma.gangOrderByWithRelationInput
  _relevance?: Prisma.chat_roomOrderByRelevanceInput
}

export type chat_roomWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.chat_roomWhereInput | Prisma.chat_roomWhereInput[]
  OR?: Prisma.chat_roomWhereInput[]
  NOT?: Prisma.chat_roomWhereInput | Prisma.chat_roomWhereInput[]
  name?: Prisma.StringFilter<"chat_room"> | string
  gangId?: Prisma.IntNullableFilter<"chat_room"> | number | null
  chat_message?: Prisma.Chat_messageListRelationFilter
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
}, "id">

export type chat_roomOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.chat_roomCountOrderByAggregateInput
  _avg?: Prisma.chat_roomAvgOrderByAggregateInput
  _max?: Prisma.chat_roomMaxOrderByAggregateInput
  _min?: Prisma.chat_roomMinOrderByAggregateInput
  _sum?: Prisma.chat_roomSumOrderByAggregateInput
}

export type chat_roomScalarWhereWithAggregatesInput = {
  AND?: Prisma.chat_roomScalarWhereWithAggregatesInput | Prisma.chat_roomScalarWhereWithAggregatesInput[]
  OR?: Prisma.chat_roomScalarWhereWithAggregatesInput[]
  NOT?: Prisma.chat_roomScalarWhereWithAggregatesInput | Prisma.chat_roomScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"chat_room"> | number
  name?: Prisma.StringWithAggregatesFilter<"chat_room"> | string
  gangId?: Prisma.IntNullableWithAggregatesFilter<"chat_room"> | number | null
}

export type chat_roomCreateInput = {
  name: string
  chat_message?: Prisma.chat_messageCreateNestedManyWithoutChat_roomInput
  gang?: Prisma.gangCreateNestedOneWithoutChat_roomInput
}

export type chat_roomUncheckedCreateInput = {
  id?: number
  name: string
  gangId?: number | null
  chat_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_roomInput
}

export type chat_roomUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  chat_message?: Prisma.chat_messageUpdateManyWithoutChat_roomNestedInput
  gang?: Prisma.gangUpdateOneWithoutChat_roomNestedInput
}

export type chat_roomUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  chat_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_roomNestedInput
}

export type chat_roomCreateManyInput = {
  id?: number
  name: string
  gangId?: number | null
}

export type chat_roomUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
}

export type chat_roomUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Chat_roomNullableScalarRelationFilter = {
  is?: Prisma.chat_roomWhereInput | null
  isNot?: Prisma.chat_roomWhereInput | null
}

export type chat_roomOrderByRelevanceInput = {
  fields: Prisma.chat_roomOrderByRelevanceFieldEnum | Prisma.chat_roomOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type chat_roomCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
}

export type chat_roomAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
}

export type chat_roomMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
}

export type chat_roomMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
}

export type chat_roomSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
}

export type Chat_roomListRelationFilter = {
  every?: Prisma.chat_roomWhereInput
  some?: Prisma.chat_roomWhereInput
  none?: Prisma.chat_roomWhereInput
}

export type chat_roomOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type chat_roomCreateNestedOneWithoutChat_messageInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutChat_messageInput, Prisma.chat_roomUncheckedCreateWithoutChat_messageInput>
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutChat_messageInput
  connect?: Prisma.chat_roomWhereUniqueInput
}

export type chat_roomUpdateOneWithoutChat_messageNestedInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutChat_messageInput, Prisma.chat_roomUncheckedCreateWithoutChat_messageInput>
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutChat_messageInput
  upsert?: Prisma.chat_roomUpsertWithoutChat_messageInput
  disconnect?: Prisma.chat_roomWhereInput | boolean
  delete?: Prisma.chat_roomWhereInput | boolean
  connect?: Prisma.chat_roomWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.chat_roomUpdateToOneWithWhereWithoutChat_messageInput, Prisma.chat_roomUpdateWithoutChat_messageInput>, Prisma.chat_roomUncheckedUpdateWithoutChat_messageInput>
}

export type chat_roomCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput> | Prisma.chat_roomCreateWithoutGangInput[] | Prisma.chat_roomUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutGangInput | Prisma.chat_roomCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.chat_roomCreateManyGangInputEnvelope
  connect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
}

export type chat_roomUncheckedCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput> | Prisma.chat_roomCreateWithoutGangInput[] | Prisma.chat_roomUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutGangInput | Prisma.chat_roomCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.chat_roomCreateManyGangInputEnvelope
  connect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
}

export type chat_roomUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput> | Prisma.chat_roomCreateWithoutGangInput[] | Prisma.chat_roomUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutGangInput | Prisma.chat_roomCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.chat_roomUpsertWithWhereUniqueWithoutGangInput | Prisma.chat_roomUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.chat_roomCreateManyGangInputEnvelope
  set?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  disconnect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  delete?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  connect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  update?: Prisma.chat_roomUpdateWithWhereUniqueWithoutGangInput | Prisma.chat_roomUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.chat_roomUpdateManyWithWhereWithoutGangInput | Prisma.chat_roomUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.chat_roomScalarWhereInput | Prisma.chat_roomScalarWhereInput[]
}

export type chat_roomUncheckedUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput> | Prisma.chat_roomCreateWithoutGangInput[] | Prisma.chat_roomUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.chat_roomCreateOrConnectWithoutGangInput | Prisma.chat_roomCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.chat_roomUpsertWithWhereUniqueWithoutGangInput | Prisma.chat_roomUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.chat_roomCreateManyGangInputEnvelope
  set?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  disconnect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  delete?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  connect?: Prisma.chat_roomWhereUniqueInput | Prisma.chat_roomWhereUniqueInput[]
  update?: Prisma.chat_roomUpdateWithWhereUniqueWithoutGangInput | Prisma.chat_roomUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.chat_roomUpdateManyWithWhereWithoutGangInput | Prisma.chat_roomUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.chat_roomScalarWhereInput | Prisma.chat_roomScalarWhereInput[]
}

export type chat_roomCreateWithoutChat_messageInput = {
  name: string
  gang?: Prisma.gangCreateNestedOneWithoutChat_roomInput
}

export type chat_roomUncheckedCreateWithoutChat_messageInput = {
  id?: number
  name: string
  gangId?: number | null
}

export type chat_roomCreateOrConnectWithoutChat_messageInput = {
  where: Prisma.chat_roomWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_roomCreateWithoutChat_messageInput, Prisma.chat_roomUncheckedCreateWithoutChat_messageInput>
}

export type chat_roomUpsertWithoutChat_messageInput = {
  update: Prisma.XOR<Prisma.chat_roomUpdateWithoutChat_messageInput, Prisma.chat_roomUncheckedUpdateWithoutChat_messageInput>
  create: Prisma.XOR<Prisma.chat_roomCreateWithoutChat_messageInput, Prisma.chat_roomUncheckedCreateWithoutChat_messageInput>
  where?: Prisma.chat_roomWhereInput
}

export type chat_roomUpdateToOneWithWhereWithoutChat_messageInput = {
  where?: Prisma.chat_roomWhereInput
  data: Prisma.XOR<Prisma.chat_roomUpdateWithoutChat_messageInput, Prisma.chat_roomUncheckedUpdateWithoutChat_messageInput>
}

export type chat_roomUpdateWithoutChat_messageInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  gang?: Prisma.gangUpdateOneWithoutChat_roomNestedInput
}

export type chat_roomUncheckedUpdateWithoutChat_messageInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type chat_roomCreateWithoutGangInput = {
  name: string
  chat_message?: Prisma.chat_messageCreateNestedManyWithoutChat_roomInput
}

export type chat_roomUncheckedCreateWithoutGangInput = {
  id?: number
  name: string
  chat_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_roomInput
}

export type chat_roomCreateOrConnectWithoutGangInput = {
  where: Prisma.chat_roomWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput>
}

export type chat_roomCreateManyGangInputEnvelope = {
  data: Prisma.chat_roomCreateManyGangInput | Prisma.chat_roomCreateManyGangInput[]
  skipDuplicates?: boolean
}

export type chat_roomUpsertWithWhereUniqueWithoutGangInput = {
  where: Prisma.chat_roomWhereUniqueInput
  update: Prisma.XOR<Prisma.chat_roomUpdateWithoutGangInput, Prisma.chat_roomUncheckedUpdateWithoutGangInput>
  create: Prisma.XOR<Prisma.chat_roomCreateWithoutGangInput, Prisma.chat_roomUncheckedCreateWithoutGangInput>
}

export type chat_roomUpdateWithWhereUniqueWithoutGangInput = {
  where: Prisma.chat_roomWhereUniqueInput
  data: Prisma.XOR<Prisma.chat_roomUpdateWithoutGangInput, Prisma.chat_roomUncheckedUpdateWithoutGangInput>
}

export type chat_roomUpdateManyWithWhereWithoutGangInput = {
  where: Prisma.chat_roomScalarWhereInput
  data: Prisma.XOR<Prisma.chat_roomUpdateManyMutationInput, Prisma.chat_roomUncheckedUpdateManyWithoutGangInput>
}

export type chat_roomScalarWhereInput = {
  AND?: Prisma.chat_roomScalarWhereInput | Prisma.chat_roomScalarWhereInput[]
  OR?: Prisma.chat_roomScalarWhereInput[]
  NOT?: Prisma.chat_roomScalarWhereInput | Prisma.chat_roomScalarWhereInput[]
  id?: Prisma.IntFilter<"chat_room"> | number
  name?: Prisma.StringFilter<"chat_room"> | string
  gangId?: Prisma.IntNullableFilter<"chat_room"> | number | null
}

export type chat_roomCreateManyGangInput = {
  id?: number
  name: string
}

export type chat_roomUpdateWithoutGangInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  chat_message?: Prisma.chat_messageUpdateManyWithoutChat_roomNestedInput
}

export type chat_roomUncheckedUpdateWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  chat_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_roomNestedInput
}

export type chat_roomUncheckedUpdateManyWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
}


/**
 * Count Type Chat_roomCountOutputType
 */

export type Chat_roomCountOutputType = {
  chat_message: number
}

export type Chat_roomCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  chat_message?: boolean | Chat_roomCountOutputTypeCountChat_messageArgs
}

/**
 * Chat_roomCountOutputType without action
 */
export type Chat_roomCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Chat_roomCountOutputType
   */
  select?: Prisma.Chat_roomCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Chat_roomCountOutputType without action
 */
export type Chat_roomCountOutputTypeCountChat_messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.chat_messageWhereInput
}


export type chat_roomSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  gangId?: boolean
  chat_message?: boolean | Prisma.chat_room$chat_messageArgs<ExtArgs>
  gang?: boolean | Prisma.chat_room$gangArgs<ExtArgs>
  _count?: boolean | Prisma.Chat_roomCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["chat_room"]>



export type chat_roomSelectScalar = {
  id?: boolean
  name?: boolean
  gangId?: boolean
}

export type chat_roomOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "gangId", ExtArgs["result"]["chat_room"]>
export type chat_roomInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  chat_message?: boolean | Prisma.chat_room$chat_messageArgs<ExtArgs>
  gang?: boolean | Prisma.chat_room$gangArgs<ExtArgs>
  _count?: boolean | Prisma.Chat_roomCountOutputTypeDefaultArgs<ExtArgs>
}

export type $chat_roomPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "chat_room"
  objects: {
    chat_message: Prisma.$chat_messagePayload<ExtArgs>[]
    gang: Prisma.$gangPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    gangId: number | null
  }, ExtArgs["result"]["chat_room"]>
  composites: {}
}

export type chat_roomGetPayload<S extends boolean | null | undefined | chat_roomDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$chat_roomPayload, S>

export type chat_roomCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<chat_roomFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Chat_roomCountAggregateInputType | true
  }

export interface chat_roomDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['chat_room'], meta: { name: 'chat_room' } }
  /**
   * Find zero or one Chat_room that matches the filter.
   * @param {chat_roomFindUniqueArgs} args - Arguments to find a Chat_room
   * @example
   * // Get one Chat_room
   * const chat_room = await prisma.chat_room.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends chat_roomFindUniqueArgs>(args: Prisma.SelectSubset<T, chat_roomFindUniqueArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Chat_room that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {chat_roomFindUniqueOrThrowArgs} args - Arguments to find a Chat_room
   * @example
   * // Get one Chat_room
   * const chat_room = await prisma.chat_room.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends chat_roomFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, chat_roomFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Chat_room that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomFindFirstArgs} args - Arguments to find a Chat_room
   * @example
   * // Get one Chat_room
   * const chat_room = await prisma.chat_room.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends chat_roomFindFirstArgs>(args?: Prisma.SelectSubset<T, chat_roomFindFirstArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Chat_room that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomFindFirstOrThrowArgs} args - Arguments to find a Chat_room
   * @example
   * // Get one Chat_room
   * const chat_room = await prisma.chat_room.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends chat_roomFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, chat_roomFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Chat_rooms that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Chat_rooms
   * const chat_rooms = await prisma.chat_room.findMany()
   * 
   * // Get first 10 Chat_rooms
   * const chat_rooms = await prisma.chat_room.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const chat_roomWithIdOnly = await prisma.chat_room.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends chat_roomFindManyArgs>(args?: Prisma.SelectSubset<T, chat_roomFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Chat_room.
   * @param {chat_roomCreateArgs} args - Arguments to create a Chat_room.
   * @example
   * // Create one Chat_room
   * const Chat_room = await prisma.chat_room.create({
   *   data: {
   *     // ... data to create a Chat_room
   *   }
   * })
   * 
   */
  create<T extends chat_roomCreateArgs>(args: Prisma.SelectSubset<T, chat_roomCreateArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Chat_rooms.
   * @param {chat_roomCreateManyArgs} args - Arguments to create many Chat_rooms.
   * @example
   * // Create many Chat_rooms
   * const chat_room = await prisma.chat_room.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends chat_roomCreateManyArgs>(args?: Prisma.SelectSubset<T, chat_roomCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Chat_room.
   * @param {chat_roomDeleteArgs} args - Arguments to delete one Chat_room.
   * @example
   * // Delete one Chat_room
   * const Chat_room = await prisma.chat_room.delete({
   *   where: {
   *     // ... filter to delete one Chat_room
   *   }
   * })
   * 
   */
  delete<T extends chat_roomDeleteArgs>(args: Prisma.SelectSubset<T, chat_roomDeleteArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Chat_room.
   * @param {chat_roomUpdateArgs} args - Arguments to update one Chat_room.
   * @example
   * // Update one Chat_room
   * const chat_room = await prisma.chat_room.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends chat_roomUpdateArgs>(args: Prisma.SelectSubset<T, chat_roomUpdateArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Chat_rooms.
   * @param {chat_roomDeleteManyArgs} args - Arguments to filter Chat_rooms to delete.
   * @example
   * // Delete a few Chat_rooms
   * const { count } = await prisma.chat_room.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends chat_roomDeleteManyArgs>(args?: Prisma.SelectSubset<T, chat_roomDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Chat_rooms.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Chat_rooms
   * const chat_room = await prisma.chat_room.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends chat_roomUpdateManyArgs>(args: Prisma.SelectSubset<T, chat_roomUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Chat_room.
   * @param {chat_roomUpsertArgs} args - Arguments to update or create a Chat_room.
   * @example
   * // Update or create a Chat_room
   * const chat_room = await prisma.chat_room.upsert({
   *   create: {
   *     // ... data to create a Chat_room
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Chat_room we want to update
   *   }
   * })
   */
  upsert<T extends chat_roomUpsertArgs>(args: Prisma.SelectSubset<T, chat_roomUpsertArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Chat_rooms.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomCountArgs} args - Arguments to filter Chat_rooms to count.
   * @example
   * // Count the number of Chat_rooms
   * const count = await prisma.chat_room.count({
   *   where: {
   *     // ... the filter for the Chat_rooms we want to count
   *   }
   * })
  **/
  count<T extends chat_roomCountArgs>(
    args?: Prisma.Subset<T, chat_roomCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Chat_roomCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Chat_room.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Chat_roomAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Chat_roomAggregateArgs>(args: Prisma.Subset<T, Chat_roomAggregateArgs>): Prisma.PrismaPromise<GetChat_roomAggregateType<T>>

  /**
   * Group by Chat_room.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_roomGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends chat_roomGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: chat_roomGroupByArgs['orderBy'] }
      : { orderBy?: chat_roomGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, chat_roomGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetChat_roomGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the chat_room model
 */
readonly fields: chat_roomFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for chat_room.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__chat_roomClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  chat_message<T extends Prisma.chat_room$chat_messageArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_room$chat_messageArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  gang<T extends Prisma.chat_room$gangArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_room$gangArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the chat_room model
 */
export interface chat_roomFieldRefs {
  readonly id: Prisma.FieldRef<"chat_room", 'Int'>
  readonly name: Prisma.FieldRef<"chat_room", 'String'>
  readonly gangId: Prisma.FieldRef<"chat_room", 'Int'>
}
    

// Custom InputTypes
/**
 * chat_room findUnique
 */
export type chat_roomFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter, which chat_room to fetch.
   */
  where: Prisma.chat_roomWhereUniqueInput
}

/**
 * chat_room findUniqueOrThrow
 */
export type chat_roomFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter, which chat_room to fetch.
   */
  where: Prisma.chat_roomWhereUniqueInput
}

/**
 * chat_room findFirst
 */
export type chat_roomFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter, which chat_room to fetch.
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_rooms to fetch.
   */
  orderBy?: Prisma.chat_roomOrderByWithRelationInput | Prisma.chat_roomOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for chat_rooms.
   */
  cursor?: Prisma.chat_roomWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_rooms from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_rooms.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of chat_rooms.
   */
  distinct?: Prisma.Chat_roomScalarFieldEnum | Prisma.Chat_roomScalarFieldEnum[]
}

/**
 * chat_room findFirstOrThrow
 */
export type chat_roomFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter, which chat_room to fetch.
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_rooms to fetch.
   */
  orderBy?: Prisma.chat_roomOrderByWithRelationInput | Prisma.chat_roomOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for chat_rooms.
   */
  cursor?: Prisma.chat_roomWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_rooms from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_rooms.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of chat_rooms.
   */
  distinct?: Prisma.Chat_roomScalarFieldEnum | Prisma.Chat_roomScalarFieldEnum[]
}

/**
 * chat_room findMany
 */
export type chat_roomFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter, which chat_rooms to fetch.
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_rooms to fetch.
   */
  orderBy?: Prisma.chat_roomOrderByWithRelationInput | Prisma.chat_roomOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing chat_rooms.
   */
  cursor?: Prisma.chat_roomWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_rooms from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_rooms.
   */
  skip?: number
  distinct?: Prisma.Chat_roomScalarFieldEnum | Prisma.Chat_roomScalarFieldEnum[]
}

/**
 * chat_room create
 */
export type chat_roomCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * The data needed to create a chat_room.
   */
  data: Prisma.XOR<Prisma.chat_roomCreateInput, Prisma.chat_roomUncheckedCreateInput>
}

/**
 * chat_room createMany
 */
export type chat_roomCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many chat_rooms.
   */
  data: Prisma.chat_roomCreateManyInput | Prisma.chat_roomCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * chat_room update
 */
export type chat_roomUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * The data needed to update a chat_room.
   */
  data: Prisma.XOR<Prisma.chat_roomUpdateInput, Prisma.chat_roomUncheckedUpdateInput>
  /**
   * Choose, which chat_room to update.
   */
  where: Prisma.chat_roomWhereUniqueInput
}

/**
 * chat_room updateMany
 */
export type chat_roomUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update chat_rooms.
   */
  data: Prisma.XOR<Prisma.chat_roomUpdateManyMutationInput, Prisma.chat_roomUncheckedUpdateManyInput>
  /**
   * Filter which chat_rooms to update
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * Limit how many chat_rooms to update.
   */
  limit?: number
}

/**
 * chat_room upsert
 */
export type chat_roomUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * The filter to search for the chat_room to update in case it exists.
   */
  where: Prisma.chat_roomWhereUniqueInput
  /**
   * In case the chat_room found by the `where` argument doesn't exist, create a new chat_room with this data.
   */
  create: Prisma.XOR<Prisma.chat_roomCreateInput, Prisma.chat_roomUncheckedCreateInput>
  /**
   * In case the chat_room was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.chat_roomUpdateInput, Prisma.chat_roomUncheckedUpdateInput>
}

/**
 * chat_room delete
 */
export type chat_roomDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  /**
   * Filter which chat_room to delete.
   */
  where: Prisma.chat_roomWhereUniqueInput
}

/**
 * chat_room deleteMany
 */
export type chat_roomDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which chat_rooms to delete
   */
  where?: Prisma.chat_roomWhereInput
  /**
   * Limit how many chat_rooms to delete.
   */
  limit?: number
}

/**
 * chat_room.chat_message
 */
export type chat_room$chat_messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  where?: Prisma.chat_messageWhereInput
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  cursor?: Prisma.chat_messageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Chat_messageScalarFieldEnum | Prisma.Chat_messageScalarFieldEnum[]
}

/**
 * chat_room.gang
 */
export type chat_room$gangArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  where?: Prisma.gangWhereInput
}

/**
 * chat_room without action
 */
export type chat_roomDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
}
