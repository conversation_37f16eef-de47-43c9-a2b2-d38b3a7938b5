
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `gang_log` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model gang_log
 * 
 */
export type gang_logModel = runtime.Types.Result.DefaultSelection<Prisma.$gang_logPayload>

export type AggregateGang_log = {
  _count: Gang_logCountAggregateOutputType | null
  _avg: Gang_logAvgAggregateOutputType | null
  _sum: Gang_logSumAggregateOutputType | null
  _min: Gang_logMinAggregateOutputType | null
  _max: Gang_logMaxAggregateOutputType | null
}

export type Gang_logAvgAggregateOutputType = {
  id: number | null
  gangId: number | null
  gangMemberId: number | null
  secondPartyId: number | null
}

export type Gang_logSumAggregateOutputType = {
  id: number | null
  gangId: number | null
  gangMemberId: number | null
  secondPartyId: number | null
}

export type Gang_logMinAggregateOutputType = {
  id: number | null
  action: string | null
  info: string | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  gangMemberId: number | null
  secondPartyId: number | null
}

export type Gang_logMaxAggregateOutputType = {
  id: number | null
  action: string | null
  info: string | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  gangMemberId: number | null
  secondPartyId: number | null
}

export type Gang_logCountAggregateOutputType = {
  id: number
  action: number
  info: number
  createdAt: number
  updatedAt: number
  gangId: number
  gangMemberId: number
  secondPartyId: number
  _all: number
}


export type Gang_logAvgAggregateInputType = {
  id?: true
  gangId?: true
  gangMemberId?: true
  secondPartyId?: true
}

export type Gang_logSumAggregateInputType = {
  id?: true
  gangId?: true
  gangMemberId?: true
  secondPartyId?: true
}

export type Gang_logMinAggregateInputType = {
  id?: true
  action?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  gangMemberId?: true
  secondPartyId?: true
}

export type Gang_logMaxAggregateInputType = {
  id?: true
  action?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  gangMemberId?: true
  secondPartyId?: true
}

export type Gang_logCountAggregateInputType = {
  id?: true
  action?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  gangMemberId?: true
  secondPartyId?: true
  _all?: true
}

export type Gang_logAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_log to aggregate.
   */
  where?: Prisma.gang_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_logs to fetch.
   */
  orderBy?: Prisma.gang_logOrderByWithRelationInput | Prisma.gang_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.gang_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned gang_logs
  **/
  _count?: true | Gang_logCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Gang_logAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Gang_logSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Gang_logMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Gang_logMaxAggregateInputType
}

export type GetGang_logAggregateType<T extends Gang_logAggregateArgs> = {
      [P in keyof T & keyof AggregateGang_log]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGang_log[P]>
    : Prisma.GetScalarType<T[P], AggregateGang_log[P]>
}




export type gang_logGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_logWhereInput
  orderBy?: Prisma.gang_logOrderByWithAggregationInput | Prisma.gang_logOrderByWithAggregationInput[]
  by: Prisma.Gang_logScalarFieldEnum[] | Prisma.Gang_logScalarFieldEnum
  having?: Prisma.gang_logScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Gang_logCountAggregateInputType | true
  _avg?: Gang_logAvgAggregateInputType
  _sum?: Gang_logSumAggregateInputType
  _min?: Gang_logMinAggregateInputType
  _max?: Gang_logMaxAggregateInputType
}

export type Gang_logGroupByOutputType = {
  id: number
  action: string | null
  info: string
  createdAt: Date
  updatedAt: Date
  gangId: number | null
  gangMemberId: number | null
  secondPartyId: number | null
  _count: Gang_logCountAggregateOutputType | null
  _avg: Gang_logAvgAggregateOutputType | null
  _sum: Gang_logSumAggregateOutputType | null
  _min: Gang_logMinAggregateOutputType | null
  _max: Gang_logMaxAggregateOutputType | null
}

type GetGang_logGroupByPayload<T extends gang_logGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Gang_logGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Gang_logGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Gang_logGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Gang_logGroupByOutputType[P]>
      }
    >
  >



export type gang_logWhereInput = {
  AND?: Prisma.gang_logWhereInput | Prisma.gang_logWhereInput[]
  OR?: Prisma.gang_logWhereInput[]
  NOT?: Prisma.gang_logWhereInput | Prisma.gang_logWhereInput[]
  id?: Prisma.IntFilter<"gang_log"> | number
  action?: Prisma.StringNullableFilter<"gang_log"> | string | null
  info?: Prisma.StringFilter<"gang_log"> | string
  createdAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  gangMemberId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user_gang_log_gangMemberIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_gang_log_secondPartyIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type gang_logOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrderInput | Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  gangMemberId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  gang?: Prisma.gangOrderByWithRelationInput
  user_gang_log_gangMemberIdTouser?: Prisma.userOrderByWithRelationInput
  user_gang_log_secondPartyIdTouser?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.gang_logOrderByRelevanceInput
}

export type gang_logWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.gang_logWhereInput | Prisma.gang_logWhereInput[]
  OR?: Prisma.gang_logWhereInput[]
  NOT?: Prisma.gang_logWhereInput | Prisma.gang_logWhereInput[]
  action?: Prisma.StringNullableFilter<"gang_log"> | string | null
  info?: Prisma.StringFilter<"gang_log"> | string
  createdAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  gangMemberId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user_gang_log_gangMemberIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_gang_log_secondPartyIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type gang_logOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrderInput | Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  gangMemberId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.gang_logCountOrderByAggregateInput
  _avg?: Prisma.gang_logAvgOrderByAggregateInput
  _max?: Prisma.gang_logMaxOrderByAggregateInput
  _min?: Prisma.gang_logMinOrderByAggregateInput
  _sum?: Prisma.gang_logSumOrderByAggregateInput
}

export type gang_logScalarWhereWithAggregatesInput = {
  AND?: Prisma.gang_logScalarWhereWithAggregatesInput | Prisma.gang_logScalarWhereWithAggregatesInput[]
  OR?: Prisma.gang_logScalarWhereWithAggregatesInput[]
  NOT?: Prisma.gang_logScalarWhereWithAggregatesInput | Prisma.gang_logScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"gang_log"> | number
  action?: Prisma.StringNullableWithAggregatesFilter<"gang_log"> | string | null
  info?: Prisma.StringWithAggregatesFilter<"gang_log"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"gang_log"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"gang_log"> | Date | string
  gangId?: Prisma.IntNullableWithAggregatesFilter<"gang_log"> | number | null
  gangMemberId?: Prisma.IntNullableWithAggregatesFilter<"gang_log"> | number | null
  secondPartyId?: Prisma.IntNullableWithAggregatesFilter<"gang_log"> | number | null
}

export type gang_logCreateInput = {
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_logInput
  user_gang_log_gangMemberIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_gangMemberIdTouserInput
  user_gang_log_secondPartyIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_secondPartyIdTouserInput
}

export type gang_logUncheckedCreateInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  gangMemberId?: number | null
  secondPartyId?: number | null
}

export type gang_logUpdateInput = {
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_logNestedInput
  user_gang_log_gangMemberIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_gangMemberIdTouserNestedInput
  user_gang_log_secondPartyIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_secondPartyIdTouserNestedInput
}

export type gang_logUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logCreateManyInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  gangMemberId?: number | null
  secondPartyId?: number | null
}

export type gang_logUpdateManyMutationInput = {
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type gang_logUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Gang_logListRelationFilter = {
  every?: Prisma.gang_logWhereInput
  some?: Prisma.gang_logWhereInput
  none?: Prisma.gang_logWhereInput
}

export type gang_logOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type gang_logOrderByRelevanceInput = {
  fields: Prisma.gang_logOrderByRelevanceFieldEnum | Prisma.gang_logOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type gang_logCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  gangMemberId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type gang_logAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  gangMemberId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type gang_logMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  gangMemberId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type gang_logMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  gangMemberId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type gang_logSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  gangMemberId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
}

export type gang_logCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput> | Prisma.gang_logCreateWithoutGangInput[] | Prisma.gang_logUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutGangInput | Prisma.gang_logCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_logCreateManyGangInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logUncheckedCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput> | Prisma.gang_logCreateWithoutGangInput[] | Prisma.gang_logUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutGangInput | Prisma.gang_logCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_logCreateManyGangInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput> | Prisma.gang_logCreateWithoutGangInput[] | Prisma.gang_logUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutGangInput | Prisma.gang_logCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_logUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_logCreateManyGangInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_logUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutGangInput | Prisma.gang_logUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logUncheckedUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput> | Prisma.gang_logCreateWithoutGangInput[] | Prisma.gang_logUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutGangInput | Prisma.gang_logCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_logUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_logCreateManyGangInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_logUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutGangInput | Prisma.gang_logUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logCreateNestedManyWithoutUser_gang_log_gangMemberIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logCreateNestedManyWithoutUser_gang_log_secondPartyIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logUncheckedCreateNestedManyWithoutUser_gang_log_gangMemberIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logUncheckedCreateNestedManyWithoutUser_gang_log_secondPartyIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInputEnvelope
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
}

export type gang_logUpdateManyWithoutUser_gang_log_gangMemberIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_gangMemberIdTouserInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logUpdateManyWithoutUser_gang_log_secondPartyIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_secondPartyIdTouserInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logUncheckedUpdateManyWithoutUser_gang_log_gangMemberIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_gangMemberIdTouserInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logUncheckedUpdateManyWithoutUser_gang_log_secondPartyIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput> | Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput[] | Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput[]
  connectOrCreate?: Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput[]
  upsert?: Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpsertWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput[]
  createMany?: Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInputEnvelope
  set?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  disconnect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  delete?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  connect?: Prisma.gang_logWhereUniqueInput | Prisma.gang_logWhereUniqueInput[]
  update?: Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpdateWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput[]
  updateMany?: Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logUpdateManyWithWhereWithoutUser_gang_log_secondPartyIdTouserInput[]
  deleteMany?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
}

export type gang_logCreateWithoutGangInput = {
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user_gang_log_gangMemberIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_gangMemberIdTouserInput
  user_gang_log_secondPartyIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_secondPartyIdTouserInput
}

export type gang_logUncheckedCreateWithoutGangInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangMemberId?: number | null
  secondPartyId?: number | null
}

export type gang_logCreateOrConnectWithoutGangInput = {
  where: Prisma.gang_logWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput>
}

export type gang_logCreateManyGangInputEnvelope = {
  data: Prisma.gang_logCreateManyGangInput | Prisma.gang_logCreateManyGangInput[]
  skipDuplicates?: boolean
}

export type gang_logUpsertWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_logWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_logUpdateWithoutGangInput, Prisma.gang_logUncheckedUpdateWithoutGangInput>
  create: Prisma.XOR<Prisma.gang_logCreateWithoutGangInput, Prisma.gang_logUncheckedCreateWithoutGangInput>
}

export type gang_logUpdateWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_logWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_logUpdateWithoutGangInput, Prisma.gang_logUncheckedUpdateWithoutGangInput>
}

export type gang_logUpdateManyWithWhereWithoutGangInput = {
  where: Prisma.gang_logScalarWhereInput
  data: Prisma.XOR<Prisma.gang_logUpdateManyMutationInput, Prisma.gang_logUncheckedUpdateManyWithoutGangInput>
}

export type gang_logScalarWhereInput = {
  AND?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
  OR?: Prisma.gang_logScalarWhereInput[]
  NOT?: Prisma.gang_logScalarWhereInput | Prisma.gang_logScalarWhereInput[]
  id?: Prisma.IntFilter<"gang_log"> | number
  action?: Prisma.StringNullableFilter<"gang_log"> | string | null
  info?: Prisma.StringFilter<"gang_log"> | string
  createdAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_log"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  gangMemberId?: Prisma.IntNullableFilter<"gang_log"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"gang_log"> | number | null
}

export type gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput = {
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_logInput
  user_gang_log_secondPartyIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_secondPartyIdTouserInput
}

export type gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  secondPartyId?: number | null
}

export type gang_logCreateOrConnectWithoutUser_gang_log_gangMemberIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput>
}

export type gang_logCreateManyUser_gang_log_gangMemberIdTouserInputEnvelope = {
  data: Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInput | Prisma.gang_logCreateManyUser_gang_log_gangMemberIdTouserInput[]
  skipDuplicates?: boolean
}

export type gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput = {
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_logInput
  user_gang_log_gangMemberIdTouser?: Prisma.userCreateNestedOneWithoutGang_log_gang_log_gangMemberIdTouserInput
}

export type gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  gangMemberId?: number | null
}

export type gang_logCreateOrConnectWithoutUser_gang_log_secondPartyIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput>
}

export type gang_logCreateManyUser_gang_log_secondPartyIdTouserInputEnvelope = {
  data: Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInput | Prisma.gang_logCreateManyUser_gang_log_secondPartyIdTouserInput[]
  skipDuplicates?: boolean
}

export type gang_logUpsertWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_logUpdateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedUpdateWithoutUser_gang_log_gangMemberIdTouserInput>
  create: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_gangMemberIdTouserInput>
}

export type gang_logUpdateWithWhereUniqueWithoutUser_gang_log_gangMemberIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_logUpdateWithoutUser_gang_log_gangMemberIdTouserInput, Prisma.gang_logUncheckedUpdateWithoutUser_gang_log_gangMemberIdTouserInput>
}

export type gang_logUpdateManyWithWhereWithoutUser_gang_log_gangMemberIdTouserInput = {
  where: Prisma.gang_logScalarWhereInput
  data: Prisma.XOR<Prisma.gang_logUpdateManyMutationInput, Prisma.gang_logUncheckedUpdateManyWithoutUser_gang_log_gangMemberIdTouserInput>
}

export type gang_logUpsertWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_logUpdateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedUpdateWithoutUser_gang_log_secondPartyIdTouserInput>
  create: Prisma.XOR<Prisma.gang_logCreateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedCreateWithoutUser_gang_log_secondPartyIdTouserInput>
}

export type gang_logUpdateWithWhereUniqueWithoutUser_gang_log_secondPartyIdTouserInput = {
  where: Prisma.gang_logWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_logUpdateWithoutUser_gang_log_secondPartyIdTouserInput, Prisma.gang_logUncheckedUpdateWithoutUser_gang_log_secondPartyIdTouserInput>
}

export type gang_logUpdateManyWithWhereWithoutUser_gang_log_secondPartyIdTouserInput = {
  where: Prisma.gang_logScalarWhereInput
  data: Prisma.XOR<Prisma.gang_logUpdateManyMutationInput, Prisma.gang_logUncheckedUpdateManyWithoutUser_gang_log_secondPartyIdTouserInput>
}

export type gang_logCreateManyGangInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangMemberId?: number | null
  secondPartyId?: number | null
}

export type gang_logUpdateWithoutGangInput = {
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_gang_log_gangMemberIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_gangMemberIdTouserNestedInput
  user_gang_log_secondPartyIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_secondPartyIdTouserNestedInput
}

export type gang_logUncheckedUpdateWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logUncheckedUpdateManyWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logCreateManyUser_gang_log_gangMemberIdTouserInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  secondPartyId?: number | null
}

export type gang_logCreateManyUser_gang_log_secondPartyIdTouserInput = {
  id?: number
  action?: string | null
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  gangMemberId?: number | null
}

export type gang_logUpdateWithoutUser_gang_log_gangMemberIdTouserInput = {
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_logNestedInput
  user_gang_log_secondPartyIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_secondPartyIdTouserNestedInput
}

export type gang_logUncheckedUpdateWithoutUser_gang_log_gangMemberIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logUncheckedUpdateManyWithoutUser_gang_log_gangMemberIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logUpdateWithoutUser_gang_log_secondPartyIdTouserInput = {
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_logNestedInput
  user_gang_log_gangMemberIdTouser?: Prisma.userUpdateOneWithoutGang_log_gang_log_gangMemberIdTouserNestedInput
}

export type gang_logUncheckedUpdateWithoutUser_gang_log_secondPartyIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_logUncheckedUpdateManyWithoutUser_gang_log_secondPartyIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  action?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMemberId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type gang_logSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  action?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  gangMemberId?: boolean
  secondPartyId?: boolean
  gang?: boolean | Prisma.gang_log$gangArgs<ExtArgs>
  user_gang_log_gangMemberIdTouser?: boolean | Prisma.gang_log$user_gang_log_gangMemberIdTouserArgs<ExtArgs>
  user_gang_log_secondPartyIdTouser?: boolean | Prisma.gang_log$user_gang_log_secondPartyIdTouserArgs<ExtArgs>
}, ExtArgs["result"]["gang_log"]>



export type gang_logSelectScalar = {
  id?: boolean
  action?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  gangMemberId?: boolean
  secondPartyId?: boolean
}

export type gang_logOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "action" | "info" | "createdAt" | "updatedAt" | "gangId" | "gangMemberId" | "secondPartyId", ExtArgs["result"]["gang_log"]>
export type gang_logInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  gang?: boolean | Prisma.gang_log$gangArgs<ExtArgs>
  user_gang_log_gangMemberIdTouser?: boolean | Prisma.gang_log$user_gang_log_gangMemberIdTouserArgs<ExtArgs>
  user_gang_log_secondPartyIdTouser?: boolean | Prisma.gang_log$user_gang_log_secondPartyIdTouserArgs<ExtArgs>
}

export type $gang_logPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "gang_log"
  objects: {
    gang: Prisma.$gangPayload<ExtArgs> | null
    user_gang_log_gangMemberIdTouser: Prisma.$userPayload<ExtArgs> | null
    user_gang_log_secondPartyIdTouser: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    action: string | null
    info: string
    createdAt: Date
    updatedAt: Date
    gangId: number | null
    gangMemberId: number | null
    secondPartyId: number | null
  }, ExtArgs["result"]["gang_log"]>
  composites: {}
}

export type gang_logGetPayload<S extends boolean | null | undefined | gang_logDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$gang_logPayload, S>

export type gang_logCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<gang_logFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Gang_logCountAggregateInputType | true
  }

export interface gang_logDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['gang_log'], meta: { name: 'gang_log' } }
  /**
   * Find zero or one Gang_log that matches the filter.
   * @param {gang_logFindUniqueArgs} args - Arguments to find a Gang_log
   * @example
   * // Get one Gang_log
   * const gang_log = await prisma.gang_log.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends gang_logFindUniqueArgs>(args: Prisma.SelectSubset<T, gang_logFindUniqueArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Gang_log that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {gang_logFindUniqueOrThrowArgs} args - Arguments to find a Gang_log
   * @example
   * // Get one Gang_log
   * const gang_log = await prisma.gang_log.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends gang_logFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, gang_logFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_log that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logFindFirstArgs} args - Arguments to find a Gang_log
   * @example
   * // Get one Gang_log
   * const gang_log = await prisma.gang_log.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends gang_logFindFirstArgs>(args?: Prisma.SelectSubset<T, gang_logFindFirstArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_log that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logFindFirstOrThrowArgs} args - Arguments to find a Gang_log
   * @example
   * // Get one Gang_log
   * const gang_log = await prisma.gang_log.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends gang_logFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, gang_logFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Gang_logs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Gang_logs
   * const gang_logs = await prisma.gang_log.findMany()
   * 
   * // Get first 10 Gang_logs
   * const gang_logs = await prisma.gang_log.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const gang_logWithIdOnly = await prisma.gang_log.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends gang_logFindManyArgs>(args?: Prisma.SelectSubset<T, gang_logFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Gang_log.
   * @param {gang_logCreateArgs} args - Arguments to create a Gang_log.
   * @example
   * // Create one Gang_log
   * const Gang_log = await prisma.gang_log.create({
   *   data: {
   *     // ... data to create a Gang_log
   *   }
   * })
   * 
   */
  create<T extends gang_logCreateArgs>(args: Prisma.SelectSubset<T, gang_logCreateArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Gang_logs.
   * @param {gang_logCreateManyArgs} args - Arguments to create many Gang_logs.
   * @example
   * // Create many Gang_logs
   * const gang_log = await prisma.gang_log.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends gang_logCreateManyArgs>(args?: Prisma.SelectSubset<T, gang_logCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Gang_log.
   * @param {gang_logDeleteArgs} args - Arguments to delete one Gang_log.
   * @example
   * // Delete one Gang_log
   * const Gang_log = await prisma.gang_log.delete({
   *   where: {
   *     // ... filter to delete one Gang_log
   *   }
   * })
   * 
   */
  delete<T extends gang_logDeleteArgs>(args: Prisma.SelectSubset<T, gang_logDeleteArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Gang_log.
   * @param {gang_logUpdateArgs} args - Arguments to update one Gang_log.
   * @example
   * // Update one Gang_log
   * const gang_log = await prisma.gang_log.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends gang_logUpdateArgs>(args: Prisma.SelectSubset<T, gang_logUpdateArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Gang_logs.
   * @param {gang_logDeleteManyArgs} args - Arguments to filter Gang_logs to delete.
   * @example
   * // Delete a few Gang_logs
   * const { count } = await prisma.gang_log.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends gang_logDeleteManyArgs>(args?: Prisma.SelectSubset<T, gang_logDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Gang_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Gang_logs
   * const gang_log = await prisma.gang_log.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends gang_logUpdateManyArgs>(args: Prisma.SelectSubset<T, gang_logUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Gang_log.
   * @param {gang_logUpsertArgs} args - Arguments to update or create a Gang_log.
   * @example
   * // Update or create a Gang_log
   * const gang_log = await prisma.gang_log.upsert({
   *   create: {
   *     // ... data to create a Gang_log
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Gang_log we want to update
   *   }
   * })
   */
  upsert<T extends gang_logUpsertArgs>(args: Prisma.SelectSubset<T, gang_logUpsertArgs<ExtArgs>>): Prisma.Prisma__gang_logClient<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Gang_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logCountArgs} args - Arguments to filter Gang_logs to count.
   * @example
   * // Count the number of Gang_logs
   * const count = await prisma.gang_log.count({
   *   where: {
   *     // ... the filter for the Gang_logs we want to count
   *   }
   * })
  **/
  count<T extends gang_logCountArgs>(
    args?: Prisma.Subset<T, gang_logCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Gang_logCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Gang_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Gang_logAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Gang_logAggregateArgs>(args: Prisma.Subset<T, Gang_logAggregateArgs>): Prisma.PrismaPromise<GetGang_logAggregateType<T>>

  /**
   * Group by Gang_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_logGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends gang_logGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: gang_logGroupByArgs['orderBy'] }
      : { orderBy?: gang_logGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, gang_logGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGang_logGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the gang_log model
 */
readonly fields: gang_logFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for gang_log.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__gang_logClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  gang<T extends Prisma.gang_log$gangArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_log$gangArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_gang_log_gangMemberIdTouser<T extends Prisma.gang_log$user_gang_log_gangMemberIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_log$user_gang_log_gangMemberIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_gang_log_secondPartyIdTouser<T extends Prisma.gang_log$user_gang_log_secondPartyIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_log$user_gang_log_secondPartyIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the gang_log model
 */
export interface gang_logFieldRefs {
  readonly id: Prisma.FieldRef<"gang_log", 'Int'>
  readonly action: Prisma.FieldRef<"gang_log", 'String'>
  readonly info: Prisma.FieldRef<"gang_log", 'String'>
  readonly createdAt: Prisma.FieldRef<"gang_log", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"gang_log", 'DateTime'>
  readonly gangId: Prisma.FieldRef<"gang_log", 'Int'>
  readonly gangMemberId: Prisma.FieldRef<"gang_log", 'Int'>
  readonly secondPartyId: Prisma.FieldRef<"gang_log", 'Int'>
}
    

// Custom InputTypes
/**
 * gang_log findUnique
 */
export type gang_logFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter, which gang_log to fetch.
   */
  where: Prisma.gang_logWhereUniqueInput
}

/**
 * gang_log findUniqueOrThrow
 */
export type gang_logFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter, which gang_log to fetch.
   */
  where: Prisma.gang_logWhereUniqueInput
}

/**
 * gang_log findFirst
 */
export type gang_logFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter, which gang_log to fetch.
   */
  where?: Prisma.gang_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_logs to fetch.
   */
  orderBy?: Prisma.gang_logOrderByWithRelationInput | Prisma.gang_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_logs.
   */
  cursor?: Prisma.gang_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_logs.
   */
  distinct?: Prisma.Gang_logScalarFieldEnum | Prisma.Gang_logScalarFieldEnum[]
}

/**
 * gang_log findFirstOrThrow
 */
export type gang_logFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter, which gang_log to fetch.
   */
  where?: Prisma.gang_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_logs to fetch.
   */
  orderBy?: Prisma.gang_logOrderByWithRelationInput | Prisma.gang_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_logs.
   */
  cursor?: Prisma.gang_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_logs.
   */
  distinct?: Prisma.Gang_logScalarFieldEnum | Prisma.Gang_logScalarFieldEnum[]
}

/**
 * gang_log findMany
 */
export type gang_logFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter, which gang_logs to fetch.
   */
  where?: Prisma.gang_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_logs to fetch.
   */
  orderBy?: Prisma.gang_logOrderByWithRelationInput | Prisma.gang_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing gang_logs.
   */
  cursor?: Prisma.gang_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_logs.
   */
  skip?: number
  distinct?: Prisma.Gang_logScalarFieldEnum | Prisma.Gang_logScalarFieldEnum[]
}

/**
 * gang_log create
 */
export type gang_logCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * The data needed to create a gang_log.
   */
  data: Prisma.XOR<Prisma.gang_logCreateInput, Prisma.gang_logUncheckedCreateInput>
}

/**
 * gang_log createMany
 */
export type gang_logCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many gang_logs.
   */
  data: Prisma.gang_logCreateManyInput | Prisma.gang_logCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * gang_log update
 */
export type gang_logUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * The data needed to update a gang_log.
   */
  data: Prisma.XOR<Prisma.gang_logUpdateInput, Prisma.gang_logUncheckedUpdateInput>
  /**
   * Choose, which gang_log to update.
   */
  where: Prisma.gang_logWhereUniqueInput
}

/**
 * gang_log updateMany
 */
export type gang_logUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update gang_logs.
   */
  data: Prisma.XOR<Prisma.gang_logUpdateManyMutationInput, Prisma.gang_logUncheckedUpdateManyInput>
  /**
   * Filter which gang_logs to update
   */
  where?: Prisma.gang_logWhereInput
  /**
   * Limit how many gang_logs to update.
   */
  limit?: number
}

/**
 * gang_log upsert
 */
export type gang_logUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * The filter to search for the gang_log to update in case it exists.
   */
  where: Prisma.gang_logWhereUniqueInput
  /**
   * In case the gang_log found by the `where` argument doesn't exist, create a new gang_log with this data.
   */
  create: Prisma.XOR<Prisma.gang_logCreateInput, Prisma.gang_logUncheckedCreateInput>
  /**
   * In case the gang_log was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.gang_logUpdateInput, Prisma.gang_logUncheckedUpdateInput>
}

/**
 * gang_log delete
 */
export type gang_logDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  /**
   * Filter which gang_log to delete.
   */
  where: Prisma.gang_logWhereUniqueInput
}

/**
 * gang_log deleteMany
 */
export type gang_logDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_logs to delete
   */
  where?: Prisma.gang_logWhereInput
  /**
   * Limit how many gang_logs to delete.
   */
  limit?: number
}

/**
 * gang_log.gang
 */
export type gang_log$gangArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  where?: Prisma.gangWhereInput
}

/**
 * gang_log.user_gang_log_gangMemberIdTouser
 */
export type gang_log$user_gang_log_gangMemberIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang_log.user_gang_log_secondPartyIdTouser
 */
export type gang_log$user_gang_log_secondPartyIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang_log without action
 */
export type gang_logDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
}
