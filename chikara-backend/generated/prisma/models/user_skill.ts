
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_skill` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_skill
 * 
 */
export type user_skillModel = runtime.Types.Result.DefaultSelection<Prisma.$user_skillPayload>

export type AggregateUser_skill = {
  _count: User_skillCountAggregateOutputType | null
  _avg: User_skillAvgAggregateOutputType | null
  _sum: User_skillSumAggregateOutputType | null
  _min: User_skillMinAggregateOutputType | null
  _max: User_skillMaxAggregateOutputType | null
}

export type User_skillAvgAggregateOutputType = {
  id: number | null
  level: number | null
  experience: number | null
  talentPoints: number | null
  userId: number | null
}

export type User_skillSumAggregateOutputType = {
  id: number | null
  level: number | null
  experience: number | null
  talentPoints: number | null
  userId: number | null
}

export type User_skillMinAggregateOutputType = {
  id: number | null
  skillType: $Enums.SkillType | null
  level: number | null
  experience: number | null
  talentPoints: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type User_skillMaxAggregateOutputType = {
  id: number | null
  skillType: $Enums.SkillType | null
  level: number | null
  experience: number | null
  talentPoints: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type User_skillCountAggregateOutputType = {
  id: number
  skillType: number
  level: number
  experience: number
  talentPoints: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type User_skillAvgAggregateInputType = {
  id?: true
  level?: true
  experience?: true
  talentPoints?: true
  userId?: true
}

export type User_skillSumAggregateInputType = {
  id?: true
  level?: true
  experience?: true
  talentPoints?: true
  userId?: true
}

export type User_skillMinAggregateInputType = {
  id?: true
  skillType?: true
  level?: true
  experience?: true
  talentPoints?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type User_skillMaxAggregateInputType = {
  id?: true
  skillType?: true
  level?: true
  experience?: true
  talentPoints?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type User_skillCountAggregateInputType = {
  id?: true
  skillType?: true
  level?: true
  experience?: true
  talentPoints?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type User_skillAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_skill to aggregate.
   */
  where?: Prisma.user_skillWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_skills to fetch.
   */
  orderBy?: Prisma.user_skillOrderByWithRelationInput | Prisma.user_skillOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_skillWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_skills from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_skills.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_skills
  **/
  _count?: true | User_skillCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_skillAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_skillSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_skillMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_skillMaxAggregateInputType
}

export type GetUser_skillAggregateType<T extends User_skillAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_skill]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_skill[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_skill[P]>
}




export type user_skillGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_skillWhereInput
  orderBy?: Prisma.user_skillOrderByWithAggregationInput | Prisma.user_skillOrderByWithAggregationInput[]
  by: Prisma.User_skillScalarFieldEnum[] | Prisma.User_skillScalarFieldEnum
  having?: Prisma.user_skillScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_skillCountAggregateInputType | true
  _avg?: User_skillAvgAggregateInputType
  _sum?: User_skillSumAggregateInputType
  _min?: User_skillMinAggregateInputType
  _max?: User_skillMaxAggregateInputType
}

export type User_skillGroupByOutputType = {
  id: number
  skillType: $Enums.SkillType
  level: number
  experience: number
  talentPoints: number | null
  createdAt: Date
  updatedAt: Date
  userId: number
  _count: User_skillCountAggregateOutputType | null
  _avg: User_skillAvgAggregateOutputType | null
  _sum: User_skillSumAggregateOutputType | null
  _min: User_skillMinAggregateOutputType | null
  _max: User_skillMaxAggregateOutputType | null
}

type GetUser_skillGroupByPayload<T extends user_skillGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_skillGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_skillGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_skillGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_skillGroupByOutputType[P]>
      }
    >
  >



export type user_skillWhereInput = {
  AND?: Prisma.user_skillWhereInput | Prisma.user_skillWhereInput[]
  OR?: Prisma.user_skillWhereInput[]
  NOT?: Prisma.user_skillWhereInput | Prisma.user_skillWhereInput[]
  id?: Prisma.IntFilter<"user_skill"> | number
  skillType?: Prisma.EnumSkillTypeFilter<"user_skill"> | $Enums.SkillType
  level?: Prisma.IntFilter<"user_skill"> | number
  experience?: Prisma.IntFilter<"user_skill"> | number
  talentPoints?: Prisma.IntNullableFilter<"user_skill"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  userId?: Prisma.IntFilter<"user_skill"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type user_skillOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  skillType?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
}

export type user_skillWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  userId_skillType?: Prisma.user_skillUserIdSkillTypeCompoundUniqueInput
  AND?: Prisma.user_skillWhereInput | Prisma.user_skillWhereInput[]
  OR?: Prisma.user_skillWhereInput[]
  NOT?: Prisma.user_skillWhereInput | Prisma.user_skillWhereInput[]
  skillType?: Prisma.EnumSkillTypeFilter<"user_skill"> | $Enums.SkillType
  level?: Prisma.IntFilter<"user_skill"> | number
  experience?: Prisma.IntFilter<"user_skill"> | number
  talentPoints?: Prisma.IntNullableFilter<"user_skill"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  userId?: Prisma.IntFilter<"user_skill"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id" | "userId_skillType">

export type user_skillOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  skillType?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.user_skillCountOrderByAggregateInput
  _avg?: Prisma.user_skillAvgOrderByAggregateInput
  _max?: Prisma.user_skillMaxOrderByAggregateInput
  _min?: Prisma.user_skillMinOrderByAggregateInput
  _sum?: Prisma.user_skillSumOrderByAggregateInput
}

export type user_skillScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_skillScalarWhereWithAggregatesInput | Prisma.user_skillScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_skillScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_skillScalarWhereWithAggregatesInput | Prisma.user_skillScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_skill"> | number
  skillType?: Prisma.EnumSkillTypeWithAggregatesFilter<"user_skill"> | $Enums.SkillType
  level?: Prisma.IntWithAggregatesFilter<"user_skill"> | number
  experience?: Prisma.IntWithAggregatesFilter<"user_skill"> | number
  talentPoints?: Prisma.IntNullableWithAggregatesFilter<"user_skill"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_skill"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_skill"> | Date | string
  userId?: Prisma.IntWithAggregatesFilter<"user_skill"> | number
}

export type user_skillCreateInput = {
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_skillsInput
}

export type user_skillUncheckedCreateInput = {
  id?: number
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_skillUpdateInput = {
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_skillsNestedInput
}

export type user_skillUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_skillCreateManyInput = {
  id?: number
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_skillUpdateManyMutationInput = {
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_skillUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type User_skillListRelationFilter = {
  every?: Prisma.user_skillWhereInput
  some?: Prisma.user_skillWhereInput
  none?: Prisma.user_skillWhereInput
}

export type user_skillOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_skillUserIdSkillTypeCompoundUniqueInput = {
  userId: number
  skillType: $Enums.SkillType
}

export type user_skillCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  skillType?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_skillAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_skillMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  skillType?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_skillMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  skillType?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_skillSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  talentPoints?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_skillCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput> | Prisma.user_skillCreateWithoutUserInput[] | Prisma.user_skillUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_skillCreateOrConnectWithoutUserInput | Prisma.user_skillCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_skillCreateManyUserInputEnvelope
  connect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
}

export type user_skillUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput> | Prisma.user_skillCreateWithoutUserInput[] | Prisma.user_skillUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_skillCreateOrConnectWithoutUserInput | Prisma.user_skillCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_skillCreateManyUserInputEnvelope
  connect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
}

export type user_skillUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput> | Prisma.user_skillCreateWithoutUserInput[] | Prisma.user_skillUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_skillCreateOrConnectWithoutUserInput | Prisma.user_skillCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_skillUpsertWithWhereUniqueWithoutUserInput | Prisma.user_skillUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_skillCreateManyUserInputEnvelope
  set?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  disconnect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  delete?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  connect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  update?: Prisma.user_skillUpdateWithWhereUniqueWithoutUserInput | Prisma.user_skillUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_skillUpdateManyWithWhereWithoutUserInput | Prisma.user_skillUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_skillScalarWhereInput | Prisma.user_skillScalarWhereInput[]
}

export type user_skillUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput> | Prisma.user_skillCreateWithoutUserInput[] | Prisma.user_skillUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_skillCreateOrConnectWithoutUserInput | Prisma.user_skillCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_skillUpsertWithWhereUniqueWithoutUserInput | Prisma.user_skillUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_skillCreateManyUserInputEnvelope
  set?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  disconnect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  delete?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  connect?: Prisma.user_skillWhereUniqueInput | Prisma.user_skillWhereUniqueInput[]
  update?: Prisma.user_skillUpdateWithWhereUniqueWithoutUserInput | Prisma.user_skillUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_skillUpdateManyWithWhereWithoutUserInput | Prisma.user_skillUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_skillScalarWhereInput | Prisma.user_skillScalarWhereInput[]
}

export type EnumSkillTypeFieldUpdateOperationsInput = {
  set?: $Enums.SkillType
}

export type user_skillCreateWithoutUserInput = {
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_skillUncheckedCreateWithoutUserInput = {
  id?: number
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_skillCreateOrConnectWithoutUserInput = {
  where: Prisma.user_skillWhereUniqueInput
  create: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput>
}

export type user_skillCreateManyUserInputEnvelope = {
  data: Prisma.user_skillCreateManyUserInput | Prisma.user_skillCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_skillUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_skillWhereUniqueInput
  update: Prisma.XOR<Prisma.user_skillUpdateWithoutUserInput, Prisma.user_skillUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_skillCreateWithoutUserInput, Prisma.user_skillUncheckedCreateWithoutUserInput>
}

export type user_skillUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_skillWhereUniqueInput
  data: Prisma.XOR<Prisma.user_skillUpdateWithoutUserInput, Prisma.user_skillUncheckedUpdateWithoutUserInput>
}

export type user_skillUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_skillScalarWhereInput
  data: Prisma.XOR<Prisma.user_skillUpdateManyMutationInput, Prisma.user_skillUncheckedUpdateManyWithoutUserInput>
}

export type user_skillScalarWhereInput = {
  AND?: Prisma.user_skillScalarWhereInput | Prisma.user_skillScalarWhereInput[]
  OR?: Prisma.user_skillScalarWhereInput[]
  NOT?: Prisma.user_skillScalarWhereInput | Prisma.user_skillScalarWhereInput[]
  id?: Prisma.IntFilter<"user_skill"> | number
  skillType?: Prisma.EnumSkillTypeFilter<"user_skill"> | $Enums.SkillType
  level?: Prisma.IntFilter<"user_skill"> | number
  experience?: Prisma.IntFilter<"user_skill"> | number
  talentPoints?: Prisma.IntNullableFilter<"user_skill"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_skill"> | Date | string
  userId?: Prisma.IntFilter<"user_skill"> | number
}

export type user_skillCreateManyUserInput = {
  id?: number
  skillType: $Enums.SkillType
  level?: number
  experience?: number
  talentPoints?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_skillUpdateWithoutUserInput = {
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_skillUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_skillUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  skillType?: Prisma.EnumSkillTypeFieldUpdateOperationsInput | $Enums.SkillType
  level?: Prisma.IntFieldUpdateOperationsInput | number
  experience?: Prisma.IntFieldUpdateOperationsInput | number
  talentPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type user_skillSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  skillType?: boolean
  level?: boolean
  experience?: boolean
  talentPoints?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_skill"]>



export type user_skillSelectScalar = {
  id?: boolean
  skillType?: boolean
  level?: boolean
  experience?: boolean
  talentPoints?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type user_skillOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "skillType" | "level" | "experience" | "talentPoints" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["user_skill"]>
export type user_skillInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $user_skillPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_skill"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    skillType: $Enums.SkillType
    level: number
    experience: number
    talentPoints: number | null
    createdAt: Date
    updatedAt: Date
    userId: number
  }, ExtArgs["result"]["user_skill"]>
  composites: {}
}

export type user_skillGetPayload<S extends boolean | null | undefined | user_skillDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_skillPayload, S>

export type user_skillCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_skillFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_skillCountAggregateInputType | true
  }

export interface user_skillDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_skill'], meta: { name: 'user_skill' } }
  /**
   * Find zero or one User_skill that matches the filter.
   * @param {user_skillFindUniqueArgs} args - Arguments to find a User_skill
   * @example
   * // Get one User_skill
   * const user_skill = await prisma.user_skill.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_skillFindUniqueArgs>(args: Prisma.SelectSubset<T, user_skillFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_skill that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_skillFindUniqueOrThrowArgs} args - Arguments to find a User_skill
   * @example
   * // Get one User_skill
   * const user_skill = await prisma.user_skill.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_skillFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_skillFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_skill that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillFindFirstArgs} args - Arguments to find a User_skill
   * @example
   * // Get one User_skill
   * const user_skill = await prisma.user_skill.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_skillFindFirstArgs>(args?: Prisma.SelectSubset<T, user_skillFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_skill that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillFindFirstOrThrowArgs} args - Arguments to find a User_skill
   * @example
   * // Get one User_skill
   * const user_skill = await prisma.user_skill.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_skillFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_skillFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_skills that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_skills
   * const user_skills = await prisma.user_skill.findMany()
   * 
   * // Get first 10 User_skills
   * const user_skills = await prisma.user_skill.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_skillWithIdOnly = await prisma.user_skill.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_skillFindManyArgs>(args?: Prisma.SelectSubset<T, user_skillFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_skill.
   * @param {user_skillCreateArgs} args - Arguments to create a User_skill.
   * @example
   * // Create one User_skill
   * const User_skill = await prisma.user_skill.create({
   *   data: {
   *     // ... data to create a User_skill
   *   }
   * })
   * 
   */
  create<T extends user_skillCreateArgs>(args: Prisma.SelectSubset<T, user_skillCreateArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_skills.
   * @param {user_skillCreateManyArgs} args - Arguments to create many User_skills.
   * @example
   * // Create many User_skills
   * const user_skill = await prisma.user_skill.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_skillCreateManyArgs>(args?: Prisma.SelectSubset<T, user_skillCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_skill.
   * @param {user_skillDeleteArgs} args - Arguments to delete one User_skill.
   * @example
   * // Delete one User_skill
   * const User_skill = await prisma.user_skill.delete({
   *   where: {
   *     // ... filter to delete one User_skill
   *   }
   * })
   * 
   */
  delete<T extends user_skillDeleteArgs>(args: Prisma.SelectSubset<T, user_skillDeleteArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_skill.
   * @param {user_skillUpdateArgs} args - Arguments to update one User_skill.
   * @example
   * // Update one User_skill
   * const user_skill = await prisma.user_skill.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_skillUpdateArgs>(args: Prisma.SelectSubset<T, user_skillUpdateArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_skills.
   * @param {user_skillDeleteManyArgs} args - Arguments to filter User_skills to delete.
   * @example
   * // Delete a few User_skills
   * const { count } = await prisma.user_skill.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_skillDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_skillDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_skills.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_skills
   * const user_skill = await prisma.user_skill.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_skillUpdateManyArgs>(args: Prisma.SelectSubset<T, user_skillUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_skill.
   * @param {user_skillUpsertArgs} args - Arguments to update or create a User_skill.
   * @example
   * // Update or create a User_skill
   * const user_skill = await prisma.user_skill.upsert({
   *   create: {
   *     // ... data to create a User_skill
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_skill we want to update
   *   }
   * })
   */
  upsert<T extends user_skillUpsertArgs>(args: Prisma.SelectSubset<T, user_skillUpsertArgs<ExtArgs>>): Prisma.Prisma__user_skillClient<runtime.Types.Result.GetResult<Prisma.$user_skillPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_skills.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillCountArgs} args - Arguments to filter User_skills to count.
   * @example
   * // Count the number of User_skills
   * const count = await prisma.user_skill.count({
   *   where: {
   *     // ... the filter for the User_skills we want to count
   *   }
   * })
  **/
  count<T extends user_skillCountArgs>(
    args?: Prisma.Subset<T, user_skillCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_skillCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_skill.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_skillAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_skillAggregateArgs>(args: Prisma.Subset<T, User_skillAggregateArgs>): Prisma.PrismaPromise<GetUser_skillAggregateType<T>>

  /**
   * Group by User_skill.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_skillGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_skillGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_skillGroupByArgs['orderBy'] }
      : { orderBy?: user_skillGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_skillGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_skillGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_skill model
 */
readonly fields: user_skillFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_skill.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_skillClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_skill model
 */
export interface user_skillFieldRefs {
  readonly id: Prisma.FieldRef<"user_skill", 'Int'>
  readonly skillType: Prisma.FieldRef<"user_skill", 'SkillType'>
  readonly level: Prisma.FieldRef<"user_skill", 'Int'>
  readonly experience: Prisma.FieldRef<"user_skill", 'Int'>
  readonly talentPoints: Prisma.FieldRef<"user_skill", 'Int'>
  readonly createdAt: Prisma.FieldRef<"user_skill", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_skill", 'DateTime'>
  readonly userId: Prisma.FieldRef<"user_skill", 'Int'>
}
    

// Custom InputTypes
/**
 * user_skill findUnique
 */
export type user_skillFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter, which user_skill to fetch.
   */
  where: Prisma.user_skillWhereUniqueInput
}

/**
 * user_skill findUniqueOrThrow
 */
export type user_skillFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter, which user_skill to fetch.
   */
  where: Prisma.user_skillWhereUniqueInput
}

/**
 * user_skill findFirst
 */
export type user_skillFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter, which user_skill to fetch.
   */
  where?: Prisma.user_skillWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_skills to fetch.
   */
  orderBy?: Prisma.user_skillOrderByWithRelationInput | Prisma.user_skillOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_skills.
   */
  cursor?: Prisma.user_skillWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_skills from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_skills.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_skills.
   */
  distinct?: Prisma.User_skillScalarFieldEnum | Prisma.User_skillScalarFieldEnum[]
}

/**
 * user_skill findFirstOrThrow
 */
export type user_skillFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter, which user_skill to fetch.
   */
  where?: Prisma.user_skillWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_skills to fetch.
   */
  orderBy?: Prisma.user_skillOrderByWithRelationInput | Prisma.user_skillOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_skills.
   */
  cursor?: Prisma.user_skillWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_skills from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_skills.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_skills.
   */
  distinct?: Prisma.User_skillScalarFieldEnum | Prisma.User_skillScalarFieldEnum[]
}

/**
 * user_skill findMany
 */
export type user_skillFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter, which user_skills to fetch.
   */
  where?: Prisma.user_skillWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_skills to fetch.
   */
  orderBy?: Prisma.user_skillOrderByWithRelationInput | Prisma.user_skillOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_skills.
   */
  cursor?: Prisma.user_skillWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_skills from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_skills.
   */
  skip?: number
  distinct?: Prisma.User_skillScalarFieldEnum | Prisma.User_skillScalarFieldEnum[]
}

/**
 * user_skill create
 */
export type user_skillCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * The data needed to create a user_skill.
   */
  data: Prisma.XOR<Prisma.user_skillCreateInput, Prisma.user_skillUncheckedCreateInput>
}

/**
 * user_skill createMany
 */
export type user_skillCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_skills.
   */
  data: Prisma.user_skillCreateManyInput | Prisma.user_skillCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_skill update
 */
export type user_skillUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * The data needed to update a user_skill.
   */
  data: Prisma.XOR<Prisma.user_skillUpdateInput, Prisma.user_skillUncheckedUpdateInput>
  /**
   * Choose, which user_skill to update.
   */
  where: Prisma.user_skillWhereUniqueInput
}

/**
 * user_skill updateMany
 */
export type user_skillUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_skills.
   */
  data: Prisma.XOR<Prisma.user_skillUpdateManyMutationInput, Prisma.user_skillUncheckedUpdateManyInput>
  /**
   * Filter which user_skills to update
   */
  where?: Prisma.user_skillWhereInput
  /**
   * Limit how many user_skills to update.
   */
  limit?: number
}

/**
 * user_skill upsert
 */
export type user_skillUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * The filter to search for the user_skill to update in case it exists.
   */
  where: Prisma.user_skillWhereUniqueInput
  /**
   * In case the user_skill found by the `where` argument doesn't exist, create a new user_skill with this data.
   */
  create: Prisma.XOR<Prisma.user_skillCreateInput, Prisma.user_skillUncheckedCreateInput>
  /**
   * In case the user_skill was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_skillUpdateInput, Prisma.user_skillUncheckedUpdateInput>
}

/**
 * user_skill delete
 */
export type user_skillDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
  /**
   * Filter which user_skill to delete.
   */
  where: Prisma.user_skillWhereUniqueInput
}

/**
 * user_skill deleteMany
 */
export type user_skillDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_skills to delete
   */
  where?: Prisma.user_skillWhereInput
  /**
   * Limit how many user_skills to delete.
   */
  limit?: number
}

/**
 * user_skill without action
 */
export type user_skillDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_skill
   */
  select?: Prisma.user_skillSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_skill
   */
  omit?: Prisma.user_skillOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_skillInclude<ExtArgs> | null
}
