
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `friend_request` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model friend_request
 * 
 */
export type friend_requestModel = runtime.Types.Result.DefaultSelection<Prisma.$friend_requestPayload>

export type AggregateFriend_request = {
  _count: Friend_requestCountAggregateOutputType | null
  _avg: Friend_requestAvgAggregateOutputType | null
  _sum: Friend_requestSumAggregateOutputType | null
  _min: Friend_requestMinAggregateOutputType | null
  _max: Friend_requestMaxAggregateOutputType | null
}

export type Friend_requestAvgAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Friend_requestSumAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Friend_requestMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Friend_requestMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Friend_requestCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  senderId: number
  receiverId: number
  _all: number
}


export type Friend_requestAvgAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Friend_requestSumAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Friend_requestMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Friend_requestMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Friend_requestCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
  _all?: true
}

export type Friend_requestAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which friend_request to aggregate.
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friend_requests to fetch.
   */
  orderBy?: Prisma.friend_requestOrderByWithRelationInput | Prisma.friend_requestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.friend_requestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friend_requests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friend_requests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned friend_requests
  **/
  _count?: true | Friend_requestCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Friend_requestAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Friend_requestSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Friend_requestMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Friend_requestMaxAggregateInputType
}

export type GetFriend_requestAggregateType<T extends Friend_requestAggregateArgs> = {
      [P in keyof T & keyof AggregateFriend_request]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateFriend_request[P]>
    : Prisma.GetScalarType<T[P], AggregateFriend_request[P]>
}




export type friend_requestGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.friend_requestWhereInput
  orderBy?: Prisma.friend_requestOrderByWithAggregationInput | Prisma.friend_requestOrderByWithAggregationInput[]
  by: Prisma.Friend_requestScalarFieldEnum[] | Prisma.Friend_requestScalarFieldEnum
  having?: Prisma.friend_requestScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Friend_requestCountAggregateInputType | true
  _avg?: Friend_requestAvgAggregateInputType
  _sum?: Friend_requestSumAggregateInputType
  _min?: Friend_requestMinAggregateInputType
  _max?: Friend_requestMaxAggregateInputType
}

export type Friend_requestGroupByOutputType = {
  id: number
  createdAt: Date
  updatedAt: Date
  senderId: number
  receiverId: number
  _count: Friend_requestCountAggregateOutputType | null
  _avg: Friend_requestAvgAggregateOutputType | null
  _sum: Friend_requestSumAggregateOutputType | null
  _min: Friend_requestMinAggregateOutputType | null
  _max: Friend_requestMaxAggregateOutputType | null
}

type GetFriend_requestGroupByPayload<T extends friend_requestGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Friend_requestGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Friend_requestGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Friend_requestGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Friend_requestGroupByOutputType[P]>
      }
    >
  >



export type friend_requestWhereInput = {
  AND?: Prisma.friend_requestWhereInput | Prisma.friend_requestWhereInput[]
  OR?: Prisma.friend_requestWhereInput[]
  NOT?: Prisma.friend_requestWhereInput | Prisma.friend_requestWhereInput[]
  id?: Prisma.IntFilter<"friend_request"> | number
  createdAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  senderId?: Prisma.IntFilter<"friend_request"> | number
  receiverId?: Prisma.IntFilter<"friend_request"> | number
  sender?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  receiver?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type friend_requestOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  sender?: Prisma.userOrderByWithRelationInput
  receiver?: Prisma.userOrderByWithRelationInput
}

export type friend_requestWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  senderId_receiverId?: Prisma.friend_requestSenderIdReceiverIdCompoundUniqueInput
  AND?: Prisma.friend_requestWhereInput | Prisma.friend_requestWhereInput[]
  OR?: Prisma.friend_requestWhereInput[]
  NOT?: Prisma.friend_requestWhereInput | Prisma.friend_requestWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  senderId?: Prisma.IntFilter<"friend_request"> | number
  receiverId?: Prisma.IntFilter<"friend_request"> | number
  sender?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  receiver?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id" | "senderId_receiverId">

export type friend_requestOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  _count?: Prisma.friend_requestCountOrderByAggregateInput
  _avg?: Prisma.friend_requestAvgOrderByAggregateInput
  _max?: Prisma.friend_requestMaxOrderByAggregateInput
  _min?: Prisma.friend_requestMinOrderByAggregateInput
  _sum?: Prisma.friend_requestSumOrderByAggregateInput
}

export type friend_requestScalarWhereWithAggregatesInput = {
  AND?: Prisma.friend_requestScalarWhereWithAggregatesInput | Prisma.friend_requestScalarWhereWithAggregatesInput[]
  OR?: Prisma.friend_requestScalarWhereWithAggregatesInput[]
  NOT?: Prisma.friend_requestScalarWhereWithAggregatesInput | Prisma.friend_requestScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"friend_request"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"friend_request"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"friend_request"> | Date | string
  senderId?: Prisma.IntWithAggregatesFilter<"friend_request"> | number
  receiverId?: Prisma.IntWithAggregatesFilter<"friend_request"> | number
}

export type friend_requestCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  sender: Prisma.userCreateNestedOneWithoutSent_friend_requestsInput
  receiver: Prisma.userCreateNestedOneWithoutReceived_friend_requestsInput
}

export type friend_requestUncheckedCreateInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId: number
  receiverId: number
}

export type friend_requestUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sender?: Prisma.userUpdateOneRequiredWithoutSent_friend_requestsNestedInput
  receiver?: Prisma.userUpdateOneRequiredWithoutReceived_friend_requestsNestedInput
}

export type friend_requestUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.IntFieldUpdateOperationsInput | number
  receiverId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type friend_requestCreateManyInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId: number
  receiverId: number
}

export type friend_requestUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type friend_requestUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.IntFieldUpdateOperationsInput | number
  receiverId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type Friend_requestListRelationFilter = {
  every?: Prisma.friend_requestWhereInput
  some?: Prisma.friend_requestWhereInput
  none?: Prisma.friend_requestWhereInput
}

export type friend_requestOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type friend_requestSenderIdReceiverIdCompoundUniqueInput = {
  senderId: number
  receiverId: number
}

export type friend_requestCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type friend_requestAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type friend_requestMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type friend_requestMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type friend_requestSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type friend_requestCreateNestedManyWithoutSenderInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput> | Prisma.friend_requestCreateWithoutSenderInput[] | Prisma.friend_requestUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutSenderInput | Prisma.friend_requestCreateOrConnectWithoutSenderInput[]
  createMany?: Prisma.friend_requestCreateManySenderInputEnvelope
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
}

export type friend_requestCreateNestedManyWithoutReceiverInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput> | Prisma.friend_requestCreateWithoutReceiverInput[] | Prisma.friend_requestUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutReceiverInput | Prisma.friend_requestCreateOrConnectWithoutReceiverInput[]
  createMany?: Prisma.friend_requestCreateManyReceiverInputEnvelope
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
}

export type friend_requestUncheckedCreateNestedManyWithoutSenderInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput> | Prisma.friend_requestCreateWithoutSenderInput[] | Prisma.friend_requestUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutSenderInput | Prisma.friend_requestCreateOrConnectWithoutSenderInput[]
  createMany?: Prisma.friend_requestCreateManySenderInputEnvelope
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
}

export type friend_requestUncheckedCreateNestedManyWithoutReceiverInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput> | Prisma.friend_requestCreateWithoutReceiverInput[] | Prisma.friend_requestUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutReceiverInput | Prisma.friend_requestCreateOrConnectWithoutReceiverInput[]
  createMany?: Prisma.friend_requestCreateManyReceiverInputEnvelope
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
}

export type friend_requestUpdateManyWithoutSenderNestedInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput> | Prisma.friend_requestCreateWithoutSenderInput[] | Prisma.friend_requestUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutSenderInput | Prisma.friend_requestCreateOrConnectWithoutSenderInput[]
  upsert?: Prisma.friend_requestUpsertWithWhereUniqueWithoutSenderInput | Prisma.friend_requestUpsertWithWhereUniqueWithoutSenderInput[]
  createMany?: Prisma.friend_requestCreateManySenderInputEnvelope
  set?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  disconnect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  delete?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  update?: Prisma.friend_requestUpdateWithWhereUniqueWithoutSenderInput | Prisma.friend_requestUpdateWithWhereUniqueWithoutSenderInput[]
  updateMany?: Prisma.friend_requestUpdateManyWithWhereWithoutSenderInput | Prisma.friend_requestUpdateManyWithWhereWithoutSenderInput[]
  deleteMany?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
}

export type friend_requestUpdateManyWithoutReceiverNestedInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput> | Prisma.friend_requestCreateWithoutReceiverInput[] | Prisma.friend_requestUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutReceiverInput | Prisma.friend_requestCreateOrConnectWithoutReceiverInput[]
  upsert?: Prisma.friend_requestUpsertWithWhereUniqueWithoutReceiverInput | Prisma.friend_requestUpsertWithWhereUniqueWithoutReceiverInput[]
  createMany?: Prisma.friend_requestCreateManyReceiverInputEnvelope
  set?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  disconnect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  delete?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  update?: Prisma.friend_requestUpdateWithWhereUniqueWithoutReceiverInput | Prisma.friend_requestUpdateWithWhereUniqueWithoutReceiverInput[]
  updateMany?: Prisma.friend_requestUpdateManyWithWhereWithoutReceiverInput | Prisma.friend_requestUpdateManyWithWhereWithoutReceiverInput[]
  deleteMany?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
}

export type friend_requestUncheckedUpdateManyWithoutSenderNestedInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput> | Prisma.friend_requestCreateWithoutSenderInput[] | Prisma.friend_requestUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutSenderInput | Prisma.friend_requestCreateOrConnectWithoutSenderInput[]
  upsert?: Prisma.friend_requestUpsertWithWhereUniqueWithoutSenderInput | Prisma.friend_requestUpsertWithWhereUniqueWithoutSenderInput[]
  createMany?: Prisma.friend_requestCreateManySenderInputEnvelope
  set?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  disconnect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  delete?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  update?: Prisma.friend_requestUpdateWithWhereUniqueWithoutSenderInput | Prisma.friend_requestUpdateWithWhereUniqueWithoutSenderInput[]
  updateMany?: Prisma.friend_requestUpdateManyWithWhereWithoutSenderInput | Prisma.friend_requestUpdateManyWithWhereWithoutSenderInput[]
  deleteMany?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
}

export type friend_requestUncheckedUpdateManyWithoutReceiverNestedInput = {
  create?: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput> | Prisma.friend_requestCreateWithoutReceiverInput[] | Prisma.friend_requestUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.friend_requestCreateOrConnectWithoutReceiverInput | Prisma.friend_requestCreateOrConnectWithoutReceiverInput[]
  upsert?: Prisma.friend_requestUpsertWithWhereUniqueWithoutReceiverInput | Prisma.friend_requestUpsertWithWhereUniqueWithoutReceiverInput[]
  createMany?: Prisma.friend_requestCreateManyReceiverInputEnvelope
  set?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  disconnect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  delete?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  connect?: Prisma.friend_requestWhereUniqueInput | Prisma.friend_requestWhereUniqueInput[]
  update?: Prisma.friend_requestUpdateWithWhereUniqueWithoutReceiverInput | Prisma.friend_requestUpdateWithWhereUniqueWithoutReceiverInput[]
  updateMany?: Prisma.friend_requestUpdateManyWithWhereWithoutReceiverInput | Prisma.friend_requestUpdateManyWithWhereWithoutReceiverInput[]
  deleteMany?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
}

export type friend_requestCreateWithoutSenderInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  receiver: Prisma.userCreateNestedOneWithoutReceived_friend_requestsInput
}

export type friend_requestUncheckedCreateWithoutSenderInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId: number
}

export type friend_requestCreateOrConnectWithoutSenderInput = {
  where: Prisma.friend_requestWhereUniqueInput
  create: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput>
}

export type friend_requestCreateManySenderInputEnvelope = {
  data: Prisma.friend_requestCreateManySenderInput | Prisma.friend_requestCreateManySenderInput[]
  skipDuplicates?: boolean
}

export type friend_requestCreateWithoutReceiverInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  sender: Prisma.userCreateNestedOneWithoutSent_friend_requestsInput
}

export type friend_requestUncheckedCreateWithoutReceiverInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId: number
}

export type friend_requestCreateOrConnectWithoutReceiverInput = {
  where: Prisma.friend_requestWhereUniqueInput
  create: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput>
}

export type friend_requestCreateManyReceiverInputEnvelope = {
  data: Prisma.friend_requestCreateManyReceiverInput | Prisma.friend_requestCreateManyReceiverInput[]
  skipDuplicates?: boolean
}

export type friend_requestUpsertWithWhereUniqueWithoutSenderInput = {
  where: Prisma.friend_requestWhereUniqueInput
  update: Prisma.XOR<Prisma.friend_requestUpdateWithoutSenderInput, Prisma.friend_requestUncheckedUpdateWithoutSenderInput>
  create: Prisma.XOR<Prisma.friend_requestCreateWithoutSenderInput, Prisma.friend_requestUncheckedCreateWithoutSenderInput>
}

export type friend_requestUpdateWithWhereUniqueWithoutSenderInput = {
  where: Prisma.friend_requestWhereUniqueInput
  data: Prisma.XOR<Prisma.friend_requestUpdateWithoutSenderInput, Prisma.friend_requestUncheckedUpdateWithoutSenderInput>
}

export type friend_requestUpdateManyWithWhereWithoutSenderInput = {
  where: Prisma.friend_requestScalarWhereInput
  data: Prisma.XOR<Prisma.friend_requestUpdateManyMutationInput, Prisma.friend_requestUncheckedUpdateManyWithoutSenderInput>
}

export type friend_requestScalarWhereInput = {
  AND?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
  OR?: Prisma.friend_requestScalarWhereInput[]
  NOT?: Prisma.friend_requestScalarWhereInput | Prisma.friend_requestScalarWhereInput[]
  id?: Prisma.IntFilter<"friend_request"> | number
  createdAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend_request"> | Date | string
  senderId?: Prisma.IntFilter<"friend_request"> | number
  receiverId?: Prisma.IntFilter<"friend_request"> | number
}

export type friend_requestUpsertWithWhereUniqueWithoutReceiverInput = {
  where: Prisma.friend_requestWhereUniqueInput
  update: Prisma.XOR<Prisma.friend_requestUpdateWithoutReceiverInput, Prisma.friend_requestUncheckedUpdateWithoutReceiverInput>
  create: Prisma.XOR<Prisma.friend_requestCreateWithoutReceiverInput, Prisma.friend_requestUncheckedCreateWithoutReceiverInput>
}

export type friend_requestUpdateWithWhereUniqueWithoutReceiverInput = {
  where: Prisma.friend_requestWhereUniqueInput
  data: Prisma.XOR<Prisma.friend_requestUpdateWithoutReceiverInput, Prisma.friend_requestUncheckedUpdateWithoutReceiverInput>
}

export type friend_requestUpdateManyWithWhereWithoutReceiverInput = {
  where: Prisma.friend_requestScalarWhereInput
  data: Prisma.XOR<Prisma.friend_requestUpdateManyMutationInput, Prisma.friend_requestUncheckedUpdateManyWithoutReceiverInput>
}

export type friend_requestCreateManySenderInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId: number
}

export type friend_requestCreateManyReceiverInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId: number
}

export type friend_requestUpdateWithoutSenderInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiver?: Prisma.userUpdateOneRequiredWithoutReceived_friend_requestsNestedInput
}

export type friend_requestUncheckedUpdateWithoutSenderInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type friend_requestUncheckedUpdateManyWithoutSenderInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type friend_requestUpdateWithoutReceiverInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sender?: Prisma.userUpdateOneRequiredWithoutSent_friend_requestsNestedInput
}

export type friend_requestUncheckedUpdateWithoutReceiverInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type friend_requestUncheckedUpdateManyWithoutReceiverInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type friend_requestSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
  sender?: boolean | Prisma.userDefaultArgs<ExtArgs>
  receiver?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["friend_request"]>



export type friend_requestSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
}

export type friend_requestOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "createdAt" | "updatedAt" | "senderId" | "receiverId", ExtArgs["result"]["friend_request"]>
export type friend_requestInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sender?: boolean | Prisma.userDefaultArgs<ExtArgs>
  receiver?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $friend_requestPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "friend_request"
  objects: {
    sender: Prisma.$userPayload<ExtArgs>
    receiver: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    createdAt: Date
    updatedAt: Date
    senderId: number
    receiverId: number
  }, ExtArgs["result"]["friend_request"]>
  composites: {}
}

export type friend_requestGetPayload<S extends boolean | null | undefined | friend_requestDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$friend_requestPayload, S>

export type friend_requestCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<friend_requestFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Friend_requestCountAggregateInputType | true
  }

export interface friend_requestDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['friend_request'], meta: { name: 'friend_request' } }
  /**
   * Find zero or one Friend_request that matches the filter.
   * @param {friend_requestFindUniqueArgs} args - Arguments to find a Friend_request
   * @example
   * // Get one Friend_request
   * const friend_request = await prisma.friend_request.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends friend_requestFindUniqueArgs>(args: Prisma.SelectSubset<T, friend_requestFindUniqueArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Friend_request that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {friend_requestFindUniqueOrThrowArgs} args - Arguments to find a Friend_request
   * @example
   * // Get one Friend_request
   * const friend_request = await prisma.friend_request.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends friend_requestFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, friend_requestFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Friend_request that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestFindFirstArgs} args - Arguments to find a Friend_request
   * @example
   * // Get one Friend_request
   * const friend_request = await prisma.friend_request.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends friend_requestFindFirstArgs>(args?: Prisma.SelectSubset<T, friend_requestFindFirstArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Friend_request that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestFindFirstOrThrowArgs} args - Arguments to find a Friend_request
   * @example
   * // Get one Friend_request
   * const friend_request = await prisma.friend_request.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends friend_requestFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, friend_requestFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Friend_requests that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Friend_requests
   * const friend_requests = await prisma.friend_request.findMany()
   * 
   * // Get first 10 Friend_requests
   * const friend_requests = await prisma.friend_request.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const friend_requestWithIdOnly = await prisma.friend_request.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends friend_requestFindManyArgs>(args?: Prisma.SelectSubset<T, friend_requestFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Friend_request.
   * @param {friend_requestCreateArgs} args - Arguments to create a Friend_request.
   * @example
   * // Create one Friend_request
   * const Friend_request = await prisma.friend_request.create({
   *   data: {
   *     // ... data to create a Friend_request
   *   }
   * })
   * 
   */
  create<T extends friend_requestCreateArgs>(args: Prisma.SelectSubset<T, friend_requestCreateArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Friend_requests.
   * @param {friend_requestCreateManyArgs} args - Arguments to create many Friend_requests.
   * @example
   * // Create many Friend_requests
   * const friend_request = await prisma.friend_request.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends friend_requestCreateManyArgs>(args?: Prisma.SelectSubset<T, friend_requestCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Friend_request.
   * @param {friend_requestDeleteArgs} args - Arguments to delete one Friend_request.
   * @example
   * // Delete one Friend_request
   * const Friend_request = await prisma.friend_request.delete({
   *   where: {
   *     // ... filter to delete one Friend_request
   *   }
   * })
   * 
   */
  delete<T extends friend_requestDeleteArgs>(args: Prisma.SelectSubset<T, friend_requestDeleteArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Friend_request.
   * @param {friend_requestUpdateArgs} args - Arguments to update one Friend_request.
   * @example
   * // Update one Friend_request
   * const friend_request = await prisma.friend_request.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends friend_requestUpdateArgs>(args: Prisma.SelectSubset<T, friend_requestUpdateArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Friend_requests.
   * @param {friend_requestDeleteManyArgs} args - Arguments to filter Friend_requests to delete.
   * @example
   * // Delete a few Friend_requests
   * const { count } = await prisma.friend_request.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends friend_requestDeleteManyArgs>(args?: Prisma.SelectSubset<T, friend_requestDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Friend_requests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Friend_requests
   * const friend_request = await prisma.friend_request.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends friend_requestUpdateManyArgs>(args: Prisma.SelectSubset<T, friend_requestUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Friend_request.
   * @param {friend_requestUpsertArgs} args - Arguments to update or create a Friend_request.
   * @example
   * // Update or create a Friend_request
   * const friend_request = await prisma.friend_request.upsert({
   *   create: {
   *     // ... data to create a Friend_request
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Friend_request we want to update
   *   }
   * })
   */
  upsert<T extends friend_requestUpsertArgs>(args: Prisma.SelectSubset<T, friend_requestUpsertArgs<ExtArgs>>): Prisma.Prisma__friend_requestClient<runtime.Types.Result.GetResult<Prisma.$friend_requestPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Friend_requests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestCountArgs} args - Arguments to filter Friend_requests to count.
   * @example
   * // Count the number of Friend_requests
   * const count = await prisma.friend_request.count({
   *   where: {
   *     // ... the filter for the Friend_requests we want to count
   *   }
   * })
  **/
  count<T extends friend_requestCountArgs>(
    args?: Prisma.Subset<T, friend_requestCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Friend_requestCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Friend_request.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Friend_requestAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Friend_requestAggregateArgs>(args: Prisma.Subset<T, Friend_requestAggregateArgs>): Prisma.PrismaPromise<GetFriend_requestAggregateType<T>>

  /**
   * Group by Friend_request.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friend_requestGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends friend_requestGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: friend_requestGroupByArgs['orderBy'] }
      : { orderBy?: friend_requestGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, friend_requestGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetFriend_requestGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the friend_request model
 */
readonly fields: friend_requestFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for friend_request.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__friend_requestClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sender<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  receiver<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the friend_request model
 */
export interface friend_requestFieldRefs {
  readonly id: Prisma.FieldRef<"friend_request", 'Int'>
  readonly createdAt: Prisma.FieldRef<"friend_request", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"friend_request", 'DateTime'>
  readonly senderId: Prisma.FieldRef<"friend_request", 'Int'>
  readonly receiverId: Prisma.FieldRef<"friend_request", 'Int'>
}
    

// Custom InputTypes
/**
 * friend_request findUnique
 */
export type friend_requestFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter, which friend_request to fetch.
   */
  where: Prisma.friend_requestWhereUniqueInput
}

/**
 * friend_request findUniqueOrThrow
 */
export type friend_requestFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter, which friend_request to fetch.
   */
  where: Prisma.friend_requestWhereUniqueInput
}

/**
 * friend_request findFirst
 */
export type friend_requestFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter, which friend_request to fetch.
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friend_requests to fetch.
   */
  orderBy?: Prisma.friend_requestOrderByWithRelationInput | Prisma.friend_requestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for friend_requests.
   */
  cursor?: Prisma.friend_requestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friend_requests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friend_requests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of friend_requests.
   */
  distinct?: Prisma.Friend_requestScalarFieldEnum | Prisma.Friend_requestScalarFieldEnum[]
}

/**
 * friend_request findFirstOrThrow
 */
export type friend_requestFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter, which friend_request to fetch.
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friend_requests to fetch.
   */
  orderBy?: Prisma.friend_requestOrderByWithRelationInput | Prisma.friend_requestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for friend_requests.
   */
  cursor?: Prisma.friend_requestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friend_requests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friend_requests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of friend_requests.
   */
  distinct?: Prisma.Friend_requestScalarFieldEnum | Prisma.Friend_requestScalarFieldEnum[]
}

/**
 * friend_request findMany
 */
export type friend_requestFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter, which friend_requests to fetch.
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friend_requests to fetch.
   */
  orderBy?: Prisma.friend_requestOrderByWithRelationInput | Prisma.friend_requestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing friend_requests.
   */
  cursor?: Prisma.friend_requestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friend_requests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friend_requests.
   */
  skip?: number
  distinct?: Prisma.Friend_requestScalarFieldEnum | Prisma.Friend_requestScalarFieldEnum[]
}

/**
 * friend_request create
 */
export type friend_requestCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * The data needed to create a friend_request.
   */
  data: Prisma.XOR<Prisma.friend_requestCreateInput, Prisma.friend_requestUncheckedCreateInput>
}

/**
 * friend_request createMany
 */
export type friend_requestCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many friend_requests.
   */
  data: Prisma.friend_requestCreateManyInput | Prisma.friend_requestCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * friend_request update
 */
export type friend_requestUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * The data needed to update a friend_request.
   */
  data: Prisma.XOR<Prisma.friend_requestUpdateInput, Prisma.friend_requestUncheckedUpdateInput>
  /**
   * Choose, which friend_request to update.
   */
  where: Prisma.friend_requestWhereUniqueInput
}

/**
 * friend_request updateMany
 */
export type friend_requestUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update friend_requests.
   */
  data: Prisma.XOR<Prisma.friend_requestUpdateManyMutationInput, Prisma.friend_requestUncheckedUpdateManyInput>
  /**
   * Filter which friend_requests to update
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * Limit how many friend_requests to update.
   */
  limit?: number
}

/**
 * friend_request upsert
 */
export type friend_requestUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * The filter to search for the friend_request to update in case it exists.
   */
  where: Prisma.friend_requestWhereUniqueInput
  /**
   * In case the friend_request found by the `where` argument doesn't exist, create a new friend_request with this data.
   */
  create: Prisma.XOR<Prisma.friend_requestCreateInput, Prisma.friend_requestUncheckedCreateInput>
  /**
   * In case the friend_request was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.friend_requestUpdateInput, Prisma.friend_requestUncheckedUpdateInput>
}

/**
 * friend_request delete
 */
export type friend_requestDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
  /**
   * Filter which friend_request to delete.
   */
  where: Prisma.friend_requestWhereUniqueInput
}

/**
 * friend_request deleteMany
 */
export type friend_requestDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which friend_requests to delete
   */
  where?: Prisma.friend_requestWhereInput
  /**
   * Limit how many friend_requests to delete.
   */
  limit?: number
}

/**
 * friend_request without action
 */
export type friend_requestDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend_request
   */
  select?: Prisma.friend_requestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend_request
   */
  omit?: Prisma.friend_requestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friend_requestInclude<ExtArgs> | null
}
