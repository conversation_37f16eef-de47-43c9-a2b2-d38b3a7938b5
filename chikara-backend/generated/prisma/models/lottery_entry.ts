
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `lottery_entry` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model lottery_entry
 * 
 */
export type lottery_entryModel = runtime.Types.Result.DefaultSelection<Prisma.$lottery_entryPayload>

export type AggregateLottery_entry = {
  _count: Lottery_entryCountAggregateOutputType | null
  _avg: Lottery_entryAvgAggregateOutputType | null
  _sum: Lottery_entrySumAggregateOutputType | null
  _min: Lottery_entryMinAggregateOutputType | null
  _max: Lottery_entryMaxAggregateOutputType | null
}

export type Lottery_entryAvgAggregateOutputType = {
  id: number | null
  lotteryId: number | null
  userId: number | null
}

export type Lottery_entrySumAggregateOutputType = {
  id: number | null
  lotteryId: number | null
  userId: number | null
}

export type Lottery_entryMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  lotteryId: number | null
  userId: number | null
}

export type Lottery_entryMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  lotteryId: number | null
  userId: number | null
}

export type Lottery_entryCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  lotteryId: number
  userId: number
  _all: number
}


export type Lottery_entryAvgAggregateInputType = {
  id?: true
  lotteryId?: true
  userId?: true
}

export type Lottery_entrySumAggregateInputType = {
  id?: true
  lotteryId?: true
  userId?: true
}

export type Lottery_entryMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  lotteryId?: true
  userId?: true
}

export type Lottery_entryMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  lotteryId?: true
  userId?: true
}

export type Lottery_entryCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  lotteryId?: true
  userId?: true
  _all?: true
}

export type Lottery_entryAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which lottery_entry to aggregate.
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lottery_entries to fetch.
   */
  orderBy?: Prisma.lottery_entryOrderByWithRelationInput | Prisma.lottery_entryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.lottery_entryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lottery_entries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lottery_entries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned lottery_entries
  **/
  _count?: true | Lottery_entryCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Lottery_entryAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Lottery_entrySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Lottery_entryMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Lottery_entryMaxAggregateInputType
}

export type GetLottery_entryAggregateType<T extends Lottery_entryAggregateArgs> = {
      [P in keyof T & keyof AggregateLottery_entry]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLottery_entry[P]>
    : Prisma.GetScalarType<T[P], AggregateLottery_entry[P]>
}




export type lottery_entryGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.lottery_entryWhereInput
  orderBy?: Prisma.lottery_entryOrderByWithAggregationInput | Prisma.lottery_entryOrderByWithAggregationInput[]
  by: Prisma.Lottery_entryScalarFieldEnum[] | Prisma.Lottery_entryScalarFieldEnum
  having?: Prisma.lottery_entryScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Lottery_entryCountAggregateInputType | true
  _avg?: Lottery_entryAvgAggregateInputType
  _sum?: Lottery_entrySumAggregateInputType
  _min?: Lottery_entryMinAggregateInputType
  _max?: Lottery_entryMaxAggregateInputType
}

export type Lottery_entryGroupByOutputType = {
  id: number
  createdAt: Date
  updatedAt: Date
  lotteryId: number | null
  userId: number | null
  _count: Lottery_entryCountAggregateOutputType | null
  _avg: Lottery_entryAvgAggregateOutputType | null
  _sum: Lottery_entrySumAggregateOutputType | null
  _min: Lottery_entryMinAggregateOutputType | null
  _max: Lottery_entryMaxAggregateOutputType | null
}

type GetLottery_entryGroupByPayload<T extends lottery_entryGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Lottery_entryGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Lottery_entryGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Lottery_entryGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Lottery_entryGroupByOutputType[P]>
      }
    >
  >



export type lottery_entryWhereInput = {
  AND?: Prisma.lottery_entryWhereInput | Prisma.lottery_entryWhereInput[]
  OR?: Prisma.lottery_entryWhereInput[]
  NOT?: Prisma.lottery_entryWhereInput | Prisma.lottery_entryWhereInput[]
  id?: Prisma.IntFilter<"lottery_entry"> | number
  createdAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  lotteryId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
  userId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
  lottery?: Prisma.XOR<Prisma.LotteryNullableScalarRelationFilter, Prisma.lotteryWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type lottery_entryOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  lottery?: Prisma.lotteryOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type lottery_entryWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.lottery_entryWhereInput | Prisma.lottery_entryWhereInput[]
  OR?: Prisma.lottery_entryWhereInput[]
  NOT?: Prisma.lottery_entryWhereInput | Prisma.lottery_entryWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  lotteryId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
  userId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
  lottery?: Prisma.XOR<Prisma.LotteryNullableScalarRelationFilter, Prisma.lotteryWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type lottery_entryOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.lottery_entryCountOrderByAggregateInput
  _avg?: Prisma.lottery_entryAvgOrderByAggregateInput
  _max?: Prisma.lottery_entryMaxOrderByAggregateInput
  _min?: Prisma.lottery_entryMinOrderByAggregateInput
  _sum?: Prisma.lottery_entrySumOrderByAggregateInput
}

export type lottery_entryScalarWhereWithAggregatesInput = {
  AND?: Prisma.lottery_entryScalarWhereWithAggregatesInput | Prisma.lottery_entryScalarWhereWithAggregatesInput[]
  OR?: Prisma.lottery_entryScalarWhereWithAggregatesInput[]
  NOT?: Prisma.lottery_entryScalarWhereWithAggregatesInput | Prisma.lottery_entryScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"lottery_entry"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"lottery_entry"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"lottery_entry"> | Date | string
  lotteryId?: Prisma.IntNullableWithAggregatesFilter<"lottery_entry"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"lottery_entry"> | number | null
}

export type lottery_entryCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  lottery?: Prisma.lotteryCreateNestedOneWithoutLottery_entryInput
  user?: Prisma.userCreateNestedOneWithoutLottery_entryInput
}

export type lottery_entryUncheckedCreateInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lotteryId?: number | null
  userId?: number | null
}

export type lottery_entryUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lottery?: Prisma.lotteryUpdateOneWithoutLottery_entryNestedInput
  user?: Prisma.userUpdateOneWithoutLottery_entryNestedInput
}

export type lottery_entryUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lotteryId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lottery_entryCreateManyInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lotteryId?: number | null
  userId?: number | null
}

export type lottery_entryUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type lottery_entryUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lotteryId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Lottery_entryListRelationFilter = {
  every?: Prisma.lottery_entryWhereInput
  some?: Prisma.lottery_entryWhereInput
  none?: Prisma.lottery_entryWhereInput
}

export type lottery_entryOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type lottery_entryCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type lottery_entryAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type lottery_entryMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type lottery_entryMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type lottery_entrySumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  lotteryId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type lottery_entryCreateNestedManyWithoutLotteryInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput> | Prisma.lottery_entryCreateWithoutLotteryInput[] | Prisma.lottery_entryUncheckedCreateWithoutLotteryInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutLotteryInput | Prisma.lottery_entryCreateOrConnectWithoutLotteryInput[]
  createMany?: Prisma.lottery_entryCreateManyLotteryInputEnvelope
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
}

export type lottery_entryUncheckedCreateNestedManyWithoutLotteryInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput> | Prisma.lottery_entryCreateWithoutLotteryInput[] | Prisma.lottery_entryUncheckedCreateWithoutLotteryInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutLotteryInput | Prisma.lottery_entryCreateOrConnectWithoutLotteryInput[]
  createMany?: Prisma.lottery_entryCreateManyLotteryInputEnvelope
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
}

export type lottery_entryUpdateManyWithoutLotteryNestedInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput> | Prisma.lottery_entryCreateWithoutLotteryInput[] | Prisma.lottery_entryUncheckedCreateWithoutLotteryInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutLotteryInput | Prisma.lottery_entryCreateOrConnectWithoutLotteryInput[]
  upsert?: Prisma.lottery_entryUpsertWithWhereUniqueWithoutLotteryInput | Prisma.lottery_entryUpsertWithWhereUniqueWithoutLotteryInput[]
  createMany?: Prisma.lottery_entryCreateManyLotteryInputEnvelope
  set?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  disconnect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  delete?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  update?: Prisma.lottery_entryUpdateWithWhereUniqueWithoutLotteryInput | Prisma.lottery_entryUpdateWithWhereUniqueWithoutLotteryInput[]
  updateMany?: Prisma.lottery_entryUpdateManyWithWhereWithoutLotteryInput | Prisma.lottery_entryUpdateManyWithWhereWithoutLotteryInput[]
  deleteMany?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
}

export type lottery_entryUncheckedUpdateManyWithoutLotteryNestedInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput> | Prisma.lottery_entryCreateWithoutLotteryInput[] | Prisma.lottery_entryUncheckedCreateWithoutLotteryInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutLotteryInput | Prisma.lottery_entryCreateOrConnectWithoutLotteryInput[]
  upsert?: Prisma.lottery_entryUpsertWithWhereUniqueWithoutLotteryInput | Prisma.lottery_entryUpsertWithWhereUniqueWithoutLotteryInput[]
  createMany?: Prisma.lottery_entryCreateManyLotteryInputEnvelope
  set?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  disconnect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  delete?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  update?: Prisma.lottery_entryUpdateWithWhereUniqueWithoutLotteryInput | Prisma.lottery_entryUpdateWithWhereUniqueWithoutLotteryInput[]
  updateMany?: Prisma.lottery_entryUpdateManyWithWhereWithoutLotteryInput | Prisma.lottery_entryUpdateManyWithWhereWithoutLotteryInput[]
  deleteMany?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
}

export type lottery_entryCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput> | Prisma.lottery_entryCreateWithoutUserInput[] | Prisma.lottery_entryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutUserInput | Prisma.lottery_entryCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.lottery_entryCreateManyUserInputEnvelope
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
}

export type lottery_entryUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput> | Prisma.lottery_entryCreateWithoutUserInput[] | Prisma.lottery_entryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutUserInput | Prisma.lottery_entryCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.lottery_entryCreateManyUserInputEnvelope
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
}

export type lottery_entryUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput> | Prisma.lottery_entryCreateWithoutUserInput[] | Prisma.lottery_entryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutUserInput | Prisma.lottery_entryCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.lottery_entryUpsertWithWhereUniqueWithoutUserInput | Prisma.lottery_entryUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.lottery_entryCreateManyUserInputEnvelope
  set?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  disconnect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  delete?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  update?: Prisma.lottery_entryUpdateWithWhereUniqueWithoutUserInput | Prisma.lottery_entryUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.lottery_entryUpdateManyWithWhereWithoutUserInput | Prisma.lottery_entryUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
}

export type lottery_entryUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput> | Prisma.lottery_entryCreateWithoutUserInput[] | Prisma.lottery_entryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lottery_entryCreateOrConnectWithoutUserInput | Prisma.lottery_entryCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.lottery_entryUpsertWithWhereUniqueWithoutUserInput | Prisma.lottery_entryUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.lottery_entryCreateManyUserInputEnvelope
  set?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  disconnect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  delete?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  connect?: Prisma.lottery_entryWhereUniqueInput | Prisma.lottery_entryWhereUniqueInput[]
  update?: Prisma.lottery_entryUpdateWithWhereUniqueWithoutUserInput | Prisma.lottery_entryUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.lottery_entryUpdateManyWithWhereWithoutUserInput | Prisma.lottery_entryUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
}

export type lottery_entryCreateWithoutLotteryInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutLottery_entryInput
}

export type lottery_entryUncheckedCreateWithoutLotteryInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type lottery_entryCreateOrConnectWithoutLotteryInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  create: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput>
}

export type lottery_entryCreateManyLotteryInputEnvelope = {
  data: Prisma.lottery_entryCreateManyLotteryInput | Prisma.lottery_entryCreateManyLotteryInput[]
  skipDuplicates?: boolean
}

export type lottery_entryUpsertWithWhereUniqueWithoutLotteryInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  update: Prisma.XOR<Prisma.lottery_entryUpdateWithoutLotteryInput, Prisma.lottery_entryUncheckedUpdateWithoutLotteryInput>
  create: Prisma.XOR<Prisma.lottery_entryCreateWithoutLotteryInput, Prisma.lottery_entryUncheckedCreateWithoutLotteryInput>
}

export type lottery_entryUpdateWithWhereUniqueWithoutLotteryInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  data: Prisma.XOR<Prisma.lottery_entryUpdateWithoutLotteryInput, Prisma.lottery_entryUncheckedUpdateWithoutLotteryInput>
}

export type lottery_entryUpdateManyWithWhereWithoutLotteryInput = {
  where: Prisma.lottery_entryScalarWhereInput
  data: Prisma.XOR<Prisma.lottery_entryUpdateManyMutationInput, Prisma.lottery_entryUncheckedUpdateManyWithoutLotteryInput>
}

export type lottery_entryScalarWhereInput = {
  AND?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
  OR?: Prisma.lottery_entryScalarWhereInput[]
  NOT?: Prisma.lottery_entryScalarWhereInput | Prisma.lottery_entryScalarWhereInput[]
  id?: Prisma.IntFilter<"lottery_entry"> | number
  createdAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery_entry"> | Date | string
  lotteryId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
  userId?: Prisma.IntNullableFilter<"lottery_entry"> | number | null
}

export type lottery_entryCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  lottery?: Prisma.lotteryCreateNestedOneWithoutLottery_entryInput
}

export type lottery_entryUncheckedCreateWithoutUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lotteryId?: number | null
}

export type lottery_entryCreateOrConnectWithoutUserInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  create: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput>
}

export type lottery_entryCreateManyUserInputEnvelope = {
  data: Prisma.lottery_entryCreateManyUserInput | Prisma.lottery_entryCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type lottery_entryUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  update: Prisma.XOR<Prisma.lottery_entryUpdateWithoutUserInput, Prisma.lottery_entryUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.lottery_entryCreateWithoutUserInput, Prisma.lottery_entryUncheckedCreateWithoutUserInput>
}

export type lottery_entryUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.lottery_entryWhereUniqueInput
  data: Prisma.XOR<Prisma.lottery_entryUpdateWithoutUserInput, Prisma.lottery_entryUncheckedUpdateWithoutUserInput>
}

export type lottery_entryUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.lottery_entryScalarWhereInput
  data: Prisma.XOR<Prisma.lottery_entryUpdateManyMutationInput, Prisma.lottery_entryUncheckedUpdateManyWithoutUserInput>
}

export type lottery_entryCreateManyLotteryInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type lottery_entryUpdateWithoutLotteryInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutLottery_entryNestedInput
}

export type lottery_entryUncheckedUpdateWithoutLotteryInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lottery_entryUncheckedUpdateManyWithoutLotteryInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lottery_entryCreateManyUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lotteryId?: number | null
}

export type lottery_entryUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lottery?: Prisma.lotteryUpdateOneWithoutLottery_entryNestedInput
}

export type lottery_entryUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lotteryId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lottery_entryUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lotteryId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type lottery_entrySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  lotteryId?: boolean
  userId?: boolean
  lottery?: boolean | Prisma.lottery_entry$lotteryArgs<ExtArgs>
  user?: boolean | Prisma.lottery_entry$userArgs<ExtArgs>
}, ExtArgs["result"]["lottery_entry"]>



export type lottery_entrySelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  lotteryId?: boolean
  userId?: boolean
}

export type lottery_entryOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "createdAt" | "updatedAt" | "lotteryId" | "userId", ExtArgs["result"]["lottery_entry"]>
export type lottery_entryInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  lottery?: boolean | Prisma.lottery_entry$lotteryArgs<ExtArgs>
  user?: boolean | Prisma.lottery_entry$userArgs<ExtArgs>
}

export type $lottery_entryPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "lottery_entry"
  objects: {
    lottery: Prisma.$lotteryPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    createdAt: Date
    updatedAt: Date
    lotteryId: number | null
    userId: number | null
  }, ExtArgs["result"]["lottery_entry"]>
  composites: {}
}

export type lottery_entryGetPayload<S extends boolean | null | undefined | lottery_entryDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload, S>

export type lottery_entryCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<lottery_entryFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Lottery_entryCountAggregateInputType | true
  }

export interface lottery_entryDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['lottery_entry'], meta: { name: 'lottery_entry' } }
  /**
   * Find zero or one Lottery_entry that matches the filter.
   * @param {lottery_entryFindUniqueArgs} args - Arguments to find a Lottery_entry
   * @example
   * // Get one Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends lottery_entryFindUniqueArgs>(args: Prisma.SelectSubset<T, lottery_entryFindUniqueArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Lottery_entry that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {lottery_entryFindUniqueOrThrowArgs} args - Arguments to find a Lottery_entry
   * @example
   * // Get one Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends lottery_entryFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, lottery_entryFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Lottery_entry that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryFindFirstArgs} args - Arguments to find a Lottery_entry
   * @example
   * // Get one Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends lottery_entryFindFirstArgs>(args?: Prisma.SelectSubset<T, lottery_entryFindFirstArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Lottery_entry that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryFindFirstOrThrowArgs} args - Arguments to find a Lottery_entry
   * @example
   * // Get one Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends lottery_entryFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, lottery_entryFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Lottery_entries that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Lottery_entries
   * const lottery_entries = await prisma.lottery_entry.findMany()
   * 
   * // Get first 10 Lottery_entries
   * const lottery_entries = await prisma.lottery_entry.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const lottery_entryWithIdOnly = await prisma.lottery_entry.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends lottery_entryFindManyArgs>(args?: Prisma.SelectSubset<T, lottery_entryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Lottery_entry.
   * @param {lottery_entryCreateArgs} args - Arguments to create a Lottery_entry.
   * @example
   * // Create one Lottery_entry
   * const Lottery_entry = await prisma.lottery_entry.create({
   *   data: {
   *     // ... data to create a Lottery_entry
   *   }
   * })
   * 
   */
  create<T extends lottery_entryCreateArgs>(args: Prisma.SelectSubset<T, lottery_entryCreateArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Lottery_entries.
   * @param {lottery_entryCreateManyArgs} args - Arguments to create many Lottery_entries.
   * @example
   * // Create many Lottery_entries
   * const lottery_entry = await prisma.lottery_entry.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends lottery_entryCreateManyArgs>(args?: Prisma.SelectSubset<T, lottery_entryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Lottery_entry.
   * @param {lottery_entryDeleteArgs} args - Arguments to delete one Lottery_entry.
   * @example
   * // Delete one Lottery_entry
   * const Lottery_entry = await prisma.lottery_entry.delete({
   *   where: {
   *     // ... filter to delete one Lottery_entry
   *   }
   * })
   * 
   */
  delete<T extends lottery_entryDeleteArgs>(args: Prisma.SelectSubset<T, lottery_entryDeleteArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Lottery_entry.
   * @param {lottery_entryUpdateArgs} args - Arguments to update one Lottery_entry.
   * @example
   * // Update one Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends lottery_entryUpdateArgs>(args: Prisma.SelectSubset<T, lottery_entryUpdateArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Lottery_entries.
   * @param {lottery_entryDeleteManyArgs} args - Arguments to filter Lottery_entries to delete.
   * @example
   * // Delete a few Lottery_entries
   * const { count } = await prisma.lottery_entry.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends lottery_entryDeleteManyArgs>(args?: Prisma.SelectSubset<T, lottery_entryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Lottery_entries.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Lottery_entries
   * const lottery_entry = await prisma.lottery_entry.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends lottery_entryUpdateManyArgs>(args: Prisma.SelectSubset<T, lottery_entryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Lottery_entry.
   * @param {lottery_entryUpsertArgs} args - Arguments to update or create a Lottery_entry.
   * @example
   * // Update or create a Lottery_entry
   * const lottery_entry = await prisma.lottery_entry.upsert({
   *   create: {
   *     // ... data to create a Lottery_entry
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Lottery_entry we want to update
   *   }
   * })
   */
  upsert<T extends lottery_entryUpsertArgs>(args: Prisma.SelectSubset<T, lottery_entryUpsertArgs<ExtArgs>>): Prisma.Prisma__lottery_entryClient<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Lottery_entries.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryCountArgs} args - Arguments to filter Lottery_entries to count.
   * @example
   * // Count the number of Lottery_entries
   * const count = await prisma.lottery_entry.count({
   *   where: {
   *     // ... the filter for the Lottery_entries we want to count
   *   }
   * })
  **/
  count<T extends lottery_entryCountArgs>(
    args?: Prisma.Subset<T, lottery_entryCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Lottery_entryCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Lottery_entry.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Lottery_entryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Lottery_entryAggregateArgs>(args: Prisma.Subset<T, Lottery_entryAggregateArgs>): Prisma.PrismaPromise<GetLottery_entryAggregateType<T>>

  /**
   * Group by Lottery_entry.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lottery_entryGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends lottery_entryGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: lottery_entryGroupByArgs['orderBy'] }
      : { orderBy?: lottery_entryGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, lottery_entryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLottery_entryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the lottery_entry model
 */
readonly fields: lottery_entryFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for lottery_entry.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__lottery_entryClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  lottery<T extends Prisma.lottery_entry$lotteryArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.lottery_entry$lotteryArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.lottery_entry$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.lottery_entry$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the lottery_entry model
 */
export interface lottery_entryFieldRefs {
  readonly id: Prisma.FieldRef<"lottery_entry", 'Int'>
  readonly createdAt: Prisma.FieldRef<"lottery_entry", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"lottery_entry", 'DateTime'>
  readonly lotteryId: Prisma.FieldRef<"lottery_entry", 'Int'>
  readonly userId: Prisma.FieldRef<"lottery_entry", 'Int'>
}
    

// Custom InputTypes
/**
 * lottery_entry findUnique
 */
export type lottery_entryFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter, which lottery_entry to fetch.
   */
  where: Prisma.lottery_entryWhereUniqueInput
}

/**
 * lottery_entry findUniqueOrThrow
 */
export type lottery_entryFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter, which lottery_entry to fetch.
   */
  where: Prisma.lottery_entryWhereUniqueInput
}

/**
 * lottery_entry findFirst
 */
export type lottery_entryFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter, which lottery_entry to fetch.
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lottery_entries to fetch.
   */
  orderBy?: Prisma.lottery_entryOrderByWithRelationInput | Prisma.lottery_entryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for lottery_entries.
   */
  cursor?: Prisma.lottery_entryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lottery_entries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lottery_entries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of lottery_entries.
   */
  distinct?: Prisma.Lottery_entryScalarFieldEnum | Prisma.Lottery_entryScalarFieldEnum[]
}

/**
 * lottery_entry findFirstOrThrow
 */
export type lottery_entryFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter, which lottery_entry to fetch.
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lottery_entries to fetch.
   */
  orderBy?: Prisma.lottery_entryOrderByWithRelationInput | Prisma.lottery_entryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for lottery_entries.
   */
  cursor?: Prisma.lottery_entryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lottery_entries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lottery_entries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of lottery_entries.
   */
  distinct?: Prisma.Lottery_entryScalarFieldEnum | Prisma.Lottery_entryScalarFieldEnum[]
}

/**
 * lottery_entry findMany
 */
export type lottery_entryFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter, which lottery_entries to fetch.
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lottery_entries to fetch.
   */
  orderBy?: Prisma.lottery_entryOrderByWithRelationInput | Prisma.lottery_entryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing lottery_entries.
   */
  cursor?: Prisma.lottery_entryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lottery_entries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lottery_entries.
   */
  skip?: number
  distinct?: Prisma.Lottery_entryScalarFieldEnum | Prisma.Lottery_entryScalarFieldEnum[]
}

/**
 * lottery_entry create
 */
export type lottery_entryCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * The data needed to create a lottery_entry.
   */
  data: Prisma.XOR<Prisma.lottery_entryCreateInput, Prisma.lottery_entryUncheckedCreateInput>
}

/**
 * lottery_entry createMany
 */
export type lottery_entryCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many lottery_entries.
   */
  data: Prisma.lottery_entryCreateManyInput | Prisma.lottery_entryCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * lottery_entry update
 */
export type lottery_entryUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * The data needed to update a lottery_entry.
   */
  data: Prisma.XOR<Prisma.lottery_entryUpdateInput, Prisma.lottery_entryUncheckedUpdateInput>
  /**
   * Choose, which lottery_entry to update.
   */
  where: Prisma.lottery_entryWhereUniqueInput
}

/**
 * lottery_entry updateMany
 */
export type lottery_entryUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update lottery_entries.
   */
  data: Prisma.XOR<Prisma.lottery_entryUpdateManyMutationInput, Prisma.lottery_entryUncheckedUpdateManyInput>
  /**
   * Filter which lottery_entries to update
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * Limit how many lottery_entries to update.
   */
  limit?: number
}

/**
 * lottery_entry upsert
 */
export type lottery_entryUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * The filter to search for the lottery_entry to update in case it exists.
   */
  where: Prisma.lottery_entryWhereUniqueInput
  /**
   * In case the lottery_entry found by the `where` argument doesn't exist, create a new lottery_entry with this data.
   */
  create: Prisma.XOR<Prisma.lottery_entryCreateInput, Prisma.lottery_entryUncheckedCreateInput>
  /**
   * In case the lottery_entry was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.lottery_entryUpdateInput, Prisma.lottery_entryUncheckedUpdateInput>
}

/**
 * lottery_entry delete
 */
export type lottery_entryDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  /**
   * Filter which lottery_entry to delete.
   */
  where: Prisma.lottery_entryWhereUniqueInput
}

/**
 * lottery_entry deleteMany
 */
export type lottery_entryDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which lottery_entries to delete
   */
  where?: Prisma.lottery_entryWhereInput
  /**
   * Limit how many lottery_entries to delete.
   */
  limit?: number
}

/**
 * lottery_entry.lottery
 */
export type lottery_entry$lotteryArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  where?: Prisma.lotteryWhereInput
}

/**
 * lottery_entry.user
 */
export type lottery_entry$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * lottery_entry without action
 */
export type lottery_entryDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
}
