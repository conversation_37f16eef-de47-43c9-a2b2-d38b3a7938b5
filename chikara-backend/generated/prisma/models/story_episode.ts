
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `story_episode` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model story_episode
 * 
 */
export type story_episodeModel = runtime.Types.Result.DefaultSelection<Prisma.$story_episodePayload>

export type AggregateStory_episode = {
  _count: Story_episodeCountAggregateOutputType | null
  _avg: Story_episodeAvgAggregateOutputType | null
  _sum: Story_episodeSumAggregateOutputType | null
  _min: Story_episodeMinAggregateOutputType | null
  _max: Story_episodeMaxAggregateOutputType | null
}

export type Story_episodeAvgAggregateOutputType = {
  id: number | null
  objectiveId: number | null
}

export type Story_episodeSumAggregateOutputType = {
  id: number | null
  objectiveId: number | null
}

export type Story_episodeMinAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  episodeType: $Enums.StoryEpisodeType | null
  exploreLocation: $Enums.ExploreNodeLocation | null
  objectiveId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_episodeMaxAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  episodeType: $Enums.StoryEpisodeType | null
  exploreLocation: $Enums.ExploreNodeLocation | null
  objectiveId: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_episodeCountAggregateOutputType = {
  id: number
  name: number
  description: number
  episodeType: number
  exploreLocation: number
  content: number
  choices: number
  objectiveId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Story_episodeAvgAggregateInputType = {
  id?: true
  objectiveId?: true
}

export type Story_episodeSumAggregateInputType = {
  id?: true
  objectiveId?: true
}

export type Story_episodeMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  episodeType?: true
  exploreLocation?: true
  objectiveId?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_episodeMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  episodeType?: true
  exploreLocation?: true
  objectiveId?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_episodeCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  episodeType?: true
  exploreLocation?: true
  content?: true
  choices?: true
  objectiveId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Story_episodeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_episode to aggregate.
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_episodes to fetch.
   */
  orderBy?: Prisma.story_episodeOrderByWithRelationInput | Prisma.story_episodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.story_episodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_episodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_episodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned story_episodes
  **/
  _count?: true | Story_episodeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Story_episodeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Story_episodeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Story_episodeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Story_episodeMaxAggregateInputType
}

export type GetStory_episodeAggregateType<T extends Story_episodeAggregateArgs> = {
      [P in keyof T & keyof AggregateStory_episode]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStory_episode[P]>
    : Prisma.GetScalarType<T[P], AggregateStory_episode[P]>
}




export type story_episodeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.story_episodeWhereInput
  orderBy?: Prisma.story_episodeOrderByWithAggregationInput | Prisma.story_episodeOrderByWithAggregationInput[]
  by: Prisma.Story_episodeScalarFieldEnum[] | Prisma.Story_episodeScalarFieldEnum
  having?: Prisma.story_episodeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Story_episodeCountAggregateInputType | true
  _avg?: Story_episodeAvgAggregateInputType
  _sum?: Story_episodeSumAggregateInputType
  _min?: Story_episodeMinAggregateInputType
  _max?: Story_episodeMaxAggregateInputType
}

export type Story_episodeGroupByOutputType = {
  id: number
  name: string
  description: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation: $Enums.ExploreNodeLocation
  content: runtime.JsonValue
  choices: runtime.JsonValue | null
  objectiveId: number
  createdAt: Date
  updatedAt: Date
  _count: Story_episodeCountAggregateOutputType | null
  _avg: Story_episodeAvgAggregateOutputType | null
  _sum: Story_episodeSumAggregateOutputType | null
  _min: Story_episodeMinAggregateOutputType | null
  _max: Story_episodeMaxAggregateOutputType | null
}

type GetStory_episodeGroupByPayload<T extends story_episodeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Story_episodeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Story_episodeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Story_episodeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Story_episodeGroupByOutputType[P]>
      }
    >
  >



export type story_episodeWhereInput = {
  AND?: Prisma.story_episodeWhereInput | Prisma.story_episodeWhereInput[]
  OR?: Prisma.story_episodeWhereInput[]
  NOT?: Prisma.story_episodeWhereInput | Prisma.story_episodeWhereInput[]
  id?: Prisma.IntFilter<"story_episode"> | number
  name?: Prisma.StringFilter<"story_episode"> | string
  description?: Prisma.StringNullableFilter<"story_episode"> | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFilter<"story_episode"> | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFilter<"story_episode"> | $Enums.ExploreNodeLocation
  content?: Prisma.JsonFilter<"story_episode">
  choices?: Prisma.JsonNullableFilter<"story_episode">
  objectiveId?: Prisma.IntFilter<"story_episode"> | number
  createdAt?: Prisma.DateTimeFilter<"story_episode"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_episode"> | Date | string
  quest_objective?: Prisma.XOR<Prisma.Quest_objectiveScalarRelationFilter, Prisma.quest_objectiveWhereInput>
}

export type story_episodeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  episodeType?: Prisma.SortOrder
  exploreLocation?: Prisma.SortOrder
  content?: Prisma.SortOrder
  choices?: Prisma.SortOrderInput | Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  quest_objective?: Prisma.quest_objectiveOrderByWithRelationInput
  _relevance?: Prisma.story_episodeOrderByRelevanceInput
}

export type story_episodeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  objectiveId?: number
  AND?: Prisma.story_episodeWhereInput | Prisma.story_episodeWhereInput[]
  OR?: Prisma.story_episodeWhereInput[]
  NOT?: Prisma.story_episodeWhereInput | Prisma.story_episodeWhereInput[]
  name?: Prisma.StringFilter<"story_episode"> | string
  description?: Prisma.StringNullableFilter<"story_episode"> | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFilter<"story_episode"> | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFilter<"story_episode"> | $Enums.ExploreNodeLocation
  content?: Prisma.JsonFilter<"story_episode">
  choices?: Prisma.JsonNullableFilter<"story_episode">
  createdAt?: Prisma.DateTimeFilter<"story_episode"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_episode"> | Date | string
  quest_objective?: Prisma.XOR<Prisma.Quest_objectiveScalarRelationFilter, Prisma.quest_objectiveWhereInput>
}, "id" | "objectiveId">

export type story_episodeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  episodeType?: Prisma.SortOrder
  exploreLocation?: Prisma.SortOrder
  content?: Prisma.SortOrder
  choices?: Prisma.SortOrderInput | Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.story_episodeCountOrderByAggregateInput
  _avg?: Prisma.story_episodeAvgOrderByAggregateInput
  _max?: Prisma.story_episodeMaxOrderByAggregateInput
  _min?: Prisma.story_episodeMinOrderByAggregateInput
  _sum?: Prisma.story_episodeSumOrderByAggregateInput
}

export type story_episodeScalarWhereWithAggregatesInput = {
  AND?: Prisma.story_episodeScalarWhereWithAggregatesInput | Prisma.story_episodeScalarWhereWithAggregatesInput[]
  OR?: Prisma.story_episodeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.story_episodeScalarWhereWithAggregatesInput | Prisma.story_episodeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"story_episode"> | number
  name?: Prisma.StringWithAggregatesFilter<"story_episode"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"story_episode"> | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeWithAggregatesFilter<"story_episode"> | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationWithAggregatesFilter<"story_episode"> | $Enums.ExploreNodeLocation
  content?: Prisma.JsonWithAggregatesFilter<"story_episode">
  choices?: Prisma.JsonNullableWithAggregatesFilter<"story_episode">
  objectiveId?: Prisma.IntWithAggregatesFilter<"story_episode"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"story_episode"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"story_episode"> | Date | string
}

export type story_episodeCreateInput = {
  name: string
  description?: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation?: $Enums.ExploreNodeLocation
  content:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  quest_objective: Prisma.quest_objectiveCreateNestedOneWithoutStory_episodeInput
}

export type story_episodeUncheckedCreateInput = {
  id?: number
  name: string
  description?: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation?: $Enums.ExploreNodeLocation
  content:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  objectiveId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_episodeUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest_objective?: Prisma.quest_objectiveUpdateOneRequiredWithoutStory_episodeNestedInput
}

export type story_episodeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  objectiveId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_episodeCreateManyInput = {
  id?: number
  name: string
  description?: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation?: $Enums.ExploreNodeLocation
  content:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  objectiveId: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_episodeUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_episodeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  objectiveId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type Story_episodeNullableScalarRelationFilter = {
  is?: Prisma.story_episodeWhereInput | null
  isNot?: Prisma.story_episodeWhereInput | null
}

export type story_episodeOrderByRelevanceInput = {
  fields: Prisma.story_episodeOrderByRelevanceFieldEnum | Prisma.story_episodeOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type story_episodeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  episodeType?: Prisma.SortOrder
  exploreLocation?: Prisma.SortOrder
  content?: Prisma.SortOrder
  choices?: Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_episodeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
}

export type story_episodeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  episodeType?: Prisma.SortOrder
  exploreLocation?: Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_episodeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  episodeType?: Prisma.SortOrder
  exploreLocation?: Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_episodeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  objectiveId?: Prisma.SortOrder
}

export type story_episodeCreateNestedOneWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.story_episodeCreateOrConnectWithoutQuest_objectiveInput
  connect?: Prisma.story_episodeWhereUniqueInput
}

export type story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.story_episodeCreateOrConnectWithoutQuest_objectiveInput
  connect?: Prisma.story_episodeWhereUniqueInput
}

export type story_episodeUpdateOneWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.story_episodeCreateOrConnectWithoutQuest_objectiveInput
  upsert?: Prisma.story_episodeUpsertWithoutQuest_objectiveInput
  disconnect?: Prisma.story_episodeWhereInput | boolean
  delete?: Prisma.story_episodeWhereInput | boolean
  connect?: Prisma.story_episodeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.story_episodeUpdateToOneWithWhereWithoutQuest_objectiveInput, Prisma.story_episodeUpdateWithoutQuest_objectiveInput>, Prisma.story_episodeUncheckedUpdateWithoutQuest_objectiveInput>
}

export type story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.story_episodeCreateOrConnectWithoutQuest_objectiveInput
  upsert?: Prisma.story_episodeUpsertWithoutQuest_objectiveInput
  disconnect?: Prisma.story_episodeWhereInput | boolean
  delete?: Prisma.story_episodeWhereInput | boolean
  connect?: Prisma.story_episodeWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.story_episodeUpdateToOneWithWhereWithoutQuest_objectiveInput, Prisma.story_episodeUpdateWithoutQuest_objectiveInput>, Prisma.story_episodeUncheckedUpdateWithoutQuest_objectiveInput>
}

export type EnumStoryEpisodeTypeFieldUpdateOperationsInput = {
  set?: $Enums.StoryEpisodeType
}

export type story_episodeCreateWithoutQuest_objectiveInput = {
  name: string
  description?: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation?: $Enums.ExploreNodeLocation
  content:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_episodeUncheckedCreateWithoutQuest_objectiveInput = {
  id?: number
  name: string
  description?: string | null
  episodeType: $Enums.StoryEpisodeType
  exploreLocation?: $Enums.ExploreNodeLocation
  content:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_episodeCreateOrConnectWithoutQuest_objectiveInput = {
  where: Prisma.story_episodeWhereUniqueInput
  create: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
}

export type story_episodeUpsertWithoutQuest_objectiveInput = {
  update: Prisma.XOR<Prisma.story_episodeUpdateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedUpdateWithoutQuest_objectiveInput>
  create: Prisma.XOR<Prisma.story_episodeCreateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedCreateWithoutQuest_objectiveInput>
  where?: Prisma.story_episodeWhereInput
}

export type story_episodeUpdateToOneWithWhereWithoutQuest_objectiveInput = {
  where?: Prisma.story_episodeWhereInput
  data: Prisma.XOR<Prisma.story_episodeUpdateWithoutQuest_objectiveInput, Prisma.story_episodeUncheckedUpdateWithoutQuest_objectiveInput>
}

export type story_episodeUpdateWithoutQuest_objectiveInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_episodeUncheckedUpdateWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  episodeType?: Prisma.EnumStoryEpisodeTypeFieldUpdateOperationsInput | $Enums.StoryEpisodeType
  exploreLocation?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  content?:PrismaJson.StoryEpisodeContent
  choices?:PrismaJson.StoryEpisodeChoices | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type story_episodeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  episodeType?: boolean
  exploreLocation?: boolean
  content?: boolean
  choices?: boolean
  objectiveId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  quest_objective?: boolean | Prisma.quest_objectiveDefaultArgs<ExtArgs>
}, ExtArgs["result"]["story_episode"]>



export type story_episodeSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  episodeType?: boolean
  exploreLocation?: boolean
  content?: boolean
  choices?: boolean
  objectiveId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type story_episodeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "episodeType" | "exploreLocation" | "content" | "choices" | "objectiveId" | "createdAt" | "updatedAt", ExtArgs["result"]["story_episode"]>
export type story_episodeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest_objective?: boolean | Prisma.quest_objectiveDefaultArgs<ExtArgs>
}

export type $story_episodePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "story_episode"
  objects: {
    quest_objective: Prisma.$quest_objectivePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    description: string | null
    episodeType: $Enums.StoryEpisodeType
    exploreLocation: $Enums.ExploreNodeLocation
    /**
     * [StoryEpisodeContent]
     */
    content:PrismaJson.StoryEpisodeContent
    /**
     * [StoryEpisodeChoices]
     */
    choices:PrismaJson.StoryEpisodeChoices | null
    objectiveId: number
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["story_episode"]>
  composites: {}
}

export type story_episodeGetPayload<S extends boolean | null | undefined | story_episodeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$story_episodePayload, S>

export type story_episodeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<story_episodeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Story_episodeCountAggregateInputType | true
  }

export interface story_episodeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['story_episode'], meta: { name: 'story_episode' } }
  /**
   * Find zero or one Story_episode that matches the filter.
   * @param {story_episodeFindUniqueArgs} args - Arguments to find a Story_episode
   * @example
   * // Get one Story_episode
   * const story_episode = await prisma.story_episode.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends story_episodeFindUniqueArgs>(args: Prisma.SelectSubset<T, story_episodeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Story_episode that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {story_episodeFindUniqueOrThrowArgs} args - Arguments to find a Story_episode
   * @example
   * // Get one Story_episode
   * const story_episode = await prisma.story_episode.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends story_episodeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, story_episodeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_episode that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeFindFirstArgs} args - Arguments to find a Story_episode
   * @example
   * // Get one Story_episode
   * const story_episode = await prisma.story_episode.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends story_episodeFindFirstArgs>(args?: Prisma.SelectSubset<T, story_episodeFindFirstArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_episode that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeFindFirstOrThrowArgs} args - Arguments to find a Story_episode
   * @example
   * // Get one Story_episode
   * const story_episode = await prisma.story_episode.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends story_episodeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, story_episodeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Story_episodes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Story_episodes
   * const story_episodes = await prisma.story_episode.findMany()
   * 
   * // Get first 10 Story_episodes
   * const story_episodes = await prisma.story_episode.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const story_episodeWithIdOnly = await prisma.story_episode.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends story_episodeFindManyArgs>(args?: Prisma.SelectSubset<T, story_episodeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Story_episode.
   * @param {story_episodeCreateArgs} args - Arguments to create a Story_episode.
   * @example
   * // Create one Story_episode
   * const Story_episode = await prisma.story_episode.create({
   *   data: {
   *     // ... data to create a Story_episode
   *   }
   * })
   * 
   */
  create<T extends story_episodeCreateArgs>(args: Prisma.SelectSubset<T, story_episodeCreateArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Story_episodes.
   * @param {story_episodeCreateManyArgs} args - Arguments to create many Story_episodes.
   * @example
   * // Create many Story_episodes
   * const story_episode = await prisma.story_episode.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends story_episodeCreateManyArgs>(args?: Prisma.SelectSubset<T, story_episodeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Story_episode.
   * @param {story_episodeDeleteArgs} args - Arguments to delete one Story_episode.
   * @example
   * // Delete one Story_episode
   * const Story_episode = await prisma.story_episode.delete({
   *   where: {
   *     // ... filter to delete one Story_episode
   *   }
   * })
   * 
   */
  delete<T extends story_episodeDeleteArgs>(args: Prisma.SelectSubset<T, story_episodeDeleteArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Story_episode.
   * @param {story_episodeUpdateArgs} args - Arguments to update one Story_episode.
   * @example
   * // Update one Story_episode
   * const story_episode = await prisma.story_episode.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends story_episodeUpdateArgs>(args: Prisma.SelectSubset<T, story_episodeUpdateArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Story_episodes.
   * @param {story_episodeDeleteManyArgs} args - Arguments to filter Story_episodes to delete.
   * @example
   * // Delete a few Story_episodes
   * const { count } = await prisma.story_episode.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends story_episodeDeleteManyArgs>(args?: Prisma.SelectSubset<T, story_episodeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Story_episodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Story_episodes
   * const story_episode = await prisma.story_episode.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends story_episodeUpdateManyArgs>(args: Prisma.SelectSubset<T, story_episodeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Story_episode.
   * @param {story_episodeUpsertArgs} args - Arguments to update or create a Story_episode.
   * @example
   * // Update or create a Story_episode
   * const story_episode = await prisma.story_episode.upsert({
   *   create: {
   *     // ... data to create a Story_episode
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Story_episode we want to update
   *   }
   * })
   */
  upsert<T extends story_episodeUpsertArgs>(args: Prisma.SelectSubset<T, story_episodeUpsertArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Story_episodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeCountArgs} args - Arguments to filter Story_episodes to count.
   * @example
   * // Count the number of Story_episodes
   * const count = await prisma.story_episode.count({
   *   where: {
   *     // ... the filter for the Story_episodes we want to count
   *   }
   * })
  **/
  count<T extends story_episodeCountArgs>(
    args?: Prisma.Subset<T, story_episodeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Story_episodeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Story_episode.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Story_episodeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Story_episodeAggregateArgs>(args: Prisma.Subset<T, Story_episodeAggregateArgs>): Prisma.PrismaPromise<GetStory_episodeAggregateType<T>>

  /**
   * Group by Story_episode.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_episodeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends story_episodeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: story_episodeGroupByArgs['orderBy'] }
      : { orderBy?: story_episodeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, story_episodeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStory_episodeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the story_episode model
 */
readonly fields: story_episodeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for story_episode.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__story_episodeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest_objective<T extends Prisma.quest_objectiveDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objectiveDefaultArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the story_episode model
 */
export interface story_episodeFieldRefs {
  readonly id: Prisma.FieldRef<"story_episode", 'Int'>
  readonly name: Prisma.FieldRef<"story_episode", 'String'>
  readonly description: Prisma.FieldRef<"story_episode", 'String'>
  readonly episodeType: Prisma.FieldRef<"story_episode", 'StoryEpisodeType'>
  readonly exploreLocation: Prisma.FieldRef<"story_episode", 'ExploreNodeLocation'>
  readonly content: Prisma.FieldRef<"story_episode", 'Json'>
  readonly choices: Prisma.FieldRef<"story_episode", 'Json'>
  readonly objectiveId: Prisma.FieldRef<"story_episode", 'Int'>
  readonly createdAt: Prisma.FieldRef<"story_episode", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"story_episode", 'DateTime'>
}
    

// Custom InputTypes
/**
 * story_episode findUnique
 */
export type story_episodeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter, which story_episode to fetch.
   */
  where: Prisma.story_episodeWhereUniqueInput
}

/**
 * story_episode findUniqueOrThrow
 */
export type story_episodeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter, which story_episode to fetch.
   */
  where: Prisma.story_episodeWhereUniqueInput
}

/**
 * story_episode findFirst
 */
export type story_episodeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter, which story_episode to fetch.
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_episodes to fetch.
   */
  orderBy?: Prisma.story_episodeOrderByWithRelationInput | Prisma.story_episodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_episodes.
   */
  cursor?: Prisma.story_episodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_episodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_episodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_episodes.
   */
  distinct?: Prisma.Story_episodeScalarFieldEnum | Prisma.Story_episodeScalarFieldEnum[]
}

/**
 * story_episode findFirstOrThrow
 */
export type story_episodeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter, which story_episode to fetch.
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_episodes to fetch.
   */
  orderBy?: Prisma.story_episodeOrderByWithRelationInput | Prisma.story_episodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_episodes.
   */
  cursor?: Prisma.story_episodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_episodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_episodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_episodes.
   */
  distinct?: Prisma.Story_episodeScalarFieldEnum | Prisma.Story_episodeScalarFieldEnum[]
}

/**
 * story_episode findMany
 */
export type story_episodeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter, which story_episodes to fetch.
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_episodes to fetch.
   */
  orderBy?: Prisma.story_episodeOrderByWithRelationInput | Prisma.story_episodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing story_episodes.
   */
  cursor?: Prisma.story_episodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_episodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_episodes.
   */
  skip?: number
  distinct?: Prisma.Story_episodeScalarFieldEnum | Prisma.Story_episodeScalarFieldEnum[]
}

/**
 * story_episode create
 */
export type story_episodeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * The data needed to create a story_episode.
   */
  data: Prisma.XOR<Prisma.story_episodeCreateInput, Prisma.story_episodeUncheckedCreateInput>
}

/**
 * story_episode createMany
 */
export type story_episodeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many story_episodes.
   */
  data: Prisma.story_episodeCreateManyInput | Prisma.story_episodeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * story_episode update
 */
export type story_episodeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * The data needed to update a story_episode.
   */
  data: Prisma.XOR<Prisma.story_episodeUpdateInput, Prisma.story_episodeUncheckedUpdateInput>
  /**
   * Choose, which story_episode to update.
   */
  where: Prisma.story_episodeWhereUniqueInput
}

/**
 * story_episode updateMany
 */
export type story_episodeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update story_episodes.
   */
  data: Prisma.XOR<Prisma.story_episodeUpdateManyMutationInput, Prisma.story_episodeUncheckedUpdateManyInput>
  /**
   * Filter which story_episodes to update
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * Limit how many story_episodes to update.
   */
  limit?: number
}

/**
 * story_episode upsert
 */
export type story_episodeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * The filter to search for the story_episode to update in case it exists.
   */
  where: Prisma.story_episodeWhereUniqueInput
  /**
   * In case the story_episode found by the `where` argument doesn't exist, create a new story_episode with this data.
   */
  create: Prisma.XOR<Prisma.story_episodeCreateInput, Prisma.story_episodeUncheckedCreateInput>
  /**
   * In case the story_episode was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.story_episodeUpdateInput, Prisma.story_episodeUncheckedUpdateInput>
}

/**
 * story_episode delete
 */
export type story_episodeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  /**
   * Filter which story_episode to delete.
   */
  where: Prisma.story_episodeWhereUniqueInput
}

/**
 * story_episode deleteMany
 */
export type story_episodeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_episodes to delete
   */
  where?: Prisma.story_episodeWhereInput
  /**
   * Limit how many story_episodes to delete.
   */
  limit?: number
}

/**
 * story_episode without action
 */
export type story_episodeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
}
