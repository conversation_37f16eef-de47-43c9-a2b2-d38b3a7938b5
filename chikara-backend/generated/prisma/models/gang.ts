
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `gang` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model gang
 * 
 */
export type gangModel = runtime.Types.Result.DefaultSelection<Prisma.$gangPayload>

export type AggregateGang = {
  _count: GangCountAggregateOutputType | null
  _avg: GangAvgAggregateOutputType | null
  _sum: GangSumAggregateOutputType | null
  _min: GangMinAggregateOutputType | null
  _max: GangMaxAggregateOutputType | null
}

export type GangAvgAggregateOutputType = {
  id: number | null
  treasury_balance: number | null
  hideout_level: number | null
  materialsResource: number | null
  essenceResource: number | null
  dailyEssenceGained: number | null
  toolsResource: number | null
  techResource: number | null
  weeklyRespect: number | null
  totalRespect: number | null
  ownerId: number | null
}

export type GangSumAggregateOutputType = {
  id: number | null
  treasury_balance: number | null
  hideout_level: number | null
  materialsResource: number | null
  essenceResource: number | null
  dailyEssenceGained: number | null
  toolsResource: number | null
  techResource: number | null
  weeklyRespect: number | null
  totalRespect: number | null
  ownerId: number | null
}

export type GangMinAggregateOutputType = {
  id: number | null
  name: string | null
  about: string | null
  avatar: string | null
  treasury_balance: number | null
  hideout_level: number | null
  materialsResource: number | null
  essenceResource: number | null
  dailyEssenceGained: number | null
  toolsResource: number | null
  techResource: number | null
  weeklyRespect: number | null
  totalRespect: number | null
  gangMOTD: string | null
  createdAt: Date | null
  updatedAt: Date | null
  ownerId: number | null
}

export type GangMaxAggregateOutputType = {
  id: number | null
  name: string | null
  about: string | null
  avatar: string | null
  treasury_balance: number | null
  hideout_level: number | null
  materialsResource: number | null
  essenceResource: number | null
  dailyEssenceGained: number | null
  toolsResource: number | null
  techResource: number | null
  weeklyRespect: number | null
  totalRespect: number | null
  gangMOTD: string | null
  createdAt: Date | null
  updatedAt: Date | null
  ownerId: number | null
}

export type GangCountAggregateOutputType = {
  id: number
  name: number
  about: number
  avatar: number
  treasury_balance: number
  hideout_level: number
  materialsResource: number
  essenceResource: number
  dailyEssenceGained: number
  toolsResource: number
  techResource: number
  weeklyRespect: number
  totalRespect: number
  gangMOTD: number
  createdAt: number
  updatedAt: number
  ownerId: number
  _all: number
}


export type GangAvgAggregateInputType = {
  id?: true
  treasury_balance?: true
  hideout_level?: true
  materialsResource?: true
  essenceResource?: true
  dailyEssenceGained?: true
  toolsResource?: true
  techResource?: true
  weeklyRespect?: true
  totalRespect?: true
  ownerId?: true
}

export type GangSumAggregateInputType = {
  id?: true
  treasury_balance?: true
  hideout_level?: true
  materialsResource?: true
  essenceResource?: true
  dailyEssenceGained?: true
  toolsResource?: true
  techResource?: true
  weeklyRespect?: true
  totalRespect?: true
  ownerId?: true
}

export type GangMinAggregateInputType = {
  id?: true
  name?: true
  about?: true
  avatar?: true
  treasury_balance?: true
  hideout_level?: true
  materialsResource?: true
  essenceResource?: true
  dailyEssenceGained?: true
  toolsResource?: true
  techResource?: true
  weeklyRespect?: true
  totalRespect?: true
  gangMOTD?: true
  createdAt?: true
  updatedAt?: true
  ownerId?: true
}

export type GangMaxAggregateInputType = {
  id?: true
  name?: true
  about?: true
  avatar?: true
  treasury_balance?: true
  hideout_level?: true
  materialsResource?: true
  essenceResource?: true
  dailyEssenceGained?: true
  toolsResource?: true
  techResource?: true
  weeklyRespect?: true
  totalRespect?: true
  gangMOTD?: true
  createdAt?: true
  updatedAt?: true
  ownerId?: true
}

export type GangCountAggregateInputType = {
  id?: true
  name?: true
  about?: true
  avatar?: true
  treasury_balance?: true
  hideout_level?: true
  materialsResource?: true
  essenceResource?: true
  dailyEssenceGained?: true
  toolsResource?: true
  techResource?: true
  weeklyRespect?: true
  totalRespect?: true
  gangMOTD?: true
  createdAt?: true
  updatedAt?: true
  ownerId?: true
  _all?: true
}

export type GangAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang to aggregate.
   */
  where?: Prisma.gangWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gangs to fetch.
   */
  orderBy?: Prisma.gangOrderByWithRelationInput | Prisma.gangOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.gangWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gangs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gangs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned gangs
  **/
  _count?: true | GangCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: GangAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: GangSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: GangMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: GangMaxAggregateInputType
}

export type GetGangAggregateType<T extends GangAggregateArgs> = {
      [P in keyof T & keyof AggregateGang]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGang[P]>
    : Prisma.GetScalarType<T[P], AggregateGang[P]>
}




export type gangGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gangWhereInput
  orderBy?: Prisma.gangOrderByWithAggregationInput | Prisma.gangOrderByWithAggregationInput[]
  by: Prisma.GangScalarFieldEnum[] | Prisma.GangScalarFieldEnum
  having?: Prisma.gangScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: GangCountAggregateInputType | true
  _avg?: GangAvgAggregateInputType
  _sum?: GangSumAggregateInputType
  _min?: GangMinAggregateInputType
  _max?: GangMaxAggregateInputType
}

export type GangGroupByOutputType = {
  id: number
  name: string
  about: string | null
  avatar: string | null
  treasury_balance: number | null
  hideout_level: number | null
  materialsResource: number | null
  essenceResource: number | null
  dailyEssenceGained: number | null
  toolsResource: number | null
  techResource: number | null
  weeklyRespect: number | null
  totalRespect: number | null
  gangMOTD: string | null
  createdAt: Date
  updatedAt: Date
  ownerId: number
  _count: GangCountAggregateOutputType | null
  _avg: GangAvgAggregateOutputType | null
  _sum: GangSumAggregateOutputType | null
  _min: GangMinAggregateOutputType | null
  _max: GangMaxAggregateOutputType | null
}

type GetGangGroupByPayload<T extends gangGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<GangGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof GangGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], GangGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], GangGroupByOutputType[P]>
      }
    >
  >



export type gangWhereInput = {
  AND?: Prisma.gangWhereInput | Prisma.gangWhereInput[]
  OR?: Prisma.gangWhereInput[]
  NOT?: Prisma.gangWhereInput | Prisma.gangWhereInput[]
  id?: Prisma.IntFilter<"gang"> | number
  name?: Prisma.StringFilter<"gang"> | string
  about?: Prisma.StringNullableFilter<"gang"> | string | null
  avatar?: Prisma.StringNullableFilter<"gang"> | string | null
  treasury_balance?: Prisma.IntNullableFilter<"gang"> | number | null
  hideout_level?: Prisma.IntNullableFilter<"gang"> | number | null
  materialsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  essenceResource?: Prisma.IntNullableFilter<"gang"> | number | null
  dailyEssenceGained?: Prisma.IntNullableFilter<"gang"> | number | null
  toolsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  techResource?: Prisma.IntNullableFilter<"gang"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  totalRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  gangMOTD?: Prisma.StringNullableFilter<"gang"> | string | null
  createdAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  ownerId?: Prisma.IntFilter<"gang"> | number
  chat_room?: Prisma.Chat_roomListRelationFilter
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  gang_invite?: Prisma.Gang_inviteListRelationFilter
  gang_log?: Prisma.Gang_logListRelationFilter
  gang_member?: Prisma.Gang_memberListRelationFilter
  members?: Prisma.UserListRelationFilter
}

export type gangOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  about?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  treasury_balance?: Prisma.SortOrderInput | Prisma.SortOrder
  hideout_level?: Prisma.SortOrderInput | Prisma.SortOrder
  materialsResource?: Prisma.SortOrderInput | Prisma.SortOrder
  essenceResource?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrderInput | Prisma.SortOrder
  toolsResource?: Prisma.SortOrderInput | Prisma.SortOrder
  techResource?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  totalRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  gangMOTD?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
  chat_room?: Prisma.chat_roomOrderByRelationAggregateInput
  user?: Prisma.userOrderByWithRelationInput
  gang_invite?: Prisma.gang_inviteOrderByRelationAggregateInput
  gang_log?: Prisma.gang_logOrderByRelationAggregateInput
  gang_member?: Prisma.gang_memberOrderByRelationAggregateInput
  members?: Prisma.userOrderByRelationAggregateInput
  _relevance?: Prisma.gangOrderByRelevanceInput
}

export type gangWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.gangWhereInput | Prisma.gangWhereInput[]
  OR?: Prisma.gangWhereInput[]
  NOT?: Prisma.gangWhereInput | Prisma.gangWhereInput[]
  name?: Prisma.StringFilter<"gang"> | string
  about?: Prisma.StringNullableFilter<"gang"> | string | null
  avatar?: Prisma.StringNullableFilter<"gang"> | string | null
  treasury_balance?: Prisma.IntNullableFilter<"gang"> | number | null
  hideout_level?: Prisma.IntNullableFilter<"gang"> | number | null
  materialsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  essenceResource?: Prisma.IntNullableFilter<"gang"> | number | null
  dailyEssenceGained?: Prisma.IntNullableFilter<"gang"> | number | null
  toolsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  techResource?: Prisma.IntNullableFilter<"gang"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  totalRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  gangMOTD?: Prisma.StringNullableFilter<"gang"> | string | null
  createdAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  ownerId?: Prisma.IntFilter<"gang"> | number
  chat_room?: Prisma.Chat_roomListRelationFilter
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  gang_invite?: Prisma.Gang_inviteListRelationFilter
  gang_log?: Prisma.Gang_logListRelationFilter
  gang_member?: Prisma.Gang_memberListRelationFilter
  members?: Prisma.UserListRelationFilter
}, "id">

export type gangOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  about?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  treasury_balance?: Prisma.SortOrderInput | Prisma.SortOrder
  hideout_level?: Prisma.SortOrderInput | Prisma.SortOrder
  materialsResource?: Prisma.SortOrderInput | Prisma.SortOrder
  essenceResource?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrderInput | Prisma.SortOrder
  toolsResource?: Prisma.SortOrderInput | Prisma.SortOrder
  techResource?: Prisma.SortOrderInput | Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  totalRespect?: Prisma.SortOrderInput | Prisma.SortOrder
  gangMOTD?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
  _count?: Prisma.gangCountOrderByAggregateInput
  _avg?: Prisma.gangAvgOrderByAggregateInput
  _max?: Prisma.gangMaxOrderByAggregateInput
  _min?: Prisma.gangMinOrderByAggregateInput
  _sum?: Prisma.gangSumOrderByAggregateInput
}

export type gangScalarWhereWithAggregatesInput = {
  AND?: Prisma.gangScalarWhereWithAggregatesInput | Prisma.gangScalarWhereWithAggregatesInput[]
  OR?: Prisma.gangScalarWhereWithAggregatesInput[]
  NOT?: Prisma.gangScalarWhereWithAggregatesInput | Prisma.gangScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"gang"> | number
  name?: Prisma.StringWithAggregatesFilter<"gang"> | string
  about?: Prisma.StringNullableWithAggregatesFilter<"gang"> | string | null
  avatar?: Prisma.StringNullableWithAggregatesFilter<"gang"> | string | null
  treasury_balance?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  hideout_level?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  materialsResource?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  essenceResource?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  dailyEssenceGained?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  toolsResource?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  techResource?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  weeklyRespect?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  totalRespect?: Prisma.IntNullableWithAggregatesFilter<"gang"> | number | null
  gangMOTD?: Prisma.StringNullableWithAggregatesFilter<"gang"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"gang"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"gang"> | Date | string
  ownerId?: Prisma.IntWithAggregatesFilter<"gang"> | number
}

export type gangCreateInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangCreateManyInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
}

export type gangUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type gangUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type GangNullableScalarRelationFilter = {
  is?: Prisma.gangWhereInput | null
  isNot?: Prisma.gangWhereInput | null
}

export type gangOrderByRelevanceInput = {
  fields: Prisma.gangOrderByRelevanceFieldEnum | Prisma.gangOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type gangCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  about?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  treasury_balance?: Prisma.SortOrder
  hideout_level?: Prisma.SortOrder
  materialsResource?: Prisma.SortOrder
  essenceResource?: Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrder
  toolsResource?: Prisma.SortOrder
  techResource?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalRespect?: Prisma.SortOrder
  gangMOTD?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
}

export type gangAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  treasury_balance?: Prisma.SortOrder
  hideout_level?: Prisma.SortOrder
  materialsResource?: Prisma.SortOrder
  essenceResource?: Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrder
  toolsResource?: Prisma.SortOrder
  techResource?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalRespect?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
}

export type gangMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  about?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  treasury_balance?: Prisma.SortOrder
  hideout_level?: Prisma.SortOrder
  materialsResource?: Prisma.SortOrder
  essenceResource?: Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrder
  toolsResource?: Prisma.SortOrder
  techResource?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalRespect?: Prisma.SortOrder
  gangMOTD?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
}

export type gangMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  about?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  treasury_balance?: Prisma.SortOrder
  hideout_level?: Prisma.SortOrder
  materialsResource?: Prisma.SortOrder
  essenceResource?: Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrder
  toolsResource?: Prisma.SortOrder
  techResource?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalRespect?: Prisma.SortOrder
  gangMOTD?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
}

export type gangSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  treasury_balance?: Prisma.SortOrder
  hideout_level?: Prisma.SortOrder
  materialsResource?: Prisma.SortOrder
  essenceResource?: Prisma.SortOrder
  dailyEssenceGained?: Prisma.SortOrder
  toolsResource?: Prisma.SortOrder
  techResource?: Prisma.SortOrder
  weeklyRespect?: Prisma.SortOrder
  totalRespect?: Prisma.SortOrder
  ownerId?: Prisma.SortOrder
}

export type GangListRelationFilter = {
  every?: Prisma.gangWhereInput
  some?: Prisma.gangWhereInput
  none?: Prisma.gangWhereInput
}

export type gangOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type gangCreateNestedOneWithoutChat_roomInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutChat_roomInput, Prisma.gangUncheckedCreateWithoutChat_roomInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutChat_roomInput
  connect?: Prisma.gangWhereUniqueInput
}

export type gangUpdateOneWithoutChat_roomNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutChat_roomInput, Prisma.gangUncheckedCreateWithoutChat_roomInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutChat_roomInput
  upsert?: Prisma.gangUpsertWithoutChat_roomInput
  disconnect?: Prisma.gangWhereInput | boolean
  delete?: Prisma.gangWhereInput | boolean
  connect?: Prisma.gangWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.gangUpdateToOneWithWhereWithoutChat_roomInput, Prisma.gangUpdateWithoutChat_roomInput>, Prisma.gangUncheckedUpdateWithoutChat_roomInput>
}

export type gangCreateNestedOneWithoutGang_inviteInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_inviteInput, Prisma.gangUncheckedCreateWithoutGang_inviteInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_inviteInput
  connect?: Prisma.gangWhereUniqueInput
}

export type gangUpdateOneWithoutGang_inviteNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_inviteInput, Prisma.gangUncheckedCreateWithoutGang_inviteInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_inviteInput
  upsert?: Prisma.gangUpsertWithoutGang_inviteInput
  disconnect?: Prisma.gangWhereInput | boolean
  delete?: Prisma.gangWhereInput | boolean
  connect?: Prisma.gangWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.gangUpdateToOneWithWhereWithoutGang_inviteInput, Prisma.gangUpdateWithoutGang_inviteInput>, Prisma.gangUncheckedUpdateWithoutGang_inviteInput>
}

export type gangCreateNestedOneWithoutGang_logInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_logInput, Prisma.gangUncheckedCreateWithoutGang_logInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_logInput
  connect?: Prisma.gangWhereUniqueInput
}

export type gangUpdateOneWithoutGang_logNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_logInput, Prisma.gangUncheckedCreateWithoutGang_logInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_logInput
  upsert?: Prisma.gangUpsertWithoutGang_logInput
  disconnect?: Prisma.gangWhereInput | boolean
  delete?: Prisma.gangWhereInput | boolean
  connect?: Prisma.gangWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.gangUpdateToOneWithWhereWithoutGang_logInput, Prisma.gangUpdateWithoutGang_logInput>, Prisma.gangUncheckedUpdateWithoutGang_logInput>
}

export type gangCreateNestedOneWithoutGang_memberInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_memberInput, Prisma.gangUncheckedCreateWithoutGang_memberInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_memberInput
  connect?: Prisma.gangWhereUniqueInput
}

export type gangUpdateOneWithoutGang_memberNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutGang_memberInput, Prisma.gangUncheckedCreateWithoutGang_memberInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutGang_memberInput
  upsert?: Prisma.gangUpsertWithoutGang_memberInput
  disconnect?: Prisma.gangWhereInput | boolean
  delete?: Prisma.gangWhereInput | boolean
  connect?: Prisma.gangWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.gangUpdateToOneWithWhereWithoutGang_memberInput, Prisma.gangUpdateWithoutGang_memberInput>, Prisma.gangUncheckedUpdateWithoutGang_memberInput>
}

export type gangCreateNestedOneWithoutMembersInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutMembersInput, Prisma.gangUncheckedCreateWithoutMembersInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutMembersInput
  connect?: Prisma.gangWhereUniqueInput
}

export type gangCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput> | Prisma.gangCreateWithoutUserInput[] | Prisma.gangUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutUserInput | Prisma.gangCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.gangCreateManyUserInputEnvelope
  connect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
}

export type gangUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput> | Prisma.gangCreateWithoutUserInput[] | Prisma.gangUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutUserInput | Prisma.gangCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.gangCreateManyUserInputEnvelope
  connect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
}

export type gangUpdateOneWithoutMembersNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutMembersInput, Prisma.gangUncheckedCreateWithoutMembersInput>
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutMembersInput
  upsert?: Prisma.gangUpsertWithoutMembersInput
  disconnect?: Prisma.gangWhereInput | boolean
  delete?: Prisma.gangWhereInput | boolean
  connect?: Prisma.gangWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.gangUpdateToOneWithWhereWithoutMembersInput, Prisma.gangUpdateWithoutMembersInput>, Prisma.gangUncheckedUpdateWithoutMembersInput>
}

export type gangUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput> | Prisma.gangCreateWithoutUserInput[] | Prisma.gangUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutUserInput | Prisma.gangCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.gangUpsertWithWhereUniqueWithoutUserInput | Prisma.gangUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.gangCreateManyUserInputEnvelope
  set?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  disconnect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  delete?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  connect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  update?: Prisma.gangUpdateWithWhereUniqueWithoutUserInput | Prisma.gangUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.gangUpdateManyWithWhereWithoutUserInput | Prisma.gangUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.gangScalarWhereInput | Prisma.gangScalarWhereInput[]
}

export type gangUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput> | Prisma.gangCreateWithoutUserInput[] | Prisma.gangUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.gangCreateOrConnectWithoutUserInput | Prisma.gangCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.gangUpsertWithWhereUniqueWithoutUserInput | Prisma.gangUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.gangCreateManyUserInputEnvelope
  set?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  disconnect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  delete?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  connect?: Prisma.gangWhereUniqueInput | Prisma.gangWhereUniqueInput[]
  update?: Prisma.gangUpdateWithWhereUniqueWithoutUserInput | Prisma.gangUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.gangUpdateManyWithWhereWithoutUserInput | Prisma.gangUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.gangScalarWhereInput | Prisma.gangScalarWhereInput[]
}

export type gangCreateWithoutChat_roomInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutChat_roomInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutChat_roomInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutChat_roomInput, Prisma.gangUncheckedCreateWithoutChat_roomInput>
}

export type gangUpsertWithoutChat_roomInput = {
  update: Prisma.XOR<Prisma.gangUpdateWithoutChat_roomInput, Prisma.gangUncheckedUpdateWithoutChat_roomInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutChat_roomInput, Prisma.gangUncheckedCreateWithoutChat_roomInput>
  where?: Prisma.gangWhereInput
}

export type gangUpdateToOneWithWhereWithoutChat_roomInput = {
  where?: Prisma.gangWhereInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutChat_roomInput, Prisma.gangUncheckedUpdateWithoutChat_roomInput>
}

export type gangUpdateWithoutChat_roomInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutChat_roomInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangCreateWithoutGang_inviteInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutGang_inviteInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutGang_inviteInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_inviteInput, Prisma.gangUncheckedCreateWithoutGang_inviteInput>
}

export type gangUpsertWithoutGang_inviteInput = {
  update: Prisma.XOR<Prisma.gangUpdateWithoutGang_inviteInput, Prisma.gangUncheckedUpdateWithoutGang_inviteInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_inviteInput, Prisma.gangUncheckedCreateWithoutGang_inviteInput>
  where?: Prisma.gangWhereInput
}

export type gangUpdateToOneWithWhereWithoutGang_inviteInput = {
  where?: Prisma.gangWhereInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutGang_inviteInput, Prisma.gangUncheckedUpdateWithoutGang_inviteInput>
}

export type gangUpdateWithoutGang_inviteInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutGang_inviteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangCreateWithoutGang_logInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutGang_logInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutGang_logInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_logInput, Prisma.gangUncheckedCreateWithoutGang_logInput>
}

export type gangUpsertWithoutGang_logInput = {
  update: Prisma.XOR<Prisma.gangUpdateWithoutGang_logInput, Prisma.gangUncheckedUpdateWithoutGang_logInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_logInput, Prisma.gangUncheckedCreateWithoutGang_logInput>
  where?: Prisma.gangWhereInput
}

export type gangUpdateToOneWithWhereWithoutGang_logInput = {
  where?: Prisma.gangWhereInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutGang_logInput, Prisma.gangUncheckedUpdateWithoutGang_logInput>
}

export type gangUpdateWithoutGang_logInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutGang_logInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangCreateWithoutGang_memberInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutGang_memberInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutGang_memberInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_memberInput, Prisma.gangUncheckedCreateWithoutGang_memberInput>
}

export type gangUpsertWithoutGang_memberInput = {
  update: Prisma.XOR<Prisma.gangUpdateWithoutGang_memberInput, Prisma.gangUncheckedUpdateWithoutGang_memberInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutGang_memberInput, Prisma.gangUncheckedCreateWithoutGang_memberInput>
  where?: Prisma.gangWhereInput
}

export type gangUpdateToOneWithWhereWithoutGang_memberInput = {
  where?: Prisma.gangWhereInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutGang_memberInput, Prisma.gangUncheckedUpdateWithoutGang_memberInput>
}

export type gangUpdateWithoutGang_memberInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutGang_memberInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangCreateWithoutMembersInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  user?: Prisma.userCreateNestedOneWithoutOwnedGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutMembersInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  ownerId: number
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutMembersInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutMembersInput, Prisma.gangUncheckedCreateWithoutMembersInput>
}

export type gangCreateWithoutUserInput = {
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberCreateNestedManyWithoutGangInput
  members?: Prisma.userCreateNestedManyWithoutGangInput
}

export type gangUncheckedCreateWithoutUserInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomUncheckedCreateNestedManyWithoutGangInput
  gang_invite?: Prisma.gang_inviteUncheckedCreateNestedManyWithoutGangInput
  gang_log?: Prisma.gang_logUncheckedCreateNestedManyWithoutGangInput
  gang_member?: Prisma.gang_memberUncheckedCreateNestedManyWithoutGangInput
  members?: Prisma.userUncheckedCreateNestedManyWithoutGangInput
}

export type gangCreateOrConnectWithoutUserInput = {
  where: Prisma.gangWhereUniqueInput
  create: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput>
}

export type gangCreateManyUserInputEnvelope = {
  data: Prisma.gangCreateManyUserInput | Prisma.gangCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type gangUpsertWithoutMembersInput = {
  update: Prisma.XOR<Prisma.gangUpdateWithoutMembersInput, Prisma.gangUncheckedUpdateWithoutMembersInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutMembersInput, Prisma.gangUncheckedCreateWithoutMembersInput>
  where?: Prisma.gangWhereInput
}

export type gangUpdateToOneWithWhereWithoutMembersInput = {
  where?: Prisma.gangWhereInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutMembersInput, Prisma.gangUncheckedUpdateWithoutMembersInput>
}

export type gangUpdateWithoutMembersInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  user?: Prisma.userUpdateOneWithoutOwnedGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutMembersInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ownerId?: Prisma.IntFieldUpdateOperationsInput | number
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
}

export type gangUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.gangWhereUniqueInput
  update: Prisma.XOR<Prisma.gangUpdateWithoutUserInput, Prisma.gangUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.gangCreateWithoutUserInput, Prisma.gangUncheckedCreateWithoutUserInput>
}

export type gangUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.gangWhereUniqueInput
  data: Prisma.XOR<Prisma.gangUpdateWithoutUserInput, Prisma.gangUncheckedUpdateWithoutUserInput>
}

export type gangUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.gangScalarWhereInput
  data: Prisma.XOR<Prisma.gangUpdateManyMutationInput, Prisma.gangUncheckedUpdateManyWithoutUserInput>
}

export type gangScalarWhereInput = {
  AND?: Prisma.gangScalarWhereInput | Prisma.gangScalarWhereInput[]
  OR?: Prisma.gangScalarWhereInput[]
  NOT?: Prisma.gangScalarWhereInput | Prisma.gangScalarWhereInput[]
  id?: Prisma.IntFilter<"gang"> | number
  name?: Prisma.StringFilter<"gang"> | string
  about?: Prisma.StringNullableFilter<"gang"> | string | null
  avatar?: Prisma.StringNullableFilter<"gang"> | string | null
  treasury_balance?: Prisma.IntNullableFilter<"gang"> | number | null
  hideout_level?: Prisma.IntNullableFilter<"gang"> | number | null
  materialsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  essenceResource?: Prisma.IntNullableFilter<"gang"> | number | null
  dailyEssenceGained?: Prisma.IntNullableFilter<"gang"> | number | null
  toolsResource?: Prisma.IntNullableFilter<"gang"> | number | null
  techResource?: Prisma.IntNullableFilter<"gang"> | number | null
  weeklyRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  totalRespect?: Prisma.IntNullableFilter<"gang"> | number | null
  gangMOTD?: Prisma.StringNullableFilter<"gang"> | string | null
  createdAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang"> | Date | string
  ownerId?: Prisma.IntFilter<"gang"> | number
}

export type gangCreateManyUserInput = {
  id?: number
  name: string
  about?: string | null
  avatar?: string | null
  treasury_balance?: number | null
  hideout_level?: number | null
  materialsResource?: number | null
  essenceResource?: number | null
  dailyEssenceGained?: number | null
  toolsResource?: number | null
  techResource?: number | null
  weeklyRespect?: number | null
  totalRespect?: number | null
  gangMOTD?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type gangUpdateWithoutUserInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUpdateManyWithoutGangNestedInput
  members?: Prisma.userUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUncheckedUpdateManyWithoutGangNestedInput
  gang_invite?: Prisma.gang_inviteUncheckedUpdateManyWithoutGangNestedInput
  gang_log?: Prisma.gang_logUncheckedUpdateManyWithoutGangNestedInput
  gang_member?: Prisma.gang_memberUncheckedUpdateManyWithoutGangNestedInput
  members?: Prisma.userUncheckedUpdateManyWithoutGangNestedInput
}

export type gangUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  treasury_balance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  hideout_level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  materialsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  essenceResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyEssenceGained?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  toolsResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  techResource?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  weeklyRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalRespect?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  gangMOTD?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type GangCountOutputType
 */

export type GangCountOutputType = {
  chat_room: number
  gang_invite: number
  gang_log: number
  gang_member: number
  members: number
}

export type GangCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  chat_room?: boolean | GangCountOutputTypeCountChat_roomArgs
  gang_invite?: boolean | GangCountOutputTypeCountGang_inviteArgs
  gang_log?: boolean | GangCountOutputTypeCountGang_logArgs
  gang_member?: boolean | GangCountOutputTypeCountGang_memberArgs
  members?: boolean | GangCountOutputTypeCountMembersArgs
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GangCountOutputType
   */
  select?: Prisma.GangCountOutputTypeSelect<ExtArgs> | null
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeCountChat_roomArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.chat_roomWhereInput
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeCountGang_inviteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_inviteWhereInput
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeCountGang_logArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_logWhereInput
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeCountGang_memberArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_memberWhereInput
}

/**
 * GangCountOutputType without action
 */
export type GangCountOutputTypeCountMembersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.userWhereInput
}


export type gangSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  about?: boolean
  avatar?: boolean
  treasury_balance?: boolean
  hideout_level?: boolean
  materialsResource?: boolean
  essenceResource?: boolean
  dailyEssenceGained?: boolean
  toolsResource?: boolean
  techResource?: boolean
  weeklyRespect?: boolean
  totalRespect?: boolean
  gangMOTD?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ownerId?: boolean
  chat_room?: boolean | Prisma.gang$chat_roomArgs<ExtArgs>
  user?: boolean | Prisma.gang$userArgs<ExtArgs>
  gang_invite?: boolean | Prisma.gang$gang_inviteArgs<ExtArgs>
  gang_log?: boolean | Prisma.gang$gang_logArgs<ExtArgs>
  gang_member?: boolean | Prisma.gang$gang_memberArgs<ExtArgs>
  members?: boolean | Prisma.gang$membersArgs<ExtArgs>
  _count?: boolean | Prisma.GangCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["gang"]>



export type gangSelectScalar = {
  id?: boolean
  name?: boolean
  about?: boolean
  avatar?: boolean
  treasury_balance?: boolean
  hideout_level?: boolean
  materialsResource?: boolean
  essenceResource?: boolean
  dailyEssenceGained?: boolean
  toolsResource?: boolean
  techResource?: boolean
  weeklyRespect?: boolean
  totalRespect?: boolean
  gangMOTD?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ownerId?: boolean
}

export type gangOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "about" | "avatar" | "treasury_balance" | "hideout_level" | "materialsResource" | "essenceResource" | "dailyEssenceGained" | "toolsResource" | "techResource" | "weeklyRespect" | "totalRespect" | "gangMOTD" | "createdAt" | "updatedAt" | "ownerId", ExtArgs["result"]["gang"]>
export type gangInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  chat_room?: boolean | Prisma.gang$chat_roomArgs<ExtArgs>
  user?: boolean | Prisma.gang$userArgs<ExtArgs>
  gang_invite?: boolean | Prisma.gang$gang_inviteArgs<ExtArgs>
  gang_log?: boolean | Prisma.gang$gang_logArgs<ExtArgs>
  gang_member?: boolean | Prisma.gang$gang_memberArgs<ExtArgs>
  members?: boolean | Prisma.gang$membersArgs<ExtArgs>
  _count?: boolean | Prisma.GangCountOutputTypeDefaultArgs<ExtArgs>
}

export type $gangPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "gang"
  objects: {
    chat_room: Prisma.$chat_roomPayload<ExtArgs>[]
    user: Prisma.$userPayload<ExtArgs> | null
    gang_invite: Prisma.$gang_invitePayload<ExtArgs>[]
    gang_log: Prisma.$gang_logPayload<ExtArgs>[]
    gang_member: Prisma.$gang_memberPayload<ExtArgs>[]
    members: Prisma.$userPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    about: string | null
    avatar: string | null
    treasury_balance: number | null
    hideout_level: number | null
    materialsResource: number | null
    essenceResource: number | null
    dailyEssenceGained: number | null
    toolsResource: number | null
    techResource: number | null
    weeklyRespect: number | null
    totalRespect: number | null
    gangMOTD: string | null
    createdAt: Date
    updatedAt: Date
    ownerId: number
  }, ExtArgs["result"]["gang"]>
  composites: {}
}

export type gangGetPayload<S extends boolean | null | undefined | gangDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$gangPayload, S>

export type gangCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<gangFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: GangCountAggregateInputType | true
  }

export interface gangDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['gang'], meta: { name: 'gang' } }
  /**
   * Find zero or one Gang that matches the filter.
   * @param {gangFindUniqueArgs} args - Arguments to find a Gang
   * @example
   * // Get one Gang
   * const gang = await prisma.gang.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends gangFindUniqueArgs>(args: Prisma.SelectSubset<T, gangFindUniqueArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Gang that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {gangFindUniqueOrThrowArgs} args - Arguments to find a Gang
   * @example
   * // Get one Gang
   * const gang = await prisma.gang.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends gangFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, gangFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangFindFirstArgs} args - Arguments to find a Gang
   * @example
   * // Get one Gang
   * const gang = await prisma.gang.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends gangFindFirstArgs>(args?: Prisma.SelectSubset<T, gangFindFirstArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangFindFirstOrThrowArgs} args - Arguments to find a Gang
   * @example
   * // Get one Gang
   * const gang = await prisma.gang.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends gangFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, gangFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Gangs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Gangs
   * const gangs = await prisma.gang.findMany()
   * 
   * // Get first 10 Gangs
   * const gangs = await prisma.gang.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const gangWithIdOnly = await prisma.gang.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends gangFindManyArgs>(args?: Prisma.SelectSubset<T, gangFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Gang.
   * @param {gangCreateArgs} args - Arguments to create a Gang.
   * @example
   * // Create one Gang
   * const Gang = await prisma.gang.create({
   *   data: {
   *     // ... data to create a Gang
   *   }
   * })
   * 
   */
  create<T extends gangCreateArgs>(args: Prisma.SelectSubset<T, gangCreateArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Gangs.
   * @param {gangCreateManyArgs} args - Arguments to create many Gangs.
   * @example
   * // Create many Gangs
   * const gang = await prisma.gang.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends gangCreateManyArgs>(args?: Prisma.SelectSubset<T, gangCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Gang.
   * @param {gangDeleteArgs} args - Arguments to delete one Gang.
   * @example
   * // Delete one Gang
   * const Gang = await prisma.gang.delete({
   *   where: {
   *     // ... filter to delete one Gang
   *   }
   * })
   * 
   */
  delete<T extends gangDeleteArgs>(args: Prisma.SelectSubset<T, gangDeleteArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Gang.
   * @param {gangUpdateArgs} args - Arguments to update one Gang.
   * @example
   * // Update one Gang
   * const gang = await prisma.gang.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends gangUpdateArgs>(args: Prisma.SelectSubset<T, gangUpdateArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Gangs.
   * @param {gangDeleteManyArgs} args - Arguments to filter Gangs to delete.
   * @example
   * // Delete a few Gangs
   * const { count } = await prisma.gang.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends gangDeleteManyArgs>(args?: Prisma.SelectSubset<T, gangDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Gangs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Gangs
   * const gang = await prisma.gang.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends gangUpdateManyArgs>(args: Prisma.SelectSubset<T, gangUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Gang.
   * @param {gangUpsertArgs} args - Arguments to update or create a Gang.
   * @example
   * // Update or create a Gang
   * const gang = await prisma.gang.upsert({
   *   create: {
   *     // ... data to create a Gang
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Gang we want to update
   *   }
   * })
   */
  upsert<T extends gangUpsertArgs>(args: Prisma.SelectSubset<T, gangUpsertArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Gangs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangCountArgs} args - Arguments to filter Gangs to count.
   * @example
   * // Count the number of Gangs
   * const count = await prisma.gang.count({
   *   where: {
   *     // ... the filter for the Gangs we want to count
   *   }
   * })
  **/
  count<T extends gangCountArgs>(
    args?: Prisma.Subset<T, gangCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], GangCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Gang.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GangAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends GangAggregateArgs>(args: Prisma.Subset<T, GangAggregateArgs>): Prisma.PrismaPromise<GetGangAggregateType<T>>

  /**
   * Group by Gang.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gangGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends gangGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: gangGroupByArgs['orderBy'] }
      : { orderBy?: gangGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, gangGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGangGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the gang model
 */
readonly fields: gangFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for gang.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__gangClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  chat_room<T extends Prisma.gang$chat_roomArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$chat_roomArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user<T extends Prisma.gang$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  gang_invite<T extends Prisma.gang$gang_inviteArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$gang_inviteArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  gang_log<T extends Prisma.gang$gang_logArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$gang_logArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_logPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  gang_member<T extends Prisma.gang$gang_memberArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$gang_memberArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_memberPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  members<T extends Prisma.gang$membersArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang$membersArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the gang model
 */
export interface gangFieldRefs {
  readonly id: Prisma.FieldRef<"gang", 'Int'>
  readonly name: Prisma.FieldRef<"gang", 'String'>
  readonly about: Prisma.FieldRef<"gang", 'String'>
  readonly avatar: Prisma.FieldRef<"gang", 'String'>
  readonly treasury_balance: Prisma.FieldRef<"gang", 'Int'>
  readonly hideout_level: Prisma.FieldRef<"gang", 'Int'>
  readonly materialsResource: Prisma.FieldRef<"gang", 'Int'>
  readonly essenceResource: Prisma.FieldRef<"gang", 'Int'>
  readonly dailyEssenceGained: Prisma.FieldRef<"gang", 'Int'>
  readonly toolsResource: Prisma.FieldRef<"gang", 'Int'>
  readonly techResource: Prisma.FieldRef<"gang", 'Int'>
  readonly weeklyRespect: Prisma.FieldRef<"gang", 'Int'>
  readonly totalRespect: Prisma.FieldRef<"gang", 'Int'>
  readonly gangMOTD: Prisma.FieldRef<"gang", 'String'>
  readonly createdAt: Prisma.FieldRef<"gang", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"gang", 'DateTime'>
  readonly ownerId: Prisma.FieldRef<"gang", 'Int'>
}
    

// Custom InputTypes
/**
 * gang findUnique
 */
export type gangFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter, which gang to fetch.
   */
  where: Prisma.gangWhereUniqueInput
}

/**
 * gang findUniqueOrThrow
 */
export type gangFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter, which gang to fetch.
   */
  where: Prisma.gangWhereUniqueInput
}

/**
 * gang findFirst
 */
export type gangFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter, which gang to fetch.
   */
  where?: Prisma.gangWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gangs to fetch.
   */
  orderBy?: Prisma.gangOrderByWithRelationInput | Prisma.gangOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gangs.
   */
  cursor?: Prisma.gangWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gangs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gangs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gangs.
   */
  distinct?: Prisma.GangScalarFieldEnum | Prisma.GangScalarFieldEnum[]
}

/**
 * gang findFirstOrThrow
 */
export type gangFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter, which gang to fetch.
   */
  where?: Prisma.gangWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gangs to fetch.
   */
  orderBy?: Prisma.gangOrderByWithRelationInput | Prisma.gangOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gangs.
   */
  cursor?: Prisma.gangWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gangs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gangs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gangs.
   */
  distinct?: Prisma.GangScalarFieldEnum | Prisma.GangScalarFieldEnum[]
}

/**
 * gang findMany
 */
export type gangFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter, which gangs to fetch.
   */
  where?: Prisma.gangWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gangs to fetch.
   */
  orderBy?: Prisma.gangOrderByWithRelationInput | Prisma.gangOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing gangs.
   */
  cursor?: Prisma.gangWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gangs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gangs.
   */
  skip?: number
  distinct?: Prisma.GangScalarFieldEnum | Prisma.GangScalarFieldEnum[]
}

/**
 * gang create
 */
export type gangCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * The data needed to create a gang.
   */
  data: Prisma.XOR<Prisma.gangCreateInput, Prisma.gangUncheckedCreateInput>
}

/**
 * gang createMany
 */
export type gangCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many gangs.
   */
  data: Prisma.gangCreateManyInput | Prisma.gangCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * gang update
 */
export type gangUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * The data needed to update a gang.
   */
  data: Prisma.XOR<Prisma.gangUpdateInput, Prisma.gangUncheckedUpdateInput>
  /**
   * Choose, which gang to update.
   */
  where: Prisma.gangWhereUniqueInput
}

/**
 * gang updateMany
 */
export type gangUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update gangs.
   */
  data: Prisma.XOR<Prisma.gangUpdateManyMutationInput, Prisma.gangUncheckedUpdateManyInput>
  /**
   * Filter which gangs to update
   */
  where?: Prisma.gangWhereInput
  /**
   * Limit how many gangs to update.
   */
  limit?: number
}

/**
 * gang upsert
 */
export type gangUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * The filter to search for the gang to update in case it exists.
   */
  where: Prisma.gangWhereUniqueInput
  /**
   * In case the gang found by the `where` argument doesn't exist, create a new gang with this data.
   */
  create: Prisma.XOR<Prisma.gangCreateInput, Prisma.gangUncheckedCreateInput>
  /**
   * In case the gang was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.gangUpdateInput, Prisma.gangUncheckedUpdateInput>
}

/**
 * gang delete
 */
export type gangDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  /**
   * Filter which gang to delete.
   */
  where: Prisma.gangWhereUniqueInput
}

/**
 * gang deleteMany
 */
export type gangDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gangs to delete
   */
  where?: Prisma.gangWhereInput
  /**
   * Limit how many gangs to delete.
   */
  limit?: number
}

/**
 * gang.chat_room
 */
export type gang$chat_roomArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  where?: Prisma.chat_roomWhereInput
  orderBy?: Prisma.chat_roomOrderByWithRelationInput | Prisma.chat_roomOrderByWithRelationInput[]
  cursor?: Prisma.chat_roomWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Chat_roomScalarFieldEnum | Prisma.Chat_roomScalarFieldEnum[]
}

/**
 * gang.user
 */
export type gang$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang.gang_invite
 */
export type gang$gang_inviteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  where?: Prisma.gang_inviteWhereInput
  orderBy?: Prisma.gang_inviteOrderByWithRelationInput | Prisma.gang_inviteOrderByWithRelationInput[]
  cursor?: Prisma.gang_inviteWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Gang_inviteScalarFieldEnum | Prisma.Gang_inviteScalarFieldEnum[]
}

/**
 * gang.gang_log
 */
export type gang$gang_logArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_log
   */
  select?: Prisma.gang_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_log
   */
  omit?: Prisma.gang_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_logInclude<ExtArgs> | null
  where?: Prisma.gang_logWhereInput
  orderBy?: Prisma.gang_logOrderByWithRelationInput | Prisma.gang_logOrderByWithRelationInput[]
  cursor?: Prisma.gang_logWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Gang_logScalarFieldEnum | Prisma.Gang_logScalarFieldEnum[]
}

/**
 * gang.gang_member
 */
export type gang$gang_memberArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_member
   */
  select?: Prisma.gang_memberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_member
   */
  omit?: Prisma.gang_memberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_memberInclude<ExtArgs> | null
  where?: Prisma.gang_memberWhereInput
  orderBy?: Prisma.gang_memberOrderByWithRelationInput | Prisma.gang_memberOrderByWithRelationInput[]
  cursor?: Prisma.gang_memberWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Gang_memberScalarFieldEnum | Prisma.Gang_memberScalarFieldEnum[]
}

/**
 * gang.members
 */
export type gang$membersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
  orderBy?: Prisma.userOrderByWithRelationInput | Prisma.userOrderByWithRelationInput[]
  cursor?: Prisma.userWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * gang without action
 */
export type gangDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
}
