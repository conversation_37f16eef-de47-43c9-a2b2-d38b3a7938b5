
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `story_chapter` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model story_chapter
 * 
 */
export type story_chapterModel = runtime.Types.Result.DefaultSelection<Prisma.$story_chapterPayload>

export type AggregateStory_chapter = {
  _count: Story_chapterCountAggregateOutputType | null
  _avg: Story_chapterAvgAggregateOutputType | null
  _sum: Story_chapterSumAggregateOutputType | null
  _min: Story_chapterMinAggregateOutputType | null
  _max: Story_chapterMaxAggregateOutputType | null
}

export type Story_chapterAvgAggregateOutputType = {
  id: number | null
  seasonId: number | null
  order: number | null
  requiredLevel: number | null
}

export type Story_chapterSumAggregateOutputType = {
  id: number | null
  seasonId: number | null
  order: number | null
  requiredLevel: number | null
}

export type Story_chapterMinAggregateOutputType = {
  id: number | null
  seasonId: number | null
  name: string | null
  description: string | null
  order: number | null
  unlockDate: Date | null
  requiredLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_chapterMaxAggregateOutputType = {
  id: number | null
  seasonId: number | null
  name: string | null
  description: string | null
  order: number | null
  unlockDate: Date | null
  requiredLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_chapterCountAggregateOutputType = {
  id: number
  seasonId: number
  name: number
  description: number
  order: number
  unlockDate: number
  requiredLevel: number
  requiredChapterIds: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Story_chapterAvgAggregateInputType = {
  id?: true
  seasonId?: true
  order?: true
  requiredLevel?: true
}

export type Story_chapterSumAggregateInputType = {
  id?: true
  seasonId?: true
  order?: true
  requiredLevel?: true
}

export type Story_chapterMinAggregateInputType = {
  id?: true
  seasonId?: true
  name?: true
  description?: true
  order?: true
  unlockDate?: true
  requiredLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_chapterMaxAggregateInputType = {
  id?: true
  seasonId?: true
  name?: true
  description?: true
  order?: true
  unlockDate?: true
  requiredLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_chapterCountAggregateInputType = {
  id?: true
  seasonId?: true
  name?: true
  description?: true
  order?: true
  unlockDate?: true
  requiredLevel?: true
  requiredChapterIds?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Story_chapterAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_chapter to aggregate.
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_chapters to fetch.
   */
  orderBy?: Prisma.story_chapterOrderByWithRelationInput | Prisma.story_chapterOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.story_chapterWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_chapters from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_chapters.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned story_chapters
  **/
  _count?: true | Story_chapterCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Story_chapterAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Story_chapterSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Story_chapterMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Story_chapterMaxAggregateInputType
}

export type GetStory_chapterAggregateType<T extends Story_chapterAggregateArgs> = {
      [P in keyof T & keyof AggregateStory_chapter]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStory_chapter[P]>
    : Prisma.GetScalarType<T[P], AggregateStory_chapter[P]>
}




export type story_chapterGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.story_chapterWhereInput
  orderBy?: Prisma.story_chapterOrderByWithAggregationInput | Prisma.story_chapterOrderByWithAggregationInput[]
  by: Prisma.Story_chapterScalarFieldEnum[] | Prisma.Story_chapterScalarFieldEnum
  having?: Prisma.story_chapterScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Story_chapterCountAggregateInputType | true
  _avg?: Story_chapterAvgAggregateInputType
  _sum?: Story_chapterSumAggregateInputType
  _min?: Story_chapterMinAggregateInputType
  _max?: Story_chapterMaxAggregateInputType
}

export type Story_chapterGroupByOutputType = {
  id: number
  seasonId: number
  name: string
  description: string | null
  order: number
  unlockDate: Date
  requiredLevel: number
  requiredChapterIds: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  _count: Story_chapterCountAggregateOutputType | null
  _avg: Story_chapterAvgAggregateOutputType | null
  _sum: Story_chapterSumAggregateOutputType | null
  _min: Story_chapterMinAggregateOutputType | null
  _max: Story_chapterMaxAggregateOutputType | null
}

type GetStory_chapterGroupByPayload<T extends story_chapterGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Story_chapterGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Story_chapterGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Story_chapterGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Story_chapterGroupByOutputType[P]>
      }
    >
  >



export type story_chapterWhereInput = {
  AND?: Prisma.story_chapterWhereInput | Prisma.story_chapterWhereInput[]
  OR?: Prisma.story_chapterWhereInput[]
  NOT?: Prisma.story_chapterWhereInput | Prisma.story_chapterWhereInput[]
  id?: Prisma.IntFilter<"story_chapter"> | number
  seasonId?: Prisma.IntFilter<"story_chapter"> | number
  name?: Prisma.StringFilter<"story_chapter"> | string
  description?: Prisma.StringNullableFilter<"story_chapter"> | string | null
  order?: Prisma.IntFilter<"story_chapter"> | number
  unlockDate?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  requiredLevel?: Prisma.IntFilter<"story_chapter"> | number
  requiredChapterIds?: Prisma.JsonNullableFilter<"story_chapter">
  createdAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  story_season?: Prisma.XOR<Prisma.Story_seasonScalarRelationFilter, Prisma.story_seasonWhereInput>
  quests?: Prisma.QuestListRelationFilter
}

export type story_chapterOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  order?: Prisma.SortOrder
  unlockDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  requiredChapterIds?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  story_season?: Prisma.story_seasonOrderByWithRelationInput
  quests?: Prisma.questOrderByRelationAggregateInput
  _relevance?: Prisma.story_chapterOrderByRelevanceInput
}

export type story_chapterWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  seasonId_order?: Prisma.story_chapterSeasonIdOrderCompoundUniqueInput
  AND?: Prisma.story_chapterWhereInput | Prisma.story_chapterWhereInput[]
  OR?: Prisma.story_chapterWhereInput[]
  NOT?: Prisma.story_chapterWhereInput | Prisma.story_chapterWhereInput[]
  seasonId?: Prisma.IntFilter<"story_chapter"> | number
  name?: Prisma.StringFilter<"story_chapter"> | string
  description?: Prisma.StringNullableFilter<"story_chapter"> | string | null
  order?: Prisma.IntFilter<"story_chapter"> | number
  unlockDate?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  requiredLevel?: Prisma.IntFilter<"story_chapter"> | number
  requiredChapterIds?: Prisma.JsonNullableFilter<"story_chapter">
  createdAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  story_season?: Prisma.XOR<Prisma.Story_seasonScalarRelationFilter, Prisma.story_seasonWhereInput>
  quests?: Prisma.QuestListRelationFilter
}, "id" | "seasonId_order">

export type story_chapterOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  order?: Prisma.SortOrder
  unlockDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  requiredChapterIds?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.story_chapterCountOrderByAggregateInput
  _avg?: Prisma.story_chapterAvgOrderByAggregateInput
  _max?: Prisma.story_chapterMaxOrderByAggregateInput
  _min?: Prisma.story_chapterMinOrderByAggregateInput
  _sum?: Prisma.story_chapterSumOrderByAggregateInput
}

export type story_chapterScalarWhereWithAggregatesInput = {
  AND?: Prisma.story_chapterScalarWhereWithAggregatesInput | Prisma.story_chapterScalarWhereWithAggregatesInput[]
  OR?: Prisma.story_chapterScalarWhereWithAggregatesInput[]
  NOT?: Prisma.story_chapterScalarWhereWithAggregatesInput | Prisma.story_chapterScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"story_chapter"> | number
  seasonId?: Prisma.IntWithAggregatesFilter<"story_chapter"> | number
  name?: Prisma.StringWithAggregatesFilter<"story_chapter"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"story_chapter"> | string | null
  order?: Prisma.IntWithAggregatesFilter<"story_chapter"> | number
  unlockDate?: Prisma.DateTimeWithAggregatesFilter<"story_chapter"> | Date | string
  requiredLevel?: Prisma.IntWithAggregatesFilter<"story_chapter"> | number
  requiredChapterIds?: Prisma.JsonNullableWithAggregatesFilter<"story_chapter">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"story_chapter"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"story_chapter"> | Date | string
}

export type story_chapterCreateInput = {
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  story_season: Prisma.story_seasonCreateNestedOneWithoutStory_chapterInput
  quests?: Prisma.questCreateNestedManyWithoutStory_chapterInput
}

export type story_chapterUncheckedCreateInput = {
  id?: number
  seasonId: number
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  quests?: Prisma.questUncheckedCreateNestedManyWithoutStory_chapterInput
}

export type story_chapterUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  story_season?: Prisma.story_seasonUpdateOneRequiredWithoutStory_chapterNestedInput
  quests?: Prisma.questUpdateManyWithoutStory_chapterNestedInput
}

export type story_chapterUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  seasonId?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quests?: Prisma.questUncheckedUpdateManyWithoutStory_chapterNestedInput
}

export type story_chapterCreateManyInput = {
  id?: number
  seasonId: number
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_chapterUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_chapterUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  seasonId?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type Story_chapterNullableScalarRelationFilter = {
  is?: Prisma.story_chapterWhereInput | null
  isNot?: Prisma.story_chapterWhereInput | null
}

export type Story_chapterListRelationFilter = {
  every?: Prisma.story_chapterWhereInput
  some?: Prisma.story_chapterWhereInput
  none?: Prisma.story_chapterWhereInput
}

export type story_chapterOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type story_chapterOrderByRelevanceInput = {
  fields: Prisma.story_chapterOrderByRelevanceFieldEnum | Prisma.story_chapterOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type story_chapterSeasonIdOrderCompoundUniqueInput = {
  seasonId: number
  order: number
}

export type story_chapterCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  order?: Prisma.SortOrder
  unlockDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  requiredChapterIds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_chapterAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  order?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
}

export type story_chapterMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  order?: Prisma.SortOrder
  unlockDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_chapterMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  order?: Prisma.SortOrder
  unlockDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_chapterSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  seasonId?: Prisma.SortOrder
  order?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
}

export type story_chapterCreateNestedOneWithoutQuestsInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutQuestsInput, Prisma.story_chapterUncheckedCreateWithoutQuestsInput>
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutQuestsInput
  connect?: Prisma.story_chapterWhereUniqueInput
}

export type story_chapterUpdateOneWithoutQuestsNestedInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutQuestsInput, Prisma.story_chapterUncheckedCreateWithoutQuestsInput>
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutQuestsInput
  upsert?: Prisma.story_chapterUpsertWithoutQuestsInput
  disconnect?: Prisma.story_chapterWhereInput | boolean
  delete?: Prisma.story_chapterWhereInput | boolean
  connect?: Prisma.story_chapterWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.story_chapterUpdateToOneWithWhereWithoutQuestsInput, Prisma.story_chapterUpdateWithoutQuestsInput>, Prisma.story_chapterUncheckedUpdateWithoutQuestsInput>
}

export type story_chapterCreateNestedManyWithoutStory_seasonInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput> | Prisma.story_chapterCreateWithoutStory_seasonInput[] | Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput[]
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput | Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput[]
  createMany?: Prisma.story_chapterCreateManyStory_seasonInputEnvelope
  connect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
}

export type story_chapterUncheckedCreateNestedManyWithoutStory_seasonInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput> | Prisma.story_chapterCreateWithoutStory_seasonInput[] | Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput[]
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput | Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput[]
  createMany?: Prisma.story_chapterCreateManyStory_seasonInputEnvelope
  connect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
}

export type story_chapterUpdateManyWithoutStory_seasonNestedInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput> | Prisma.story_chapterCreateWithoutStory_seasonInput[] | Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput[]
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput | Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput[]
  upsert?: Prisma.story_chapterUpsertWithWhereUniqueWithoutStory_seasonInput | Prisma.story_chapterUpsertWithWhereUniqueWithoutStory_seasonInput[]
  createMany?: Prisma.story_chapterCreateManyStory_seasonInputEnvelope
  set?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  disconnect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  delete?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  connect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  update?: Prisma.story_chapterUpdateWithWhereUniqueWithoutStory_seasonInput | Prisma.story_chapterUpdateWithWhereUniqueWithoutStory_seasonInput[]
  updateMany?: Prisma.story_chapterUpdateManyWithWhereWithoutStory_seasonInput | Prisma.story_chapterUpdateManyWithWhereWithoutStory_seasonInput[]
  deleteMany?: Prisma.story_chapterScalarWhereInput | Prisma.story_chapterScalarWhereInput[]
}

export type story_chapterUncheckedUpdateManyWithoutStory_seasonNestedInput = {
  create?: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput> | Prisma.story_chapterCreateWithoutStory_seasonInput[] | Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput[]
  connectOrCreate?: Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput | Prisma.story_chapterCreateOrConnectWithoutStory_seasonInput[]
  upsert?: Prisma.story_chapterUpsertWithWhereUniqueWithoutStory_seasonInput | Prisma.story_chapterUpsertWithWhereUniqueWithoutStory_seasonInput[]
  createMany?: Prisma.story_chapterCreateManyStory_seasonInputEnvelope
  set?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  disconnect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  delete?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  connect?: Prisma.story_chapterWhereUniqueInput | Prisma.story_chapterWhereUniqueInput[]
  update?: Prisma.story_chapterUpdateWithWhereUniqueWithoutStory_seasonInput | Prisma.story_chapterUpdateWithWhereUniqueWithoutStory_seasonInput[]
  updateMany?: Prisma.story_chapterUpdateManyWithWhereWithoutStory_seasonInput | Prisma.story_chapterUpdateManyWithWhereWithoutStory_seasonInput[]
  deleteMany?: Prisma.story_chapterScalarWhereInput | Prisma.story_chapterScalarWhereInput[]
}

export type story_chapterCreateWithoutQuestsInput = {
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  story_season: Prisma.story_seasonCreateNestedOneWithoutStory_chapterInput
}

export type story_chapterUncheckedCreateWithoutQuestsInput = {
  id?: number
  seasonId: number
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_chapterCreateOrConnectWithoutQuestsInput = {
  where: Prisma.story_chapterWhereUniqueInput
  create: Prisma.XOR<Prisma.story_chapterCreateWithoutQuestsInput, Prisma.story_chapterUncheckedCreateWithoutQuestsInput>
}

export type story_chapterUpsertWithoutQuestsInput = {
  update: Prisma.XOR<Prisma.story_chapterUpdateWithoutQuestsInput, Prisma.story_chapterUncheckedUpdateWithoutQuestsInput>
  create: Prisma.XOR<Prisma.story_chapterCreateWithoutQuestsInput, Prisma.story_chapterUncheckedCreateWithoutQuestsInput>
  where?: Prisma.story_chapterWhereInput
}

export type story_chapterUpdateToOneWithWhereWithoutQuestsInput = {
  where?: Prisma.story_chapterWhereInput
  data: Prisma.XOR<Prisma.story_chapterUpdateWithoutQuestsInput, Prisma.story_chapterUncheckedUpdateWithoutQuestsInput>
}

export type story_chapterUpdateWithoutQuestsInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  story_season?: Prisma.story_seasonUpdateOneRequiredWithoutStory_chapterNestedInput
}

export type story_chapterUncheckedUpdateWithoutQuestsInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  seasonId?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_chapterCreateWithoutStory_seasonInput = {
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  quests?: Prisma.questCreateNestedManyWithoutStory_chapterInput
}

export type story_chapterUncheckedCreateWithoutStory_seasonInput = {
  id?: number
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  quests?: Prisma.questUncheckedCreateNestedManyWithoutStory_chapterInput
}

export type story_chapterCreateOrConnectWithoutStory_seasonInput = {
  where: Prisma.story_chapterWhereUniqueInput
  create: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput>
}

export type story_chapterCreateManyStory_seasonInputEnvelope = {
  data: Prisma.story_chapterCreateManyStory_seasonInput | Prisma.story_chapterCreateManyStory_seasonInput[]
  skipDuplicates?: boolean
}

export type story_chapterUpsertWithWhereUniqueWithoutStory_seasonInput = {
  where: Prisma.story_chapterWhereUniqueInput
  update: Prisma.XOR<Prisma.story_chapterUpdateWithoutStory_seasonInput, Prisma.story_chapterUncheckedUpdateWithoutStory_seasonInput>
  create: Prisma.XOR<Prisma.story_chapterCreateWithoutStory_seasonInput, Prisma.story_chapterUncheckedCreateWithoutStory_seasonInput>
}

export type story_chapterUpdateWithWhereUniqueWithoutStory_seasonInput = {
  where: Prisma.story_chapterWhereUniqueInput
  data: Prisma.XOR<Prisma.story_chapterUpdateWithoutStory_seasonInput, Prisma.story_chapterUncheckedUpdateWithoutStory_seasonInput>
}

export type story_chapterUpdateManyWithWhereWithoutStory_seasonInput = {
  where: Prisma.story_chapterScalarWhereInput
  data: Prisma.XOR<Prisma.story_chapterUpdateManyMutationInput, Prisma.story_chapterUncheckedUpdateManyWithoutStory_seasonInput>
}

export type story_chapterScalarWhereInput = {
  AND?: Prisma.story_chapterScalarWhereInput | Prisma.story_chapterScalarWhereInput[]
  OR?: Prisma.story_chapterScalarWhereInput[]
  NOT?: Prisma.story_chapterScalarWhereInput | Prisma.story_chapterScalarWhereInput[]
  id?: Prisma.IntFilter<"story_chapter"> | number
  seasonId?: Prisma.IntFilter<"story_chapter"> | number
  name?: Prisma.StringFilter<"story_chapter"> | string
  description?: Prisma.StringNullableFilter<"story_chapter"> | string | null
  order?: Prisma.IntFilter<"story_chapter"> | number
  unlockDate?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  requiredLevel?: Prisma.IntFilter<"story_chapter"> | number
  requiredChapterIds?: Prisma.JsonNullableFilter<"story_chapter">
  createdAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_chapter"> | Date | string
}

export type story_chapterCreateManyStory_seasonInput = {
  id?: number
  name: string
  description?: string | null
  order: number
  unlockDate: Date | string
  requiredLevel?: number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_chapterUpdateWithoutStory_seasonInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quests?: Prisma.questUpdateManyWithoutStory_chapterNestedInput
}

export type story_chapterUncheckedUpdateWithoutStory_seasonInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quests?: Prisma.questUncheckedUpdateManyWithoutStory_chapterNestedInput
}

export type story_chapterUncheckedUpdateManyWithoutStory_seasonInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  order?: Prisma.IntFieldUpdateOperationsInput | number
  unlockDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  requiredChapterIds?:PrismaJson.RequiredChapterIds | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type Story_chapterCountOutputType
 */

export type Story_chapterCountOutputType = {
  quests: number
}

export type Story_chapterCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quests?: boolean | Story_chapterCountOutputTypeCountQuestsArgs
}

/**
 * Story_chapterCountOutputType without action
 */
export type Story_chapterCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Story_chapterCountOutputType
   */
  select?: Prisma.Story_chapterCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Story_chapterCountOutputType without action
 */
export type Story_chapterCountOutputTypeCountQuestsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.questWhereInput
}


export type story_chapterSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  seasonId?: boolean
  name?: boolean
  description?: boolean
  order?: boolean
  unlockDate?: boolean
  requiredLevel?: boolean
  requiredChapterIds?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  story_season?: boolean | Prisma.story_seasonDefaultArgs<ExtArgs>
  quests?: boolean | Prisma.story_chapter$questsArgs<ExtArgs>
  _count?: boolean | Prisma.Story_chapterCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["story_chapter"]>



export type story_chapterSelectScalar = {
  id?: boolean
  seasonId?: boolean
  name?: boolean
  description?: boolean
  order?: boolean
  unlockDate?: boolean
  requiredLevel?: boolean
  requiredChapterIds?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type story_chapterOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "seasonId" | "name" | "description" | "order" | "unlockDate" | "requiredLevel" | "requiredChapterIds" | "createdAt" | "updatedAt", ExtArgs["result"]["story_chapter"]>
export type story_chapterInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  story_season?: boolean | Prisma.story_seasonDefaultArgs<ExtArgs>
  quests?: boolean | Prisma.story_chapter$questsArgs<ExtArgs>
  _count?: boolean | Prisma.Story_chapterCountOutputTypeDefaultArgs<ExtArgs>
}

export type $story_chapterPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "story_chapter"
  objects: {
    story_season: Prisma.$story_seasonPayload<ExtArgs>
    quests: Prisma.$questPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    seasonId: number
    name: string
    description: string | null
    order: number
    unlockDate: Date
    requiredLevel: number
    /**
     * [RequiredChapterIds]
     */
    requiredChapterIds:PrismaJson.RequiredChapterIds | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["story_chapter"]>
  composites: {}
}

export type story_chapterGetPayload<S extends boolean | null | undefined | story_chapterDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$story_chapterPayload, S>

export type story_chapterCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<story_chapterFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Story_chapterCountAggregateInputType | true
  }

export interface story_chapterDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['story_chapter'], meta: { name: 'story_chapter' } }
  /**
   * Find zero or one Story_chapter that matches the filter.
   * @param {story_chapterFindUniqueArgs} args - Arguments to find a Story_chapter
   * @example
   * // Get one Story_chapter
   * const story_chapter = await prisma.story_chapter.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends story_chapterFindUniqueArgs>(args: Prisma.SelectSubset<T, story_chapterFindUniqueArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Story_chapter that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {story_chapterFindUniqueOrThrowArgs} args - Arguments to find a Story_chapter
   * @example
   * // Get one Story_chapter
   * const story_chapter = await prisma.story_chapter.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends story_chapterFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, story_chapterFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_chapter that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterFindFirstArgs} args - Arguments to find a Story_chapter
   * @example
   * // Get one Story_chapter
   * const story_chapter = await prisma.story_chapter.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends story_chapterFindFirstArgs>(args?: Prisma.SelectSubset<T, story_chapterFindFirstArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_chapter that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterFindFirstOrThrowArgs} args - Arguments to find a Story_chapter
   * @example
   * // Get one Story_chapter
   * const story_chapter = await prisma.story_chapter.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends story_chapterFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, story_chapterFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Story_chapters that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Story_chapters
   * const story_chapters = await prisma.story_chapter.findMany()
   * 
   * // Get first 10 Story_chapters
   * const story_chapters = await prisma.story_chapter.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const story_chapterWithIdOnly = await prisma.story_chapter.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends story_chapterFindManyArgs>(args?: Prisma.SelectSubset<T, story_chapterFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Story_chapter.
   * @param {story_chapterCreateArgs} args - Arguments to create a Story_chapter.
   * @example
   * // Create one Story_chapter
   * const Story_chapter = await prisma.story_chapter.create({
   *   data: {
   *     // ... data to create a Story_chapter
   *   }
   * })
   * 
   */
  create<T extends story_chapterCreateArgs>(args: Prisma.SelectSubset<T, story_chapterCreateArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Story_chapters.
   * @param {story_chapterCreateManyArgs} args - Arguments to create many Story_chapters.
   * @example
   * // Create many Story_chapters
   * const story_chapter = await prisma.story_chapter.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends story_chapterCreateManyArgs>(args?: Prisma.SelectSubset<T, story_chapterCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Story_chapter.
   * @param {story_chapterDeleteArgs} args - Arguments to delete one Story_chapter.
   * @example
   * // Delete one Story_chapter
   * const Story_chapter = await prisma.story_chapter.delete({
   *   where: {
   *     // ... filter to delete one Story_chapter
   *   }
   * })
   * 
   */
  delete<T extends story_chapterDeleteArgs>(args: Prisma.SelectSubset<T, story_chapterDeleteArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Story_chapter.
   * @param {story_chapterUpdateArgs} args - Arguments to update one Story_chapter.
   * @example
   * // Update one Story_chapter
   * const story_chapter = await prisma.story_chapter.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends story_chapterUpdateArgs>(args: Prisma.SelectSubset<T, story_chapterUpdateArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Story_chapters.
   * @param {story_chapterDeleteManyArgs} args - Arguments to filter Story_chapters to delete.
   * @example
   * // Delete a few Story_chapters
   * const { count } = await prisma.story_chapter.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends story_chapterDeleteManyArgs>(args?: Prisma.SelectSubset<T, story_chapterDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Story_chapters.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Story_chapters
   * const story_chapter = await prisma.story_chapter.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends story_chapterUpdateManyArgs>(args: Prisma.SelectSubset<T, story_chapterUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Story_chapter.
   * @param {story_chapterUpsertArgs} args - Arguments to update or create a Story_chapter.
   * @example
   * // Update or create a Story_chapter
   * const story_chapter = await prisma.story_chapter.upsert({
   *   create: {
   *     // ... data to create a Story_chapter
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Story_chapter we want to update
   *   }
   * })
   */
  upsert<T extends story_chapterUpsertArgs>(args: Prisma.SelectSubset<T, story_chapterUpsertArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Story_chapters.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterCountArgs} args - Arguments to filter Story_chapters to count.
   * @example
   * // Count the number of Story_chapters
   * const count = await prisma.story_chapter.count({
   *   where: {
   *     // ... the filter for the Story_chapters we want to count
   *   }
   * })
  **/
  count<T extends story_chapterCountArgs>(
    args?: Prisma.Subset<T, story_chapterCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Story_chapterCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Story_chapter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Story_chapterAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Story_chapterAggregateArgs>(args: Prisma.Subset<T, Story_chapterAggregateArgs>): Prisma.PrismaPromise<GetStory_chapterAggregateType<T>>

  /**
   * Group by Story_chapter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_chapterGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends story_chapterGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: story_chapterGroupByArgs['orderBy'] }
      : { orderBy?: story_chapterGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, story_chapterGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStory_chapterGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the story_chapter model
 */
readonly fields: story_chapterFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for story_chapter.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__story_chapterClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  story_season<T extends Prisma.story_seasonDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.story_seasonDefaultArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  quests<T extends Prisma.story_chapter$questsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.story_chapter$questsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the story_chapter model
 */
export interface story_chapterFieldRefs {
  readonly id: Prisma.FieldRef<"story_chapter", 'Int'>
  readonly seasonId: Prisma.FieldRef<"story_chapter", 'Int'>
  readonly name: Prisma.FieldRef<"story_chapter", 'String'>
  readonly description: Prisma.FieldRef<"story_chapter", 'String'>
  readonly order: Prisma.FieldRef<"story_chapter", 'Int'>
  readonly unlockDate: Prisma.FieldRef<"story_chapter", 'DateTime'>
  readonly requiredLevel: Prisma.FieldRef<"story_chapter", 'Int'>
  readonly requiredChapterIds: Prisma.FieldRef<"story_chapter", 'Json'>
  readonly createdAt: Prisma.FieldRef<"story_chapter", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"story_chapter", 'DateTime'>
}
    

// Custom InputTypes
/**
 * story_chapter findUnique
 */
export type story_chapterFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter, which story_chapter to fetch.
   */
  where: Prisma.story_chapterWhereUniqueInput
}

/**
 * story_chapter findUniqueOrThrow
 */
export type story_chapterFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter, which story_chapter to fetch.
   */
  where: Prisma.story_chapterWhereUniqueInput
}

/**
 * story_chapter findFirst
 */
export type story_chapterFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter, which story_chapter to fetch.
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_chapters to fetch.
   */
  orderBy?: Prisma.story_chapterOrderByWithRelationInput | Prisma.story_chapterOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_chapters.
   */
  cursor?: Prisma.story_chapterWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_chapters from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_chapters.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_chapters.
   */
  distinct?: Prisma.Story_chapterScalarFieldEnum | Prisma.Story_chapterScalarFieldEnum[]
}

/**
 * story_chapter findFirstOrThrow
 */
export type story_chapterFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter, which story_chapter to fetch.
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_chapters to fetch.
   */
  orderBy?: Prisma.story_chapterOrderByWithRelationInput | Prisma.story_chapterOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_chapters.
   */
  cursor?: Prisma.story_chapterWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_chapters from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_chapters.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_chapters.
   */
  distinct?: Prisma.Story_chapterScalarFieldEnum | Prisma.Story_chapterScalarFieldEnum[]
}

/**
 * story_chapter findMany
 */
export type story_chapterFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter, which story_chapters to fetch.
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_chapters to fetch.
   */
  orderBy?: Prisma.story_chapterOrderByWithRelationInput | Prisma.story_chapterOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing story_chapters.
   */
  cursor?: Prisma.story_chapterWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_chapters from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_chapters.
   */
  skip?: number
  distinct?: Prisma.Story_chapterScalarFieldEnum | Prisma.Story_chapterScalarFieldEnum[]
}

/**
 * story_chapter create
 */
export type story_chapterCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * The data needed to create a story_chapter.
   */
  data: Prisma.XOR<Prisma.story_chapterCreateInput, Prisma.story_chapterUncheckedCreateInput>
}

/**
 * story_chapter createMany
 */
export type story_chapterCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many story_chapters.
   */
  data: Prisma.story_chapterCreateManyInput | Prisma.story_chapterCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * story_chapter update
 */
export type story_chapterUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * The data needed to update a story_chapter.
   */
  data: Prisma.XOR<Prisma.story_chapterUpdateInput, Prisma.story_chapterUncheckedUpdateInput>
  /**
   * Choose, which story_chapter to update.
   */
  where: Prisma.story_chapterWhereUniqueInput
}

/**
 * story_chapter updateMany
 */
export type story_chapterUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update story_chapters.
   */
  data: Prisma.XOR<Prisma.story_chapterUpdateManyMutationInput, Prisma.story_chapterUncheckedUpdateManyInput>
  /**
   * Filter which story_chapters to update
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * Limit how many story_chapters to update.
   */
  limit?: number
}

/**
 * story_chapter upsert
 */
export type story_chapterUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * The filter to search for the story_chapter to update in case it exists.
   */
  where: Prisma.story_chapterWhereUniqueInput
  /**
   * In case the story_chapter found by the `where` argument doesn't exist, create a new story_chapter with this data.
   */
  create: Prisma.XOR<Prisma.story_chapterCreateInput, Prisma.story_chapterUncheckedCreateInput>
  /**
   * In case the story_chapter was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.story_chapterUpdateInput, Prisma.story_chapterUncheckedUpdateInput>
}

/**
 * story_chapter delete
 */
export type story_chapterDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  /**
   * Filter which story_chapter to delete.
   */
  where: Prisma.story_chapterWhereUniqueInput
}

/**
 * story_chapter deleteMany
 */
export type story_chapterDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_chapters to delete
   */
  where?: Prisma.story_chapterWhereInput
  /**
   * Limit how many story_chapters to delete.
   */
  limit?: number
}

/**
 * story_chapter.quests
 */
export type story_chapter$questsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  cursor?: Prisma.questWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * story_chapter without action
 */
export type story_chapterDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
}
