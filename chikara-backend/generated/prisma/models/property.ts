
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `property` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model property
 * 
 */
export type propertyModel = runtime.Types.Result.DefaultSelection<Prisma.$propertyPayload>

export type AggregateProperty = {
  _count: PropertyCountAggregateOutputType | null
  _avg: PropertyAvgAggregateOutputType | null
  _sum: PropertySumAggregateOutputType | null
  _min: PropertyMinAggregateOutputType | null
  _max: PropertyMaxAggregateOutputType | null
}

export type PropertyAvgAggregateOutputType = {
  id: number | null
  cost: number | null
  upkeep: number | null
  slots: number | null
}

export type PropertySumAggregateOutputType = {
  id: number | null
  cost: number | null
  upkeep: number | null
  slots: number | null
}

export type PropertyMinAggregateOutputType = {
  id: number | null
  name: string | null
  propertyType: string | null
  cost: number | null
  upkeep: number | null
  slots: number | null
  description: string | null
  image: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PropertyMaxAggregateOutputType = {
  id: number | null
  name: string | null
  propertyType: string | null
  cost: number | null
  upkeep: number | null
  slots: number | null
  description: string | null
  image: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PropertyCountAggregateOutputType = {
  id: number
  name: number
  propertyType: number
  cost: number
  upkeep: number
  slots: number
  buffs: number
  description: number
  image: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type PropertyAvgAggregateInputType = {
  id?: true
  cost?: true
  upkeep?: true
  slots?: true
}

export type PropertySumAggregateInputType = {
  id?: true
  cost?: true
  upkeep?: true
  slots?: true
}

export type PropertyMinAggregateInputType = {
  id?: true
  name?: true
  propertyType?: true
  cost?: true
  upkeep?: true
  slots?: true
  description?: true
  image?: true
  createdAt?: true
  updatedAt?: true
}

export type PropertyMaxAggregateInputType = {
  id?: true
  name?: true
  propertyType?: true
  cost?: true
  upkeep?: true
  slots?: true
  description?: true
  image?: true
  createdAt?: true
  updatedAt?: true
}

export type PropertyCountAggregateInputType = {
  id?: true
  name?: true
  propertyType?: true
  cost?: true
  upkeep?: true
  slots?: true
  buffs?: true
  description?: true
  image?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PropertyAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which property to aggregate.
   */
  where?: Prisma.propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of properties to fetch.
   */
  orderBy?: Prisma.propertyOrderByWithRelationInput | Prisma.propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned properties
  **/
  _count?: true | PropertyCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PropertyAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PropertySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PropertyMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PropertyMaxAggregateInputType
}

export type GetPropertyAggregateType<T extends PropertyAggregateArgs> = {
      [P in keyof T & keyof AggregateProperty]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProperty[P]>
    : Prisma.GetScalarType<T[P], AggregateProperty[P]>
}




export type propertyGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.propertyWhereInput
  orderBy?: Prisma.propertyOrderByWithAggregationInput | Prisma.propertyOrderByWithAggregationInput[]
  by: Prisma.PropertyScalarFieldEnum[] | Prisma.PropertyScalarFieldEnum
  having?: Prisma.propertyScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PropertyCountAggregateInputType | true
  _avg?: PropertyAvgAggregateInputType
  _sum?: PropertySumAggregateInputType
  _min?: PropertyMinAggregateInputType
  _max?: PropertyMaxAggregateInputType
}

export type PropertyGroupByOutputType = {
  id: number
  name: string
  propertyType: string
  cost: number
  upkeep: number
  slots: number
  buffs: runtime.JsonValue
  description: string
  image: string | null
  createdAt: Date
  updatedAt: Date
  _count: PropertyCountAggregateOutputType | null
  _avg: PropertyAvgAggregateOutputType | null
  _sum: PropertySumAggregateOutputType | null
  _min: PropertyMinAggregateOutputType | null
  _max: PropertyMaxAggregateOutputType | null
}

type GetPropertyGroupByPayload<T extends propertyGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PropertyGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PropertyGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PropertyGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PropertyGroupByOutputType[P]>
      }
    >
  >



export type propertyWhereInput = {
  AND?: Prisma.propertyWhereInput | Prisma.propertyWhereInput[]
  OR?: Prisma.propertyWhereInput[]
  NOT?: Prisma.propertyWhereInput | Prisma.propertyWhereInput[]
  id?: Prisma.IntFilter<"property"> | number
  name?: Prisma.StringFilter<"property"> | string
  propertyType?: Prisma.StringFilter<"property"> | string
  cost?: Prisma.IntFilter<"property"> | number
  upkeep?: Prisma.IntFilter<"property"> | number
  slots?: Prisma.IntFilter<"property"> | number
  buffs?: Prisma.JsonFilter<"property">
  description?: Prisma.StringFilter<"property"> | string
  image?: Prisma.StringNullableFilter<"property"> | string | null
  createdAt?: Prisma.DateTimeFilter<"property"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"property"> | Date | string
  user_property?: Prisma.User_propertyListRelationFilter
}

export type propertyOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  propertyType?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
  buffs?: Prisma.SortOrder
  description?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user_property?: Prisma.user_propertyOrderByRelationAggregateInput
  _relevance?: Prisma.propertyOrderByRelevanceInput
}

export type propertyWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.propertyWhereInput | Prisma.propertyWhereInput[]
  OR?: Prisma.propertyWhereInput[]
  NOT?: Prisma.propertyWhereInput | Prisma.propertyWhereInput[]
  name?: Prisma.StringFilter<"property"> | string
  propertyType?: Prisma.StringFilter<"property"> | string
  cost?: Prisma.IntFilter<"property"> | number
  upkeep?: Prisma.IntFilter<"property"> | number
  slots?: Prisma.IntFilter<"property"> | number
  buffs?: Prisma.JsonFilter<"property">
  description?: Prisma.StringFilter<"property"> | string
  image?: Prisma.StringNullableFilter<"property"> | string | null
  createdAt?: Prisma.DateTimeFilter<"property"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"property"> | Date | string
  user_property?: Prisma.User_propertyListRelationFilter
}, "id">

export type propertyOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  propertyType?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
  buffs?: Prisma.SortOrder
  description?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.propertyCountOrderByAggregateInput
  _avg?: Prisma.propertyAvgOrderByAggregateInput
  _max?: Prisma.propertyMaxOrderByAggregateInput
  _min?: Prisma.propertyMinOrderByAggregateInput
  _sum?: Prisma.propertySumOrderByAggregateInput
}

export type propertyScalarWhereWithAggregatesInput = {
  AND?: Prisma.propertyScalarWhereWithAggregatesInput | Prisma.propertyScalarWhereWithAggregatesInput[]
  OR?: Prisma.propertyScalarWhereWithAggregatesInput[]
  NOT?: Prisma.propertyScalarWhereWithAggregatesInput | Prisma.propertyScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"property"> | number
  name?: Prisma.StringWithAggregatesFilter<"property"> | string
  propertyType?: Prisma.StringWithAggregatesFilter<"property"> | string
  cost?: Prisma.IntWithAggregatesFilter<"property"> | number
  upkeep?: Prisma.IntWithAggregatesFilter<"property"> | number
  slots?: Prisma.IntWithAggregatesFilter<"property"> | number
  buffs?: Prisma.JsonWithAggregatesFilter<"property">
  description?: Prisma.StringWithAggregatesFilter<"property"> | string
  image?: Prisma.StringNullableWithAggregatesFilter<"property"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"property"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"property"> | Date | string
}

export type propertyCreateInput = {
  name: string
  propertyType?: string
  cost: number
  upkeep: number
  slots: number
  buffs:unknown
  description: string
  image?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_property?: Prisma.user_propertyCreateNestedManyWithoutPropertyInput
}

export type propertyUncheckedCreateInput = {
  id?: number
  name: string
  propertyType?: string
  cost: number
  upkeep: number
  slots: number
  buffs:unknown
  description: string
  image?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_property?: Prisma.user_propertyUncheckedCreateNestedManyWithoutPropertyInput
}

export type propertyUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_property?: Prisma.user_propertyUpdateManyWithoutPropertyNestedInput
}

export type propertyUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_property?: Prisma.user_propertyUncheckedUpdateManyWithoutPropertyNestedInput
}

export type propertyCreateManyInput = {
  id?: number
  name: string
  propertyType?: string
  cost: number
  upkeep: number
  slots: number
  buffs:unknown
  description: string
  image?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type propertyUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type propertyUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type propertyOrderByRelevanceInput = {
  fields: Prisma.propertyOrderByRelevanceFieldEnum | Prisma.propertyOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type propertyCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  propertyType?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
  buffs?: Prisma.SortOrder
  description?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type propertyAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
}

export type propertyMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  propertyType?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
  description?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type propertyMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  propertyType?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
  description?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type propertySumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cost?: Prisma.SortOrder
  upkeep?: Prisma.SortOrder
  slots?: Prisma.SortOrder
}

export type PropertyScalarRelationFilter = {
  is?: Prisma.propertyWhereInput
  isNot?: Prisma.propertyWhereInput
}

export type propertyCreateNestedOneWithoutUser_propertyInput = {
  create?: Prisma.XOR<Prisma.propertyCreateWithoutUser_propertyInput, Prisma.propertyUncheckedCreateWithoutUser_propertyInput>
  connectOrCreate?: Prisma.propertyCreateOrConnectWithoutUser_propertyInput
  connect?: Prisma.propertyWhereUniqueInput
}

export type propertyUpdateOneRequiredWithoutUser_propertyNestedInput = {
  create?: Prisma.XOR<Prisma.propertyCreateWithoutUser_propertyInput, Prisma.propertyUncheckedCreateWithoutUser_propertyInput>
  connectOrCreate?: Prisma.propertyCreateOrConnectWithoutUser_propertyInput
  upsert?: Prisma.propertyUpsertWithoutUser_propertyInput
  connect?: Prisma.propertyWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.propertyUpdateToOneWithWhereWithoutUser_propertyInput, Prisma.propertyUpdateWithoutUser_propertyInput>, Prisma.propertyUncheckedUpdateWithoutUser_propertyInput>
}

export type propertyCreateWithoutUser_propertyInput = {
  name: string
  propertyType?: string
  cost: number
  upkeep: number
  slots: number
  buffs:unknown
  description: string
  image?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type propertyUncheckedCreateWithoutUser_propertyInput = {
  id?: number
  name: string
  propertyType?: string
  cost: number
  upkeep: number
  slots: number
  buffs:unknown
  description: string
  image?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type propertyCreateOrConnectWithoutUser_propertyInput = {
  where: Prisma.propertyWhereUniqueInput
  create: Prisma.XOR<Prisma.propertyCreateWithoutUser_propertyInput, Prisma.propertyUncheckedCreateWithoutUser_propertyInput>
}

export type propertyUpsertWithoutUser_propertyInput = {
  update: Prisma.XOR<Prisma.propertyUpdateWithoutUser_propertyInput, Prisma.propertyUncheckedUpdateWithoutUser_propertyInput>
  create: Prisma.XOR<Prisma.propertyCreateWithoutUser_propertyInput, Prisma.propertyUncheckedCreateWithoutUser_propertyInput>
  where?: Prisma.propertyWhereInput
}

export type propertyUpdateToOneWithWhereWithoutUser_propertyInput = {
  where?: Prisma.propertyWhereInput
  data: Prisma.XOR<Prisma.propertyUpdateWithoutUser_propertyInput, Prisma.propertyUncheckedUpdateWithoutUser_propertyInput>
}

export type propertyUpdateWithoutUser_propertyInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type propertyUncheckedUpdateWithoutUser_propertyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  propertyType?: Prisma.StringFieldUpdateOperationsInput | string
  cost?: Prisma.IntFieldUpdateOperationsInput | number
  upkeep?: Prisma.IntFieldUpdateOperationsInput | number
  slots?: Prisma.IntFieldUpdateOperationsInput | number
  buffs?:unknown
  description?: Prisma.StringFieldUpdateOperationsInput | string
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type PropertyCountOutputType
 */

export type PropertyCountOutputType = {
  user_property: number
}

export type PropertyCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_property?: boolean | PropertyCountOutputTypeCountUser_propertyArgs
}

/**
 * PropertyCountOutputType without action
 */
export type PropertyCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PropertyCountOutputType
   */
  select?: Prisma.PropertyCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PropertyCountOutputType without action
 */
export type PropertyCountOutputTypeCountUser_propertyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_propertyWhereInput
}


export type propertySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  propertyType?: boolean
  cost?: boolean
  upkeep?: boolean
  slots?: boolean
  buffs?: boolean
  description?: boolean
  image?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user_property?: boolean | Prisma.property$user_propertyArgs<ExtArgs>
  _count?: boolean | Prisma.PropertyCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["property"]>



export type propertySelectScalar = {
  id?: boolean
  name?: boolean
  propertyType?: boolean
  cost?: boolean
  upkeep?: boolean
  slots?: boolean
  buffs?: boolean
  description?: boolean
  image?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type propertyOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "propertyType" | "cost" | "upkeep" | "slots" | "buffs" | "description" | "image" | "createdAt" | "updatedAt", ExtArgs["result"]["property"]>
export type propertyInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_property?: boolean | Prisma.property$user_propertyArgs<ExtArgs>
  _count?: boolean | Prisma.PropertyCountOutputTypeDefaultArgs<ExtArgs>
}

export type $propertyPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "property"
  objects: {
    user_property: Prisma.$user_propertyPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    propertyType: string
    cost: number
    upkeep: number
    slots: number
    buffs:unknown
    description: string
    image: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["property"]>
  composites: {}
}

export type propertyGetPayload<S extends boolean | null | undefined | propertyDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$propertyPayload, S>

export type propertyCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<propertyFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PropertyCountAggregateInputType | true
  }

export interface propertyDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['property'], meta: { name: 'property' } }
  /**
   * Find zero or one Property that matches the filter.
   * @param {propertyFindUniqueArgs} args - Arguments to find a Property
   * @example
   * // Get one Property
   * const property = await prisma.property.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends propertyFindUniqueArgs>(args: Prisma.SelectSubset<T, propertyFindUniqueArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Property that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {propertyFindUniqueOrThrowArgs} args - Arguments to find a Property
   * @example
   * // Get one Property
   * const property = await prisma.property.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends propertyFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, propertyFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Property that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyFindFirstArgs} args - Arguments to find a Property
   * @example
   * // Get one Property
   * const property = await prisma.property.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends propertyFindFirstArgs>(args?: Prisma.SelectSubset<T, propertyFindFirstArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Property that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyFindFirstOrThrowArgs} args - Arguments to find a Property
   * @example
   * // Get one Property
   * const property = await prisma.property.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends propertyFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, propertyFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Properties that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Properties
   * const properties = await prisma.property.findMany()
   * 
   * // Get first 10 Properties
   * const properties = await prisma.property.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const propertyWithIdOnly = await prisma.property.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends propertyFindManyArgs>(args?: Prisma.SelectSubset<T, propertyFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Property.
   * @param {propertyCreateArgs} args - Arguments to create a Property.
   * @example
   * // Create one Property
   * const Property = await prisma.property.create({
   *   data: {
   *     // ... data to create a Property
   *   }
   * })
   * 
   */
  create<T extends propertyCreateArgs>(args: Prisma.SelectSubset<T, propertyCreateArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Properties.
   * @param {propertyCreateManyArgs} args - Arguments to create many Properties.
   * @example
   * // Create many Properties
   * const property = await prisma.property.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends propertyCreateManyArgs>(args?: Prisma.SelectSubset<T, propertyCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Property.
   * @param {propertyDeleteArgs} args - Arguments to delete one Property.
   * @example
   * // Delete one Property
   * const Property = await prisma.property.delete({
   *   where: {
   *     // ... filter to delete one Property
   *   }
   * })
   * 
   */
  delete<T extends propertyDeleteArgs>(args: Prisma.SelectSubset<T, propertyDeleteArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Property.
   * @param {propertyUpdateArgs} args - Arguments to update one Property.
   * @example
   * // Update one Property
   * const property = await prisma.property.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends propertyUpdateArgs>(args: Prisma.SelectSubset<T, propertyUpdateArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Properties.
   * @param {propertyDeleteManyArgs} args - Arguments to filter Properties to delete.
   * @example
   * // Delete a few Properties
   * const { count } = await prisma.property.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends propertyDeleteManyArgs>(args?: Prisma.SelectSubset<T, propertyDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Properties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Properties
   * const property = await prisma.property.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends propertyUpdateManyArgs>(args: Prisma.SelectSubset<T, propertyUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Property.
   * @param {propertyUpsertArgs} args - Arguments to update or create a Property.
   * @example
   * // Update or create a Property
   * const property = await prisma.property.upsert({
   *   create: {
   *     // ... data to create a Property
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Property we want to update
   *   }
   * })
   */
  upsert<T extends propertyUpsertArgs>(args: Prisma.SelectSubset<T, propertyUpsertArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Properties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyCountArgs} args - Arguments to filter Properties to count.
   * @example
   * // Count the number of Properties
   * const count = await prisma.property.count({
   *   where: {
   *     // ... the filter for the Properties we want to count
   *   }
   * })
  **/
  count<T extends propertyCountArgs>(
    args?: Prisma.Subset<T, propertyCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PropertyCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Property.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PropertyAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PropertyAggregateArgs>(args: Prisma.Subset<T, PropertyAggregateArgs>): Prisma.PrismaPromise<GetPropertyAggregateType<T>>

  /**
   * Group by Property.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {propertyGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends propertyGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: propertyGroupByArgs['orderBy'] }
      : { orderBy?: propertyGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, propertyGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPropertyGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the property model
 */
readonly fields: propertyFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for property.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__propertyClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_property<T extends Prisma.property$user_propertyArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.property$user_propertyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the property model
 */
export interface propertyFieldRefs {
  readonly id: Prisma.FieldRef<"property", 'Int'>
  readonly name: Prisma.FieldRef<"property", 'String'>
  readonly propertyType: Prisma.FieldRef<"property", 'String'>
  readonly cost: Prisma.FieldRef<"property", 'Int'>
  readonly upkeep: Prisma.FieldRef<"property", 'Int'>
  readonly slots: Prisma.FieldRef<"property", 'Int'>
  readonly buffs: Prisma.FieldRef<"property", 'Json'>
  readonly description: Prisma.FieldRef<"property", 'String'>
  readonly image: Prisma.FieldRef<"property", 'String'>
  readonly createdAt: Prisma.FieldRef<"property", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"property", 'DateTime'>
}
    

// Custom InputTypes
/**
 * property findUnique
 */
export type propertyFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter, which property to fetch.
   */
  where: Prisma.propertyWhereUniqueInput
}

/**
 * property findUniqueOrThrow
 */
export type propertyFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter, which property to fetch.
   */
  where: Prisma.propertyWhereUniqueInput
}

/**
 * property findFirst
 */
export type propertyFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter, which property to fetch.
   */
  where?: Prisma.propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of properties to fetch.
   */
  orderBy?: Prisma.propertyOrderByWithRelationInput | Prisma.propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for properties.
   */
  cursor?: Prisma.propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of properties.
   */
  distinct?: Prisma.PropertyScalarFieldEnum | Prisma.PropertyScalarFieldEnum[]
}

/**
 * property findFirstOrThrow
 */
export type propertyFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter, which property to fetch.
   */
  where?: Prisma.propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of properties to fetch.
   */
  orderBy?: Prisma.propertyOrderByWithRelationInput | Prisma.propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for properties.
   */
  cursor?: Prisma.propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of properties.
   */
  distinct?: Prisma.PropertyScalarFieldEnum | Prisma.PropertyScalarFieldEnum[]
}

/**
 * property findMany
 */
export type propertyFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter, which properties to fetch.
   */
  where?: Prisma.propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of properties to fetch.
   */
  orderBy?: Prisma.propertyOrderByWithRelationInput | Prisma.propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing properties.
   */
  cursor?: Prisma.propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` properties.
   */
  skip?: number
  distinct?: Prisma.PropertyScalarFieldEnum | Prisma.PropertyScalarFieldEnum[]
}

/**
 * property create
 */
export type propertyCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * The data needed to create a property.
   */
  data: Prisma.XOR<Prisma.propertyCreateInput, Prisma.propertyUncheckedCreateInput>
}

/**
 * property createMany
 */
export type propertyCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many properties.
   */
  data: Prisma.propertyCreateManyInput | Prisma.propertyCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * property update
 */
export type propertyUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * The data needed to update a property.
   */
  data: Prisma.XOR<Prisma.propertyUpdateInput, Prisma.propertyUncheckedUpdateInput>
  /**
   * Choose, which property to update.
   */
  where: Prisma.propertyWhereUniqueInput
}

/**
 * property updateMany
 */
export type propertyUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update properties.
   */
  data: Prisma.XOR<Prisma.propertyUpdateManyMutationInput, Prisma.propertyUncheckedUpdateManyInput>
  /**
   * Filter which properties to update
   */
  where?: Prisma.propertyWhereInput
  /**
   * Limit how many properties to update.
   */
  limit?: number
}

/**
 * property upsert
 */
export type propertyUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * The filter to search for the property to update in case it exists.
   */
  where: Prisma.propertyWhereUniqueInput
  /**
   * In case the property found by the `where` argument doesn't exist, create a new property with this data.
   */
  create: Prisma.XOR<Prisma.propertyCreateInput, Prisma.propertyUncheckedCreateInput>
  /**
   * In case the property was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.propertyUpdateInput, Prisma.propertyUncheckedUpdateInput>
}

/**
 * property delete
 */
export type propertyDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
  /**
   * Filter which property to delete.
   */
  where: Prisma.propertyWhereUniqueInput
}

/**
 * property deleteMany
 */
export type propertyDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which properties to delete
   */
  where?: Prisma.propertyWhereInput
  /**
   * Limit how many properties to delete.
   */
  limit?: number
}

/**
 * property.user_property
 */
export type property$user_propertyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  where?: Prisma.user_propertyWhereInput
  orderBy?: Prisma.user_propertyOrderByWithRelationInput | Prisma.user_propertyOrderByWithRelationInput[]
  cursor?: Prisma.user_propertyWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_propertyScalarFieldEnum | Prisma.User_propertyScalarFieldEnum[]
}

/**
 * property without action
 */
export type propertyDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the property
   */
  select?: Prisma.propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the property
   */
  omit?: Prisma.propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.propertyInclude<ExtArgs> | null
}
