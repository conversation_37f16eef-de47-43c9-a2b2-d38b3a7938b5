
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `trader_rep` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model trader_rep
 * 
 */
export type trader_repModel = runtime.Types.Result.DefaultSelection<Prisma.$trader_repPayload>

export type AggregateTrader_rep = {
  _count: Trader_repCountAggregateOutputType | null
  _avg: Trader_repAvgAggregateOutputType | null
  _sum: Trader_repSumAggregateOutputType | null
  _min: Trader_repMinAggregateOutputType | null
  _max: Trader_repMaxAggregateOutputType | null
}

export type Trader_repAvgAggregateOutputType = {
  id: number | null
  reputationLevel: number | null
  shopId: number | null
  userId: number | null
}

export type Trader_repSumAggregateOutputType = {
  id: number | null
  reputationLevel: number | null
  shopId: number | null
  userId: number | null
}

export type Trader_repMinAggregateOutputType = {
  id: number | null
  reputationLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
  shopId: number | null
  userId: number | null
}

export type Trader_repMaxAggregateOutputType = {
  id: number | null
  reputationLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
  shopId: number | null
  userId: number | null
}

export type Trader_repCountAggregateOutputType = {
  id: number
  reputationLevel: number
  createdAt: number
  updatedAt: number
  shopId: number
  userId: number
  _all: number
}


export type Trader_repAvgAggregateInputType = {
  id?: true
  reputationLevel?: true
  shopId?: true
  userId?: true
}

export type Trader_repSumAggregateInputType = {
  id?: true
  reputationLevel?: true
  shopId?: true
  userId?: true
}

export type Trader_repMinAggregateInputType = {
  id?: true
  reputationLevel?: true
  createdAt?: true
  updatedAt?: true
  shopId?: true
  userId?: true
}

export type Trader_repMaxAggregateInputType = {
  id?: true
  reputationLevel?: true
  createdAt?: true
  updatedAt?: true
  shopId?: true
  userId?: true
}

export type Trader_repCountAggregateInputType = {
  id?: true
  reputationLevel?: true
  createdAt?: true
  updatedAt?: true
  shopId?: true
  userId?: true
  _all?: true
}

export type Trader_repAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which trader_rep to aggregate.
   */
  where?: Prisma.trader_repWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of trader_reps to fetch.
   */
  orderBy?: Prisma.trader_repOrderByWithRelationInput | Prisma.trader_repOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.trader_repWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` trader_reps from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` trader_reps.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned trader_reps
  **/
  _count?: true | Trader_repCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Trader_repAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Trader_repSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Trader_repMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Trader_repMaxAggregateInputType
}

export type GetTrader_repAggregateType<T extends Trader_repAggregateArgs> = {
      [P in keyof T & keyof AggregateTrader_rep]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrader_rep[P]>
    : Prisma.GetScalarType<T[P], AggregateTrader_rep[P]>
}




export type trader_repGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.trader_repWhereInput
  orderBy?: Prisma.trader_repOrderByWithAggregationInput | Prisma.trader_repOrderByWithAggregationInput[]
  by: Prisma.Trader_repScalarFieldEnum[] | Prisma.Trader_repScalarFieldEnum
  having?: Prisma.trader_repScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Trader_repCountAggregateInputType | true
  _avg?: Trader_repAvgAggregateInputType
  _sum?: Trader_repSumAggregateInputType
  _min?: Trader_repMinAggregateInputType
  _max?: Trader_repMaxAggregateInputType
}

export type Trader_repGroupByOutputType = {
  id: number
  reputationLevel: number
  createdAt: Date
  updatedAt: Date
  shopId: number | null
  userId: number | null
  _count: Trader_repCountAggregateOutputType | null
  _avg: Trader_repAvgAggregateOutputType | null
  _sum: Trader_repSumAggregateOutputType | null
  _min: Trader_repMinAggregateOutputType | null
  _max: Trader_repMaxAggregateOutputType | null
}

type GetTrader_repGroupByPayload<T extends trader_repGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Trader_repGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Trader_repGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Trader_repGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Trader_repGroupByOutputType[P]>
      }
    >
  >



export type trader_repWhereInput = {
  AND?: Prisma.trader_repWhereInput | Prisma.trader_repWhereInput[]
  OR?: Prisma.trader_repWhereInput[]
  NOT?: Prisma.trader_repWhereInput | Prisma.trader_repWhereInput[]
  id?: Prisma.IntFilter<"trader_rep"> | number
  reputationLevel?: Prisma.FloatFilter<"trader_rep"> | number
  createdAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  shopId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
  userId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type trader_repOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  shop?: Prisma.shopOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type trader_repWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.trader_repWhereInput | Prisma.trader_repWhereInput[]
  OR?: Prisma.trader_repWhereInput[]
  NOT?: Prisma.trader_repWhereInput | Prisma.trader_repWhereInput[]
  reputationLevel?: Prisma.FloatFilter<"trader_rep"> | number
  createdAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  shopId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
  userId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type trader_repOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.trader_repCountOrderByAggregateInput
  _avg?: Prisma.trader_repAvgOrderByAggregateInput
  _max?: Prisma.trader_repMaxOrderByAggregateInput
  _min?: Prisma.trader_repMinOrderByAggregateInput
  _sum?: Prisma.trader_repSumOrderByAggregateInput
}

export type trader_repScalarWhereWithAggregatesInput = {
  AND?: Prisma.trader_repScalarWhereWithAggregatesInput | Prisma.trader_repScalarWhereWithAggregatesInput[]
  OR?: Prisma.trader_repScalarWhereWithAggregatesInput[]
  NOT?: Prisma.trader_repScalarWhereWithAggregatesInput | Prisma.trader_repScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"trader_rep"> | number
  reputationLevel?: Prisma.FloatWithAggregatesFilter<"trader_rep"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"trader_rep"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"trader_rep"> | Date | string
  shopId?: Prisma.IntNullableWithAggregatesFilter<"trader_rep"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"trader_rep"> | number | null
}

export type trader_repCreateInput = {
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shop?: Prisma.shopCreateNestedOneWithoutTrader_repInput
  user?: Prisma.userCreateNestedOneWithoutTrader_repInput
}

export type trader_repUncheckedCreateInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shopId?: number | null
  userId?: number | null
}

export type trader_repUpdateInput = {
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shop?: Prisma.shopUpdateOneWithoutTrader_repNestedInput
  user?: Prisma.userUpdateOneWithoutTrader_repNestedInput
}

export type trader_repUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type trader_repCreateManyInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shopId?: number | null
  userId?: number | null
}

export type trader_repUpdateManyMutationInput = {
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type trader_repUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Trader_repListRelationFilter = {
  every?: Prisma.trader_repWhereInput
  some?: Prisma.trader_repWhereInput
  none?: Prisma.trader_repWhereInput
}

export type trader_repOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type trader_repCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type trader_repAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type trader_repMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type trader_repMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type trader_repSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reputationLevel?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type trader_repCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput> | Prisma.trader_repCreateWithoutShopInput[] | Prisma.trader_repUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutShopInput | Prisma.trader_repCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.trader_repCreateManyShopInputEnvelope
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
}

export type trader_repUncheckedCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput> | Prisma.trader_repCreateWithoutShopInput[] | Prisma.trader_repUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutShopInput | Prisma.trader_repCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.trader_repCreateManyShopInputEnvelope
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
}

export type trader_repUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput> | Prisma.trader_repCreateWithoutShopInput[] | Prisma.trader_repUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutShopInput | Prisma.trader_repCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.trader_repUpsertWithWhereUniqueWithoutShopInput | Prisma.trader_repUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.trader_repCreateManyShopInputEnvelope
  set?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  disconnect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  delete?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  update?: Prisma.trader_repUpdateWithWhereUniqueWithoutShopInput | Prisma.trader_repUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.trader_repUpdateManyWithWhereWithoutShopInput | Prisma.trader_repUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
}

export type trader_repUncheckedUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput> | Prisma.trader_repCreateWithoutShopInput[] | Prisma.trader_repUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutShopInput | Prisma.trader_repCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.trader_repUpsertWithWhereUniqueWithoutShopInput | Prisma.trader_repUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.trader_repCreateManyShopInputEnvelope
  set?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  disconnect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  delete?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  update?: Prisma.trader_repUpdateWithWhereUniqueWithoutShopInput | Prisma.trader_repUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.trader_repUpdateManyWithWhereWithoutShopInput | Prisma.trader_repUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
}

export type trader_repCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput> | Prisma.trader_repCreateWithoutUserInput[] | Prisma.trader_repUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutUserInput | Prisma.trader_repCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.trader_repCreateManyUserInputEnvelope
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
}

export type trader_repUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput> | Prisma.trader_repCreateWithoutUserInput[] | Prisma.trader_repUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutUserInput | Prisma.trader_repCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.trader_repCreateManyUserInputEnvelope
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
}

export type trader_repUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput> | Prisma.trader_repCreateWithoutUserInput[] | Prisma.trader_repUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutUserInput | Prisma.trader_repCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.trader_repUpsertWithWhereUniqueWithoutUserInput | Prisma.trader_repUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.trader_repCreateManyUserInputEnvelope
  set?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  disconnect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  delete?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  update?: Prisma.trader_repUpdateWithWhereUniqueWithoutUserInput | Prisma.trader_repUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.trader_repUpdateManyWithWhereWithoutUserInput | Prisma.trader_repUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
}

export type trader_repUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput> | Prisma.trader_repCreateWithoutUserInput[] | Prisma.trader_repUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.trader_repCreateOrConnectWithoutUserInput | Prisma.trader_repCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.trader_repUpsertWithWhereUniqueWithoutUserInput | Prisma.trader_repUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.trader_repCreateManyUserInputEnvelope
  set?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  disconnect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  delete?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  connect?: Prisma.trader_repWhereUniqueInput | Prisma.trader_repWhereUniqueInput[]
  update?: Prisma.trader_repUpdateWithWhereUniqueWithoutUserInput | Prisma.trader_repUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.trader_repUpdateManyWithWhereWithoutUserInput | Prisma.trader_repUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
}

export type trader_repCreateWithoutShopInput = {
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutTrader_repInput
}

export type trader_repUncheckedCreateWithoutShopInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type trader_repCreateOrConnectWithoutShopInput = {
  where: Prisma.trader_repWhereUniqueInput
  create: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput>
}

export type trader_repCreateManyShopInputEnvelope = {
  data: Prisma.trader_repCreateManyShopInput | Prisma.trader_repCreateManyShopInput[]
  skipDuplicates?: boolean
}

export type trader_repUpsertWithWhereUniqueWithoutShopInput = {
  where: Prisma.trader_repWhereUniqueInput
  update: Prisma.XOR<Prisma.trader_repUpdateWithoutShopInput, Prisma.trader_repUncheckedUpdateWithoutShopInput>
  create: Prisma.XOR<Prisma.trader_repCreateWithoutShopInput, Prisma.trader_repUncheckedCreateWithoutShopInput>
}

export type trader_repUpdateWithWhereUniqueWithoutShopInput = {
  where: Prisma.trader_repWhereUniqueInput
  data: Prisma.XOR<Prisma.trader_repUpdateWithoutShopInput, Prisma.trader_repUncheckedUpdateWithoutShopInput>
}

export type trader_repUpdateManyWithWhereWithoutShopInput = {
  where: Prisma.trader_repScalarWhereInput
  data: Prisma.XOR<Prisma.trader_repUpdateManyMutationInput, Prisma.trader_repUncheckedUpdateManyWithoutShopInput>
}

export type trader_repScalarWhereInput = {
  AND?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
  OR?: Prisma.trader_repScalarWhereInput[]
  NOT?: Prisma.trader_repScalarWhereInput | Prisma.trader_repScalarWhereInput[]
  id?: Prisma.IntFilter<"trader_rep"> | number
  reputationLevel?: Prisma.FloatFilter<"trader_rep"> | number
  createdAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"trader_rep"> | Date | string
  shopId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
  userId?: Prisma.IntNullableFilter<"trader_rep"> | number | null
}

export type trader_repCreateWithoutUserInput = {
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shop?: Prisma.shopCreateNestedOneWithoutTrader_repInput
}

export type trader_repUncheckedCreateWithoutUserInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shopId?: number | null
}

export type trader_repCreateOrConnectWithoutUserInput = {
  where: Prisma.trader_repWhereUniqueInput
  create: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput>
}

export type trader_repCreateManyUserInputEnvelope = {
  data: Prisma.trader_repCreateManyUserInput | Prisma.trader_repCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type trader_repUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.trader_repWhereUniqueInput
  update: Prisma.XOR<Prisma.trader_repUpdateWithoutUserInput, Prisma.trader_repUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.trader_repCreateWithoutUserInput, Prisma.trader_repUncheckedCreateWithoutUserInput>
}

export type trader_repUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.trader_repWhereUniqueInput
  data: Prisma.XOR<Prisma.trader_repUpdateWithoutUserInput, Prisma.trader_repUncheckedUpdateWithoutUserInput>
}

export type trader_repUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.trader_repScalarWhereInput
  data: Prisma.XOR<Prisma.trader_repUpdateManyMutationInput, Prisma.trader_repUncheckedUpdateManyWithoutUserInput>
}

export type trader_repCreateManyShopInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type trader_repUpdateWithoutShopInput = {
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutTrader_repNestedInput
}

export type trader_repUncheckedUpdateWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type trader_repUncheckedUpdateManyWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type trader_repCreateManyUserInput = {
  id?: number
  reputationLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  shopId?: number | null
}

export type trader_repUpdateWithoutUserInput = {
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shop?: Prisma.shopUpdateOneWithoutTrader_repNestedInput
}

export type trader_repUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type trader_repUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  reputationLevel?: Prisma.FloatFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type trader_repSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  reputationLevel?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shopId?: boolean
  userId?: boolean
  shop?: boolean | Prisma.trader_rep$shopArgs<ExtArgs>
  user?: boolean | Prisma.trader_rep$userArgs<ExtArgs>
}, ExtArgs["result"]["trader_rep"]>



export type trader_repSelectScalar = {
  id?: boolean
  reputationLevel?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shopId?: boolean
  userId?: boolean
}

export type trader_repOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "reputationLevel" | "createdAt" | "updatedAt" | "shopId" | "userId", ExtArgs["result"]["trader_rep"]>
export type trader_repInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shop?: boolean | Prisma.trader_rep$shopArgs<ExtArgs>
  user?: boolean | Prisma.trader_rep$userArgs<ExtArgs>
}

export type $trader_repPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "trader_rep"
  objects: {
    shop: Prisma.$shopPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    reputationLevel: number
    createdAt: Date
    updatedAt: Date
    shopId: number | null
    userId: number | null
  }, ExtArgs["result"]["trader_rep"]>
  composites: {}
}

export type trader_repGetPayload<S extends boolean | null | undefined | trader_repDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$trader_repPayload, S>

export type trader_repCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<trader_repFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Trader_repCountAggregateInputType | true
  }

export interface trader_repDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['trader_rep'], meta: { name: 'trader_rep' } }
  /**
   * Find zero or one Trader_rep that matches the filter.
   * @param {trader_repFindUniqueArgs} args - Arguments to find a Trader_rep
   * @example
   * // Get one Trader_rep
   * const trader_rep = await prisma.trader_rep.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends trader_repFindUniqueArgs>(args: Prisma.SelectSubset<T, trader_repFindUniqueArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Trader_rep that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {trader_repFindUniqueOrThrowArgs} args - Arguments to find a Trader_rep
   * @example
   * // Get one Trader_rep
   * const trader_rep = await prisma.trader_rep.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends trader_repFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, trader_repFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Trader_rep that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repFindFirstArgs} args - Arguments to find a Trader_rep
   * @example
   * // Get one Trader_rep
   * const trader_rep = await prisma.trader_rep.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends trader_repFindFirstArgs>(args?: Prisma.SelectSubset<T, trader_repFindFirstArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Trader_rep that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repFindFirstOrThrowArgs} args - Arguments to find a Trader_rep
   * @example
   * // Get one Trader_rep
   * const trader_rep = await prisma.trader_rep.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends trader_repFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, trader_repFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Trader_reps that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Trader_reps
   * const trader_reps = await prisma.trader_rep.findMany()
   * 
   * // Get first 10 Trader_reps
   * const trader_reps = await prisma.trader_rep.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const trader_repWithIdOnly = await prisma.trader_rep.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends trader_repFindManyArgs>(args?: Prisma.SelectSubset<T, trader_repFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Trader_rep.
   * @param {trader_repCreateArgs} args - Arguments to create a Trader_rep.
   * @example
   * // Create one Trader_rep
   * const Trader_rep = await prisma.trader_rep.create({
   *   data: {
   *     // ... data to create a Trader_rep
   *   }
   * })
   * 
   */
  create<T extends trader_repCreateArgs>(args: Prisma.SelectSubset<T, trader_repCreateArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Trader_reps.
   * @param {trader_repCreateManyArgs} args - Arguments to create many Trader_reps.
   * @example
   * // Create many Trader_reps
   * const trader_rep = await prisma.trader_rep.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends trader_repCreateManyArgs>(args?: Prisma.SelectSubset<T, trader_repCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Trader_rep.
   * @param {trader_repDeleteArgs} args - Arguments to delete one Trader_rep.
   * @example
   * // Delete one Trader_rep
   * const Trader_rep = await prisma.trader_rep.delete({
   *   where: {
   *     // ... filter to delete one Trader_rep
   *   }
   * })
   * 
   */
  delete<T extends trader_repDeleteArgs>(args: Prisma.SelectSubset<T, trader_repDeleteArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Trader_rep.
   * @param {trader_repUpdateArgs} args - Arguments to update one Trader_rep.
   * @example
   * // Update one Trader_rep
   * const trader_rep = await prisma.trader_rep.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends trader_repUpdateArgs>(args: Prisma.SelectSubset<T, trader_repUpdateArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Trader_reps.
   * @param {trader_repDeleteManyArgs} args - Arguments to filter Trader_reps to delete.
   * @example
   * // Delete a few Trader_reps
   * const { count } = await prisma.trader_rep.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends trader_repDeleteManyArgs>(args?: Prisma.SelectSubset<T, trader_repDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Trader_reps.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Trader_reps
   * const trader_rep = await prisma.trader_rep.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends trader_repUpdateManyArgs>(args: Prisma.SelectSubset<T, trader_repUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Trader_rep.
   * @param {trader_repUpsertArgs} args - Arguments to update or create a Trader_rep.
   * @example
   * // Update or create a Trader_rep
   * const trader_rep = await prisma.trader_rep.upsert({
   *   create: {
   *     // ... data to create a Trader_rep
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Trader_rep we want to update
   *   }
   * })
   */
  upsert<T extends trader_repUpsertArgs>(args: Prisma.SelectSubset<T, trader_repUpsertArgs<ExtArgs>>): Prisma.Prisma__trader_repClient<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Trader_reps.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repCountArgs} args - Arguments to filter Trader_reps to count.
   * @example
   * // Count the number of Trader_reps
   * const count = await prisma.trader_rep.count({
   *   where: {
   *     // ... the filter for the Trader_reps we want to count
   *   }
   * })
  **/
  count<T extends trader_repCountArgs>(
    args?: Prisma.Subset<T, trader_repCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Trader_repCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Trader_rep.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Trader_repAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Trader_repAggregateArgs>(args: Prisma.Subset<T, Trader_repAggregateArgs>): Prisma.PrismaPromise<GetTrader_repAggregateType<T>>

  /**
   * Group by Trader_rep.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {trader_repGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends trader_repGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: trader_repGroupByArgs['orderBy'] }
      : { orderBy?: trader_repGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, trader_repGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrader_repGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the trader_rep model
 */
readonly fields: trader_repFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for trader_rep.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__trader_repClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  shop<T extends Prisma.trader_rep$shopArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.trader_rep$shopArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.trader_rep$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.trader_rep$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the trader_rep model
 */
export interface trader_repFieldRefs {
  readonly id: Prisma.FieldRef<"trader_rep", 'Int'>
  readonly reputationLevel: Prisma.FieldRef<"trader_rep", 'Float'>
  readonly createdAt: Prisma.FieldRef<"trader_rep", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"trader_rep", 'DateTime'>
  readonly shopId: Prisma.FieldRef<"trader_rep", 'Int'>
  readonly userId: Prisma.FieldRef<"trader_rep", 'Int'>
}
    

// Custom InputTypes
/**
 * trader_rep findUnique
 */
export type trader_repFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter, which trader_rep to fetch.
   */
  where: Prisma.trader_repWhereUniqueInput
}

/**
 * trader_rep findUniqueOrThrow
 */
export type trader_repFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter, which trader_rep to fetch.
   */
  where: Prisma.trader_repWhereUniqueInput
}

/**
 * trader_rep findFirst
 */
export type trader_repFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter, which trader_rep to fetch.
   */
  where?: Prisma.trader_repWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of trader_reps to fetch.
   */
  orderBy?: Prisma.trader_repOrderByWithRelationInput | Prisma.trader_repOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for trader_reps.
   */
  cursor?: Prisma.trader_repWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` trader_reps from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` trader_reps.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of trader_reps.
   */
  distinct?: Prisma.Trader_repScalarFieldEnum | Prisma.Trader_repScalarFieldEnum[]
}

/**
 * trader_rep findFirstOrThrow
 */
export type trader_repFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter, which trader_rep to fetch.
   */
  where?: Prisma.trader_repWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of trader_reps to fetch.
   */
  orderBy?: Prisma.trader_repOrderByWithRelationInput | Prisma.trader_repOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for trader_reps.
   */
  cursor?: Prisma.trader_repWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` trader_reps from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` trader_reps.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of trader_reps.
   */
  distinct?: Prisma.Trader_repScalarFieldEnum | Prisma.Trader_repScalarFieldEnum[]
}

/**
 * trader_rep findMany
 */
export type trader_repFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter, which trader_reps to fetch.
   */
  where?: Prisma.trader_repWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of trader_reps to fetch.
   */
  orderBy?: Prisma.trader_repOrderByWithRelationInput | Prisma.trader_repOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing trader_reps.
   */
  cursor?: Prisma.trader_repWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` trader_reps from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` trader_reps.
   */
  skip?: number
  distinct?: Prisma.Trader_repScalarFieldEnum | Prisma.Trader_repScalarFieldEnum[]
}

/**
 * trader_rep create
 */
export type trader_repCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * The data needed to create a trader_rep.
   */
  data: Prisma.XOR<Prisma.trader_repCreateInput, Prisma.trader_repUncheckedCreateInput>
}

/**
 * trader_rep createMany
 */
export type trader_repCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many trader_reps.
   */
  data: Prisma.trader_repCreateManyInput | Prisma.trader_repCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * trader_rep update
 */
export type trader_repUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * The data needed to update a trader_rep.
   */
  data: Prisma.XOR<Prisma.trader_repUpdateInput, Prisma.trader_repUncheckedUpdateInput>
  /**
   * Choose, which trader_rep to update.
   */
  where: Prisma.trader_repWhereUniqueInput
}

/**
 * trader_rep updateMany
 */
export type trader_repUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update trader_reps.
   */
  data: Prisma.XOR<Prisma.trader_repUpdateManyMutationInput, Prisma.trader_repUncheckedUpdateManyInput>
  /**
   * Filter which trader_reps to update
   */
  where?: Prisma.trader_repWhereInput
  /**
   * Limit how many trader_reps to update.
   */
  limit?: number
}

/**
 * trader_rep upsert
 */
export type trader_repUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * The filter to search for the trader_rep to update in case it exists.
   */
  where: Prisma.trader_repWhereUniqueInput
  /**
   * In case the trader_rep found by the `where` argument doesn't exist, create a new trader_rep with this data.
   */
  create: Prisma.XOR<Prisma.trader_repCreateInput, Prisma.trader_repUncheckedCreateInput>
  /**
   * In case the trader_rep was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.trader_repUpdateInput, Prisma.trader_repUncheckedUpdateInput>
}

/**
 * trader_rep delete
 */
export type trader_repDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  /**
   * Filter which trader_rep to delete.
   */
  where: Prisma.trader_repWhereUniqueInput
}

/**
 * trader_rep deleteMany
 */
export type trader_repDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which trader_reps to delete
   */
  where?: Prisma.trader_repWhereInput
  /**
   * Limit how many trader_reps to delete.
   */
  limit?: number
}

/**
 * trader_rep.shop
 */
export type trader_rep$shopArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  where?: Prisma.shopWhereInput
}

/**
 * trader_rep.user
 */
export type trader_rep$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * trader_rep without action
 */
export type trader_repDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
}
