
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `session` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model session
 * 
 */
export type sessionModel = runtime.Types.Result.DefaultSelection<Prisma.$sessionPayload>

export type AggregateSession = {
  _count: SessionCountAggregateOutputType | null
  _avg: SessionAvgAggregateOutputType | null
  _sum: SessionSumAggregateOutputType | null
  _min: SessionMinAggregateOutputType | null
  _max: SessionMaxAggregateOutputType | null
}

export type SessionAvgAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type SessionSumAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type SessionMinAggregateOutputType = {
  id: number | null
  expiresAt: Date | null
  token: string | null
  ipAddress: string | null
  userAgent: string | null
  userId: number | null
  impersonatedBy: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SessionMaxAggregateOutputType = {
  id: number | null
  expiresAt: Date | null
  token: string | null
  ipAddress: string | null
  userAgent: string | null
  userId: number | null
  impersonatedBy: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SessionCountAggregateOutputType = {
  id: number
  expiresAt: number
  token: number
  ipAddress: number
  userAgent: number
  userId: number
  impersonatedBy: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type SessionAvgAggregateInputType = {
  id?: true
  userId?: true
}

export type SessionSumAggregateInputType = {
  id?: true
  userId?: true
}

export type SessionMinAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  ipAddress?: true
  userAgent?: true
  userId?: true
  impersonatedBy?: true
  createdAt?: true
  updatedAt?: true
}

export type SessionMaxAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  ipAddress?: true
  userAgent?: true
  userId?: true
  impersonatedBy?: true
  createdAt?: true
  updatedAt?: true
}

export type SessionCountAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  ipAddress?: true
  userAgent?: true
  userId?: true
  impersonatedBy?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type SessionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which session to aggregate.
   */
  where?: Prisma.sessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of sessions to fetch.
   */
  orderBy?: Prisma.sessionOrderByWithRelationInput | Prisma.sessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.sessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned sessions
  **/
  _count?: true | SessionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: SessionAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: SessionSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SessionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SessionMaxAggregateInputType
}

export type GetSessionAggregateType<T extends SessionAggregateArgs> = {
      [P in keyof T & keyof AggregateSession]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSession[P]>
    : Prisma.GetScalarType<T[P], AggregateSession[P]>
}




export type sessionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.sessionWhereInput
  orderBy?: Prisma.sessionOrderByWithAggregationInput | Prisma.sessionOrderByWithAggregationInput[]
  by: Prisma.SessionScalarFieldEnum[] | Prisma.SessionScalarFieldEnum
  having?: Prisma.sessionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SessionCountAggregateInputType | true
  _avg?: SessionAvgAggregateInputType
  _sum?: SessionSumAggregateInputType
  _min?: SessionMinAggregateInputType
  _max?: SessionMaxAggregateInputType
}

export type SessionGroupByOutputType = {
  id: number
  expiresAt: Date
  token: string
  ipAddress: string | null
  userAgent: string | null
  userId: number
  impersonatedBy: string | null
  createdAt: Date
  updatedAt: Date
  _count: SessionCountAggregateOutputType | null
  _avg: SessionAvgAggregateOutputType | null
  _sum: SessionSumAggregateOutputType | null
  _min: SessionMinAggregateOutputType | null
  _max: SessionMaxAggregateOutputType | null
}

type GetSessionGroupByPayload<T extends sessionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SessionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SessionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SessionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SessionGroupByOutputType[P]>
      }
    >
  >



export type sessionWhereInput = {
  AND?: Prisma.sessionWhereInput | Prisma.sessionWhereInput[]
  OR?: Prisma.sessionWhereInput[]
  NOT?: Prisma.sessionWhereInput | Prisma.sessionWhereInput[]
  id?: Prisma.IntFilter<"session"> | number
  expiresAt?: Prisma.DateTimeFilter<"session"> | Date | string
  token?: Prisma.StringFilter<"session"> | string
  ipAddress?: Prisma.StringNullableFilter<"session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"session"> | string | null
  userId?: Prisma.IntFilter<"session"> | number
  impersonatedBy?: Prisma.StringNullableFilter<"session"> | string | null
  createdAt?: Prisma.DateTimeFilter<"session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"session"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type sessionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.sessionOrderByRelevanceInput
}

export type sessionWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  token?: string
  AND?: Prisma.sessionWhereInput | Prisma.sessionWhereInput[]
  OR?: Prisma.sessionWhereInput[]
  NOT?: Prisma.sessionWhereInput | Prisma.sessionWhereInput[]
  expiresAt?: Prisma.DateTimeFilter<"session"> | Date | string
  ipAddress?: Prisma.StringNullableFilter<"session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"session"> | string | null
  userId?: Prisma.IntFilter<"session"> | number
  impersonatedBy?: Prisma.StringNullableFilter<"session"> | string | null
  createdAt?: Prisma.DateTimeFilter<"session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"session"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id" | "token">

export type sessionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.sessionCountOrderByAggregateInput
  _avg?: Prisma.sessionAvgOrderByAggregateInput
  _max?: Prisma.sessionMaxOrderByAggregateInput
  _min?: Prisma.sessionMinOrderByAggregateInput
  _sum?: Prisma.sessionSumOrderByAggregateInput
}

export type sessionScalarWhereWithAggregatesInput = {
  AND?: Prisma.sessionScalarWhereWithAggregatesInput | Prisma.sessionScalarWhereWithAggregatesInput[]
  OR?: Prisma.sessionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.sessionScalarWhereWithAggregatesInput | Prisma.sessionScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"session"> | number
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"session"> | Date | string
  token?: Prisma.StringWithAggregatesFilter<"session"> | string
  ipAddress?: Prisma.StringNullableWithAggregatesFilter<"session"> | string | null
  userAgent?: Prisma.StringNullableWithAggregatesFilter<"session"> | string | null
  userId?: Prisma.IntWithAggregatesFilter<"session"> | number
  impersonatedBy?: Prisma.StringNullableWithAggregatesFilter<"session"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"session"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"session"> | Date | string
}

export type sessionCreateInput = {
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  user: Prisma.userCreateNestedOneWithoutSessionInput
}

export type sessionUncheckedCreateInput = {
  id?: number
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  userId: number
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type sessionUpdateInput = {
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutSessionNestedInput
}

export type sessionUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type sessionCreateManyInput = {
  id?: number
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  userId: number
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type sessionUpdateManyMutationInput = {
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type sessionUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SessionListRelationFilter = {
  every?: Prisma.sessionWhereInput
  some?: Prisma.sessionWhereInput
  none?: Prisma.sessionWhereInput
}

export type sessionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type sessionOrderByRelevanceInput = {
  fields: Prisma.sessionOrderByRelevanceFieldEnum | Prisma.sessionOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type sessionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type sessionAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type sessionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type sessionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type sessionSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type sessionCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput> | Prisma.sessionCreateWithoutUserInput[] | Prisma.sessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.sessionCreateOrConnectWithoutUserInput | Prisma.sessionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.sessionCreateManyUserInputEnvelope
  connect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
}

export type sessionUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput> | Prisma.sessionCreateWithoutUserInput[] | Prisma.sessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.sessionCreateOrConnectWithoutUserInput | Prisma.sessionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.sessionCreateManyUserInputEnvelope
  connect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
}

export type sessionUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput> | Prisma.sessionCreateWithoutUserInput[] | Prisma.sessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.sessionCreateOrConnectWithoutUserInput | Prisma.sessionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.sessionUpsertWithWhereUniqueWithoutUserInput | Prisma.sessionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.sessionCreateManyUserInputEnvelope
  set?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  disconnect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  delete?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  connect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  update?: Prisma.sessionUpdateWithWhereUniqueWithoutUserInput | Prisma.sessionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.sessionUpdateManyWithWhereWithoutUserInput | Prisma.sessionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.sessionScalarWhereInput | Prisma.sessionScalarWhereInput[]
}

export type sessionUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput> | Prisma.sessionCreateWithoutUserInput[] | Prisma.sessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.sessionCreateOrConnectWithoutUserInput | Prisma.sessionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.sessionUpsertWithWhereUniqueWithoutUserInput | Prisma.sessionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.sessionCreateManyUserInputEnvelope
  set?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  disconnect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  delete?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  connect?: Prisma.sessionWhereUniqueInput | Prisma.sessionWhereUniqueInput[]
  update?: Prisma.sessionUpdateWithWhereUniqueWithoutUserInput | Prisma.sessionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.sessionUpdateManyWithWhereWithoutUserInput | Prisma.sessionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.sessionScalarWhereInput | Prisma.sessionScalarWhereInput[]
}

export type sessionCreateWithoutUserInput = {
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type sessionUncheckedCreateWithoutUserInput = {
  id?: number
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type sessionCreateOrConnectWithoutUserInput = {
  where: Prisma.sessionWhereUniqueInput
  create: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput>
}

export type sessionCreateManyUserInputEnvelope = {
  data: Prisma.sessionCreateManyUserInput | Prisma.sessionCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type sessionUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.sessionWhereUniqueInput
  update: Prisma.XOR<Prisma.sessionUpdateWithoutUserInput, Prisma.sessionUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.sessionCreateWithoutUserInput, Prisma.sessionUncheckedCreateWithoutUserInput>
}

export type sessionUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.sessionWhereUniqueInput
  data: Prisma.XOR<Prisma.sessionUpdateWithoutUserInput, Prisma.sessionUncheckedUpdateWithoutUserInput>
}

export type sessionUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.sessionScalarWhereInput
  data: Prisma.XOR<Prisma.sessionUpdateManyMutationInput, Prisma.sessionUncheckedUpdateManyWithoutUserInput>
}

export type sessionScalarWhereInput = {
  AND?: Prisma.sessionScalarWhereInput | Prisma.sessionScalarWhereInput[]
  OR?: Prisma.sessionScalarWhereInput[]
  NOT?: Prisma.sessionScalarWhereInput | Prisma.sessionScalarWhereInput[]
  id?: Prisma.IntFilter<"session"> | number
  expiresAt?: Prisma.DateTimeFilter<"session"> | Date | string
  token?: Prisma.StringFilter<"session"> | string
  ipAddress?: Prisma.StringNullableFilter<"session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"session"> | string | null
  userId?: Prisma.IntFilter<"session"> | number
  impersonatedBy?: Prisma.StringNullableFilter<"session"> | string | null
  createdAt?: Prisma.DateTimeFilter<"session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"session"> | Date | string
}

export type sessionCreateManyUserInput = {
  id?: number
  expiresAt: Date | string
  token: string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type sessionUpdateWithoutUserInput = {
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type sessionUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type sessionUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type sessionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  userId?: boolean
  impersonatedBy?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["session"]>



export type sessionSelectScalar = {
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  userId?: boolean
  impersonatedBy?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type sessionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "expiresAt" | "token" | "ipAddress" | "userAgent" | "userId" | "impersonatedBy" | "createdAt" | "updatedAt", ExtArgs["result"]["session"]>
export type sessionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $sessionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "session"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    expiresAt: Date
    token: string
    ipAddress: string | null
    userAgent: string | null
    userId: number
    impersonatedBy: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["session"]>
  composites: {}
}

export type sessionGetPayload<S extends boolean | null | undefined | sessionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$sessionPayload, S>

export type sessionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<sessionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SessionCountAggregateInputType | true
  }

export interface sessionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['session'], meta: { name: 'session' } }
  /**
   * Find zero or one Session that matches the filter.
   * @param {sessionFindUniqueArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends sessionFindUniqueArgs>(args: Prisma.SelectSubset<T, sessionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Session that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {sessionFindUniqueOrThrowArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends sessionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, sessionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Session that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionFindFirstArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends sessionFindFirstArgs>(args?: Prisma.SelectSubset<T, sessionFindFirstArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Session that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionFindFirstOrThrowArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends sessionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, sessionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Sessions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Sessions
   * const sessions = await prisma.session.findMany()
   * 
   * // Get first 10 Sessions
   * const sessions = await prisma.session.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const sessionWithIdOnly = await prisma.session.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends sessionFindManyArgs>(args?: Prisma.SelectSubset<T, sessionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Session.
   * @param {sessionCreateArgs} args - Arguments to create a Session.
   * @example
   * // Create one Session
   * const Session = await prisma.session.create({
   *   data: {
   *     // ... data to create a Session
   *   }
   * })
   * 
   */
  create<T extends sessionCreateArgs>(args: Prisma.SelectSubset<T, sessionCreateArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Sessions.
   * @param {sessionCreateManyArgs} args - Arguments to create many Sessions.
   * @example
   * // Create many Sessions
   * const session = await prisma.session.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends sessionCreateManyArgs>(args?: Prisma.SelectSubset<T, sessionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Session.
   * @param {sessionDeleteArgs} args - Arguments to delete one Session.
   * @example
   * // Delete one Session
   * const Session = await prisma.session.delete({
   *   where: {
   *     // ... filter to delete one Session
   *   }
   * })
   * 
   */
  delete<T extends sessionDeleteArgs>(args: Prisma.SelectSubset<T, sessionDeleteArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Session.
   * @param {sessionUpdateArgs} args - Arguments to update one Session.
   * @example
   * // Update one Session
   * const session = await prisma.session.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends sessionUpdateArgs>(args: Prisma.SelectSubset<T, sessionUpdateArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Sessions.
   * @param {sessionDeleteManyArgs} args - Arguments to filter Sessions to delete.
   * @example
   * // Delete a few Sessions
   * const { count } = await prisma.session.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends sessionDeleteManyArgs>(args?: Prisma.SelectSubset<T, sessionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Sessions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Sessions
   * const session = await prisma.session.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends sessionUpdateManyArgs>(args: Prisma.SelectSubset<T, sessionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Session.
   * @param {sessionUpsertArgs} args - Arguments to update or create a Session.
   * @example
   * // Update or create a Session
   * const session = await prisma.session.upsert({
   *   create: {
   *     // ... data to create a Session
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Session we want to update
   *   }
   * })
   */
  upsert<T extends sessionUpsertArgs>(args: Prisma.SelectSubset<T, sessionUpsertArgs<ExtArgs>>): Prisma.Prisma__sessionClient<runtime.Types.Result.GetResult<Prisma.$sessionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Sessions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionCountArgs} args - Arguments to filter Sessions to count.
   * @example
   * // Count the number of Sessions
   * const count = await prisma.session.count({
   *   where: {
   *     // ... the filter for the Sessions we want to count
   *   }
   * })
  **/
  count<T extends sessionCountArgs>(
    args?: Prisma.Subset<T, sessionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SessionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Session.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SessionAggregateArgs>(args: Prisma.Subset<T, SessionAggregateArgs>): Prisma.PrismaPromise<GetSessionAggregateType<T>>

  /**
   * Group by Session.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {sessionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends sessionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: sessionGroupByArgs['orderBy'] }
      : { orderBy?: sessionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, sessionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSessionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the session model
 */
readonly fields: sessionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for session.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__sessionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the session model
 */
export interface sessionFieldRefs {
  readonly id: Prisma.FieldRef<"session", 'Int'>
  readonly expiresAt: Prisma.FieldRef<"session", 'DateTime'>
  readonly token: Prisma.FieldRef<"session", 'String'>
  readonly ipAddress: Prisma.FieldRef<"session", 'String'>
  readonly userAgent: Prisma.FieldRef<"session", 'String'>
  readonly userId: Prisma.FieldRef<"session", 'Int'>
  readonly impersonatedBy: Prisma.FieldRef<"session", 'String'>
  readonly createdAt: Prisma.FieldRef<"session", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"session", 'DateTime'>
}
    

// Custom InputTypes
/**
 * session findUnique
 */
export type sessionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter, which session to fetch.
   */
  where: Prisma.sessionWhereUniqueInput
}

/**
 * session findUniqueOrThrow
 */
export type sessionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter, which session to fetch.
   */
  where: Prisma.sessionWhereUniqueInput
}

/**
 * session findFirst
 */
export type sessionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter, which session to fetch.
   */
  where?: Prisma.sessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of sessions to fetch.
   */
  orderBy?: Prisma.sessionOrderByWithRelationInput | Prisma.sessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for sessions.
   */
  cursor?: Prisma.sessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of sessions.
   */
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * session findFirstOrThrow
 */
export type sessionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter, which session to fetch.
   */
  where?: Prisma.sessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of sessions to fetch.
   */
  orderBy?: Prisma.sessionOrderByWithRelationInput | Prisma.sessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for sessions.
   */
  cursor?: Prisma.sessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of sessions.
   */
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * session findMany
 */
export type sessionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter, which sessions to fetch.
   */
  where?: Prisma.sessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of sessions to fetch.
   */
  orderBy?: Prisma.sessionOrderByWithRelationInput | Prisma.sessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing sessions.
   */
  cursor?: Prisma.sessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` sessions.
   */
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * session create
 */
export type sessionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * The data needed to create a session.
   */
  data: Prisma.XOR<Prisma.sessionCreateInput, Prisma.sessionUncheckedCreateInput>
}

/**
 * session createMany
 */
export type sessionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many sessions.
   */
  data: Prisma.sessionCreateManyInput | Prisma.sessionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * session update
 */
export type sessionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * The data needed to update a session.
   */
  data: Prisma.XOR<Prisma.sessionUpdateInput, Prisma.sessionUncheckedUpdateInput>
  /**
   * Choose, which session to update.
   */
  where: Prisma.sessionWhereUniqueInput
}

/**
 * session updateMany
 */
export type sessionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update sessions.
   */
  data: Prisma.XOR<Prisma.sessionUpdateManyMutationInput, Prisma.sessionUncheckedUpdateManyInput>
  /**
   * Filter which sessions to update
   */
  where?: Prisma.sessionWhereInput
  /**
   * Limit how many sessions to update.
   */
  limit?: number
}

/**
 * session upsert
 */
export type sessionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * The filter to search for the session to update in case it exists.
   */
  where: Prisma.sessionWhereUniqueInput
  /**
   * In case the session found by the `where` argument doesn't exist, create a new session with this data.
   */
  create: Prisma.XOR<Prisma.sessionCreateInput, Prisma.sessionUncheckedCreateInput>
  /**
   * In case the session was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.sessionUpdateInput, Prisma.sessionUncheckedUpdateInput>
}

/**
 * session delete
 */
export type sessionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
  /**
   * Filter which session to delete.
   */
  where: Prisma.sessionWhereUniqueInput
}

/**
 * session deleteMany
 */
export type sessionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which sessions to delete
   */
  where?: Prisma.sessionWhereInput
  /**
   * Limit how many sessions to delete.
   */
  limit?: number
}

/**
 * session without action
 */
export type sessionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the session
   */
  select?: Prisma.sessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the session
   */
  omit?: Prisma.sessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.sessionInclude<ExtArgs> | null
}
