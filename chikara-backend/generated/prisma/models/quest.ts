
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `quest` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model quest
 * 
 */
export type questModel = runtime.Types.Result.DefaultSelection<Prisma.$questPayload>

export type AggregateQuest = {
  _count: QuestCountAggregateOutputType | null
  _avg: QuestAvgAggregateOutputType | null
  _sum: QuestSumAggregateOutputType | null
  _min: QuestMinAggregateOutputType | null
  _max: QuestMaxAggregateOutputType | null
}

export type QuestAvgAggregateOutputType = {
  id: number | null
  levelReq: number | null
  xpReward: number | null
  cashReward: number | null
  repReward: number | null
  talentPointReward: number | null
  shopId: number | null
  requiredQuestId: number | null
  chapterId: number | null
  orderInChapter: number | null
}

export type QuestSumAggregateOutputType = {
  id: number | null
  levelReq: number | null
  xpReward: number | null
  cashReward: number | null
  repReward: number | null
  talentPointReward: number | null
  shopId: number | null
  requiredQuestId: number | null
  chapterId: number | null
  orderInChapter: number | null
}

export type QuestMinAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  questInfo: string | null
  levelReq: number | null
  disabled: boolean | null
  questChainName: string | null
  xpReward: number | null
  cashReward: number | null
  repReward: number | null
  talentPointReward: number | null
  shopId: number | null
  requiredQuestId: number | null
  isStoryQuest: boolean | null
  chapterId: number | null
  orderInChapter: number | null
}

export type QuestMaxAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  questInfo: string | null
  levelReq: number | null
  disabled: boolean | null
  questChainName: string | null
  xpReward: number | null
  cashReward: number | null
  repReward: number | null
  talentPointReward: number | null
  shopId: number | null
  requiredQuestId: number | null
  isStoryQuest: boolean | null
  chapterId: number | null
  orderInChapter: number | null
}

export type QuestCountAggregateOutputType = {
  id: number
  name: number
  description: number
  questInfo: number
  levelReq: number
  disabled: number
  questChainName: number
  xpReward: number
  cashReward: number
  repReward: number
  talentPointReward: number
  shopId: number
  requiredQuestId: number
  isStoryQuest: number
  chapterId: number
  orderInChapter: number
  _all: number
}


export type QuestAvgAggregateInputType = {
  id?: true
  levelReq?: true
  xpReward?: true
  cashReward?: true
  repReward?: true
  talentPointReward?: true
  shopId?: true
  requiredQuestId?: true
  chapterId?: true
  orderInChapter?: true
}

export type QuestSumAggregateInputType = {
  id?: true
  levelReq?: true
  xpReward?: true
  cashReward?: true
  repReward?: true
  talentPointReward?: true
  shopId?: true
  requiredQuestId?: true
  chapterId?: true
  orderInChapter?: true
}

export type QuestMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  questInfo?: true
  levelReq?: true
  disabled?: true
  questChainName?: true
  xpReward?: true
  cashReward?: true
  repReward?: true
  talentPointReward?: true
  shopId?: true
  requiredQuestId?: true
  isStoryQuest?: true
  chapterId?: true
  orderInChapter?: true
}

export type QuestMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  questInfo?: true
  levelReq?: true
  disabled?: true
  questChainName?: true
  xpReward?: true
  cashReward?: true
  repReward?: true
  talentPointReward?: true
  shopId?: true
  requiredQuestId?: true
  isStoryQuest?: true
  chapterId?: true
  orderInChapter?: true
}

export type QuestCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  questInfo?: true
  levelReq?: true
  disabled?: true
  questChainName?: true
  xpReward?: true
  cashReward?: true
  repReward?: true
  talentPointReward?: true
  shopId?: true
  requiredQuestId?: true
  isStoryQuest?: true
  chapterId?: true
  orderInChapter?: true
  _all?: true
}

export type QuestAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest to aggregate.
   */
  where?: Prisma.questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quests to fetch.
   */
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned quests
  **/
  _count?: true | QuestCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: QuestAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: QuestSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: QuestMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: QuestMaxAggregateInputType
}

export type GetQuestAggregateType<T extends QuestAggregateArgs> = {
      [P in keyof T & keyof AggregateQuest]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateQuest[P]>
    : Prisma.GetScalarType<T[P], AggregateQuest[P]>
}




export type questGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.questWhereInput
  orderBy?: Prisma.questOrderByWithAggregationInput | Prisma.questOrderByWithAggregationInput[]
  by: Prisma.QuestScalarFieldEnum[] | Prisma.QuestScalarFieldEnum
  having?: Prisma.questScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: QuestCountAggregateInputType | true
  _avg?: QuestAvgAggregateInputType
  _sum?: QuestSumAggregateInputType
  _min?: QuestMinAggregateInputType
  _max?: QuestMaxAggregateInputType
}

export type QuestGroupByOutputType = {
  id: number
  name: string
  description: string | null
  questInfo: string | null
  levelReq: number
  disabled: boolean | null
  questChainName: string | null
  xpReward: number | null
  cashReward: number
  repReward: number
  talentPointReward: number
  shopId: number | null
  requiredQuestId: number | null
  isStoryQuest: boolean
  chapterId: number | null
  orderInChapter: number | null
  _count: QuestCountAggregateOutputType | null
  _avg: QuestAvgAggregateOutputType | null
  _sum: QuestSumAggregateOutputType | null
  _min: QuestMinAggregateOutputType | null
  _max: QuestMaxAggregateOutputType | null
}

type GetQuestGroupByPayload<T extends questGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<QuestGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof QuestGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], QuestGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], QuestGroupByOutputType[P]>
      }
    >
  >



export type questWhereInput = {
  AND?: Prisma.questWhereInput | Prisma.questWhereInput[]
  OR?: Prisma.questWhereInput[]
  NOT?: Prisma.questWhereInput | Prisma.questWhereInput[]
  id?: Prisma.IntFilter<"quest"> | number
  name?: Prisma.StringFilter<"quest"> | string
  description?: Prisma.StringNullableFilter<"quest"> | string | null
  questInfo?: Prisma.StringNullableFilter<"quest"> | string | null
  levelReq?: Prisma.IntFilter<"quest"> | number
  disabled?: Prisma.BoolNullableFilter<"quest"> | boolean | null
  questChainName?: Prisma.StringNullableFilter<"quest"> | string | null
  xpReward?: Prisma.IntNullableFilter<"quest"> | number | null
  cashReward?: Prisma.IntFilter<"quest"> | number
  repReward?: Prisma.FloatFilter<"quest"> | number
  talentPointReward?: Prisma.IntFilter<"quest"> | number
  shopId?: Prisma.IntNullableFilter<"quest"> | number | null
  requiredQuestId?: Prisma.IntNullableFilter<"quest"> | number | null
  isStoryQuest?: Prisma.BoolFilter<"quest"> | boolean
  chapterId?: Prisma.IntNullableFilter<"quest"> | number | null
  orderInChapter?: Prisma.IntNullableFilter<"quest"> | number | null
  story_chapter?: Prisma.XOR<Prisma.Story_chapterNullableScalarRelationFilter, Prisma.story_chapterWhereInput> | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  other_quest?: Prisma.QuestListRelationFilter
  quest_progress?: Prisma.Quest_progressListRelationFilter
  quest_reward?: Prisma.Quest_rewardListRelationFilter
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}

export type questOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  questInfo?: Prisma.SortOrderInput | Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  disabled?: Prisma.SortOrderInput | Prisma.SortOrder
  questChainName?: Prisma.SortOrderInput | Prisma.SortOrder
  xpReward?: Prisma.SortOrderInput | Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStoryQuest?: Prisma.SortOrder
  chapterId?: Prisma.SortOrderInput | Prisma.SortOrder
  orderInChapter?: Prisma.SortOrderInput | Prisma.SortOrder
  story_chapter?: Prisma.story_chapterOrderByWithRelationInput
  shop?: Prisma.shopOrderByWithRelationInput
  quest?: Prisma.questOrderByWithRelationInput
  other_quest?: Prisma.questOrderByRelationAggregateInput
  quest_progress?: Prisma.quest_progressOrderByRelationAggregateInput
  quest_reward?: Prisma.quest_rewardOrderByRelationAggregateInput
  quest_objective?: Prisma.quest_objectiveOrderByRelationAggregateInput
  _relevance?: Prisma.questOrderByRelevanceInput
}

export type questWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  chapterId_orderInChapter?: Prisma.questChapterIdOrderInChapterCompoundUniqueInput
  AND?: Prisma.questWhereInput | Prisma.questWhereInput[]
  OR?: Prisma.questWhereInput[]
  NOT?: Prisma.questWhereInput | Prisma.questWhereInput[]
  name?: Prisma.StringFilter<"quest"> | string
  description?: Prisma.StringNullableFilter<"quest"> | string | null
  questInfo?: Prisma.StringNullableFilter<"quest"> | string | null
  levelReq?: Prisma.IntFilter<"quest"> | number
  disabled?: Prisma.BoolNullableFilter<"quest"> | boolean | null
  questChainName?: Prisma.StringNullableFilter<"quest"> | string | null
  xpReward?: Prisma.IntNullableFilter<"quest"> | number | null
  cashReward?: Prisma.IntFilter<"quest"> | number
  repReward?: Prisma.FloatFilter<"quest"> | number
  talentPointReward?: Prisma.IntFilter<"quest"> | number
  shopId?: Prisma.IntNullableFilter<"quest"> | number | null
  requiredQuestId?: Prisma.IntNullableFilter<"quest"> | number | null
  isStoryQuest?: Prisma.BoolFilter<"quest"> | boolean
  chapterId?: Prisma.IntNullableFilter<"quest"> | number | null
  orderInChapter?: Prisma.IntNullableFilter<"quest"> | number | null
  story_chapter?: Prisma.XOR<Prisma.Story_chapterNullableScalarRelationFilter, Prisma.story_chapterWhereInput> | null
  shop?: Prisma.XOR<Prisma.ShopNullableScalarRelationFilter, Prisma.shopWhereInput> | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  other_quest?: Prisma.QuestListRelationFilter
  quest_progress?: Prisma.Quest_progressListRelationFilter
  quest_reward?: Prisma.Quest_rewardListRelationFilter
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}, "id" | "chapterId_orderInChapter">

export type questOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  questInfo?: Prisma.SortOrderInput | Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  disabled?: Prisma.SortOrderInput | Prisma.SortOrder
  questChainName?: Prisma.SortOrderInput | Prisma.SortOrder
  xpReward?: Prisma.SortOrderInput | Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrderInput | Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStoryQuest?: Prisma.SortOrder
  chapterId?: Prisma.SortOrderInput | Prisma.SortOrder
  orderInChapter?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.questCountOrderByAggregateInput
  _avg?: Prisma.questAvgOrderByAggregateInput
  _max?: Prisma.questMaxOrderByAggregateInput
  _min?: Prisma.questMinOrderByAggregateInput
  _sum?: Prisma.questSumOrderByAggregateInput
}

export type questScalarWhereWithAggregatesInput = {
  AND?: Prisma.questScalarWhereWithAggregatesInput | Prisma.questScalarWhereWithAggregatesInput[]
  OR?: Prisma.questScalarWhereWithAggregatesInput[]
  NOT?: Prisma.questScalarWhereWithAggregatesInput | Prisma.questScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"quest"> | number
  name?: Prisma.StringWithAggregatesFilter<"quest"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"quest"> | string | null
  questInfo?: Prisma.StringNullableWithAggregatesFilter<"quest"> | string | null
  levelReq?: Prisma.IntWithAggregatesFilter<"quest"> | number
  disabled?: Prisma.BoolNullableWithAggregatesFilter<"quest"> | boolean | null
  questChainName?: Prisma.StringNullableWithAggregatesFilter<"quest"> | string | null
  xpReward?: Prisma.IntNullableWithAggregatesFilter<"quest"> | number | null
  cashReward?: Prisma.IntWithAggregatesFilter<"quest"> | number
  repReward?: Prisma.FloatWithAggregatesFilter<"quest"> | number
  talentPointReward?: Prisma.IntWithAggregatesFilter<"quest"> | number
  shopId?: Prisma.IntNullableWithAggregatesFilter<"quest"> | number | null
  requiredQuestId?: Prisma.IntNullableWithAggregatesFilter<"quest"> | number | null
  isStoryQuest?: Prisma.BoolWithAggregatesFilter<"quest"> | boolean
  chapterId?: Prisma.IntNullableWithAggregatesFilter<"quest"> | number | null
  orderInChapter?: Prisma.IntNullableWithAggregatesFilter<"quest"> | number | null
}

export type questCreateInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questCreateManyInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
}

export type questUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type questUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type QuestNullableScalarRelationFilter = {
  is?: Prisma.questWhereInput | null
  isNot?: Prisma.questWhereInput | null
}

export type QuestListRelationFilter = {
  every?: Prisma.questWhereInput
  some?: Prisma.questWhereInput
  none?: Prisma.questWhereInput
}

export type questOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type questOrderByRelevanceInput = {
  fields: Prisma.questOrderByRelevanceFieldEnum | Prisma.questOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type questChapterIdOrderInChapterCompoundUniqueInput = {
  chapterId: number
  orderInChapter: number
}

export type questCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  questInfo?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  questChainName?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrder
  isStoryQuest?: Prisma.SortOrder
  chapterId?: Prisma.SortOrder
  orderInChapter?: Prisma.SortOrder
}

export type questAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrder
  chapterId?: Prisma.SortOrder
  orderInChapter?: Prisma.SortOrder
}

export type questMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  questInfo?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  questChainName?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrder
  isStoryQuest?: Prisma.SortOrder
  chapterId?: Prisma.SortOrder
  orderInChapter?: Prisma.SortOrder
}

export type questMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  questInfo?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  questChainName?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrder
  isStoryQuest?: Prisma.SortOrder
  chapterId?: Prisma.SortOrder
  orderInChapter?: Prisma.SortOrder
}

export type questSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  repReward?: Prisma.SortOrder
  talentPointReward?: Prisma.SortOrder
  shopId?: Prisma.SortOrder
  requiredQuestId?: Prisma.SortOrder
  chapterId?: Prisma.SortOrder
  orderInChapter?: Prisma.SortOrder
}

export type questCreateNestedOneWithoutOther_questInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutOther_questInput, Prisma.questUncheckedCreateWithoutOther_questInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutOther_questInput
  connect?: Prisma.questWhereUniqueInput
}

export type questCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput> | Prisma.questCreateWithoutQuestInput[] | Prisma.questUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuestInput | Prisma.questCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.questCreateManyQuestInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUncheckedCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput> | Prisma.questCreateWithoutQuestInput[] | Prisma.questUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuestInput | Prisma.questCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.questCreateManyQuestInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUpdateOneWithoutOther_questNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutOther_questInput, Prisma.questUncheckedCreateWithoutOther_questInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutOther_questInput
  upsert?: Prisma.questUpsertWithoutOther_questInput
  disconnect?: Prisma.questWhereInput | boolean
  delete?: Prisma.questWhereInput | boolean
  connect?: Prisma.questWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.questUpdateToOneWithWhereWithoutOther_questInput, Prisma.questUpdateWithoutOther_questInput>, Prisma.questUncheckedUpdateWithoutOther_questInput>
}

export type questUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput> | Prisma.questCreateWithoutQuestInput[] | Prisma.questUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuestInput | Prisma.questCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutQuestInput | Prisma.questUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.questCreateManyQuestInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutQuestInput | Prisma.questUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutQuestInput | Prisma.questUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questUncheckedUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput> | Prisma.questCreateWithoutQuestInput[] | Prisma.questUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuestInput | Prisma.questCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutQuestInput | Prisma.questUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.questCreateManyQuestInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutQuestInput | Prisma.questUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutQuestInput | Prisma.questUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questCreateNestedOneWithoutQuest_progressInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_progressInput, Prisma.questUncheckedCreateWithoutQuest_progressInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_progressInput
  connect?: Prisma.questWhereUniqueInput
}

export type questUpdateOneWithoutQuest_progressNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_progressInput, Prisma.questUncheckedCreateWithoutQuest_progressInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_progressInput
  upsert?: Prisma.questUpsertWithoutQuest_progressInput
  disconnect?: Prisma.questWhereInput | boolean
  delete?: Prisma.questWhereInput | boolean
  connect?: Prisma.questWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.questUpdateToOneWithWhereWithoutQuest_progressInput, Prisma.questUpdateWithoutQuest_progressInput>, Prisma.questUncheckedUpdateWithoutQuest_progressInput>
}

export type questCreateNestedOneWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_objectiveInput, Prisma.questUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_objectiveInput
  connect?: Prisma.questWhereUniqueInput
}

export type questUpdateOneWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_objectiveInput, Prisma.questUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_objectiveInput
  upsert?: Prisma.questUpsertWithoutQuest_objectiveInput
  disconnect?: Prisma.questWhereInput | boolean
  delete?: Prisma.questWhereInput | boolean
  connect?: Prisma.questWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.questUpdateToOneWithWhereWithoutQuest_objectiveInput, Prisma.questUpdateWithoutQuest_objectiveInput>, Prisma.questUncheckedUpdateWithoutQuest_objectiveInput>
}

export type questCreateNestedOneWithoutQuest_rewardInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_rewardInput, Prisma.questUncheckedCreateWithoutQuest_rewardInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_rewardInput
  connect?: Prisma.questWhereUniqueInput
}

export type questUpdateOneWithoutQuest_rewardNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutQuest_rewardInput, Prisma.questUncheckedCreateWithoutQuest_rewardInput>
  connectOrCreate?: Prisma.questCreateOrConnectWithoutQuest_rewardInput
  upsert?: Prisma.questUpsertWithoutQuest_rewardInput
  disconnect?: Prisma.questWhereInput | boolean
  delete?: Prisma.questWhereInput | boolean
  connect?: Prisma.questWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.questUpdateToOneWithWhereWithoutQuest_rewardInput, Prisma.questUpdateWithoutQuest_rewardInput>, Prisma.questUncheckedUpdateWithoutQuest_rewardInput>
}

export type questCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput> | Prisma.questCreateWithoutShopInput[] | Prisma.questUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutShopInput | Prisma.questCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.questCreateManyShopInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUncheckedCreateNestedManyWithoutShopInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput> | Prisma.questCreateWithoutShopInput[] | Prisma.questUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutShopInput | Prisma.questCreateOrConnectWithoutShopInput[]
  createMany?: Prisma.questCreateManyShopInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput> | Prisma.questCreateWithoutShopInput[] | Prisma.questUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutShopInput | Prisma.questCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutShopInput | Prisma.questUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.questCreateManyShopInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutShopInput | Prisma.questUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutShopInput | Prisma.questUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questUncheckedUpdateManyWithoutShopNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput> | Prisma.questCreateWithoutShopInput[] | Prisma.questUncheckedCreateWithoutShopInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutShopInput | Prisma.questCreateOrConnectWithoutShopInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutShopInput | Prisma.questUpsertWithWhereUniqueWithoutShopInput[]
  createMany?: Prisma.questCreateManyShopInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutShopInput | Prisma.questUpdateWithWhereUniqueWithoutShopInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutShopInput | Prisma.questUpdateManyWithWhereWithoutShopInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questCreateNestedManyWithoutStory_chapterInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput> | Prisma.questCreateWithoutStory_chapterInput[] | Prisma.questUncheckedCreateWithoutStory_chapterInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutStory_chapterInput | Prisma.questCreateOrConnectWithoutStory_chapterInput[]
  createMany?: Prisma.questCreateManyStory_chapterInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUncheckedCreateNestedManyWithoutStory_chapterInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput> | Prisma.questCreateWithoutStory_chapterInput[] | Prisma.questUncheckedCreateWithoutStory_chapterInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutStory_chapterInput | Prisma.questCreateOrConnectWithoutStory_chapterInput[]
  createMany?: Prisma.questCreateManyStory_chapterInputEnvelope
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
}

export type questUpdateManyWithoutStory_chapterNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput> | Prisma.questCreateWithoutStory_chapterInput[] | Prisma.questUncheckedCreateWithoutStory_chapterInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutStory_chapterInput | Prisma.questCreateOrConnectWithoutStory_chapterInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutStory_chapterInput | Prisma.questUpsertWithWhereUniqueWithoutStory_chapterInput[]
  createMany?: Prisma.questCreateManyStory_chapterInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutStory_chapterInput | Prisma.questUpdateWithWhereUniqueWithoutStory_chapterInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutStory_chapterInput | Prisma.questUpdateManyWithWhereWithoutStory_chapterInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questUncheckedUpdateManyWithoutStory_chapterNestedInput = {
  create?: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput> | Prisma.questCreateWithoutStory_chapterInput[] | Prisma.questUncheckedCreateWithoutStory_chapterInput[]
  connectOrCreate?: Prisma.questCreateOrConnectWithoutStory_chapterInput | Prisma.questCreateOrConnectWithoutStory_chapterInput[]
  upsert?: Prisma.questUpsertWithWhereUniqueWithoutStory_chapterInput | Prisma.questUpsertWithWhereUniqueWithoutStory_chapterInput[]
  createMany?: Prisma.questCreateManyStory_chapterInputEnvelope
  set?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  disconnect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  delete?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  connect?: Prisma.questWhereUniqueInput | Prisma.questWhereUniqueInput[]
  update?: Prisma.questUpdateWithWhereUniqueWithoutStory_chapterInput | Prisma.questUpdateWithWhereUniqueWithoutStory_chapterInput[]
  updateMany?: Prisma.questUpdateManyWithWhereWithoutStory_chapterInput | Prisma.questUpdateManyWithWhereWithoutStory_chapterInput[]
  deleteMany?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
}

export type questCreateWithoutOther_questInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutOther_questInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutOther_questInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutOther_questInput, Prisma.questUncheckedCreateWithoutOther_questInput>
}

export type questCreateWithoutQuestInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutQuestInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutQuestInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput>
}

export type questCreateManyQuestInputEnvelope = {
  data: Prisma.questCreateManyQuestInput | Prisma.questCreateManyQuestInput[]
  skipDuplicates?: boolean
}

export type questUpsertWithoutOther_questInput = {
  update: Prisma.XOR<Prisma.questUpdateWithoutOther_questInput, Prisma.questUncheckedUpdateWithoutOther_questInput>
  create: Prisma.XOR<Prisma.questCreateWithoutOther_questInput, Prisma.questUncheckedCreateWithoutOther_questInput>
  where?: Prisma.questWhereInput
}

export type questUpdateToOneWithWhereWithoutOther_questInput = {
  where?: Prisma.questWhereInput
  data: Prisma.XOR<Prisma.questUpdateWithoutOther_questInput, Prisma.questUncheckedUpdateWithoutOther_questInput>
}

export type questUpdateWithoutOther_questInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutOther_questInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questUpsertWithWhereUniqueWithoutQuestInput = {
  where: Prisma.questWhereUniqueInput
  update: Prisma.XOR<Prisma.questUpdateWithoutQuestInput, Prisma.questUncheckedUpdateWithoutQuestInput>
  create: Prisma.XOR<Prisma.questCreateWithoutQuestInput, Prisma.questUncheckedCreateWithoutQuestInput>
}

export type questUpdateWithWhereUniqueWithoutQuestInput = {
  where: Prisma.questWhereUniqueInput
  data: Prisma.XOR<Prisma.questUpdateWithoutQuestInput, Prisma.questUncheckedUpdateWithoutQuestInput>
}

export type questUpdateManyWithWhereWithoutQuestInput = {
  where: Prisma.questScalarWhereInput
  data: Prisma.XOR<Prisma.questUpdateManyMutationInput, Prisma.questUncheckedUpdateManyWithoutQuestInput>
}

export type questScalarWhereInput = {
  AND?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
  OR?: Prisma.questScalarWhereInput[]
  NOT?: Prisma.questScalarWhereInput | Prisma.questScalarWhereInput[]
  id?: Prisma.IntFilter<"quest"> | number
  name?: Prisma.StringFilter<"quest"> | string
  description?: Prisma.StringNullableFilter<"quest"> | string | null
  questInfo?: Prisma.StringNullableFilter<"quest"> | string | null
  levelReq?: Prisma.IntFilter<"quest"> | number
  disabled?: Prisma.BoolNullableFilter<"quest"> | boolean | null
  questChainName?: Prisma.StringNullableFilter<"quest"> | string | null
  xpReward?: Prisma.IntNullableFilter<"quest"> | number | null
  cashReward?: Prisma.IntFilter<"quest"> | number
  repReward?: Prisma.FloatFilter<"quest"> | number
  talentPointReward?: Prisma.IntFilter<"quest"> | number
  shopId?: Prisma.IntNullableFilter<"quest"> | number | null
  requiredQuestId?: Prisma.IntNullableFilter<"quest"> | number | null
  isStoryQuest?: Prisma.BoolFilter<"quest"> | boolean
  chapterId?: Prisma.IntNullableFilter<"quest"> | number | null
  orderInChapter?: Prisma.IntNullableFilter<"quest"> | number | null
}

export type questCreateWithoutQuest_progressInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutQuest_progressInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutQuest_progressInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_progressInput, Prisma.questUncheckedCreateWithoutQuest_progressInput>
}

export type questUpsertWithoutQuest_progressInput = {
  update: Prisma.XOR<Prisma.questUpdateWithoutQuest_progressInput, Prisma.questUncheckedUpdateWithoutQuest_progressInput>
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_progressInput, Prisma.questUncheckedCreateWithoutQuest_progressInput>
  where?: Prisma.questWhereInput
}

export type questUpdateToOneWithWhereWithoutQuest_progressInput = {
  where?: Prisma.questWhereInput
  data: Prisma.XOR<Prisma.questUpdateWithoutQuest_progressInput, Prisma.questUncheckedUpdateWithoutQuest_progressInput>
}

export type questUpdateWithoutQuest_progressInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutQuest_progressInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questCreateWithoutQuest_objectiveInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutQuest_objectiveInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutQuest_objectiveInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_objectiveInput, Prisma.questUncheckedCreateWithoutQuest_objectiveInput>
}

export type questUpsertWithoutQuest_objectiveInput = {
  update: Prisma.XOR<Prisma.questUpdateWithoutQuest_objectiveInput, Prisma.questUncheckedUpdateWithoutQuest_objectiveInput>
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_objectiveInput, Prisma.questUncheckedCreateWithoutQuest_objectiveInput>
  where?: Prisma.questWhereInput
}

export type questUpdateToOneWithWhereWithoutQuest_objectiveInput = {
  where?: Prisma.questWhereInput
  data: Prisma.XOR<Prisma.questUpdateWithoutQuest_objectiveInput, Prisma.questUncheckedUpdateWithoutQuest_objectiveInput>
}

export type questUpdateWithoutQuest_objectiveInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
}

export type questCreateWithoutQuest_rewardInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutQuest_rewardInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutQuest_rewardInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_rewardInput, Prisma.questUncheckedCreateWithoutQuest_rewardInput>
}

export type questUpsertWithoutQuest_rewardInput = {
  update: Prisma.XOR<Prisma.questUpdateWithoutQuest_rewardInput, Prisma.questUncheckedUpdateWithoutQuest_rewardInput>
  create: Prisma.XOR<Prisma.questCreateWithoutQuest_rewardInput, Prisma.questUncheckedCreateWithoutQuest_rewardInput>
  where?: Prisma.questWhereInput
}

export type questUpdateToOneWithWhereWithoutQuest_rewardInput = {
  where?: Prisma.questWhereInput
  data: Prisma.XOR<Prisma.questUpdateWithoutQuest_rewardInput, Prisma.questUncheckedUpdateWithoutQuest_rewardInput>
}

export type questUpdateWithoutQuest_rewardInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutQuest_rewardInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questCreateWithoutShopInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  story_chapter?: Prisma.story_chapterCreateNestedOneWithoutQuestsInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutShopInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutShopInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput>
}

export type questCreateManyShopInputEnvelope = {
  data: Prisma.questCreateManyShopInput | Prisma.questCreateManyShopInput[]
  skipDuplicates?: boolean
}

export type questUpsertWithWhereUniqueWithoutShopInput = {
  where: Prisma.questWhereUniqueInput
  update: Prisma.XOR<Prisma.questUpdateWithoutShopInput, Prisma.questUncheckedUpdateWithoutShopInput>
  create: Prisma.XOR<Prisma.questCreateWithoutShopInput, Prisma.questUncheckedCreateWithoutShopInput>
}

export type questUpdateWithWhereUniqueWithoutShopInput = {
  where: Prisma.questWhereUniqueInput
  data: Prisma.XOR<Prisma.questUpdateWithoutShopInput, Prisma.questUncheckedUpdateWithoutShopInput>
}

export type questUpdateManyWithWhereWithoutShopInput = {
  where: Prisma.questScalarWhereInput
  data: Prisma.XOR<Prisma.questUpdateManyMutationInput, Prisma.questUncheckedUpdateManyWithoutShopInput>
}

export type questCreateWithoutStory_chapterInput = {
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  isStoryQuest?: boolean
  orderInChapter?: number | null
  shop?: Prisma.shopCreateNestedOneWithoutQuestInput
  quest?: Prisma.questCreateNestedOneWithoutOther_questInput
  other_quest?: Prisma.questCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutQuestInput
}

export type questUncheckedCreateWithoutStory_chapterInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  orderInChapter?: number | null
  other_quest?: Prisma.questUncheckedCreateNestedManyWithoutQuestInput
  quest_progress?: Prisma.quest_progressUncheckedCreateNestedManyWithoutQuestInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutQuestInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutQuestInput
}

export type questCreateOrConnectWithoutStory_chapterInput = {
  where: Prisma.questWhereUniqueInput
  create: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput>
}

export type questCreateManyStory_chapterInputEnvelope = {
  data: Prisma.questCreateManyStory_chapterInput | Prisma.questCreateManyStory_chapterInput[]
  skipDuplicates?: boolean
}

export type questUpsertWithWhereUniqueWithoutStory_chapterInput = {
  where: Prisma.questWhereUniqueInput
  update: Prisma.XOR<Prisma.questUpdateWithoutStory_chapterInput, Prisma.questUncheckedUpdateWithoutStory_chapterInput>
  create: Prisma.XOR<Prisma.questCreateWithoutStory_chapterInput, Prisma.questUncheckedCreateWithoutStory_chapterInput>
}

export type questUpdateWithWhereUniqueWithoutStory_chapterInput = {
  where: Prisma.questWhereUniqueInput
  data: Prisma.XOR<Prisma.questUpdateWithoutStory_chapterInput, Prisma.questUncheckedUpdateWithoutStory_chapterInput>
}

export type questUpdateManyWithWhereWithoutStory_chapterInput = {
  where: Prisma.questScalarWhereInput
  data: Prisma.XOR<Prisma.questUpdateManyMutationInput, Prisma.questUncheckedUpdateManyWithoutStory_chapterInput>
}

export type questCreateManyQuestInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
}

export type questUpdateWithoutQuestInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateManyWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type questCreateManyShopInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  chapterId?: number | null
  orderInChapter?: number | null
}

export type questUpdateWithoutShopInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_chapter?: Prisma.story_chapterUpdateOneWithoutQuestsNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateManyWithoutShopInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  chapterId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type questCreateManyStory_chapterInput = {
  id?: number
  name: string
  description?: string | null
  questInfo?: string | null
  levelReq?: number
  disabled?: boolean | null
  questChainName?: string | null
  xpReward?: number | null
  cashReward?: number
  repReward?: number
  talentPointReward?: number
  shopId?: number | null
  requiredQuestId?: number | null
  isStoryQuest?: boolean
  orderInChapter?: number | null
}

export type questUpdateWithoutStory_chapterInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  shop?: Prisma.shopUpdateOneWithoutQuestNestedInput
  quest?: Prisma.questUpdateOneWithoutOther_questNestedInput
  other_quest?: Prisma.questUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateWithoutStory_chapterInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  other_quest?: Prisma.questUncheckedUpdateManyWithoutQuestNestedInput
  quest_progress?: Prisma.quest_progressUncheckedUpdateManyWithoutQuestNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutQuestNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput
}

export type questUncheckedUpdateManyWithoutStory_chapterInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  questInfo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  questChainName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  xpReward?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  repReward?: Prisma.FloatFieldUpdateOperationsInput | number
  talentPointReward?: Prisma.IntFieldUpdateOperationsInput | number
  shopId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  requiredQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  isStoryQuest?: Prisma.BoolFieldUpdateOperationsInput | boolean
  orderInChapter?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}


/**
 * Count Type QuestCountOutputType
 */

export type QuestCountOutputType = {
  other_quest: number
  quest_progress: number
  quest_reward: number
  quest_objective: number
}

export type QuestCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  other_quest?: boolean | QuestCountOutputTypeCountOther_questArgs
  quest_progress?: boolean | QuestCountOutputTypeCountQuest_progressArgs
  quest_reward?: boolean | QuestCountOutputTypeCountQuest_rewardArgs
  quest_objective?: boolean | QuestCountOutputTypeCountQuest_objectiveArgs
}

/**
 * QuestCountOutputType without action
 */
export type QuestCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the QuestCountOutputType
   */
  select?: Prisma.QuestCountOutputTypeSelect<ExtArgs> | null
}

/**
 * QuestCountOutputType without action
 */
export type QuestCountOutputTypeCountOther_questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.questWhereInput
}

/**
 * QuestCountOutputType without action
 */
export type QuestCountOutputTypeCountQuest_progressArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_progressWhereInput
}

/**
 * QuestCountOutputType without action
 */
export type QuestCountOutputTypeCountQuest_rewardArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_rewardWhereInput
}

/**
 * QuestCountOutputType without action
 */
export type QuestCountOutputTypeCountQuest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objectiveWhereInput
}


export type questSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  questInfo?: boolean
  levelReq?: boolean
  disabled?: boolean
  questChainName?: boolean
  xpReward?: boolean
  cashReward?: boolean
  repReward?: boolean
  talentPointReward?: boolean
  shopId?: boolean
  requiredQuestId?: boolean
  isStoryQuest?: boolean
  chapterId?: boolean
  orderInChapter?: boolean
  story_chapter?: boolean | Prisma.quest$story_chapterArgs<ExtArgs>
  shop?: boolean | Prisma.quest$shopArgs<ExtArgs>
  quest?: boolean | Prisma.quest$questArgs<ExtArgs>
  other_quest?: boolean | Prisma.quest$other_questArgs<ExtArgs>
  quest_progress?: boolean | Prisma.quest$quest_progressArgs<ExtArgs>
  quest_reward?: boolean | Prisma.quest$quest_rewardArgs<ExtArgs>
  quest_objective?: boolean | Prisma.quest$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.QuestCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["quest"]>



export type questSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  questInfo?: boolean
  levelReq?: boolean
  disabled?: boolean
  questChainName?: boolean
  xpReward?: boolean
  cashReward?: boolean
  repReward?: boolean
  talentPointReward?: boolean
  shopId?: boolean
  requiredQuestId?: boolean
  isStoryQuest?: boolean
  chapterId?: boolean
  orderInChapter?: boolean
}

export type questOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "questInfo" | "levelReq" | "disabled" | "questChainName" | "xpReward" | "cashReward" | "repReward" | "talentPointReward" | "shopId" | "requiredQuestId" | "isStoryQuest" | "chapterId" | "orderInChapter", ExtArgs["result"]["quest"]>
export type questInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  story_chapter?: boolean | Prisma.quest$story_chapterArgs<ExtArgs>
  shop?: boolean | Prisma.quest$shopArgs<ExtArgs>
  quest?: boolean | Prisma.quest$questArgs<ExtArgs>
  other_quest?: boolean | Prisma.quest$other_questArgs<ExtArgs>
  quest_progress?: boolean | Prisma.quest$quest_progressArgs<ExtArgs>
  quest_reward?: boolean | Prisma.quest$quest_rewardArgs<ExtArgs>
  quest_objective?: boolean | Prisma.quest$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.QuestCountOutputTypeDefaultArgs<ExtArgs>
}

export type $questPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "quest"
  objects: {
    story_chapter: Prisma.$story_chapterPayload<ExtArgs> | null
    shop: Prisma.$shopPayload<ExtArgs> | null
    quest: Prisma.$questPayload<ExtArgs> | null
    other_quest: Prisma.$questPayload<ExtArgs>[]
    quest_progress: Prisma.$quest_progressPayload<ExtArgs>[]
    quest_reward: Prisma.$quest_rewardPayload<ExtArgs>[]
    quest_objective: Prisma.$quest_objectivePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    description: string | null
    questInfo: string | null
    levelReq: number
    disabled: boolean | null
    questChainName: string | null
    xpReward: number | null
    cashReward: number
    repReward: number
    talentPointReward: number
    shopId: number | null
    requiredQuestId: number | null
    isStoryQuest: boolean
    chapterId: number | null
    orderInChapter: number | null
  }, ExtArgs["result"]["quest"]>
  composites: {}
}

export type questGetPayload<S extends boolean | null | undefined | questDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$questPayload, S>

export type questCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<questFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: QuestCountAggregateInputType | true
  }

export interface questDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['quest'], meta: { name: 'quest' } }
  /**
   * Find zero or one Quest that matches the filter.
   * @param {questFindUniqueArgs} args - Arguments to find a Quest
   * @example
   * // Get one Quest
   * const quest = await prisma.quest.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends questFindUniqueArgs>(args: Prisma.SelectSubset<T, questFindUniqueArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Quest that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {questFindUniqueOrThrowArgs} args - Arguments to find a Quest
   * @example
   * // Get one Quest
   * const quest = await prisma.quest.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends questFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, questFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questFindFirstArgs} args - Arguments to find a Quest
   * @example
   * // Get one Quest
   * const quest = await prisma.quest.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends questFindFirstArgs>(args?: Prisma.SelectSubset<T, questFindFirstArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questFindFirstOrThrowArgs} args - Arguments to find a Quest
   * @example
   * // Get one Quest
   * const quest = await prisma.quest.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends questFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, questFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Quests that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Quests
   * const quests = await prisma.quest.findMany()
   * 
   * // Get first 10 Quests
   * const quests = await prisma.quest.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const questWithIdOnly = await prisma.quest.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends questFindManyArgs>(args?: Prisma.SelectSubset<T, questFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Quest.
   * @param {questCreateArgs} args - Arguments to create a Quest.
   * @example
   * // Create one Quest
   * const Quest = await prisma.quest.create({
   *   data: {
   *     // ... data to create a Quest
   *   }
   * })
   * 
   */
  create<T extends questCreateArgs>(args: Prisma.SelectSubset<T, questCreateArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Quests.
   * @param {questCreateManyArgs} args - Arguments to create many Quests.
   * @example
   * // Create many Quests
   * const quest = await prisma.quest.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends questCreateManyArgs>(args?: Prisma.SelectSubset<T, questCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Quest.
   * @param {questDeleteArgs} args - Arguments to delete one Quest.
   * @example
   * // Delete one Quest
   * const Quest = await prisma.quest.delete({
   *   where: {
   *     // ... filter to delete one Quest
   *   }
   * })
   * 
   */
  delete<T extends questDeleteArgs>(args: Prisma.SelectSubset<T, questDeleteArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Quest.
   * @param {questUpdateArgs} args - Arguments to update one Quest.
   * @example
   * // Update one Quest
   * const quest = await prisma.quest.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends questUpdateArgs>(args: Prisma.SelectSubset<T, questUpdateArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Quests.
   * @param {questDeleteManyArgs} args - Arguments to filter Quests to delete.
   * @example
   * // Delete a few Quests
   * const { count } = await prisma.quest.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends questDeleteManyArgs>(args?: Prisma.SelectSubset<T, questDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Quests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Quests
   * const quest = await prisma.quest.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends questUpdateManyArgs>(args: Prisma.SelectSubset<T, questUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Quest.
   * @param {questUpsertArgs} args - Arguments to update or create a Quest.
   * @example
   * // Update or create a Quest
   * const quest = await prisma.quest.upsert({
   *   create: {
   *     // ... data to create a Quest
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Quest we want to update
   *   }
   * })
   */
  upsert<T extends questUpsertArgs>(args: Prisma.SelectSubset<T, questUpsertArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Quests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questCountArgs} args - Arguments to filter Quests to count.
   * @example
   * // Count the number of Quests
   * const count = await prisma.quest.count({
   *   where: {
   *     // ... the filter for the Quests we want to count
   *   }
   * })
  **/
  count<T extends questCountArgs>(
    args?: Prisma.Subset<T, questCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], QuestCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Quest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {QuestAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends QuestAggregateArgs>(args: Prisma.Subset<T, QuestAggregateArgs>): Prisma.PrismaPromise<GetQuestAggregateType<T>>

  /**
   * Group by Quest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {questGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends questGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: questGroupByArgs['orderBy'] }
      : { orderBy?: questGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, questGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQuestGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the quest model
 */
readonly fields: questFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for quest.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__questClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  story_chapter<T extends Prisma.quest$story_chapterArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$story_chapterArgs<ExtArgs>>): Prisma.Prisma__story_chapterClient<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  shop<T extends Prisma.quest$shopArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$shopArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  quest<T extends Prisma.quest$questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$questArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  other_quest<T extends Prisma.quest$other_questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$other_questArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  quest_progress<T extends Prisma.quest$quest_progressArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$quest_progressArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  quest_reward<T extends Prisma.quest$quest_rewardArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$quest_rewardArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  quest_objective<T extends Prisma.quest$quest_objectiveArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest$quest_objectiveArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the quest model
 */
export interface questFieldRefs {
  readonly id: Prisma.FieldRef<"quest", 'Int'>
  readonly name: Prisma.FieldRef<"quest", 'String'>
  readonly description: Prisma.FieldRef<"quest", 'String'>
  readonly questInfo: Prisma.FieldRef<"quest", 'String'>
  readonly levelReq: Prisma.FieldRef<"quest", 'Int'>
  readonly disabled: Prisma.FieldRef<"quest", 'Boolean'>
  readonly questChainName: Prisma.FieldRef<"quest", 'String'>
  readonly xpReward: Prisma.FieldRef<"quest", 'Int'>
  readonly cashReward: Prisma.FieldRef<"quest", 'Int'>
  readonly repReward: Prisma.FieldRef<"quest", 'Float'>
  readonly talentPointReward: Prisma.FieldRef<"quest", 'Int'>
  readonly shopId: Prisma.FieldRef<"quest", 'Int'>
  readonly requiredQuestId: Prisma.FieldRef<"quest", 'Int'>
  readonly isStoryQuest: Prisma.FieldRef<"quest", 'Boolean'>
  readonly chapterId: Prisma.FieldRef<"quest", 'Int'>
  readonly orderInChapter: Prisma.FieldRef<"quest", 'Int'>
}
    

// Custom InputTypes
/**
 * quest findUnique
 */
export type questFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter, which quest to fetch.
   */
  where: Prisma.questWhereUniqueInput
}

/**
 * quest findUniqueOrThrow
 */
export type questFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter, which quest to fetch.
   */
  where: Prisma.questWhereUniqueInput
}

/**
 * quest findFirst
 */
export type questFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter, which quest to fetch.
   */
  where?: Prisma.questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quests to fetch.
   */
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quests.
   */
  cursor?: Prisma.questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quests.
   */
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * quest findFirstOrThrow
 */
export type questFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter, which quest to fetch.
   */
  where?: Prisma.questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quests to fetch.
   */
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quests.
   */
  cursor?: Prisma.questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quests.
   */
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * quest findMany
 */
export type questFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter, which quests to fetch.
   */
  where?: Prisma.questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quests to fetch.
   */
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing quests.
   */
  cursor?: Prisma.questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quests.
   */
  skip?: number
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * quest create
 */
export type questCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * The data needed to create a quest.
   */
  data: Prisma.XOR<Prisma.questCreateInput, Prisma.questUncheckedCreateInput>
}

/**
 * quest createMany
 */
export type questCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many quests.
   */
  data: Prisma.questCreateManyInput | Prisma.questCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * quest update
 */
export type questUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * The data needed to update a quest.
   */
  data: Prisma.XOR<Prisma.questUpdateInput, Prisma.questUncheckedUpdateInput>
  /**
   * Choose, which quest to update.
   */
  where: Prisma.questWhereUniqueInput
}

/**
 * quest updateMany
 */
export type questUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update quests.
   */
  data: Prisma.XOR<Prisma.questUpdateManyMutationInput, Prisma.questUncheckedUpdateManyInput>
  /**
   * Filter which quests to update
   */
  where?: Prisma.questWhereInput
  /**
   * Limit how many quests to update.
   */
  limit?: number
}

/**
 * quest upsert
 */
export type questUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * The filter to search for the quest to update in case it exists.
   */
  where: Prisma.questWhereUniqueInput
  /**
   * In case the quest found by the `where` argument doesn't exist, create a new quest with this data.
   */
  create: Prisma.XOR<Prisma.questCreateInput, Prisma.questUncheckedCreateInput>
  /**
   * In case the quest was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.questUpdateInput, Prisma.questUncheckedUpdateInput>
}

/**
 * quest delete
 */
export type questDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  /**
   * Filter which quest to delete.
   */
  where: Prisma.questWhereUniqueInput
}

/**
 * quest deleteMany
 */
export type questDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quests to delete
   */
  where?: Prisma.questWhereInput
  /**
   * Limit how many quests to delete.
   */
  limit?: number
}

/**
 * quest.story_chapter
 */
export type quest$story_chapterArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  where?: Prisma.story_chapterWhereInput
}

/**
 * quest.shop
 */
export type quest$shopArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  where?: Prisma.shopWhereInput
}

/**
 * quest.quest
 */
export type quest$questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
}

/**
 * quest.other_quest
 */
export type quest$other_questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  cursor?: Prisma.questWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * quest.quest_progress
 */
export type quest$quest_progressArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  where?: Prisma.quest_progressWhereInput
  orderBy?: Prisma.quest_progressOrderByWithRelationInput | Prisma.quest_progressOrderByWithRelationInput[]
  cursor?: Prisma.quest_progressWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_progressScalarFieldEnum | Prisma.Quest_progressScalarFieldEnum[]
}

/**
 * quest.quest_reward
 */
export type quest$quest_rewardArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  where?: Prisma.quest_rewardWhereInput
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  cursor?: Prisma.quest_rewardWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_rewardScalarFieldEnum | Prisma.Quest_rewardScalarFieldEnum[]
}

/**
 * quest.quest_objective
 */
export type quest$quest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  where?: Prisma.quest_objectiveWhereInput
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * quest without action
 */
export type questDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
}
