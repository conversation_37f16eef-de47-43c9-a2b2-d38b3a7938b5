
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_achievements` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_achievements
 * 
 */
export type user_achievementsModel = runtime.Types.Result.DefaultSelection<Prisma.$user_achievementsPayload>

export type AggregateUser_achievements = {
  _count: User_achievementsCountAggregateOutputType | null
  _avg: User_achievementsAvgAggregateOutputType | null
  _sum: User_achievementsSumAggregateOutputType | null
  _min: User_achievementsMinAggregateOutputType | null
  _max: User_achievementsMaxAggregateOutputType | null
}

export type User_achievementsAvgAggregateOutputType = {
  userId: number | null
  battleWins: number | null
  npcBattleWins: number | null
  craftsCompleted: number | null
  marketItemsSold: number | null
  marketMoneyMade: number | null
  totalMuggingGain: number | null
  totalMuggingLoss: number | null
  totalCasinoProfitLoss: number | null
  questsCompleted: number | null
  dailyQuestsCompleted: number | null
  coursesCompleted: number | null
  roguelikeNodesCompleted: number | null
  roguelikeMapsCompleted: number | null
  examsCompleted: number | null
  totalBountyRewards: number | null
  totalBountyPlaced: number | null
  totalMissionHours: number | null
  suggestionsVoted: number | null
  encountersCompleted: number | null
}

export type User_achievementsSumAggregateOutputType = {
  userId: number | null
  battleWins: number | null
  npcBattleWins: number | null
  craftsCompleted: number | null
  marketItemsSold: number | null
  marketMoneyMade: number | null
  totalMuggingGain: number | null
  totalMuggingLoss: number | null
  totalCasinoProfitLoss: number | null
  questsCompleted: number | null
  dailyQuestsCompleted: number | null
  coursesCompleted: number | null
  roguelikeNodesCompleted: number | null
  roguelikeMapsCompleted: number | null
  examsCompleted: number | null
  totalBountyRewards: number | null
  totalBountyPlaced: number | null
  totalMissionHours: number | null
  suggestionsVoted: number | null
  encountersCompleted: number | null
}

export type User_achievementsMinAggregateOutputType = {
  userId: number | null
  battleWins: number | null
  npcBattleWins: number | null
  craftsCompleted: number | null
  marketItemsSold: number | null
  marketMoneyMade: number | null
  totalMuggingGain: number | null
  totalMuggingLoss: number | null
  totalCasinoProfitLoss: number | null
  questsCompleted: number | null
  dailyQuestsCompleted: number | null
  coursesCompleted: number | null
  roguelikeNodesCompleted: number | null
  roguelikeMapsCompleted: number | null
  examsCompleted: number | null
  totalBountyRewards: number | null
  totalBountyPlaced: number | null
  totalMissionHours: number | null
  suggestionsVoted: number | null
  encountersCompleted: number | null
}

export type User_achievementsMaxAggregateOutputType = {
  userId: number | null
  battleWins: number | null
  npcBattleWins: number | null
  craftsCompleted: number | null
  marketItemsSold: number | null
  marketMoneyMade: number | null
  totalMuggingGain: number | null
  totalMuggingLoss: number | null
  totalCasinoProfitLoss: number | null
  questsCompleted: number | null
  dailyQuestsCompleted: number | null
  coursesCompleted: number | null
  roguelikeNodesCompleted: number | null
  roguelikeMapsCompleted: number | null
  examsCompleted: number | null
  totalBountyRewards: number | null
  totalBountyPlaced: number | null
  totalMissionHours: number | null
  suggestionsVoted: number | null
  encountersCompleted: number | null
}

export type User_achievementsCountAggregateOutputType = {
  userId: number
  battleWins: number
  npcBattleWins: number
  craftsCompleted: number
  marketItemsSold: number
  marketMoneyMade: number
  totalMuggingGain: number
  totalMuggingLoss: number
  totalCasinoProfitLoss: number
  questsCompleted: number
  dailyQuestsCompleted: number
  coursesCompleted: number
  roguelikeNodesCompleted: number
  roguelikeMapsCompleted: number
  examsCompleted: number
  totalBountyRewards: number
  totalBountyPlaced: number
  totalMissionHours: number
  suggestionsVoted: number
  encountersCompleted: number
  _all: number
}


export type User_achievementsAvgAggregateInputType = {
  userId?: true
  battleWins?: true
  npcBattleWins?: true
  craftsCompleted?: true
  marketItemsSold?: true
  marketMoneyMade?: true
  totalMuggingGain?: true
  totalMuggingLoss?: true
  totalCasinoProfitLoss?: true
  questsCompleted?: true
  dailyQuestsCompleted?: true
  coursesCompleted?: true
  roguelikeNodesCompleted?: true
  roguelikeMapsCompleted?: true
  examsCompleted?: true
  totalBountyRewards?: true
  totalBountyPlaced?: true
  totalMissionHours?: true
  suggestionsVoted?: true
  encountersCompleted?: true
}

export type User_achievementsSumAggregateInputType = {
  userId?: true
  battleWins?: true
  npcBattleWins?: true
  craftsCompleted?: true
  marketItemsSold?: true
  marketMoneyMade?: true
  totalMuggingGain?: true
  totalMuggingLoss?: true
  totalCasinoProfitLoss?: true
  questsCompleted?: true
  dailyQuestsCompleted?: true
  coursesCompleted?: true
  roguelikeNodesCompleted?: true
  roguelikeMapsCompleted?: true
  examsCompleted?: true
  totalBountyRewards?: true
  totalBountyPlaced?: true
  totalMissionHours?: true
  suggestionsVoted?: true
  encountersCompleted?: true
}

export type User_achievementsMinAggregateInputType = {
  userId?: true
  battleWins?: true
  npcBattleWins?: true
  craftsCompleted?: true
  marketItemsSold?: true
  marketMoneyMade?: true
  totalMuggingGain?: true
  totalMuggingLoss?: true
  totalCasinoProfitLoss?: true
  questsCompleted?: true
  dailyQuestsCompleted?: true
  coursesCompleted?: true
  roguelikeNodesCompleted?: true
  roguelikeMapsCompleted?: true
  examsCompleted?: true
  totalBountyRewards?: true
  totalBountyPlaced?: true
  totalMissionHours?: true
  suggestionsVoted?: true
  encountersCompleted?: true
}

export type User_achievementsMaxAggregateInputType = {
  userId?: true
  battleWins?: true
  npcBattleWins?: true
  craftsCompleted?: true
  marketItemsSold?: true
  marketMoneyMade?: true
  totalMuggingGain?: true
  totalMuggingLoss?: true
  totalCasinoProfitLoss?: true
  questsCompleted?: true
  dailyQuestsCompleted?: true
  coursesCompleted?: true
  roguelikeNodesCompleted?: true
  roguelikeMapsCompleted?: true
  examsCompleted?: true
  totalBountyRewards?: true
  totalBountyPlaced?: true
  totalMissionHours?: true
  suggestionsVoted?: true
  encountersCompleted?: true
}

export type User_achievementsCountAggregateInputType = {
  userId?: true
  battleWins?: true
  npcBattleWins?: true
  craftsCompleted?: true
  marketItemsSold?: true
  marketMoneyMade?: true
  totalMuggingGain?: true
  totalMuggingLoss?: true
  totalCasinoProfitLoss?: true
  questsCompleted?: true
  dailyQuestsCompleted?: true
  coursesCompleted?: true
  roguelikeNodesCompleted?: true
  roguelikeMapsCompleted?: true
  examsCompleted?: true
  totalBountyRewards?: true
  totalBountyPlaced?: true
  totalMissionHours?: true
  suggestionsVoted?: true
  encountersCompleted?: true
  _all?: true
}

export type User_achievementsAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_achievements to aggregate.
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_achievements to fetch.
   */
  orderBy?: Prisma.user_achievementsOrderByWithRelationInput | Prisma.user_achievementsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_achievementsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_achievements from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_achievements.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_achievements
  **/
  _count?: true | User_achievementsCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_achievementsAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_achievementsSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_achievementsMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_achievementsMaxAggregateInputType
}

export type GetUser_achievementsAggregateType<T extends User_achievementsAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_achievements]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_achievements[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_achievements[P]>
}




export type user_achievementsGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_achievementsWhereInput
  orderBy?: Prisma.user_achievementsOrderByWithAggregationInput | Prisma.user_achievementsOrderByWithAggregationInput[]
  by: Prisma.User_achievementsScalarFieldEnum[] | Prisma.User_achievementsScalarFieldEnum
  having?: Prisma.user_achievementsScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_achievementsCountAggregateInputType | true
  _avg?: User_achievementsAvgAggregateInputType
  _sum?: User_achievementsSumAggregateInputType
  _min?: User_achievementsMinAggregateInputType
  _max?: User_achievementsMaxAggregateInputType
}

export type User_achievementsGroupByOutputType = {
  userId: number
  battleWins: number | null
  npcBattleWins: number | null
  craftsCompleted: number | null
  marketItemsSold: number | null
  marketMoneyMade: number | null
  totalMuggingGain: number | null
  totalMuggingLoss: number | null
  totalCasinoProfitLoss: number | null
  questsCompleted: number | null
  dailyQuestsCompleted: number | null
  coursesCompleted: number | null
  roguelikeNodesCompleted: number | null
  roguelikeMapsCompleted: number | null
  examsCompleted: number | null
  totalBountyRewards: number | null
  totalBountyPlaced: number | null
  totalMissionHours: number | null
  suggestionsVoted: number | null
  encountersCompleted: number | null
  _count: User_achievementsCountAggregateOutputType | null
  _avg: User_achievementsAvgAggregateOutputType | null
  _sum: User_achievementsSumAggregateOutputType | null
  _min: User_achievementsMinAggregateOutputType | null
  _max: User_achievementsMaxAggregateOutputType | null
}

type GetUser_achievementsGroupByPayload<T extends user_achievementsGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_achievementsGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_achievementsGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_achievementsGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_achievementsGroupByOutputType[P]>
      }
    >
  >



export type user_achievementsWhereInput = {
  AND?: Prisma.user_achievementsWhereInput | Prisma.user_achievementsWhereInput[]
  OR?: Prisma.user_achievementsWhereInput[]
  NOT?: Prisma.user_achievementsWhereInput | Prisma.user_achievementsWhereInput[]
  userId?: Prisma.IntFilter<"user_achievements"> | number
  battleWins?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  npcBattleWins?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  craftsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  marketItemsSold?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  marketMoneyMade?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMuggingGain?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMuggingLoss?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalCasinoProfitLoss?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  questsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  dailyQuestsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  coursesCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  roguelikeNodesCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  roguelikeMapsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  examsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalBountyRewards?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalBountyPlaced?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMissionHours?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  suggestionsVoted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  encountersCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type user_achievementsOrderByWithRelationInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrderInput | Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrderInput | Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrderInput | Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrderInput | Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrderInput | Prisma.SortOrder
  questsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  examsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrderInput | Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrderInput | Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
}

export type user_achievementsWhereUniqueInput = Prisma.AtLeast<{
  userId?: number
  AND?: Prisma.user_achievementsWhereInput | Prisma.user_achievementsWhereInput[]
  OR?: Prisma.user_achievementsWhereInput[]
  NOT?: Prisma.user_achievementsWhereInput | Prisma.user_achievementsWhereInput[]
  battleWins?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  npcBattleWins?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  craftsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  marketItemsSold?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  marketMoneyMade?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMuggingGain?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMuggingLoss?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalCasinoProfitLoss?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  questsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  dailyQuestsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  coursesCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  roguelikeNodesCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  roguelikeMapsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  examsCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalBountyRewards?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalBountyPlaced?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  totalMissionHours?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  suggestionsVoted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  encountersCompleted?: Prisma.IntNullableFilter<"user_achievements"> | number | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "userId">

export type user_achievementsOrderByWithAggregationInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrderInput | Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrderInput | Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrderInput | Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrderInput | Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrderInput | Prisma.SortOrder
  questsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  examsCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrderInput | Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrderInput | Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_achievementsCountOrderByAggregateInput
  _avg?: Prisma.user_achievementsAvgOrderByAggregateInput
  _max?: Prisma.user_achievementsMaxOrderByAggregateInput
  _min?: Prisma.user_achievementsMinOrderByAggregateInput
  _sum?: Prisma.user_achievementsSumOrderByAggregateInput
}

export type user_achievementsScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_achievementsScalarWhereWithAggregatesInput | Prisma.user_achievementsScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_achievementsScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_achievementsScalarWhereWithAggregatesInput | Prisma.user_achievementsScalarWhereWithAggregatesInput[]
  userId?: Prisma.IntWithAggregatesFilter<"user_achievements"> | number
  battleWins?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  npcBattleWins?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  craftsCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  marketItemsSold?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  marketMoneyMade?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalMuggingGain?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalMuggingLoss?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalCasinoProfitLoss?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  questsCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  dailyQuestsCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  coursesCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  roguelikeNodesCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  roguelikeMapsCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  examsCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalBountyRewards?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalBountyPlaced?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  totalMissionHours?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  suggestionsVoted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
  encountersCompleted?: Prisma.IntNullableWithAggregatesFilter<"user_achievements"> | number | null
}

export type user_achievementsCreateInput = {
  battleWins?: number | null
  npcBattleWins?: number | null
  craftsCompleted?: number | null
  marketItemsSold?: number | null
  marketMoneyMade?: number | null
  totalMuggingGain?: number | null
  totalMuggingLoss?: number | null
  totalCasinoProfitLoss?: number | null
  questsCompleted?: number | null
  dailyQuestsCompleted?: number | null
  coursesCompleted?: number | null
  roguelikeNodesCompleted?: number | null
  roguelikeMapsCompleted?: number | null
  examsCompleted?: number | null
  totalBountyRewards?: number | null
  totalBountyPlaced?: number | null
  totalMissionHours?: number | null
  suggestionsVoted?: number | null
  encountersCompleted?: number | null
  user: Prisma.userCreateNestedOneWithoutUser_achievementsInput
}

export type user_achievementsUncheckedCreateInput = {
  userId: number
  battleWins?: number | null
  npcBattleWins?: number | null
  craftsCompleted?: number | null
  marketItemsSold?: number | null
  marketMoneyMade?: number | null
  totalMuggingGain?: number | null
  totalMuggingLoss?: number | null
  totalCasinoProfitLoss?: number | null
  questsCompleted?: number | null
  dailyQuestsCompleted?: number | null
  coursesCompleted?: number | null
  roguelikeNodesCompleted?: number | null
  roguelikeMapsCompleted?: number | null
  examsCompleted?: number | null
  totalBountyRewards?: number | null
  totalBountyPlaced?: number | null
  totalMissionHours?: number | null
  suggestionsVoted?: number | null
  encountersCompleted?: number | null
}

export type user_achievementsUpdateInput = {
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  user?: Prisma.userUpdateOneRequiredWithoutUser_achievementsNestedInput
}

export type user_achievementsUncheckedUpdateInput = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_achievementsCreateManyInput = {
  userId: number
  battleWins?: number | null
  npcBattleWins?: number | null
  craftsCompleted?: number | null
  marketItemsSold?: number | null
  marketMoneyMade?: number | null
  totalMuggingGain?: number | null
  totalMuggingLoss?: number | null
  totalCasinoProfitLoss?: number | null
  questsCompleted?: number | null
  dailyQuestsCompleted?: number | null
  coursesCompleted?: number | null
  roguelikeNodesCompleted?: number | null
  roguelikeMapsCompleted?: number | null
  examsCompleted?: number | null
  totalBountyRewards?: number | null
  totalBountyPlaced?: number | null
  totalMissionHours?: number | null
  suggestionsVoted?: number | null
  encountersCompleted?: number | null
}

export type user_achievementsUpdateManyMutationInput = {
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_achievementsUncheckedUpdateManyInput = {
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_achievementsNullableScalarRelationFilter = {
  is?: Prisma.user_achievementsWhereInput | null
  isNot?: Prisma.user_achievementsWhereInput | null
}

export type user_achievementsCountOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrder
  questsCompleted?: Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrder
  examsCompleted?: Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrder
}

export type user_achievementsAvgOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrder
  questsCompleted?: Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrder
  examsCompleted?: Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrder
}

export type user_achievementsMaxOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrder
  questsCompleted?: Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrder
  examsCompleted?: Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrder
}

export type user_achievementsMinOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrder
  questsCompleted?: Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrder
  examsCompleted?: Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrder
}

export type user_achievementsSumOrderByAggregateInput = {
  userId?: Prisma.SortOrder
  battleWins?: Prisma.SortOrder
  npcBattleWins?: Prisma.SortOrder
  craftsCompleted?: Prisma.SortOrder
  marketItemsSold?: Prisma.SortOrder
  marketMoneyMade?: Prisma.SortOrder
  totalMuggingGain?: Prisma.SortOrder
  totalMuggingLoss?: Prisma.SortOrder
  totalCasinoProfitLoss?: Prisma.SortOrder
  questsCompleted?: Prisma.SortOrder
  dailyQuestsCompleted?: Prisma.SortOrder
  coursesCompleted?: Prisma.SortOrder
  roguelikeNodesCompleted?: Prisma.SortOrder
  roguelikeMapsCompleted?: Prisma.SortOrder
  examsCompleted?: Prisma.SortOrder
  totalBountyRewards?: Prisma.SortOrder
  totalBountyPlaced?: Prisma.SortOrder
  totalMissionHours?: Prisma.SortOrder
  suggestionsVoted?: Prisma.SortOrder
  encountersCompleted?: Prisma.SortOrder
}

export type user_achievementsCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_achievementsCreateOrConnectWithoutUserInput
  connect?: Prisma.user_achievementsWhereUniqueInput
}

export type user_achievementsUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_achievementsCreateOrConnectWithoutUserInput
  connect?: Prisma.user_achievementsWhereUniqueInput
}

export type user_achievementsUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_achievementsCreateOrConnectWithoutUserInput
  upsert?: Prisma.user_achievementsUpsertWithoutUserInput
  disconnect?: Prisma.user_achievementsWhereInput | boolean
  delete?: Prisma.user_achievementsWhereInput | boolean
  connect?: Prisma.user_achievementsWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.user_achievementsUpdateToOneWithWhereWithoutUserInput, Prisma.user_achievementsUpdateWithoutUserInput>, Prisma.user_achievementsUncheckedUpdateWithoutUserInput>
}

export type user_achievementsUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.user_achievementsCreateOrConnectWithoutUserInput
  upsert?: Prisma.user_achievementsUpsertWithoutUserInput
  disconnect?: Prisma.user_achievementsWhereInput | boolean
  delete?: Prisma.user_achievementsWhereInput | boolean
  connect?: Prisma.user_achievementsWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.user_achievementsUpdateToOneWithWhereWithoutUserInput, Prisma.user_achievementsUpdateWithoutUserInput>, Prisma.user_achievementsUncheckedUpdateWithoutUserInput>
}

export type user_achievementsCreateWithoutUserInput = {
  battleWins?: number | null
  npcBattleWins?: number | null
  craftsCompleted?: number | null
  marketItemsSold?: number | null
  marketMoneyMade?: number | null
  totalMuggingGain?: number | null
  totalMuggingLoss?: number | null
  totalCasinoProfitLoss?: number | null
  questsCompleted?: number | null
  dailyQuestsCompleted?: number | null
  coursesCompleted?: number | null
  roguelikeNodesCompleted?: number | null
  roguelikeMapsCompleted?: number | null
  examsCompleted?: number | null
  totalBountyRewards?: number | null
  totalBountyPlaced?: number | null
  totalMissionHours?: number | null
  suggestionsVoted?: number | null
  encountersCompleted?: number | null
}

export type user_achievementsUncheckedCreateWithoutUserInput = {
  battleWins?: number | null
  npcBattleWins?: number | null
  craftsCompleted?: number | null
  marketItemsSold?: number | null
  marketMoneyMade?: number | null
  totalMuggingGain?: number | null
  totalMuggingLoss?: number | null
  totalCasinoProfitLoss?: number | null
  questsCompleted?: number | null
  dailyQuestsCompleted?: number | null
  coursesCompleted?: number | null
  roguelikeNodesCompleted?: number | null
  roguelikeMapsCompleted?: number | null
  examsCompleted?: number | null
  totalBountyRewards?: number | null
  totalBountyPlaced?: number | null
  totalMissionHours?: number | null
  suggestionsVoted?: number | null
  encountersCompleted?: number | null
}

export type user_achievementsCreateOrConnectWithoutUserInput = {
  where: Prisma.user_achievementsWhereUniqueInput
  create: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
}

export type user_achievementsUpsertWithoutUserInput = {
  update: Prisma.XOR<Prisma.user_achievementsUpdateWithoutUserInput, Prisma.user_achievementsUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_achievementsCreateWithoutUserInput, Prisma.user_achievementsUncheckedCreateWithoutUserInput>
  where?: Prisma.user_achievementsWhereInput
}

export type user_achievementsUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.user_achievementsWhereInput
  data: Prisma.XOR<Prisma.user_achievementsUpdateWithoutUserInput, Prisma.user_achievementsUncheckedUpdateWithoutUserInput>
}

export type user_achievementsUpdateWithoutUserInput = {
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_achievementsUncheckedUpdateWithoutUserInput = {
  battleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  npcBattleWins?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketItemsSold?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  marketMoneyMade?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingGain?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMuggingLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalCasinoProfitLoss?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  coursesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeNodesCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  roguelikeMapsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  examsCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyRewards?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalBountyPlaced?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  totalMissionHours?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionsVoted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  encountersCompleted?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type user_achievementsSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  userId?: boolean
  battleWins?: boolean
  npcBattleWins?: boolean
  craftsCompleted?: boolean
  marketItemsSold?: boolean
  marketMoneyMade?: boolean
  totalMuggingGain?: boolean
  totalMuggingLoss?: boolean
  totalCasinoProfitLoss?: boolean
  questsCompleted?: boolean
  dailyQuestsCompleted?: boolean
  coursesCompleted?: boolean
  roguelikeNodesCompleted?: boolean
  roguelikeMapsCompleted?: boolean
  examsCompleted?: boolean
  totalBountyRewards?: boolean
  totalBountyPlaced?: boolean
  totalMissionHours?: boolean
  suggestionsVoted?: boolean
  encountersCompleted?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_achievements"]>



export type user_achievementsSelectScalar = {
  userId?: boolean
  battleWins?: boolean
  npcBattleWins?: boolean
  craftsCompleted?: boolean
  marketItemsSold?: boolean
  marketMoneyMade?: boolean
  totalMuggingGain?: boolean
  totalMuggingLoss?: boolean
  totalCasinoProfitLoss?: boolean
  questsCompleted?: boolean
  dailyQuestsCompleted?: boolean
  coursesCompleted?: boolean
  roguelikeNodesCompleted?: boolean
  roguelikeMapsCompleted?: boolean
  examsCompleted?: boolean
  totalBountyRewards?: boolean
  totalBountyPlaced?: boolean
  totalMissionHours?: boolean
  suggestionsVoted?: boolean
  encountersCompleted?: boolean
}

export type user_achievementsOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"userId" | "battleWins" | "npcBattleWins" | "craftsCompleted" | "marketItemsSold" | "marketMoneyMade" | "totalMuggingGain" | "totalMuggingLoss" | "totalCasinoProfitLoss" | "questsCompleted" | "dailyQuestsCompleted" | "coursesCompleted" | "roguelikeNodesCompleted" | "roguelikeMapsCompleted" | "examsCompleted" | "totalBountyRewards" | "totalBountyPlaced" | "totalMissionHours" | "suggestionsVoted" | "encountersCompleted", ExtArgs["result"]["user_achievements"]>
export type user_achievementsInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $user_achievementsPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_achievements"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    userId: number
    battleWins: number | null
    npcBattleWins: number | null
    craftsCompleted: number | null
    marketItemsSold: number | null
    marketMoneyMade: number | null
    totalMuggingGain: number | null
    totalMuggingLoss: number | null
    totalCasinoProfitLoss: number | null
    questsCompleted: number | null
    dailyQuestsCompleted: number | null
    coursesCompleted: number | null
    roguelikeNodesCompleted: number | null
    roguelikeMapsCompleted: number | null
    examsCompleted: number | null
    totalBountyRewards: number | null
    totalBountyPlaced: number | null
    totalMissionHours: number | null
    suggestionsVoted: number | null
    encountersCompleted: number | null
  }, ExtArgs["result"]["user_achievements"]>
  composites: {}
}

export type user_achievementsGetPayload<S extends boolean | null | undefined | user_achievementsDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload, S>

export type user_achievementsCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_achievementsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_achievementsCountAggregateInputType | true
  }

export interface user_achievementsDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_achievements'], meta: { name: 'user_achievements' } }
  /**
   * Find zero or one User_achievements that matches the filter.
   * @param {user_achievementsFindUniqueArgs} args - Arguments to find a User_achievements
   * @example
   * // Get one User_achievements
   * const user_achievements = await prisma.user_achievements.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_achievementsFindUniqueArgs>(args: Prisma.SelectSubset<T, user_achievementsFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_achievements that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_achievementsFindUniqueOrThrowArgs} args - Arguments to find a User_achievements
   * @example
   * // Get one User_achievements
   * const user_achievements = await prisma.user_achievements.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_achievementsFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_achievementsFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_achievements that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsFindFirstArgs} args - Arguments to find a User_achievements
   * @example
   * // Get one User_achievements
   * const user_achievements = await prisma.user_achievements.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_achievementsFindFirstArgs>(args?: Prisma.SelectSubset<T, user_achievementsFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_achievements that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsFindFirstOrThrowArgs} args - Arguments to find a User_achievements
   * @example
   * // Get one User_achievements
   * const user_achievements = await prisma.user_achievements.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_achievementsFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_achievementsFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_achievements that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_achievements
   * const user_achievements = await prisma.user_achievements.findMany()
   * 
   * // Get first 10 User_achievements
   * const user_achievements = await prisma.user_achievements.findMany({ take: 10 })
   * 
   * // Only select the `userId`
   * const user_achievementsWithUserIdOnly = await prisma.user_achievements.findMany({ select: { userId: true } })
   * 
   */
  findMany<T extends user_achievementsFindManyArgs>(args?: Prisma.SelectSubset<T, user_achievementsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_achievements.
   * @param {user_achievementsCreateArgs} args - Arguments to create a User_achievements.
   * @example
   * // Create one User_achievements
   * const User_achievements = await prisma.user_achievements.create({
   *   data: {
   *     // ... data to create a User_achievements
   *   }
   * })
   * 
   */
  create<T extends user_achievementsCreateArgs>(args: Prisma.SelectSubset<T, user_achievementsCreateArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_achievements.
   * @param {user_achievementsCreateManyArgs} args - Arguments to create many User_achievements.
   * @example
   * // Create many User_achievements
   * const user_achievements = await prisma.user_achievements.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_achievementsCreateManyArgs>(args?: Prisma.SelectSubset<T, user_achievementsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_achievements.
   * @param {user_achievementsDeleteArgs} args - Arguments to delete one User_achievements.
   * @example
   * // Delete one User_achievements
   * const User_achievements = await prisma.user_achievements.delete({
   *   where: {
   *     // ... filter to delete one User_achievements
   *   }
   * })
   * 
   */
  delete<T extends user_achievementsDeleteArgs>(args: Prisma.SelectSubset<T, user_achievementsDeleteArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_achievements.
   * @param {user_achievementsUpdateArgs} args - Arguments to update one User_achievements.
   * @example
   * // Update one User_achievements
   * const user_achievements = await prisma.user_achievements.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_achievementsUpdateArgs>(args: Prisma.SelectSubset<T, user_achievementsUpdateArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_achievements.
   * @param {user_achievementsDeleteManyArgs} args - Arguments to filter User_achievements to delete.
   * @example
   * // Delete a few User_achievements
   * const { count } = await prisma.user_achievements.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_achievementsDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_achievementsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_achievements.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_achievements
   * const user_achievements = await prisma.user_achievements.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_achievementsUpdateManyArgs>(args: Prisma.SelectSubset<T, user_achievementsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_achievements.
   * @param {user_achievementsUpsertArgs} args - Arguments to update or create a User_achievements.
   * @example
   * // Update or create a User_achievements
   * const user_achievements = await prisma.user_achievements.upsert({
   *   create: {
   *     // ... data to create a User_achievements
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_achievements we want to update
   *   }
   * })
   */
  upsert<T extends user_achievementsUpsertArgs>(args: Prisma.SelectSubset<T, user_achievementsUpsertArgs<ExtArgs>>): Prisma.Prisma__user_achievementsClient<runtime.Types.Result.GetResult<Prisma.$user_achievementsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_achievements.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsCountArgs} args - Arguments to filter User_achievements to count.
   * @example
   * // Count the number of User_achievements
   * const count = await prisma.user_achievements.count({
   *   where: {
   *     // ... the filter for the User_achievements we want to count
   *   }
   * })
  **/
  count<T extends user_achievementsCountArgs>(
    args?: Prisma.Subset<T, user_achievementsCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_achievementsCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_achievements.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_achievementsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_achievementsAggregateArgs>(args: Prisma.Subset<T, User_achievementsAggregateArgs>): Prisma.PrismaPromise<GetUser_achievementsAggregateType<T>>

  /**
   * Group by User_achievements.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_achievementsGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_achievementsGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_achievementsGroupByArgs['orderBy'] }
      : { orderBy?: user_achievementsGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_achievementsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_achievementsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_achievements model
 */
readonly fields: user_achievementsFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_achievements.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_achievementsClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_achievements model
 */
export interface user_achievementsFieldRefs {
  readonly userId: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly battleWins: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly npcBattleWins: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly craftsCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly marketItemsSold: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly marketMoneyMade: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalMuggingGain: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalMuggingLoss: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalCasinoProfitLoss: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly questsCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly dailyQuestsCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly coursesCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly roguelikeNodesCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly roguelikeMapsCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly examsCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalBountyRewards: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalBountyPlaced: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly totalMissionHours: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly suggestionsVoted: Prisma.FieldRef<"user_achievements", 'Int'>
  readonly encountersCompleted: Prisma.FieldRef<"user_achievements", 'Int'>
}
    

// Custom InputTypes
/**
 * user_achievements findUnique
 */
export type user_achievementsFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter, which user_achievements to fetch.
   */
  where: Prisma.user_achievementsWhereUniqueInput
}

/**
 * user_achievements findUniqueOrThrow
 */
export type user_achievementsFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter, which user_achievements to fetch.
   */
  where: Prisma.user_achievementsWhereUniqueInput
}

/**
 * user_achievements findFirst
 */
export type user_achievementsFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter, which user_achievements to fetch.
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_achievements to fetch.
   */
  orderBy?: Prisma.user_achievementsOrderByWithRelationInput | Prisma.user_achievementsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_achievements.
   */
  cursor?: Prisma.user_achievementsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_achievements from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_achievements.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_achievements.
   */
  distinct?: Prisma.User_achievementsScalarFieldEnum | Prisma.User_achievementsScalarFieldEnum[]
}

/**
 * user_achievements findFirstOrThrow
 */
export type user_achievementsFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter, which user_achievements to fetch.
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_achievements to fetch.
   */
  orderBy?: Prisma.user_achievementsOrderByWithRelationInput | Prisma.user_achievementsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_achievements.
   */
  cursor?: Prisma.user_achievementsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_achievements from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_achievements.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_achievements.
   */
  distinct?: Prisma.User_achievementsScalarFieldEnum | Prisma.User_achievementsScalarFieldEnum[]
}

/**
 * user_achievements findMany
 */
export type user_achievementsFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter, which user_achievements to fetch.
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_achievements to fetch.
   */
  orderBy?: Prisma.user_achievementsOrderByWithRelationInput | Prisma.user_achievementsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_achievements.
   */
  cursor?: Prisma.user_achievementsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_achievements from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_achievements.
   */
  skip?: number
  distinct?: Prisma.User_achievementsScalarFieldEnum | Prisma.User_achievementsScalarFieldEnum[]
}

/**
 * user_achievements create
 */
export type user_achievementsCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * The data needed to create a user_achievements.
   */
  data: Prisma.XOR<Prisma.user_achievementsCreateInput, Prisma.user_achievementsUncheckedCreateInput>
}

/**
 * user_achievements createMany
 */
export type user_achievementsCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_achievements.
   */
  data: Prisma.user_achievementsCreateManyInput | Prisma.user_achievementsCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_achievements update
 */
export type user_achievementsUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * The data needed to update a user_achievements.
   */
  data: Prisma.XOR<Prisma.user_achievementsUpdateInput, Prisma.user_achievementsUncheckedUpdateInput>
  /**
   * Choose, which user_achievements to update.
   */
  where: Prisma.user_achievementsWhereUniqueInput
}

/**
 * user_achievements updateMany
 */
export type user_achievementsUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_achievements.
   */
  data: Prisma.XOR<Prisma.user_achievementsUpdateManyMutationInput, Prisma.user_achievementsUncheckedUpdateManyInput>
  /**
   * Filter which user_achievements to update
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * Limit how many user_achievements to update.
   */
  limit?: number
}

/**
 * user_achievements upsert
 */
export type user_achievementsUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * The filter to search for the user_achievements to update in case it exists.
   */
  where: Prisma.user_achievementsWhereUniqueInput
  /**
   * In case the user_achievements found by the `where` argument doesn't exist, create a new user_achievements with this data.
   */
  create: Prisma.XOR<Prisma.user_achievementsCreateInput, Prisma.user_achievementsUncheckedCreateInput>
  /**
   * In case the user_achievements was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_achievementsUpdateInput, Prisma.user_achievementsUncheckedUpdateInput>
}

/**
 * user_achievements delete
 */
export type user_achievementsDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
  /**
   * Filter which user_achievements to delete.
   */
  where: Prisma.user_achievementsWhereUniqueInput
}

/**
 * user_achievements deleteMany
 */
export type user_achievementsDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_achievements to delete
   */
  where?: Prisma.user_achievementsWhereInput
  /**
   * Limit how many user_achievements to delete.
   */
  limit?: number
}

/**
 * user_achievements without action
 */
export type user_achievementsDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_achievements
   */
  select?: Prisma.user_achievementsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_achievements
   */
  omit?: Prisma.user_achievementsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_achievementsInclude<ExtArgs> | null
}
