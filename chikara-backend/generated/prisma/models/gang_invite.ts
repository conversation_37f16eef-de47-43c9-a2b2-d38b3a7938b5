
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `gang_invite` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model gang_invite
 * 
 */
export type gang_inviteModel = runtime.Types.Result.DefaultSelection<Prisma.$gang_invitePayload>

export type AggregateGang_invite = {
  _count: Gang_inviteCountAggregateOutputType | null
  _avg: Gang_inviteAvgAggregateOutputType | null
  _sum: Gang_inviteSumAggregateOutputType | null
  _min: Gang_inviteMinAggregateOutputType | null
  _max: Gang_inviteMaxAggregateOutputType | null
}

export type Gang_inviteAvgAggregateOutputType = {
  id: number | null
  gangId: number | null
  senderId: number | null
  recipientId: number | null
}

export type Gang_inviteSumAggregateOutputType = {
  id: number | null
  gangId: number | null
  senderId: number | null
  recipientId: number | null
}

export type Gang_inviteMinAggregateOutputType = {
  id: number | null
  inviteType: $Enums.GangInviteTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  senderId: number | null
  recipientId: number | null
}

export type Gang_inviteMaxAggregateOutputType = {
  id: number | null
  inviteType: $Enums.GangInviteTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  gangId: number | null
  senderId: number | null
  recipientId: number | null
}

export type Gang_inviteCountAggregateOutputType = {
  id: number
  inviteType: number
  createdAt: number
  updatedAt: number
  gangId: number
  senderId: number
  recipientId: number
  _all: number
}


export type Gang_inviteAvgAggregateInputType = {
  id?: true
  gangId?: true
  senderId?: true
  recipientId?: true
}

export type Gang_inviteSumAggregateInputType = {
  id?: true
  gangId?: true
  senderId?: true
  recipientId?: true
}

export type Gang_inviteMinAggregateInputType = {
  id?: true
  inviteType?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  senderId?: true
  recipientId?: true
}

export type Gang_inviteMaxAggregateInputType = {
  id?: true
  inviteType?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  senderId?: true
  recipientId?: true
}

export type Gang_inviteCountAggregateInputType = {
  id?: true
  inviteType?: true
  createdAt?: true
  updatedAt?: true
  gangId?: true
  senderId?: true
  recipientId?: true
  _all?: true
}

export type Gang_inviteAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_invite to aggregate.
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_invites to fetch.
   */
  orderBy?: Prisma.gang_inviteOrderByWithRelationInput | Prisma.gang_inviteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.gang_inviteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_invites from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_invites.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned gang_invites
  **/
  _count?: true | Gang_inviteCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Gang_inviteAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Gang_inviteSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Gang_inviteMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Gang_inviteMaxAggregateInputType
}

export type GetGang_inviteAggregateType<T extends Gang_inviteAggregateArgs> = {
      [P in keyof T & keyof AggregateGang_invite]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGang_invite[P]>
    : Prisma.GetScalarType<T[P], AggregateGang_invite[P]>
}




export type gang_inviteGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.gang_inviteWhereInput
  orderBy?: Prisma.gang_inviteOrderByWithAggregationInput | Prisma.gang_inviteOrderByWithAggregationInput[]
  by: Prisma.Gang_inviteScalarFieldEnum[] | Prisma.Gang_inviteScalarFieldEnum
  having?: Prisma.gang_inviteScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Gang_inviteCountAggregateInputType | true
  _avg?: Gang_inviteAvgAggregateInputType
  _sum?: Gang_inviteSumAggregateInputType
  _min?: Gang_inviteMinAggregateInputType
  _max?: Gang_inviteMaxAggregateInputType
}

export type Gang_inviteGroupByOutputType = {
  id: number
  inviteType: $Enums.GangInviteTypes
  createdAt: Date
  updatedAt: Date
  gangId: number | null
  senderId: number | null
  recipientId: number | null
  _count: Gang_inviteCountAggregateOutputType | null
  _avg: Gang_inviteAvgAggregateOutputType | null
  _sum: Gang_inviteSumAggregateOutputType | null
  _min: Gang_inviteMinAggregateOutputType | null
  _max: Gang_inviteMaxAggregateOutputType | null
}

type GetGang_inviteGroupByPayload<T extends gang_inviteGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Gang_inviteGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Gang_inviteGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Gang_inviteGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Gang_inviteGroupByOutputType[P]>
      }
    >
  >



export type gang_inviteWhereInput = {
  AND?: Prisma.gang_inviteWhereInput | Prisma.gang_inviteWhereInput[]
  OR?: Prisma.gang_inviteWhereInput[]
  NOT?: Prisma.gang_inviteWhereInput | Prisma.gang_inviteWhereInput[]
  id?: Prisma.IntFilter<"gang_invite"> | number
  inviteType?: Prisma.EnumGangInviteTypesFilter<"gang_invite"> | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  senderId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  recipientId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user_gang_invite_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_gang_invite_recipientIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type gang_inviteOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  inviteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  gang?: Prisma.gangOrderByWithRelationInput
  user_gang_invite_senderIdTouser?: Prisma.userOrderByWithRelationInput
  user_gang_invite_recipientIdTouser?: Prisma.userOrderByWithRelationInput
}

export type gang_inviteWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.gang_inviteWhereInput | Prisma.gang_inviteWhereInput[]
  OR?: Prisma.gang_inviteWhereInput[]
  NOT?: Prisma.gang_inviteWhereInput | Prisma.gang_inviteWhereInput[]
  inviteType?: Prisma.EnumGangInviteTypesFilter<"gang_invite"> | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  senderId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  recipientId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  gang?: Prisma.XOR<Prisma.GangNullableScalarRelationFilter, Prisma.gangWhereInput> | null
  user_gang_invite_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_gang_invite_recipientIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type gang_inviteOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  inviteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrderInput | Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  recipientId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.gang_inviteCountOrderByAggregateInput
  _avg?: Prisma.gang_inviteAvgOrderByAggregateInput
  _max?: Prisma.gang_inviteMaxOrderByAggregateInput
  _min?: Prisma.gang_inviteMinOrderByAggregateInput
  _sum?: Prisma.gang_inviteSumOrderByAggregateInput
}

export type gang_inviteScalarWhereWithAggregatesInput = {
  AND?: Prisma.gang_inviteScalarWhereWithAggregatesInput | Prisma.gang_inviteScalarWhereWithAggregatesInput[]
  OR?: Prisma.gang_inviteScalarWhereWithAggregatesInput[]
  NOT?: Prisma.gang_inviteScalarWhereWithAggregatesInput | Prisma.gang_inviteScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"gang_invite"> | number
  inviteType?: Prisma.EnumGangInviteTypesWithAggregatesFilter<"gang_invite"> | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"gang_invite"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"gang_invite"> | Date | string
  gangId?: Prisma.IntNullableWithAggregatesFilter<"gang_invite"> | number | null
  senderId?: Prisma.IntNullableWithAggregatesFilter<"gang_invite"> | number | null
  recipientId?: Prisma.IntNullableWithAggregatesFilter<"gang_invite"> | number | null
}

export type gang_inviteCreateInput = {
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_inviteInput
  user_gang_invite_senderIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_senderIdTouserInput
  user_gang_invite_recipientIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_recipientIdTouserInput
}

export type gang_inviteUncheckedCreateInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  senderId?: number | null
  recipientId?: number | null
}

export type gang_inviteUpdateInput = {
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_inviteNestedInput
  user_gang_invite_senderIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_senderIdTouserNestedInput
  user_gang_invite_recipientIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_recipientIdTouserNestedInput
}

export type gang_inviteUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteCreateManyInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  senderId?: number | null
  recipientId?: number | null
}

export type gang_inviteUpdateManyMutationInput = {
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type gang_inviteUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Gang_inviteListRelationFilter = {
  every?: Prisma.gang_inviteWhereInput
  some?: Prisma.gang_inviteWhereInput
  none?: Prisma.gang_inviteWhereInput
}

export type gang_inviteOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type gang_inviteCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  inviteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
}

export type gang_inviteAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
}

export type gang_inviteMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  inviteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
}

export type gang_inviteMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  inviteType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
}

export type gang_inviteSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  gangId?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
}

export type gang_inviteCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput> | Prisma.gang_inviteCreateWithoutGangInput[] | Prisma.gang_inviteUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutGangInput | Prisma.gang_inviteCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_inviteCreateManyGangInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteUncheckedCreateNestedManyWithoutGangInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput> | Prisma.gang_inviteCreateWithoutGangInput[] | Prisma.gang_inviteUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutGangInput | Prisma.gang_inviteCreateOrConnectWithoutGangInput[]
  createMany?: Prisma.gang_inviteCreateManyGangInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput> | Prisma.gang_inviteCreateWithoutGangInput[] | Prisma.gang_inviteUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutGangInput | Prisma.gang_inviteCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_inviteCreateManyGangInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutGangInput | Prisma.gang_inviteUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type gang_inviteUncheckedUpdateManyWithoutGangNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput> | Prisma.gang_inviteCreateWithoutGangInput[] | Prisma.gang_inviteUncheckedCreateWithoutGangInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutGangInput | Prisma.gang_inviteCreateOrConnectWithoutGangInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutGangInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutGangInput[]
  createMany?: Prisma.gang_inviteCreateManyGangInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutGangInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutGangInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutGangInput | Prisma.gang_inviteUpdateManyWithWhereWithoutGangInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type EnumGangInviteTypesFieldUpdateOperationsInput = {
  set?: $Enums.GangInviteTypes
}

export type gang_inviteCreateNestedManyWithoutUser_gang_invite_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteCreateNestedManyWithoutUser_gang_invite_recipientIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteUncheckedCreateNestedManyWithoutUser_gang_invite_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteUncheckedCreateNestedManyWithoutUser_gang_invite_recipientIdTouserInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInputEnvelope
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
}

export type gang_inviteUpdateManyWithoutUser_gang_invite_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_senderIdTouserInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type gang_inviteUpdateManyWithoutUser_gang_invite_recipientIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_recipientIdTouserInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_senderIdTouserInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_recipientIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput> | Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput[] | Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput[]
  connectOrCreate?: Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput[]
  upsert?: Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput[]
  createMany?: Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInputEnvelope
  set?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  disconnect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  delete?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  connect?: Prisma.gang_inviteWhereUniqueInput | Prisma.gang_inviteWhereUniqueInput[]
  update?: Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput[]
  updateMany?: Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_recipientIdTouserInput[]
  deleteMany?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
}

export type gang_inviteCreateWithoutGangInput = {
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  user_gang_invite_senderIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_senderIdTouserInput
  user_gang_invite_recipientIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_recipientIdTouserInput
}

export type gang_inviteUncheckedCreateWithoutGangInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  recipientId?: number | null
}

export type gang_inviteCreateOrConnectWithoutGangInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput>
}

export type gang_inviteCreateManyGangInputEnvelope = {
  data: Prisma.gang_inviteCreateManyGangInput | Prisma.gang_inviteCreateManyGangInput[]
  skipDuplicates?: boolean
}

export type gang_inviteUpsertWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_inviteUpdateWithoutGangInput, Prisma.gang_inviteUncheckedUpdateWithoutGangInput>
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutGangInput, Prisma.gang_inviteUncheckedCreateWithoutGangInput>
}

export type gang_inviteUpdateWithWhereUniqueWithoutGangInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateWithoutGangInput, Prisma.gang_inviteUncheckedUpdateWithoutGangInput>
}

export type gang_inviteUpdateManyWithWhereWithoutGangInput = {
  where: Prisma.gang_inviteScalarWhereInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateManyMutationInput, Prisma.gang_inviteUncheckedUpdateManyWithoutGangInput>
}

export type gang_inviteScalarWhereInput = {
  AND?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
  OR?: Prisma.gang_inviteScalarWhereInput[]
  NOT?: Prisma.gang_inviteScalarWhereInput | Prisma.gang_inviteScalarWhereInput[]
  id?: Prisma.IntFilter<"gang_invite"> | number
  inviteType?: Prisma.EnumGangInviteTypesFilter<"gang_invite"> | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"gang_invite"> | Date | string
  gangId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  senderId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
  recipientId?: Prisma.IntNullableFilter<"gang_invite"> | number | null
}

export type gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput = {
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_inviteInput
  user_gang_invite_recipientIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_recipientIdTouserInput
}

export type gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  recipientId?: number | null
}

export type gang_inviteCreateOrConnectWithoutUser_gang_invite_senderIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput>
}

export type gang_inviteCreateManyUser_gang_invite_senderIdTouserInputEnvelope = {
  data: Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInput | Prisma.gang_inviteCreateManyUser_gang_invite_senderIdTouserInput[]
  skipDuplicates?: boolean
}

export type gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput = {
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gang?: Prisma.gangCreateNestedOneWithoutGang_inviteInput
  user_gang_invite_senderIdTouser?: Prisma.userCreateNestedOneWithoutGang_invite_gang_invite_senderIdTouserInput
}

export type gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  senderId?: number | null
}

export type gang_inviteCreateOrConnectWithoutUser_gang_invite_recipientIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput>
}

export type gang_inviteCreateManyUser_gang_invite_recipientIdTouserInputEnvelope = {
  data: Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInput | Prisma.gang_inviteCreateManyUser_gang_invite_recipientIdTouserInput[]
  skipDuplicates?: boolean
}

export type gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_inviteUpdateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedUpdateWithoutUser_gang_invite_senderIdTouserInput>
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_senderIdTouserInput>
}

export type gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_senderIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateWithoutUser_gang_invite_senderIdTouserInput, Prisma.gang_inviteUncheckedUpdateWithoutUser_gang_invite_senderIdTouserInput>
}

export type gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_senderIdTouserInput = {
  where: Prisma.gang_inviteScalarWhereInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateManyMutationInput, Prisma.gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_senderIdTouserInput>
}

export type gang_inviteUpsertWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  update: Prisma.XOR<Prisma.gang_inviteUpdateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedUpdateWithoutUser_gang_invite_recipientIdTouserInput>
  create: Prisma.XOR<Prisma.gang_inviteCreateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedCreateWithoutUser_gang_invite_recipientIdTouserInput>
}

export type gang_inviteUpdateWithWhereUniqueWithoutUser_gang_invite_recipientIdTouserInput = {
  where: Prisma.gang_inviteWhereUniqueInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateWithoutUser_gang_invite_recipientIdTouserInput, Prisma.gang_inviteUncheckedUpdateWithoutUser_gang_invite_recipientIdTouserInput>
}

export type gang_inviteUpdateManyWithWhereWithoutUser_gang_invite_recipientIdTouserInput = {
  where: Prisma.gang_inviteScalarWhereInput
  data: Prisma.XOR<Prisma.gang_inviteUpdateManyMutationInput, Prisma.gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_recipientIdTouserInput>
}

export type gang_inviteCreateManyGangInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  recipientId?: number | null
}

export type gang_inviteUpdateWithoutGangInput = {
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_gang_invite_senderIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_senderIdTouserNestedInput
  user_gang_invite_recipientIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_recipientIdTouserNestedInput
}

export type gang_inviteUncheckedUpdateWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteUncheckedUpdateManyWithoutGangInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteCreateManyUser_gang_invite_senderIdTouserInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  recipientId?: number | null
}

export type gang_inviteCreateManyUser_gang_invite_recipientIdTouserInput = {
  id?: number
  inviteType: $Enums.GangInviteTypes
  createdAt?: Date | string
  updatedAt?: Date | string
  gangId?: number | null
  senderId?: number | null
}

export type gang_inviteUpdateWithoutUser_gang_invite_senderIdTouserInput = {
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_inviteNestedInput
  user_gang_invite_recipientIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_recipientIdTouserNestedInput
}

export type gang_inviteUncheckedUpdateWithoutUser_gang_invite_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  recipientId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteUpdateWithoutUser_gang_invite_recipientIdTouserInput = {
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gang?: Prisma.gangUpdateOneWithoutGang_inviteNestedInput
  user_gang_invite_senderIdTouser?: Prisma.userUpdateOneWithoutGang_invite_gang_invite_senderIdTouserNestedInput
}

export type gang_inviteUncheckedUpdateWithoutUser_gang_invite_recipientIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type gang_inviteUncheckedUpdateManyWithoutUser_gang_invite_recipientIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  inviteType?: Prisma.EnumGangInviteTypesFieldUpdateOperationsInput | $Enums.GangInviteTypes
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  gangId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type gang_inviteSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  inviteType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  senderId?: boolean
  recipientId?: boolean
  gang?: boolean | Prisma.gang_invite$gangArgs<ExtArgs>
  user_gang_invite_senderIdTouser?: boolean | Prisma.gang_invite$user_gang_invite_senderIdTouserArgs<ExtArgs>
  user_gang_invite_recipientIdTouser?: boolean | Prisma.gang_invite$user_gang_invite_recipientIdTouserArgs<ExtArgs>
}, ExtArgs["result"]["gang_invite"]>



export type gang_inviteSelectScalar = {
  id?: boolean
  inviteType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  gangId?: boolean
  senderId?: boolean
  recipientId?: boolean
}

export type gang_inviteOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "inviteType" | "createdAt" | "updatedAt" | "gangId" | "senderId" | "recipientId", ExtArgs["result"]["gang_invite"]>
export type gang_inviteInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  gang?: boolean | Prisma.gang_invite$gangArgs<ExtArgs>
  user_gang_invite_senderIdTouser?: boolean | Prisma.gang_invite$user_gang_invite_senderIdTouserArgs<ExtArgs>
  user_gang_invite_recipientIdTouser?: boolean | Prisma.gang_invite$user_gang_invite_recipientIdTouserArgs<ExtArgs>
}

export type $gang_invitePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "gang_invite"
  objects: {
    gang: Prisma.$gangPayload<ExtArgs> | null
    user_gang_invite_senderIdTouser: Prisma.$userPayload<ExtArgs> | null
    user_gang_invite_recipientIdTouser: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    inviteType: $Enums.GangInviteTypes
    createdAt: Date
    updatedAt: Date
    gangId: number | null
    senderId: number | null
    recipientId: number | null
  }, ExtArgs["result"]["gang_invite"]>
  composites: {}
}

export type gang_inviteGetPayload<S extends boolean | null | undefined | gang_inviteDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$gang_invitePayload, S>

export type gang_inviteCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<gang_inviteFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Gang_inviteCountAggregateInputType | true
  }

export interface gang_inviteDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['gang_invite'], meta: { name: 'gang_invite' } }
  /**
   * Find zero or one Gang_invite that matches the filter.
   * @param {gang_inviteFindUniqueArgs} args - Arguments to find a Gang_invite
   * @example
   * // Get one Gang_invite
   * const gang_invite = await prisma.gang_invite.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends gang_inviteFindUniqueArgs>(args: Prisma.SelectSubset<T, gang_inviteFindUniqueArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Gang_invite that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {gang_inviteFindUniqueOrThrowArgs} args - Arguments to find a Gang_invite
   * @example
   * // Get one Gang_invite
   * const gang_invite = await prisma.gang_invite.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends gang_inviteFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, gang_inviteFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_invite that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteFindFirstArgs} args - Arguments to find a Gang_invite
   * @example
   * // Get one Gang_invite
   * const gang_invite = await prisma.gang_invite.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends gang_inviteFindFirstArgs>(args?: Prisma.SelectSubset<T, gang_inviteFindFirstArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Gang_invite that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteFindFirstOrThrowArgs} args - Arguments to find a Gang_invite
   * @example
   * // Get one Gang_invite
   * const gang_invite = await prisma.gang_invite.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends gang_inviteFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, gang_inviteFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Gang_invites that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Gang_invites
   * const gang_invites = await prisma.gang_invite.findMany()
   * 
   * // Get first 10 Gang_invites
   * const gang_invites = await prisma.gang_invite.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const gang_inviteWithIdOnly = await prisma.gang_invite.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends gang_inviteFindManyArgs>(args?: Prisma.SelectSubset<T, gang_inviteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Gang_invite.
   * @param {gang_inviteCreateArgs} args - Arguments to create a Gang_invite.
   * @example
   * // Create one Gang_invite
   * const Gang_invite = await prisma.gang_invite.create({
   *   data: {
   *     // ... data to create a Gang_invite
   *   }
   * })
   * 
   */
  create<T extends gang_inviteCreateArgs>(args: Prisma.SelectSubset<T, gang_inviteCreateArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Gang_invites.
   * @param {gang_inviteCreateManyArgs} args - Arguments to create many Gang_invites.
   * @example
   * // Create many Gang_invites
   * const gang_invite = await prisma.gang_invite.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends gang_inviteCreateManyArgs>(args?: Prisma.SelectSubset<T, gang_inviteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Gang_invite.
   * @param {gang_inviteDeleteArgs} args - Arguments to delete one Gang_invite.
   * @example
   * // Delete one Gang_invite
   * const Gang_invite = await prisma.gang_invite.delete({
   *   where: {
   *     // ... filter to delete one Gang_invite
   *   }
   * })
   * 
   */
  delete<T extends gang_inviteDeleteArgs>(args: Prisma.SelectSubset<T, gang_inviteDeleteArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Gang_invite.
   * @param {gang_inviteUpdateArgs} args - Arguments to update one Gang_invite.
   * @example
   * // Update one Gang_invite
   * const gang_invite = await prisma.gang_invite.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends gang_inviteUpdateArgs>(args: Prisma.SelectSubset<T, gang_inviteUpdateArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Gang_invites.
   * @param {gang_inviteDeleteManyArgs} args - Arguments to filter Gang_invites to delete.
   * @example
   * // Delete a few Gang_invites
   * const { count } = await prisma.gang_invite.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends gang_inviteDeleteManyArgs>(args?: Prisma.SelectSubset<T, gang_inviteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Gang_invites.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Gang_invites
   * const gang_invite = await prisma.gang_invite.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends gang_inviteUpdateManyArgs>(args: Prisma.SelectSubset<T, gang_inviteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Gang_invite.
   * @param {gang_inviteUpsertArgs} args - Arguments to update or create a Gang_invite.
   * @example
   * // Update or create a Gang_invite
   * const gang_invite = await prisma.gang_invite.upsert({
   *   create: {
   *     // ... data to create a Gang_invite
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Gang_invite we want to update
   *   }
   * })
   */
  upsert<T extends gang_inviteUpsertArgs>(args: Prisma.SelectSubset<T, gang_inviteUpsertArgs<ExtArgs>>): Prisma.Prisma__gang_inviteClient<runtime.Types.Result.GetResult<Prisma.$gang_invitePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Gang_invites.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteCountArgs} args - Arguments to filter Gang_invites to count.
   * @example
   * // Count the number of Gang_invites
   * const count = await prisma.gang_invite.count({
   *   where: {
   *     // ... the filter for the Gang_invites we want to count
   *   }
   * })
  **/
  count<T extends gang_inviteCountArgs>(
    args?: Prisma.Subset<T, gang_inviteCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Gang_inviteCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Gang_invite.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Gang_inviteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Gang_inviteAggregateArgs>(args: Prisma.Subset<T, Gang_inviteAggregateArgs>): Prisma.PrismaPromise<GetGang_inviteAggregateType<T>>

  /**
   * Group by Gang_invite.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {gang_inviteGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends gang_inviteGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: gang_inviteGroupByArgs['orderBy'] }
      : { orderBy?: gang_inviteGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, gang_inviteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGang_inviteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the gang_invite model
 */
readonly fields: gang_inviteFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for gang_invite.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__gang_inviteClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  gang<T extends Prisma.gang_invite$gangArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_invite$gangArgs<ExtArgs>>): Prisma.Prisma__gangClient<runtime.Types.Result.GetResult<Prisma.$gangPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_gang_invite_senderIdTouser<T extends Prisma.gang_invite$user_gang_invite_senderIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_invite$user_gang_invite_senderIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_gang_invite_recipientIdTouser<T extends Prisma.gang_invite$user_gang_invite_recipientIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.gang_invite$user_gang_invite_recipientIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the gang_invite model
 */
export interface gang_inviteFieldRefs {
  readonly id: Prisma.FieldRef<"gang_invite", 'Int'>
  readonly inviteType: Prisma.FieldRef<"gang_invite", 'GangInviteTypes'>
  readonly createdAt: Prisma.FieldRef<"gang_invite", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"gang_invite", 'DateTime'>
  readonly gangId: Prisma.FieldRef<"gang_invite", 'Int'>
  readonly senderId: Prisma.FieldRef<"gang_invite", 'Int'>
  readonly recipientId: Prisma.FieldRef<"gang_invite", 'Int'>
}
    

// Custom InputTypes
/**
 * gang_invite findUnique
 */
export type gang_inviteFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter, which gang_invite to fetch.
   */
  where: Prisma.gang_inviteWhereUniqueInput
}

/**
 * gang_invite findUniqueOrThrow
 */
export type gang_inviteFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter, which gang_invite to fetch.
   */
  where: Prisma.gang_inviteWhereUniqueInput
}

/**
 * gang_invite findFirst
 */
export type gang_inviteFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter, which gang_invite to fetch.
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_invites to fetch.
   */
  orderBy?: Prisma.gang_inviteOrderByWithRelationInput | Prisma.gang_inviteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_invites.
   */
  cursor?: Prisma.gang_inviteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_invites from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_invites.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_invites.
   */
  distinct?: Prisma.Gang_inviteScalarFieldEnum | Prisma.Gang_inviteScalarFieldEnum[]
}

/**
 * gang_invite findFirstOrThrow
 */
export type gang_inviteFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter, which gang_invite to fetch.
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_invites to fetch.
   */
  orderBy?: Prisma.gang_inviteOrderByWithRelationInput | Prisma.gang_inviteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for gang_invites.
   */
  cursor?: Prisma.gang_inviteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_invites from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_invites.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of gang_invites.
   */
  distinct?: Prisma.Gang_inviteScalarFieldEnum | Prisma.Gang_inviteScalarFieldEnum[]
}

/**
 * gang_invite findMany
 */
export type gang_inviteFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter, which gang_invites to fetch.
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of gang_invites to fetch.
   */
  orderBy?: Prisma.gang_inviteOrderByWithRelationInput | Prisma.gang_inviteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing gang_invites.
   */
  cursor?: Prisma.gang_inviteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` gang_invites from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` gang_invites.
   */
  skip?: number
  distinct?: Prisma.Gang_inviteScalarFieldEnum | Prisma.Gang_inviteScalarFieldEnum[]
}

/**
 * gang_invite create
 */
export type gang_inviteCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * The data needed to create a gang_invite.
   */
  data: Prisma.XOR<Prisma.gang_inviteCreateInput, Prisma.gang_inviteUncheckedCreateInput>
}

/**
 * gang_invite createMany
 */
export type gang_inviteCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many gang_invites.
   */
  data: Prisma.gang_inviteCreateManyInput | Prisma.gang_inviteCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * gang_invite update
 */
export type gang_inviteUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * The data needed to update a gang_invite.
   */
  data: Prisma.XOR<Prisma.gang_inviteUpdateInput, Prisma.gang_inviteUncheckedUpdateInput>
  /**
   * Choose, which gang_invite to update.
   */
  where: Prisma.gang_inviteWhereUniqueInput
}

/**
 * gang_invite updateMany
 */
export type gang_inviteUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update gang_invites.
   */
  data: Prisma.XOR<Prisma.gang_inviteUpdateManyMutationInput, Prisma.gang_inviteUncheckedUpdateManyInput>
  /**
   * Filter which gang_invites to update
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * Limit how many gang_invites to update.
   */
  limit?: number
}

/**
 * gang_invite upsert
 */
export type gang_inviteUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * The filter to search for the gang_invite to update in case it exists.
   */
  where: Prisma.gang_inviteWhereUniqueInput
  /**
   * In case the gang_invite found by the `where` argument doesn't exist, create a new gang_invite with this data.
   */
  create: Prisma.XOR<Prisma.gang_inviteCreateInput, Prisma.gang_inviteUncheckedCreateInput>
  /**
   * In case the gang_invite was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.gang_inviteUpdateInput, Prisma.gang_inviteUncheckedUpdateInput>
}

/**
 * gang_invite delete
 */
export type gang_inviteDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
  /**
   * Filter which gang_invite to delete.
   */
  where: Prisma.gang_inviteWhereUniqueInput
}

/**
 * gang_invite deleteMany
 */
export type gang_inviteDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which gang_invites to delete
   */
  where?: Prisma.gang_inviteWhereInput
  /**
   * Limit how many gang_invites to delete.
   */
  limit?: number
}

/**
 * gang_invite.gang
 */
export type gang_invite$gangArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang
   */
  select?: Prisma.gangSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang
   */
  omit?: Prisma.gangOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gangInclude<ExtArgs> | null
  where?: Prisma.gangWhereInput
}

/**
 * gang_invite.user_gang_invite_senderIdTouser
 */
export type gang_invite$user_gang_invite_senderIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang_invite.user_gang_invite_recipientIdTouser
 */
export type gang_invite$user_gang_invite_recipientIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * gang_invite without action
 */
export type gang_inviteDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the gang_invite
   */
  select?: Prisma.gang_inviteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the gang_invite
   */
  omit?: Prisma.gang_inviteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.gang_inviteInclude<ExtArgs> | null
}
