
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `recipe_item` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model recipe_item
 * 
 */
export type recipe_itemModel = runtime.Types.Result.DefaultSelection<Prisma.$recipe_itemPayload>

export type AggregateRecipe_item = {
  _count: Recipe_itemCountAggregateOutputType | null
  _avg: Recipe_itemAvgAggregateOutputType | null
  _sum: Recipe_itemSumAggregateOutputType | null
  _min: Recipe_itemMinAggregateOutputType | null
  _max: Recipe_itemMaxAggregateOutputType | null
}

export type Recipe_itemAvgAggregateOutputType = {
  count: number | null
  craftingRecipeId: number | null
  itemId: number | null
}

export type Recipe_itemSumAggregateOutputType = {
  count: number | null
  craftingRecipeId: number | null
  itemId: number | null
}

export type Recipe_itemMinAggregateOutputType = {
  count: number | null
  itemType: $Enums.RecipeItemTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  craftingRecipeId: number | null
  itemId: number | null
}

export type Recipe_itemMaxAggregateOutputType = {
  count: number | null
  itemType: $Enums.RecipeItemTypes | null
  createdAt: Date | null
  updatedAt: Date | null
  craftingRecipeId: number | null
  itemId: number | null
}

export type Recipe_itemCountAggregateOutputType = {
  count: number
  itemType: number
  createdAt: number
  updatedAt: number
  craftingRecipeId: number
  itemId: number
  _all: number
}


export type Recipe_itemAvgAggregateInputType = {
  count?: true
  craftingRecipeId?: true
  itemId?: true
}

export type Recipe_itemSumAggregateInputType = {
  count?: true
  craftingRecipeId?: true
  itemId?: true
}

export type Recipe_itemMinAggregateInputType = {
  count?: true
  itemType?: true
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  itemId?: true
}

export type Recipe_itemMaxAggregateInputType = {
  count?: true
  itemType?: true
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  itemId?: true
}

export type Recipe_itemCountAggregateInputType = {
  count?: true
  itemType?: true
  createdAt?: true
  updatedAt?: true
  craftingRecipeId?: true
  itemId?: true
  _all?: true
}

export type Recipe_itemAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which recipe_item to aggregate.
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of recipe_items to fetch.
   */
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.recipe_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` recipe_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` recipe_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned recipe_items
  **/
  _count?: true | Recipe_itemCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Recipe_itemAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Recipe_itemSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Recipe_itemMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Recipe_itemMaxAggregateInputType
}

export type GetRecipe_itemAggregateType<T extends Recipe_itemAggregateArgs> = {
      [P in keyof T & keyof AggregateRecipe_item]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRecipe_item[P]>
    : Prisma.GetScalarType<T[P], AggregateRecipe_item[P]>
}




export type recipe_itemGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.recipe_itemWhereInput
  orderBy?: Prisma.recipe_itemOrderByWithAggregationInput | Prisma.recipe_itemOrderByWithAggregationInput[]
  by: Prisma.Recipe_itemScalarFieldEnum[] | Prisma.Recipe_itemScalarFieldEnum
  having?: Prisma.recipe_itemScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Recipe_itemCountAggregateInputType | true
  _avg?: Recipe_itemAvgAggregateInputType
  _sum?: Recipe_itemSumAggregateInputType
  _min?: Recipe_itemMinAggregateInputType
  _max?: Recipe_itemMaxAggregateInputType
}

export type Recipe_itemGroupByOutputType = {
  count: number | null
  itemType: $Enums.RecipeItemTypes | null
  createdAt: Date
  updatedAt: Date
  craftingRecipeId: number
  itemId: number
  _count: Recipe_itemCountAggregateOutputType | null
  _avg: Recipe_itemAvgAggregateOutputType | null
  _sum: Recipe_itemSumAggregateOutputType | null
  _min: Recipe_itemMinAggregateOutputType | null
  _max: Recipe_itemMaxAggregateOutputType | null
}

type GetRecipe_itemGroupByPayload<T extends recipe_itemGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Recipe_itemGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Recipe_itemGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Recipe_itemGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Recipe_itemGroupByOutputType[P]>
      }
    >
  >



export type recipe_itemWhereInput = {
  AND?: Prisma.recipe_itemWhereInput | Prisma.recipe_itemWhereInput[]
  OR?: Prisma.recipe_itemWhereInput[]
  NOT?: Prisma.recipe_itemWhereInput | Prisma.recipe_itemWhereInput[]
  count?: Prisma.IntNullableFilter<"recipe_item"> | number | null
  itemType?: Prisma.EnumRecipeItemTypesNullableFilter<"recipe_item"> | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"recipe_item"> | number
  itemId?: Prisma.IntFilter<"recipe_item"> | number
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeScalarRelationFilter, Prisma.crafting_recipeWhereInput>
  item?: Prisma.XOR<Prisma.ItemScalarRelationFilter, Prisma.itemWhereInput>
}

export type recipe_itemOrderByWithRelationInput = {
  count?: Prisma.SortOrderInput | Prisma.SortOrder
  itemType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  crafting_recipe?: Prisma.crafting_recipeOrderByWithRelationInput
  item?: Prisma.itemOrderByWithRelationInput
}

export type recipe_itemWhereUniqueInput = Prisma.AtLeast<{
  craftingRecipeId_itemId?: Prisma.recipe_itemCraftingRecipeIdItemIdCompoundUniqueInput
  AND?: Prisma.recipe_itemWhereInput | Prisma.recipe_itemWhereInput[]
  OR?: Prisma.recipe_itemWhereInput[]
  NOT?: Prisma.recipe_itemWhereInput | Prisma.recipe_itemWhereInput[]
  count?: Prisma.IntNullableFilter<"recipe_item"> | number | null
  itemType?: Prisma.EnumRecipeItemTypesNullableFilter<"recipe_item"> | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"recipe_item"> | number
  itemId?: Prisma.IntFilter<"recipe_item"> | number
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeScalarRelationFilter, Prisma.crafting_recipeWhereInput>
  item?: Prisma.XOR<Prisma.ItemScalarRelationFilter, Prisma.itemWhereInput>
}, "craftingRecipeId_itemId">

export type recipe_itemOrderByWithAggregationInput = {
  count?: Prisma.SortOrderInput | Prisma.SortOrder
  itemType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  _count?: Prisma.recipe_itemCountOrderByAggregateInput
  _avg?: Prisma.recipe_itemAvgOrderByAggregateInput
  _max?: Prisma.recipe_itemMaxOrderByAggregateInput
  _min?: Prisma.recipe_itemMinOrderByAggregateInput
  _sum?: Prisma.recipe_itemSumOrderByAggregateInput
}

export type recipe_itemScalarWhereWithAggregatesInput = {
  AND?: Prisma.recipe_itemScalarWhereWithAggregatesInput | Prisma.recipe_itemScalarWhereWithAggregatesInput[]
  OR?: Prisma.recipe_itemScalarWhereWithAggregatesInput[]
  NOT?: Prisma.recipe_itemScalarWhereWithAggregatesInput | Prisma.recipe_itemScalarWhereWithAggregatesInput[]
  count?: Prisma.IntNullableWithAggregatesFilter<"recipe_item"> | number | null
  itemType?: Prisma.EnumRecipeItemTypesNullableWithAggregatesFilter<"recipe_item"> | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"recipe_item"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"recipe_item"> | Date | string
  craftingRecipeId?: Prisma.IntWithAggregatesFilter<"recipe_item"> | number
  itemId?: Prisma.IntWithAggregatesFilter<"recipe_item"> | number
}

export type recipe_itemCreateInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  crafting_recipe: Prisma.crafting_recipeCreateNestedOneWithoutRecipe_itemInput
  item: Prisma.itemCreateNestedOneWithoutRecipe_itemInput
}

export type recipe_itemUncheckedCreateInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
  itemId: number
}

export type recipe_itemUpdateInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  crafting_recipe?: Prisma.crafting_recipeUpdateOneRequiredWithoutRecipe_itemNestedInput
  item?: Prisma.itemUpdateOneRequiredWithoutRecipe_itemNestedInput
}

export type recipe_itemUncheckedUpdateInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
  itemId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type recipe_itemCreateManyInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
  itemId: number
}

export type recipe_itemUpdateManyMutationInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type recipe_itemUncheckedUpdateManyInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
  itemId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type Recipe_itemListRelationFilter = {
  every?: Prisma.recipe_itemWhereInput
  some?: Prisma.recipe_itemWhereInput
  none?: Prisma.recipe_itemWhereInput
}

export type recipe_itemOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type recipe_itemCraftingRecipeIdItemIdCompoundUniqueInput = {
  craftingRecipeId: number
  itemId: number
}

export type recipe_itemCountOrderByAggregateInput = {
  count?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type recipe_itemAvgOrderByAggregateInput = {
  count?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type recipe_itemMaxOrderByAggregateInput = {
  count?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type recipe_itemMinOrderByAggregateInput = {
  count?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type recipe_itemSumOrderByAggregateInput = {
  count?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type recipe_itemCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.recipe_itemCreateWithoutCrafting_recipeInput[] | Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.recipe_itemCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
}

export type recipe_itemUncheckedCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.recipe_itemCreateWithoutCrafting_recipeInput[] | Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.recipe_itemCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
}

export type recipe_itemUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.recipe_itemCreateWithoutCrafting_recipeInput[] | Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.recipe_itemUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.recipe_itemUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.recipe_itemCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  disconnect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  delete?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  update?: Prisma.recipe_itemUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.recipe_itemUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.recipe_itemUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.recipe_itemUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
}

export type recipe_itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.recipe_itemCreateWithoutCrafting_recipeInput[] | Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.recipe_itemCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.recipe_itemUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.recipe_itemUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.recipe_itemCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  disconnect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  delete?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  update?: Prisma.recipe_itemUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.recipe_itemUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.recipe_itemUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.recipe_itemUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
}

export type recipe_itemCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput> | Prisma.recipe_itemCreateWithoutItemInput[] | Prisma.recipe_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutItemInput | Prisma.recipe_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.recipe_itemCreateManyItemInputEnvelope
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
}

export type recipe_itemUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput> | Prisma.recipe_itemCreateWithoutItemInput[] | Prisma.recipe_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutItemInput | Prisma.recipe_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.recipe_itemCreateManyItemInputEnvelope
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
}

export type recipe_itemUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput> | Prisma.recipe_itemCreateWithoutItemInput[] | Prisma.recipe_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutItemInput | Prisma.recipe_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.recipe_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.recipe_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.recipe_itemCreateManyItemInputEnvelope
  set?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  disconnect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  delete?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  update?: Prisma.recipe_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.recipe_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.recipe_itemUpdateManyWithWhereWithoutItemInput | Prisma.recipe_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
}

export type recipe_itemUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput> | Prisma.recipe_itemCreateWithoutItemInput[] | Prisma.recipe_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.recipe_itemCreateOrConnectWithoutItemInput | Prisma.recipe_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.recipe_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.recipe_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.recipe_itemCreateManyItemInputEnvelope
  set?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  disconnect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  delete?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  connect?: Prisma.recipe_itemWhereUniqueInput | Prisma.recipe_itemWhereUniqueInput[]
  update?: Prisma.recipe_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.recipe_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.recipe_itemUpdateManyWithWhereWithoutItemInput | Prisma.recipe_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
}

export type NullableEnumRecipeItemTypesFieldUpdateOperationsInput = {
  set?: $Enums.RecipeItemTypes | null
}

export type recipe_itemCreateWithoutCrafting_recipeInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  item: Prisma.itemCreateNestedOneWithoutRecipe_itemInput
}

export type recipe_itemUncheckedCreateWithoutCrafting_recipeInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId: number
}

export type recipe_itemCreateOrConnectWithoutCrafting_recipeInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput>
}

export type recipe_itemCreateManyCrafting_recipeInputEnvelope = {
  data: Prisma.recipe_itemCreateManyCrafting_recipeInput | Prisma.recipe_itemCreateManyCrafting_recipeInput[]
  skipDuplicates?: boolean
}

export type recipe_itemUpsertWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.recipe_itemUpdateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedUpdateWithoutCrafting_recipeInput>
  create: Prisma.XOR<Prisma.recipe_itemCreateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedCreateWithoutCrafting_recipeInput>
}

export type recipe_itemUpdateWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.recipe_itemUpdateWithoutCrafting_recipeInput, Prisma.recipe_itemUncheckedUpdateWithoutCrafting_recipeInput>
}

export type recipe_itemUpdateManyWithWhereWithoutCrafting_recipeInput = {
  where: Prisma.recipe_itemScalarWhereInput
  data: Prisma.XOR<Prisma.recipe_itemUpdateManyMutationInput, Prisma.recipe_itemUncheckedUpdateManyWithoutCrafting_recipeInput>
}

export type recipe_itemScalarWhereInput = {
  AND?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
  OR?: Prisma.recipe_itemScalarWhereInput[]
  NOT?: Prisma.recipe_itemScalarWhereInput | Prisma.recipe_itemScalarWhereInput[]
  count?: Prisma.IntNullableFilter<"recipe_item"> | number | null
  itemType?: Prisma.EnumRecipeItemTypesNullableFilter<"recipe_item"> | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"recipe_item"> | Date | string
  craftingRecipeId?: Prisma.IntFilter<"recipe_item"> | number
  itemId?: Prisma.IntFilter<"recipe_item"> | number
}

export type recipe_itemCreateWithoutItemInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  crafting_recipe: Prisma.crafting_recipeCreateNestedOneWithoutRecipe_itemInput
}

export type recipe_itemUncheckedCreateWithoutItemInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
}

export type recipe_itemCreateOrConnectWithoutItemInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput>
}

export type recipe_itemCreateManyItemInputEnvelope = {
  data: Prisma.recipe_itemCreateManyItemInput | Prisma.recipe_itemCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type recipe_itemUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.recipe_itemUpdateWithoutItemInput, Prisma.recipe_itemUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.recipe_itemCreateWithoutItemInput, Prisma.recipe_itemUncheckedCreateWithoutItemInput>
}

export type recipe_itemUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.recipe_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.recipe_itemUpdateWithoutItemInput, Prisma.recipe_itemUncheckedUpdateWithoutItemInput>
}

export type recipe_itemUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.recipe_itemScalarWhereInput
  data: Prisma.XOR<Prisma.recipe_itemUpdateManyMutationInput, Prisma.recipe_itemUncheckedUpdateManyWithoutItemInput>
}

export type recipe_itemCreateManyCrafting_recipeInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId: number
}

export type recipe_itemUpdateWithoutCrafting_recipeInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneRequiredWithoutRecipe_itemNestedInput
}

export type recipe_itemUncheckedUpdateWithoutCrafting_recipeInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type recipe_itemUncheckedUpdateManyWithoutCrafting_recipeInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type recipe_itemCreateManyItemInput = {
  count?: number | null
  itemType?: $Enums.RecipeItemTypes | null
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId: number
}

export type recipe_itemUpdateWithoutItemInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  crafting_recipe?: Prisma.crafting_recipeUpdateOneRequiredWithoutRecipe_itemNestedInput
}

export type recipe_itemUncheckedUpdateWithoutItemInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type recipe_itemUncheckedUpdateManyWithoutItemInput = {
  count?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemType?: Prisma.NullableEnumRecipeItemTypesFieldUpdateOperationsInput | $Enums.RecipeItemTypes | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type recipe_itemSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  count?: boolean
  itemType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  craftingRecipeId?: boolean
  itemId?: boolean
  crafting_recipe?: boolean | Prisma.crafting_recipeDefaultArgs<ExtArgs>
  item?: boolean | Prisma.itemDefaultArgs<ExtArgs>
}, ExtArgs["result"]["recipe_item"]>



export type recipe_itemSelectScalar = {
  count?: boolean
  itemType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  craftingRecipeId?: boolean
  itemId?: boolean
}

export type recipe_itemOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"count" | "itemType" | "createdAt" | "updatedAt" | "craftingRecipeId" | "itemId", ExtArgs["result"]["recipe_item"]>
export type recipe_itemInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  crafting_recipe?: boolean | Prisma.crafting_recipeDefaultArgs<ExtArgs>
  item?: boolean | Prisma.itemDefaultArgs<ExtArgs>
}

export type $recipe_itemPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "recipe_item"
  objects: {
    crafting_recipe: Prisma.$crafting_recipePayload<ExtArgs>
    item: Prisma.$itemPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    count: number | null
    itemType: $Enums.RecipeItemTypes | null
    createdAt: Date
    updatedAt: Date
    craftingRecipeId: number
    itemId: number
  }, ExtArgs["result"]["recipe_item"]>
  composites: {}
}

export type recipe_itemGetPayload<S extends boolean | null | undefined | recipe_itemDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload, S>

export type recipe_itemCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<recipe_itemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Recipe_itemCountAggregateInputType | true
  }

export interface recipe_itemDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['recipe_item'], meta: { name: 'recipe_item' } }
  /**
   * Find zero or one Recipe_item that matches the filter.
   * @param {recipe_itemFindUniqueArgs} args - Arguments to find a Recipe_item
   * @example
   * // Get one Recipe_item
   * const recipe_item = await prisma.recipe_item.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends recipe_itemFindUniqueArgs>(args: Prisma.SelectSubset<T, recipe_itemFindUniqueArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Recipe_item that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {recipe_itemFindUniqueOrThrowArgs} args - Arguments to find a Recipe_item
   * @example
   * // Get one Recipe_item
   * const recipe_item = await prisma.recipe_item.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends recipe_itemFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, recipe_itemFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Recipe_item that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemFindFirstArgs} args - Arguments to find a Recipe_item
   * @example
   * // Get one Recipe_item
   * const recipe_item = await prisma.recipe_item.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends recipe_itemFindFirstArgs>(args?: Prisma.SelectSubset<T, recipe_itemFindFirstArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Recipe_item that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemFindFirstOrThrowArgs} args - Arguments to find a Recipe_item
   * @example
   * // Get one Recipe_item
   * const recipe_item = await prisma.recipe_item.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends recipe_itemFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, recipe_itemFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Recipe_items that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Recipe_items
   * const recipe_items = await prisma.recipe_item.findMany()
   * 
   * // Get first 10 Recipe_items
   * const recipe_items = await prisma.recipe_item.findMany({ take: 10 })
   * 
   * // Only select the `count`
   * const recipe_itemWithCountOnly = await prisma.recipe_item.findMany({ select: { count: true } })
   * 
   */
  findMany<T extends recipe_itemFindManyArgs>(args?: Prisma.SelectSubset<T, recipe_itemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Recipe_item.
   * @param {recipe_itemCreateArgs} args - Arguments to create a Recipe_item.
   * @example
   * // Create one Recipe_item
   * const Recipe_item = await prisma.recipe_item.create({
   *   data: {
   *     // ... data to create a Recipe_item
   *   }
   * })
   * 
   */
  create<T extends recipe_itemCreateArgs>(args: Prisma.SelectSubset<T, recipe_itemCreateArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Recipe_items.
   * @param {recipe_itemCreateManyArgs} args - Arguments to create many Recipe_items.
   * @example
   * // Create many Recipe_items
   * const recipe_item = await prisma.recipe_item.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends recipe_itemCreateManyArgs>(args?: Prisma.SelectSubset<T, recipe_itemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Recipe_item.
   * @param {recipe_itemDeleteArgs} args - Arguments to delete one Recipe_item.
   * @example
   * // Delete one Recipe_item
   * const Recipe_item = await prisma.recipe_item.delete({
   *   where: {
   *     // ... filter to delete one Recipe_item
   *   }
   * })
   * 
   */
  delete<T extends recipe_itemDeleteArgs>(args: Prisma.SelectSubset<T, recipe_itemDeleteArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Recipe_item.
   * @param {recipe_itemUpdateArgs} args - Arguments to update one Recipe_item.
   * @example
   * // Update one Recipe_item
   * const recipe_item = await prisma.recipe_item.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends recipe_itemUpdateArgs>(args: Prisma.SelectSubset<T, recipe_itemUpdateArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Recipe_items.
   * @param {recipe_itemDeleteManyArgs} args - Arguments to filter Recipe_items to delete.
   * @example
   * // Delete a few Recipe_items
   * const { count } = await prisma.recipe_item.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends recipe_itemDeleteManyArgs>(args?: Prisma.SelectSubset<T, recipe_itemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Recipe_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Recipe_items
   * const recipe_item = await prisma.recipe_item.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends recipe_itemUpdateManyArgs>(args: Prisma.SelectSubset<T, recipe_itemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Recipe_item.
   * @param {recipe_itemUpsertArgs} args - Arguments to update or create a Recipe_item.
   * @example
   * // Update or create a Recipe_item
   * const recipe_item = await prisma.recipe_item.upsert({
   *   create: {
   *     // ... data to create a Recipe_item
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Recipe_item we want to update
   *   }
   * })
   */
  upsert<T extends recipe_itemUpsertArgs>(args: Prisma.SelectSubset<T, recipe_itemUpsertArgs<ExtArgs>>): Prisma.Prisma__recipe_itemClient<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Recipe_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemCountArgs} args - Arguments to filter Recipe_items to count.
   * @example
   * // Count the number of Recipe_items
   * const count = await prisma.recipe_item.count({
   *   where: {
   *     // ... the filter for the Recipe_items we want to count
   *   }
   * })
  **/
  count<T extends recipe_itemCountArgs>(
    args?: Prisma.Subset<T, recipe_itemCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Recipe_itemCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Recipe_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Recipe_itemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Recipe_itemAggregateArgs>(args: Prisma.Subset<T, Recipe_itemAggregateArgs>): Prisma.PrismaPromise<GetRecipe_itemAggregateType<T>>

  /**
   * Group by Recipe_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {recipe_itemGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends recipe_itemGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: recipe_itemGroupByArgs['orderBy'] }
      : { orderBy?: recipe_itemGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, recipe_itemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipe_itemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the recipe_item model
 */
readonly fields: recipe_itemFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for recipe_item.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__recipe_itemClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  crafting_recipe<T extends Prisma.crafting_recipeDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.crafting_recipeDefaultArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  item<T extends Prisma.itemDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.itemDefaultArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the recipe_item model
 */
export interface recipe_itemFieldRefs {
  readonly count: Prisma.FieldRef<"recipe_item", 'Int'>
  readonly itemType: Prisma.FieldRef<"recipe_item", 'RecipeItemTypes'>
  readonly createdAt: Prisma.FieldRef<"recipe_item", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"recipe_item", 'DateTime'>
  readonly craftingRecipeId: Prisma.FieldRef<"recipe_item", 'Int'>
  readonly itemId: Prisma.FieldRef<"recipe_item", 'Int'>
}
    

// Custom InputTypes
/**
 * recipe_item findUnique
 */
export type recipe_itemFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter, which recipe_item to fetch.
   */
  where: Prisma.recipe_itemWhereUniqueInput
}

/**
 * recipe_item findUniqueOrThrow
 */
export type recipe_itemFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter, which recipe_item to fetch.
   */
  where: Prisma.recipe_itemWhereUniqueInput
}

/**
 * recipe_item findFirst
 */
export type recipe_itemFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter, which recipe_item to fetch.
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of recipe_items to fetch.
   */
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for recipe_items.
   */
  cursor?: Prisma.recipe_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` recipe_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` recipe_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of recipe_items.
   */
  distinct?: Prisma.Recipe_itemScalarFieldEnum | Prisma.Recipe_itemScalarFieldEnum[]
}

/**
 * recipe_item findFirstOrThrow
 */
export type recipe_itemFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter, which recipe_item to fetch.
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of recipe_items to fetch.
   */
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for recipe_items.
   */
  cursor?: Prisma.recipe_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` recipe_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` recipe_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of recipe_items.
   */
  distinct?: Prisma.Recipe_itemScalarFieldEnum | Prisma.Recipe_itemScalarFieldEnum[]
}

/**
 * recipe_item findMany
 */
export type recipe_itemFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter, which recipe_items to fetch.
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of recipe_items to fetch.
   */
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing recipe_items.
   */
  cursor?: Prisma.recipe_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` recipe_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` recipe_items.
   */
  skip?: number
  distinct?: Prisma.Recipe_itemScalarFieldEnum | Prisma.Recipe_itemScalarFieldEnum[]
}

/**
 * recipe_item create
 */
export type recipe_itemCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * The data needed to create a recipe_item.
   */
  data: Prisma.XOR<Prisma.recipe_itemCreateInput, Prisma.recipe_itemUncheckedCreateInput>
}

/**
 * recipe_item createMany
 */
export type recipe_itemCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many recipe_items.
   */
  data: Prisma.recipe_itemCreateManyInput | Prisma.recipe_itemCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * recipe_item update
 */
export type recipe_itemUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * The data needed to update a recipe_item.
   */
  data: Prisma.XOR<Prisma.recipe_itemUpdateInput, Prisma.recipe_itemUncheckedUpdateInput>
  /**
   * Choose, which recipe_item to update.
   */
  where: Prisma.recipe_itemWhereUniqueInput
}

/**
 * recipe_item updateMany
 */
export type recipe_itemUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update recipe_items.
   */
  data: Prisma.XOR<Prisma.recipe_itemUpdateManyMutationInput, Prisma.recipe_itemUncheckedUpdateManyInput>
  /**
   * Filter which recipe_items to update
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * Limit how many recipe_items to update.
   */
  limit?: number
}

/**
 * recipe_item upsert
 */
export type recipe_itemUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * The filter to search for the recipe_item to update in case it exists.
   */
  where: Prisma.recipe_itemWhereUniqueInput
  /**
   * In case the recipe_item found by the `where` argument doesn't exist, create a new recipe_item with this data.
   */
  create: Prisma.XOR<Prisma.recipe_itemCreateInput, Prisma.recipe_itemUncheckedCreateInput>
  /**
   * In case the recipe_item was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.recipe_itemUpdateInput, Prisma.recipe_itemUncheckedUpdateInput>
}

/**
 * recipe_item delete
 */
export type recipe_itemDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  /**
   * Filter which recipe_item to delete.
   */
  where: Prisma.recipe_itemWhereUniqueInput
}

/**
 * recipe_item deleteMany
 */
export type recipe_itemDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which recipe_items to delete
   */
  where?: Prisma.recipe_itemWhereInput
  /**
   * Limit how many recipe_items to delete.
   */
  limit?: number
}

/**
 * recipe_item without action
 */
export type recipe_itemDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
}
