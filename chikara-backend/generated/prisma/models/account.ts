
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `account` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model account
 * 
 */
export type accountModel = runtime.Types.Result.DefaultSelection<Prisma.$accountPayload>

export type AggregateAccount = {
  _count: AccountCountAggregateOutputType | null
  _avg: AccountAvgAggregateOutputType | null
  _sum: AccountSumAggregateOutputType | null
  _min: AccountMinAggregateOutputType | null
  _max: AccountMaxAggregateOutputType | null
}

export type AccountAvgAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type AccountSumAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type AccountMinAggregateOutputType = {
  id: number | null
  accountId: string | null
  providerId: string | null
  userId: number | null
  accessToken: string | null
  refreshToken: string | null
  idToken: string | null
  accessTokenExpiresAt: Date | null
  refreshTokenExpiresAt: Date | null
  scope: string | null
  password: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AccountMaxAggregateOutputType = {
  id: number | null
  accountId: string | null
  providerId: string | null
  userId: number | null
  accessToken: string | null
  refreshToken: string | null
  idToken: string | null
  accessTokenExpiresAt: Date | null
  refreshTokenExpiresAt: Date | null
  scope: string | null
  password: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AccountCountAggregateOutputType = {
  id: number
  accountId: number
  providerId: number
  userId: number
  accessToken: number
  refreshToken: number
  idToken: number
  accessTokenExpiresAt: number
  refreshTokenExpiresAt: number
  scope: number
  password: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type AccountAvgAggregateInputType = {
  id?: true
  userId?: true
}

export type AccountSumAggregateInputType = {
  id?: true
  userId?: true
}

export type AccountMinAggregateInputType = {
  id?: true
  accountId?: true
  providerId?: true
  userId?: true
  accessToken?: true
  refreshToken?: true
  idToken?: true
  accessTokenExpiresAt?: true
  refreshTokenExpiresAt?: true
  scope?: true
  password?: true
  createdAt?: true
  updatedAt?: true
}

export type AccountMaxAggregateInputType = {
  id?: true
  accountId?: true
  providerId?: true
  userId?: true
  accessToken?: true
  refreshToken?: true
  idToken?: true
  accessTokenExpiresAt?: true
  refreshTokenExpiresAt?: true
  scope?: true
  password?: true
  createdAt?: true
  updatedAt?: true
}

export type AccountCountAggregateInputType = {
  id?: true
  accountId?: true
  providerId?: true
  userId?: true
  accessToken?: true
  refreshToken?: true
  idToken?: true
  accessTokenExpiresAt?: true
  refreshTokenExpiresAt?: true
  scope?: true
  password?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type AccountAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which account to aggregate.
   */
  where?: Prisma.accountWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of accounts to fetch.
   */
  orderBy?: Prisma.accountOrderByWithRelationInput | Prisma.accountOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.accountWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` accounts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` accounts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned accounts
  **/
  _count?: true | AccountCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AccountAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AccountSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AccountMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AccountMaxAggregateInputType
}

export type GetAccountAggregateType<T extends AccountAggregateArgs> = {
      [P in keyof T & keyof AggregateAccount]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAccount[P]>
    : Prisma.GetScalarType<T[P], AggregateAccount[P]>
}




export type accountGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.accountWhereInput
  orderBy?: Prisma.accountOrderByWithAggregationInput | Prisma.accountOrderByWithAggregationInput[]
  by: Prisma.AccountScalarFieldEnum[] | Prisma.AccountScalarFieldEnum
  having?: Prisma.accountScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AccountCountAggregateInputType | true
  _avg?: AccountAvgAggregateInputType
  _sum?: AccountSumAggregateInputType
  _min?: AccountMinAggregateInputType
  _max?: AccountMaxAggregateInputType
}

export type AccountGroupByOutputType = {
  id: number
  accountId: string
  providerId: string
  userId: number
  accessToken: string | null
  refreshToken: string | null
  idToken: string | null
  accessTokenExpiresAt: Date | null
  refreshTokenExpiresAt: Date | null
  scope: string | null
  password: string | null
  createdAt: Date
  updatedAt: Date
  _count: AccountCountAggregateOutputType | null
  _avg: AccountAvgAggregateOutputType | null
  _sum: AccountSumAggregateOutputType | null
  _min: AccountMinAggregateOutputType | null
  _max: AccountMaxAggregateOutputType | null
}

type GetAccountGroupByPayload<T extends accountGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AccountGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AccountGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AccountGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AccountGroupByOutputType[P]>
      }
    >
  >



export type accountWhereInput = {
  AND?: Prisma.accountWhereInput | Prisma.accountWhereInput[]
  OR?: Prisma.accountWhereInput[]
  NOT?: Prisma.accountWhereInput | Prisma.accountWhereInput[]
  id?: Prisma.IntFilter<"account"> | number
  accountId?: Prisma.StringFilter<"account"> | string
  providerId?: Prisma.StringFilter<"account"> | string
  userId?: Prisma.IntFilter<"account"> | number
  accessToken?: Prisma.StringNullableFilter<"account"> | string | null
  refreshToken?: Prisma.StringNullableFilter<"account"> | string | null
  idToken?: Prisma.StringNullableFilter<"account"> | string | null
  accessTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  refreshTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  scope?: Prisma.StringNullableFilter<"account"> | string | null
  password?: Prisma.StringNullableFilter<"account"> | string | null
  createdAt?: Prisma.DateTimeFilter<"account"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"account"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type accountOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  accountId?: Prisma.SortOrder
  providerId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  accessToken?: Prisma.SortOrderInput | Prisma.SortOrder
  refreshToken?: Prisma.SortOrderInput | Prisma.SortOrder
  idToken?: Prisma.SortOrderInput | Prisma.SortOrder
  accessTokenExpiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  refreshTokenExpiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  scope?: Prisma.SortOrderInput | Prisma.SortOrder
  password?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.accountOrderByRelevanceInput
}

export type accountWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.accountWhereInput | Prisma.accountWhereInput[]
  OR?: Prisma.accountWhereInput[]
  NOT?: Prisma.accountWhereInput | Prisma.accountWhereInput[]
  accountId?: Prisma.StringFilter<"account"> | string
  providerId?: Prisma.StringFilter<"account"> | string
  userId?: Prisma.IntFilter<"account"> | number
  accessToken?: Prisma.StringNullableFilter<"account"> | string | null
  refreshToken?: Prisma.StringNullableFilter<"account"> | string | null
  idToken?: Prisma.StringNullableFilter<"account"> | string | null
  accessTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  refreshTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  scope?: Prisma.StringNullableFilter<"account"> | string | null
  password?: Prisma.StringNullableFilter<"account"> | string | null
  createdAt?: Prisma.DateTimeFilter<"account"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"account"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id">

export type accountOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  accountId?: Prisma.SortOrder
  providerId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  accessToken?: Prisma.SortOrderInput | Prisma.SortOrder
  refreshToken?: Prisma.SortOrderInput | Prisma.SortOrder
  idToken?: Prisma.SortOrderInput | Prisma.SortOrder
  accessTokenExpiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  refreshTokenExpiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  scope?: Prisma.SortOrderInput | Prisma.SortOrder
  password?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.accountCountOrderByAggregateInput
  _avg?: Prisma.accountAvgOrderByAggregateInput
  _max?: Prisma.accountMaxOrderByAggregateInput
  _min?: Prisma.accountMinOrderByAggregateInput
  _sum?: Prisma.accountSumOrderByAggregateInput
}

export type accountScalarWhereWithAggregatesInput = {
  AND?: Prisma.accountScalarWhereWithAggregatesInput | Prisma.accountScalarWhereWithAggregatesInput[]
  OR?: Prisma.accountScalarWhereWithAggregatesInput[]
  NOT?: Prisma.accountScalarWhereWithAggregatesInput | Prisma.accountScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"account"> | number
  accountId?: Prisma.StringWithAggregatesFilter<"account"> | string
  providerId?: Prisma.StringWithAggregatesFilter<"account"> | string
  userId?: Prisma.IntWithAggregatesFilter<"account"> | number
  accessToken?: Prisma.StringNullableWithAggregatesFilter<"account"> | string | null
  refreshToken?: Prisma.StringNullableWithAggregatesFilter<"account"> | string | null
  idToken?: Prisma.StringNullableWithAggregatesFilter<"account"> | string | null
  accessTokenExpiresAt?: Prisma.DateTimeNullableWithAggregatesFilter<"account"> | Date | string | null
  refreshTokenExpiresAt?: Prisma.DateTimeNullableWithAggregatesFilter<"account"> | Date | string | null
  scope?: Prisma.StringNullableWithAggregatesFilter<"account"> | string | null
  password?: Prisma.StringNullableWithAggregatesFilter<"account"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"account"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"account"> | Date | string
}

export type accountCreateInput = {
  accountId: string
  providerId: string
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  user: Prisma.userCreateNestedOneWithoutAccountsInput
}

export type accountUncheckedCreateInput = {
  id?: number
  accountId: string
  providerId: string
  userId: number
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type accountUpdateInput = {
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutAccountsNestedInput
}

export type accountUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type accountCreateManyInput = {
  id?: number
  accountId: string
  providerId: string
  userId: number
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type accountUpdateManyMutationInput = {
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type accountUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AccountListRelationFilter = {
  every?: Prisma.accountWhereInput
  some?: Prisma.accountWhereInput
  none?: Prisma.accountWhereInput
}

export type accountOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type accountOrderByRelevanceInput = {
  fields: Prisma.accountOrderByRelevanceFieldEnum | Prisma.accountOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type accountCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  accountId?: Prisma.SortOrder
  providerId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  accessToken?: Prisma.SortOrder
  refreshToken?: Prisma.SortOrder
  idToken?: Prisma.SortOrder
  accessTokenExpiresAt?: Prisma.SortOrder
  refreshTokenExpiresAt?: Prisma.SortOrder
  scope?: Prisma.SortOrder
  password?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type accountAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type accountMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  accountId?: Prisma.SortOrder
  providerId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  accessToken?: Prisma.SortOrder
  refreshToken?: Prisma.SortOrder
  idToken?: Prisma.SortOrder
  accessTokenExpiresAt?: Prisma.SortOrder
  refreshTokenExpiresAt?: Prisma.SortOrder
  scope?: Prisma.SortOrder
  password?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type accountMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  accountId?: Prisma.SortOrder
  providerId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  accessToken?: Prisma.SortOrder
  refreshToken?: Prisma.SortOrder
  idToken?: Prisma.SortOrder
  accessTokenExpiresAt?: Prisma.SortOrder
  refreshTokenExpiresAt?: Prisma.SortOrder
  scope?: Prisma.SortOrder
  password?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type accountSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type accountCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput> | Prisma.accountCreateWithoutUserInput[] | Prisma.accountUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.accountCreateOrConnectWithoutUserInput | Prisma.accountCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.accountCreateManyUserInputEnvelope
  connect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
}

export type accountUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput> | Prisma.accountCreateWithoutUserInput[] | Prisma.accountUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.accountCreateOrConnectWithoutUserInput | Prisma.accountCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.accountCreateManyUserInputEnvelope
  connect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
}

export type accountUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput> | Prisma.accountCreateWithoutUserInput[] | Prisma.accountUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.accountCreateOrConnectWithoutUserInput | Prisma.accountCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.accountUpsertWithWhereUniqueWithoutUserInput | Prisma.accountUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.accountCreateManyUserInputEnvelope
  set?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  disconnect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  delete?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  connect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  update?: Prisma.accountUpdateWithWhereUniqueWithoutUserInput | Prisma.accountUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.accountUpdateManyWithWhereWithoutUserInput | Prisma.accountUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.accountScalarWhereInput | Prisma.accountScalarWhereInput[]
}

export type accountUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput> | Prisma.accountCreateWithoutUserInput[] | Prisma.accountUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.accountCreateOrConnectWithoutUserInput | Prisma.accountCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.accountUpsertWithWhereUniqueWithoutUserInput | Prisma.accountUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.accountCreateManyUserInputEnvelope
  set?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  disconnect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  delete?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  connect?: Prisma.accountWhereUniqueInput | Prisma.accountWhereUniqueInput[]
  update?: Prisma.accountUpdateWithWhereUniqueWithoutUserInput | Prisma.accountUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.accountUpdateManyWithWhereWithoutUserInput | Prisma.accountUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.accountScalarWhereInput | Prisma.accountScalarWhereInput[]
}

export type accountCreateWithoutUserInput = {
  accountId: string
  providerId: string
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type accountUncheckedCreateWithoutUserInput = {
  id?: number
  accountId: string
  providerId: string
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type accountCreateOrConnectWithoutUserInput = {
  where: Prisma.accountWhereUniqueInput
  create: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput>
}

export type accountCreateManyUserInputEnvelope = {
  data: Prisma.accountCreateManyUserInput | Prisma.accountCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type accountUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.accountWhereUniqueInput
  update: Prisma.XOR<Prisma.accountUpdateWithoutUserInput, Prisma.accountUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.accountCreateWithoutUserInput, Prisma.accountUncheckedCreateWithoutUserInput>
}

export type accountUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.accountWhereUniqueInput
  data: Prisma.XOR<Prisma.accountUpdateWithoutUserInput, Prisma.accountUncheckedUpdateWithoutUserInput>
}

export type accountUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.accountScalarWhereInput
  data: Prisma.XOR<Prisma.accountUpdateManyMutationInput, Prisma.accountUncheckedUpdateManyWithoutUserInput>
}

export type accountScalarWhereInput = {
  AND?: Prisma.accountScalarWhereInput | Prisma.accountScalarWhereInput[]
  OR?: Prisma.accountScalarWhereInput[]
  NOT?: Prisma.accountScalarWhereInput | Prisma.accountScalarWhereInput[]
  id?: Prisma.IntFilter<"account"> | number
  accountId?: Prisma.StringFilter<"account"> | string
  providerId?: Prisma.StringFilter<"account"> | string
  userId?: Prisma.IntFilter<"account"> | number
  accessToken?: Prisma.StringNullableFilter<"account"> | string | null
  refreshToken?: Prisma.StringNullableFilter<"account"> | string | null
  idToken?: Prisma.StringNullableFilter<"account"> | string | null
  accessTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  refreshTokenExpiresAt?: Prisma.DateTimeNullableFilter<"account"> | Date | string | null
  scope?: Prisma.StringNullableFilter<"account"> | string | null
  password?: Prisma.StringNullableFilter<"account"> | string | null
  createdAt?: Prisma.DateTimeFilter<"account"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"account"> | Date | string
}

export type accountCreateManyUserInput = {
  id?: number
  accountId: string
  providerId: string
  accessToken?: string | null
  refreshToken?: string | null
  idToken?: string | null
  accessTokenExpiresAt?: Date | string | null
  refreshTokenExpiresAt?: Date | string | null
  scope?: string | null
  password?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type accountUpdateWithoutUserInput = {
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type accountUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type accountUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  accountId?: Prisma.StringFieldUpdateOperationsInput | string
  providerId?: Prisma.StringFieldUpdateOperationsInput | string
  accessToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refreshToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  idToken?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  accessTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refreshTokenExpiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  scope?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  password?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type accountSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  accountId?: boolean
  providerId?: boolean
  userId?: boolean
  accessToken?: boolean
  refreshToken?: boolean
  idToken?: boolean
  accessTokenExpiresAt?: boolean
  refreshTokenExpiresAt?: boolean
  scope?: boolean
  password?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["account"]>



export type accountSelectScalar = {
  id?: boolean
  accountId?: boolean
  providerId?: boolean
  userId?: boolean
  accessToken?: boolean
  refreshToken?: boolean
  idToken?: boolean
  accessTokenExpiresAt?: boolean
  refreshTokenExpiresAt?: boolean
  scope?: boolean
  password?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type accountOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "accountId" | "providerId" | "userId" | "accessToken" | "refreshToken" | "idToken" | "accessTokenExpiresAt" | "refreshTokenExpiresAt" | "scope" | "password" | "createdAt" | "updatedAt", ExtArgs["result"]["account"]>
export type accountInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $accountPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "account"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    accountId: string
    providerId: string
    userId: number
    accessToken: string | null
    refreshToken: string | null
    idToken: string | null
    accessTokenExpiresAt: Date | null
    refreshTokenExpiresAt: Date | null
    scope: string | null
    password: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["account"]>
  composites: {}
}

export type accountGetPayload<S extends boolean | null | undefined | accountDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$accountPayload, S>

export type accountCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<accountFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AccountCountAggregateInputType | true
  }

export interface accountDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['account'], meta: { name: 'account' } }
  /**
   * Find zero or one Account that matches the filter.
   * @param {accountFindUniqueArgs} args - Arguments to find a Account
   * @example
   * // Get one Account
   * const account = await prisma.account.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends accountFindUniqueArgs>(args: Prisma.SelectSubset<T, accountFindUniqueArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Account that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {accountFindUniqueOrThrowArgs} args - Arguments to find a Account
   * @example
   * // Get one Account
   * const account = await prisma.account.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends accountFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, accountFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Account that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountFindFirstArgs} args - Arguments to find a Account
   * @example
   * // Get one Account
   * const account = await prisma.account.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends accountFindFirstArgs>(args?: Prisma.SelectSubset<T, accountFindFirstArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Account that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountFindFirstOrThrowArgs} args - Arguments to find a Account
   * @example
   * // Get one Account
   * const account = await prisma.account.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends accountFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, accountFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Accounts that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Accounts
   * const accounts = await prisma.account.findMany()
   * 
   * // Get first 10 Accounts
   * const accounts = await prisma.account.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const accountWithIdOnly = await prisma.account.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends accountFindManyArgs>(args?: Prisma.SelectSubset<T, accountFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Account.
   * @param {accountCreateArgs} args - Arguments to create a Account.
   * @example
   * // Create one Account
   * const Account = await prisma.account.create({
   *   data: {
   *     // ... data to create a Account
   *   }
   * })
   * 
   */
  create<T extends accountCreateArgs>(args: Prisma.SelectSubset<T, accountCreateArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Accounts.
   * @param {accountCreateManyArgs} args - Arguments to create many Accounts.
   * @example
   * // Create many Accounts
   * const account = await prisma.account.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends accountCreateManyArgs>(args?: Prisma.SelectSubset<T, accountCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Account.
   * @param {accountDeleteArgs} args - Arguments to delete one Account.
   * @example
   * // Delete one Account
   * const Account = await prisma.account.delete({
   *   where: {
   *     // ... filter to delete one Account
   *   }
   * })
   * 
   */
  delete<T extends accountDeleteArgs>(args: Prisma.SelectSubset<T, accountDeleteArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Account.
   * @param {accountUpdateArgs} args - Arguments to update one Account.
   * @example
   * // Update one Account
   * const account = await prisma.account.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends accountUpdateArgs>(args: Prisma.SelectSubset<T, accountUpdateArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Accounts.
   * @param {accountDeleteManyArgs} args - Arguments to filter Accounts to delete.
   * @example
   * // Delete a few Accounts
   * const { count } = await prisma.account.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends accountDeleteManyArgs>(args?: Prisma.SelectSubset<T, accountDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Accounts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Accounts
   * const account = await prisma.account.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends accountUpdateManyArgs>(args: Prisma.SelectSubset<T, accountUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Account.
   * @param {accountUpsertArgs} args - Arguments to update or create a Account.
   * @example
   * // Update or create a Account
   * const account = await prisma.account.upsert({
   *   create: {
   *     // ... data to create a Account
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Account we want to update
   *   }
   * })
   */
  upsert<T extends accountUpsertArgs>(args: Prisma.SelectSubset<T, accountUpsertArgs<ExtArgs>>): Prisma.Prisma__accountClient<runtime.Types.Result.GetResult<Prisma.$accountPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Accounts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountCountArgs} args - Arguments to filter Accounts to count.
   * @example
   * // Count the number of Accounts
   * const count = await prisma.account.count({
   *   where: {
   *     // ... the filter for the Accounts we want to count
   *   }
   * })
  **/
  count<T extends accountCountArgs>(
    args?: Prisma.Subset<T, accountCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AccountCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Account.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AccountAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AccountAggregateArgs>(args: Prisma.Subset<T, AccountAggregateArgs>): Prisma.PrismaPromise<GetAccountAggregateType<T>>

  /**
   * Group by Account.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {accountGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends accountGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: accountGroupByArgs['orderBy'] }
      : { orderBy?: accountGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, accountGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAccountGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the account model
 */
readonly fields: accountFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for account.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__accountClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the account model
 */
export interface accountFieldRefs {
  readonly id: Prisma.FieldRef<"account", 'Int'>
  readonly accountId: Prisma.FieldRef<"account", 'String'>
  readonly providerId: Prisma.FieldRef<"account", 'String'>
  readonly userId: Prisma.FieldRef<"account", 'Int'>
  readonly accessToken: Prisma.FieldRef<"account", 'String'>
  readonly refreshToken: Prisma.FieldRef<"account", 'String'>
  readonly idToken: Prisma.FieldRef<"account", 'String'>
  readonly accessTokenExpiresAt: Prisma.FieldRef<"account", 'DateTime'>
  readonly refreshTokenExpiresAt: Prisma.FieldRef<"account", 'DateTime'>
  readonly scope: Prisma.FieldRef<"account", 'String'>
  readonly password: Prisma.FieldRef<"account", 'String'>
  readonly createdAt: Prisma.FieldRef<"account", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"account", 'DateTime'>
}
    

// Custom InputTypes
/**
 * account findUnique
 */
export type accountFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter, which account to fetch.
   */
  where: Prisma.accountWhereUniqueInput
}

/**
 * account findUniqueOrThrow
 */
export type accountFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter, which account to fetch.
   */
  where: Prisma.accountWhereUniqueInput
}

/**
 * account findFirst
 */
export type accountFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter, which account to fetch.
   */
  where?: Prisma.accountWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of accounts to fetch.
   */
  orderBy?: Prisma.accountOrderByWithRelationInput | Prisma.accountOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for accounts.
   */
  cursor?: Prisma.accountWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` accounts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` accounts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of accounts.
   */
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * account findFirstOrThrow
 */
export type accountFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter, which account to fetch.
   */
  where?: Prisma.accountWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of accounts to fetch.
   */
  orderBy?: Prisma.accountOrderByWithRelationInput | Prisma.accountOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for accounts.
   */
  cursor?: Prisma.accountWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` accounts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` accounts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of accounts.
   */
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * account findMany
 */
export type accountFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter, which accounts to fetch.
   */
  where?: Prisma.accountWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of accounts to fetch.
   */
  orderBy?: Prisma.accountOrderByWithRelationInput | Prisma.accountOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing accounts.
   */
  cursor?: Prisma.accountWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` accounts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` accounts.
   */
  skip?: number
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * account create
 */
export type accountCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * The data needed to create a account.
   */
  data: Prisma.XOR<Prisma.accountCreateInput, Prisma.accountUncheckedCreateInput>
}

/**
 * account createMany
 */
export type accountCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many accounts.
   */
  data: Prisma.accountCreateManyInput | Prisma.accountCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * account update
 */
export type accountUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * The data needed to update a account.
   */
  data: Prisma.XOR<Prisma.accountUpdateInput, Prisma.accountUncheckedUpdateInput>
  /**
   * Choose, which account to update.
   */
  where: Prisma.accountWhereUniqueInput
}

/**
 * account updateMany
 */
export type accountUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update accounts.
   */
  data: Prisma.XOR<Prisma.accountUpdateManyMutationInput, Prisma.accountUncheckedUpdateManyInput>
  /**
   * Filter which accounts to update
   */
  where?: Prisma.accountWhereInput
  /**
   * Limit how many accounts to update.
   */
  limit?: number
}

/**
 * account upsert
 */
export type accountUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * The filter to search for the account to update in case it exists.
   */
  where: Prisma.accountWhereUniqueInput
  /**
   * In case the account found by the `where` argument doesn't exist, create a new account with this data.
   */
  create: Prisma.XOR<Prisma.accountCreateInput, Prisma.accountUncheckedCreateInput>
  /**
   * In case the account was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.accountUpdateInput, Prisma.accountUncheckedUpdateInput>
}

/**
 * account delete
 */
export type accountDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
  /**
   * Filter which account to delete.
   */
  where: Prisma.accountWhereUniqueInput
}

/**
 * account deleteMany
 */
export type accountDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which accounts to delete
   */
  where?: Prisma.accountWhereInput
  /**
   * Limit how many accounts to delete.
   */
  limit?: number
}

/**
 * account without action
 */
export type accountDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the account
   */
  select?: Prisma.accountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the account
   */
  omit?: Prisma.accountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.accountInclude<ExtArgs> | null
}
