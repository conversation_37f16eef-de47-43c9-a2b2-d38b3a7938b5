
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `status_effect` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model status_effect
 * 
 */
export type status_effectModel = runtime.Types.Result.DefaultSelection<Prisma.$status_effectPayload>

export type AggregateStatus_effect = {
  _count: Status_effectCountAggregateOutputType | null
  _avg: Status_effectAvgAggregateOutputType | null
  _sum: Status_effectSumAggregateOutputType | null
  _min: Status_effectMinAggregateOutputType | null
  _max: Status_effectMaxAggregateOutputType | null
}

export type Status_effectAvgAggregateOutputType = {
  id: number | null
  duration: number | null
  modifier: number | null
  maxStacks: number | null
}

export type Status_effectSumAggregateOutputType = {
  id: number | null
  duration: number | null
  modifier: number | null
  maxStacks: number | null
}

export type Status_effectMinAggregateOutputType = {
  id: number | null
  name: string | null
  source: string | null
  effectType: $Enums.StatusEffectType | null
  category: string | null
  tier: $Enums.StatusEffectTier | null
  duration: number | null
  modifier: number | null
  modifierType: $Enums.StatusEffectModifierType | null
  stackable: boolean | null
  maxStacks: number | null
  disabled: boolean | null
  description: string | null
}

export type Status_effectMaxAggregateOutputType = {
  id: number | null
  name: string | null
  source: string | null
  effectType: $Enums.StatusEffectType | null
  category: string | null
  tier: $Enums.StatusEffectTier | null
  duration: number | null
  modifier: number | null
  modifierType: $Enums.StatusEffectModifierType | null
  stackable: boolean | null
  maxStacks: number | null
  disabled: boolean | null
  description: string | null
}

export type Status_effectCountAggregateOutputType = {
  id: number
  name: number
  source: number
  effectType: number
  category: number
  tier: number
  duration: number
  modifier: number
  modifierType: number
  stackable: number
  maxStacks: number
  disabled: number
  description: number
  _all: number
}


export type Status_effectAvgAggregateInputType = {
  id?: true
  duration?: true
  modifier?: true
  maxStacks?: true
}

export type Status_effectSumAggregateInputType = {
  id?: true
  duration?: true
  modifier?: true
  maxStacks?: true
}

export type Status_effectMinAggregateInputType = {
  id?: true
  name?: true
  source?: true
  effectType?: true
  category?: true
  tier?: true
  duration?: true
  modifier?: true
  modifierType?: true
  stackable?: true
  maxStacks?: true
  disabled?: true
  description?: true
}

export type Status_effectMaxAggregateInputType = {
  id?: true
  name?: true
  source?: true
  effectType?: true
  category?: true
  tier?: true
  duration?: true
  modifier?: true
  modifierType?: true
  stackable?: true
  maxStacks?: true
  disabled?: true
  description?: true
}

export type Status_effectCountAggregateInputType = {
  id?: true
  name?: true
  source?: true
  effectType?: true
  category?: true
  tier?: true
  duration?: true
  modifier?: true
  modifierType?: true
  stackable?: true
  maxStacks?: true
  disabled?: true
  description?: true
  _all?: true
}

export type Status_effectAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which status_effect to aggregate.
   */
  where?: Prisma.status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of status_effects to fetch.
   */
  orderBy?: Prisma.status_effectOrderByWithRelationInput | Prisma.status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned status_effects
  **/
  _count?: true | Status_effectCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Status_effectAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Status_effectSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Status_effectMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Status_effectMaxAggregateInputType
}

export type GetStatus_effectAggregateType<T extends Status_effectAggregateArgs> = {
      [P in keyof T & keyof AggregateStatus_effect]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStatus_effect[P]>
    : Prisma.GetScalarType<T[P], AggregateStatus_effect[P]>
}




export type status_effectGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.status_effectWhereInput
  orderBy?: Prisma.status_effectOrderByWithAggregationInput | Prisma.status_effectOrderByWithAggregationInput[]
  by: Prisma.Status_effectScalarFieldEnum[] | Prisma.Status_effectScalarFieldEnum
  having?: Prisma.status_effectScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Status_effectCountAggregateInputType | true
  _avg?: Status_effectAvgAggregateInputType
  _sum?: Status_effectSumAggregateInputType
  _min?: Status_effectMinAggregateInputType
  _max?: Status_effectMaxAggregateInputType
}

export type Status_effectGroupByOutputType = {
  id: number
  name: string
  source: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier: $Enums.StatusEffectTier | null
  duration: number
  modifier: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable: boolean
  maxStacks: number | null
  disabled: boolean
  description: string | null
  _count: Status_effectCountAggregateOutputType | null
  _avg: Status_effectAvgAggregateOutputType | null
  _sum: Status_effectSumAggregateOutputType | null
  _min: Status_effectMinAggregateOutputType | null
  _max: Status_effectMaxAggregateOutputType | null
}

type GetStatus_effectGroupByPayload<T extends status_effectGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Status_effectGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Status_effectGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Status_effectGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Status_effectGroupByOutputType[P]>
      }
    >
  >



export type status_effectWhereInput = {
  AND?: Prisma.status_effectWhereInput | Prisma.status_effectWhereInput[]
  OR?: Prisma.status_effectWhereInput[]
  NOT?: Prisma.status_effectWhereInput | Prisma.status_effectWhereInput[]
  id?: Prisma.IntFilter<"status_effect"> | number
  name?: Prisma.StringFilter<"status_effect"> | string
  source?: Prisma.StringNullableFilter<"status_effect"> | string | null
  effectType?: Prisma.EnumStatusEffectTypeFilter<"status_effect"> | $Enums.StatusEffectType
  category?: Prisma.StringFilter<"status_effect"> | string
  tier?: Prisma.EnumStatusEffectTierNullableFilter<"status_effect"> | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFilter<"status_effect"> | number
  modifier?: Prisma.FloatNullableFilter<"status_effect"> | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFilter<"status_effect"> | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFilter<"status_effect"> | boolean
  maxStacks?: Prisma.IntNullableFilter<"status_effect"> | number | null
  disabled?: Prisma.BoolFilter<"status_effect"> | boolean
  description?: Prisma.StringNullableFilter<"status_effect"> | string | null
  user_status_effect?: Prisma.User_status_effectListRelationFilter
}

export type status_effectOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  source?: Prisma.SortOrderInput | Prisma.SortOrder
  effectType?: Prisma.SortOrder
  category?: Prisma.SortOrder
  tier?: Prisma.SortOrderInput | Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  modifierType?: Prisma.SortOrder
  stackable?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrderInput | Prisma.SortOrder
  disabled?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  user_status_effect?: Prisma.user_status_effectOrderByRelationAggregateInput
  _relevance?: Prisma.status_effectOrderByRelevanceInput
}

export type status_effectWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.status_effectWhereInput | Prisma.status_effectWhereInput[]
  OR?: Prisma.status_effectWhereInput[]
  NOT?: Prisma.status_effectWhereInput | Prisma.status_effectWhereInput[]
  name?: Prisma.StringFilter<"status_effect"> | string
  source?: Prisma.StringNullableFilter<"status_effect"> | string | null
  effectType?: Prisma.EnumStatusEffectTypeFilter<"status_effect"> | $Enums.StatusEffectType
  category?: Prisma.StringFilter<"status_effect"> | string
  tier?: Prisma.EnumStatusEffectTierNullableFilter<"status_effect"> | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFilter<"status_effect"> | number
  modifier?: Prisma.FloatNullableFilter<"status_effect"> | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFilter<"status_effect"> | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFilter<"status_effect"> | boolean
  maxStacks?: Prisma.IntNullableFilter<"status_effect"> | number | null
  disabled?: Prisma.BoolFilter<"status_effect"> | boolean
  description?: Prisma.StringNullableFilter<"status_effect"> | string | null
  user_status_effect?: Prisma.User_status_effectListRelationFilter
}, "id">

export type status_effectOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  source?: Prisma.SortOrderInput | Prisma.SortOrder
  effectType?: Prisma.SortOrder
  category?: Prisma.SortOrder
  tier?: Prisma.SortOrderInput | Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrderInput | Prisma.SortOrder
  modifierType?: Prisma.SortOrder
  stackable?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrderInput | Prisma.SortOrder
  disabled?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.status_effectCountOrderByAggregateInput
  _avg?: Prisma.status_effectAvgOrderByAggregateInput
  _max?: Prisma.status_effectMaxOrderByAggregateInput
  _min?: Prisma.status_effectMinOrderByAggregateInput
  _sum?: Prisma.status_effectSumOrderByAggregateInput
}

export type status_effectScalarWhereWithAggregatesInput = {
  AND?: Prisma.status_effectScalarWhereWithAggregatesInput | Prisma.status_effectScalarWhereWithAggregatesInput[]
  OR?: Prisma.status_effectScalarWhereWithAggregatesInput[]
  NOT?: Prisma.status_effectScalarWhereWithAggregatesInput | Prisma.status_effectScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"status_effect"> | number
  name?: Prisma.StringWithAggregatesFilter<"status_effect"> | string
  source?: Prisma.StringNullableWithAggregatesFilter<"status_effect"> | string | null
  effectType?: Prisma.EnumStatusEffectTypeWithAggregatesFilter<"status_effect"> | $Enums.StatusEffectType
  category?: Prisma.StringWithAggregatesFilter<"status_effect"> | string
  tier?: Prisma.EnumStatusEffectTierNullableWithAggregatesFilter<"status_effect"> | $Enums.StatusEffectTier | null
  duration?: Prisma.IntWithAggregatesFilter<"status_effect"> | number
  modifier?: Prisma.FloatNullableWithAggregatesFilter<"status_effect"> | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeWithAggregatesFilter<"status_effect"> | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolWithAggregatesFilter<"status_effect"> | boolean
  maxStacks?: Prisma.IntNullableWithAggregatesFilter<"status_effect"> | number | null
  disabled?: Prisma.BoolWithAggregatesFilter<"status_effect"> | boolean
  description?: Prisma.StringNullableWithAggregatesFilter<"status_effect"> | string | null
}

export type status_effectCreateInput = {
  name: string
  source?: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier?: $Enums.StatusEffectTier | null
  duration: number
  modifier?: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable?: boolean
  maxStacks?: number | null
  disabled?: boolean
  description?: string | null
  user_status_effect?: Prisma.user_status_effectCreateNestedManyWithoutEffectInput
}

export type status_effectUncheckedCreateInput = {
  id?: number
  name: string
  source?: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier?: $Enums.StatusEffectTier | null
  duration: number
  modifier?: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable?: boolean
  maxStacks?: number | null
  disabled?: boolean
  description?: string | null
  user_status_effect?: Prisma.user_status_effectUncheckedCreateNestedManyWithoutEffectInput
}

export type status_effectUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user_status_effect?: Prisma.user_status_effectUpdateManyWithoutEffectNestedInput
}

export type status_effectUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user_status_effect?: Prisma.user_status_effectUncheckedUpdateManyWithoutEffectNestedInput
}

export type status_effectCreateManyInput = {
  id?: number
  name: string
  source?: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier?: $Enums.StatusEffectTier | null
  duration: number
  modifier?: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable?: boolean
  maxStacks?: number | null
  disabled?: boolean
  description?: string | null
}

export type status_effectUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type status_effectUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type status_effectOrderByRelevanceInput = {
  fields: Prisma.status_effectOrderByRelevanceFieldEnum | Prisma.status_effectOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type status_effectCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  source?: Prisma.SortOrder
  effectType?: Prisma.SortOrder
  category?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrder
  modifierType?: Prisma.SortOrder
  stackable?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  description?: Prisma.SortOrder
}

export type status_effectAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrder
}

export type status_effectMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  source?: Prisma.SortOrder
  effectType?: Prisma.SortOrder
  category?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrder
  modifierType?: Prisma.SortOrder
  stackable?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  description?: Prisma.SortOrder
}

export type status_effectMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  source?: Prisma.SortOrder
  effectType?: Prisma.SortOrder
  category?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrder
  modifierType?: Prisma.SortOrder
  stackable?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
  description?: Prisma.SortOrder
}

export type status_effectSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  modifier?: Prisma.SortOrder
  maxStacks?: Prisma.SortOrder
}

export type Status_effectNullableScalarRelationFilter = {
  is?: Prisma.status_effectWhereInput | null
  isNot?: Prisma.status_effectWhereInput | null
}

export type EnumStatusEffectTypeFieldUpdateOperationsInput = {
  set?: $Enums.StatusEffectType
}

export type NullableEnumStatusEffectTierFieldUpdateOperationsInput = {
  set?: $Enums.StatusEffectTier | null
}

export type EnumStatusEffectModifierTypeFieldUpdateOperationsInput = {
  set?: $Enums.StatusEffectModifierType
}

export type status_effectCreateNestedOneWithoutUser_status_effectInput = {
  create?: Prisma.XOR<Prisma.status_effectCreateWithoutUser_status_effectInput, Prisma.status_effectUncheckedCreateWithoutUser_status_effectInput>
  connectOrCreate?: Prisma.status_effectCreateOrConnectWithoutUser_status_effectInput
  connect?: Prisma.status_effectWhereUniqueInput
}

export type status_effectUpdateOneWithoutUser_status_effectNestedInput = {
  create?: Prisma.XOR<Prisma.status_effectCreateWithoutUser_status_effectInput, Prisma.status_effectUncheckedCreateWithoutUser_status_effectInput>
  connectOrCreate?: Prisma.status_effectCreateOrConnectWithoutUser_status_effectInput
  upsert?: Prisma.status_effectUpsertWithoutUser_status_effectInput
  disconnect?: Prisma.status_effectWhereInput | boolean
  delete?: Prisma.status_effectWhereInput | boolean
  connect?: Prisma.status_effectWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.status_effectUpdateToOneWithWhereWithoutUser_status_effectInput, Prisma.status_effectUpdateWithoutUser_status_effectInput>, Prisma.status_effectUncheckedUpdateWithoutUser_status_effectInput>
}

export type status_effectCreateWithoutUser_status_effectInput = {
  name: string
  source?: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier?: $Enums.StatusEffectTier | null
  duration: number
  modifier?: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable?: boolean
  maxStacks?: number | null
  disabled?: boolean
  description?: string | null
}

export type status_effectUncheckedCreateWithoutUser_status_effectInput = {
  id?: number
  name: string
  source?: string | null
  effectType: $Enums.StatusEffectType
  category: string
  tier?: $Enums.StatusEffectTier | null
  duration: number
  modifier?: number | null
  modifierType: $Enums.StatusEffectModifierType
  stackable?: boolean
  maxStacks?: number | null
  disabled?: boolean
  description?: string | null
}

export type status_effectCreateOrConnectWithoutUser_status_effectInput = {
  where: Prisma.status_effectWhereUniqueInput
  create: Prisma.XOR<Prisma.status_effectCreateWithoutUser_status_effectInput, Prisma.status_effectUncheckedCreateWithoutUser_status_effectInput>
}

export type status_effectUpsertWithoutUser_status_effectInput = {
  update: Prisma.XOR<Prisma.status_effectUpdateWithoutUser_status_effectInput, Prisma.status_effectUncheckedUpdateWithoutUser_status_effectInput>
  create: Prisma.XOR<Prisma.status_effectCreateWithoutUser_status_effectInput, Prisma.status_effectUncheckedCreateWithoutUser_status_effectInput>
  where?: Prisma.status_effectWhereInput
}

export type status_effectUpdateToOneWithWhereWithoutUser_status_effectInput = {
  where?: Prisma.status_effectWhereInput
  data: Prisma.XOR<Prisma.status_effectUpdateWithoutUser_status_effectInput, Prisma.status_effectUncheckedUpdateWithoutUser_status_effectInput>
}

export type status_effectUpdateWithoutUser_status_effectInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type status_effectUncheckedUpdateWithoutUser_status_effectInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  source?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  effectType?: Prisma.EnumStatusEffectTypeFieldUpdateOperationsInput | $Enums.StatusEffectType
  category?: Prisma.StringFieldUpdateOperationsInput | string
  tier?: Prisma.NullableEnumStatusEffectTierFieldUpdateOperationsInput | $Enums.StatusEffectTier | null
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  modifier?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  modifierType?: Prisma.EnumStatusEffectModifierTypeFieldUpdateOperationsInput | $Enums.StatusEffectModifierType
  stackable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  maxStacks?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  disabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}


/**
 * Count Type Status_effectCountOutputType
 */

export type Status_effectCountOutputType = {
  user_status_effect: number
}

export type Status_effectCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_status_effect?: boolean | Status_effectCountOutputTypeCountUser_status_effectArgs
}

/**
 * Status_effectCountOutputType without action
 */
export type Status_effectCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Status_effectCountOutputType
   */
  select?: Prisma.Status_effectCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Status_effectCountOutputType without action
 */
export type Status_effectCountOutputTypeCountUser_status_effectArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_status_effectWhereInput
}


export type status_effectSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  source?: boolean
  effectType?: boolean
  category?: boolean
  tier?: boolean
  duration?: boolean
  modifier?: boolean
  modifierType?: boolean
  stackable?: boolean
  maxStacks?: boolean
  disabled?: boolean
  description?: boolean
  user_status_effect?: boolean | Prisma.status_effect$user_status_effectArgs<ExtArgs>
  _count?: boolean | Prisma.Status_effectCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["status_effect"]>



export type status_effectSelectScalar = {
  id?: boolean
  name?: boolean
  source?: boolean
  effectType?: boolean
  category?: boolean
  tier?: boolean
  duration?: boolean
  modifier?: boolean
  modifierType?: boolean
  stackable?: boolean
  maxStacks?: boolean
  disabled?: boolean
  description?: boolean
}

export type status_effectOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "source" | "effectType" | "category" | "tier" | "duration" | "modifier" | "modifierType" | "stackable" | "maxStacks" | "disabled" | "description", ExtArgs["result"]["status_effect"]>
export type status_effectInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_status_effect?: boolean | Prisma.status_effect$user_status_effectArgs<ExtArgs>
  _count?: boolean | Prisma.Status_effectCountOutputTypeDefaultArgs<ExtArgs>
}

export type $status_effectPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "status_effect"
  objects: {
    user_status_effect: Prisma.$user_status_effectPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    source: string | null
    effectType: $Enums.StatusEffectType
    category: string
    tier: $Enums.StatusEffectTier | null
    duration: number
    modifier: number | null
    modifierType: $Enums.StatusEffectModifierType
    stackable: boolean
    maxStacks: number | null
    disabled: boolean
    description: string | null
  }, ExtArgs["result"]["status_effect"]>
  composites: {}
}

export type status_effectGetPayload<S extends boolean | null | undefined | status_effectDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$status_effectPayload, S>

export type status_effectCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<status_effectFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Status_effectCountAggregateInputType | true
  }

export interface status_effectDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['status_effect'], meta: { name: 'status_effect' } }
  /**
   * Find zero or one Status_effect that matches the filter.
   * @param {status_effectFindUniqueArgs} args - Arguments to find a Status_effect
   * @example
   * // Get one Status_effect
   * const status_effect = await prisma.status_effect.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends status_effectFindUniqueArgs>(args: Prisma.SelectSubset<T, status_effectFindUniqueArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Status_effect that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {status_effectFindUniqueOrThrowArgs} args - Arguments to find a Status_effect
   * @example
   * // Get one Status_effect
   * const status_effect = await prisma.status_effect.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends status_effectFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, status_effectFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Status_effect that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectFindFirstArgs} args - Arguments to find a Status_effect
   * @example
   * // Get one Status_effect
   * const status_effect = await prisma.status_effect.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends status_effectFindFirstArgs>(args?: Prisma.SelectSubset<T, status_effectFindFirstArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Status_effect that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectFindFirstOrThrowArgs} args - Arguments to find a Status_effect
   * @example
   * // Get one Status_effect
   * const status_effect = await prisma.status_effect.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends status_effectFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, status_effectFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Status_effects that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Status_effects
   * const status_effects = await prisma.status_effect.findMany()
   * 
   * // Get first 10 Status_effects
   * const status_effects = await prisma.status_effect.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const status_effectWithIdOnly = await prisma.status_effect.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends status_effectFindManyArgs>(args?: Prisma.SelectSubset<T, status_effectFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Status_effect.
   * @param {status_effectCreateArgs} args - Arguments to create a Status_effect.
   * @example
   * // Create one Status_effect
   * const Status_effect = await prisma.status_effect.create({
   *   data: {
   *     // ... data to create a Status_effect
   *   }
   * })
   * 
   */
  create<T extends status_effectCreateArgs>(args: Prisma.SelectSubset<T, status_effectCreateArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Status_effects.
   * @param {status_effectCreateManyArgs} args - Arguments to create many Status_effects.
   * @example
   * // Create many Status_effects
   * const status_effect = await prisma.status_effect.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends status_effectCreateManyArgs>(args?: Prisma.SelectSubset<T, status_effectCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Status_effect.
   * @param {status_effectDeleteArgs} args - Arguments to delete one Status_effect.
   * @example
   * // Delete one Status_effect
   * const Status_effect = await prisma.status_effect.delete({
   *   where: {
   *     // ... filter to delete one Status_effect
   *   }
   * })
   * 
   */
  delete<T extends status_effectDeleteArgs>(args: Prisma.SelectSubset<T, status_effectDeleteArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Status_effect.
   * @param {status_effectUpdateArgs} args - Arguments to update one Status_effect.
   * @example
   * // Update one Status_effect
   * const status_effect = await prisma.status_effect.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends status_effectUpdateArgs>(args: Prisma.SelectSubset<T, status_effectUpdateArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Status_effects.
   * @param {status_effectDeleteManyArgs} args - Arguments to filter Status_effects to delete.
   * @example
   * // Delete a few Status_effects
   * const { count } = await prisma.status_effect.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends status_effectDeleteManyArgs>(args?: Prisma.SelectSubset<T, status_effectDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Status_effects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Status_effects
   * const status_effect = await prisma.status_effect.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends status_effectUpdateManyArgs>(args: Prisma.SelectSubset<T, status_effectUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Status_effect.
   * @param {status_effectUpsertArgs} args - Arguments to update or create a Status_effect.
   * @example
   * // Update or create a Status_effect
   * const status_effect = await prisma.status_effect.upsert({
   *   create: {
   *     // ... data to create a Status_effect
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Status_effect we want to update
   *   }
   * })
   */
  upsert<T extends status_effectUpsertArgs>(args: Prisma.SelectSubset<T, status_effectUpsertArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Status_effects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectCountArgs} args - Arguments to filter Status_effects to count.
   * @example
   * // Count the number of Status_effects
   * const count = await prisma.status_effect.count({
   *   where: {
   *     // ... the filter for the Status_effects we want to count
   *   }
   * })
  **/
  count<T extends status_effectCountArgs>(
    args?: Prisma.Subset<T, status_effectCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Status_effectCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Status_effect.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Status_effectAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Status_effectAggregateArgs>(args: Prisma.Subset<T, Status_effectAggregateArgs>): Prisma.PrismaPromise<GetStatus_effectAggregateType<T>>

  /**
   * Group by Status_effect.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {status_effectGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends status_effectGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: status_effectGroupByArgs['orderBy'] }
      : { orderBy?: status_effectGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, status_effectGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStatus_effectGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the status_effect model
 */
readonly fields: status_effectFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for status_effect.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__status_effectClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_status_effect<T extends Prisma.status_effect$user_status_effectArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.status_effect$user_status_effectArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the status_effect model
 */
export interface status_effectFieldRefs {
  readonly id: Prisma.FieldRef<"status_effect", 'Int'>
  readonly name: Prisma.FieldRef<"status_effect", 'String'>
  readonly source: Prisma.FieldRef<"status_effect", 'String'>
  readonly effectType: Prisma.FieldRef<"status_effect", 'StatusEffectType'>
  readonly category: Prisma.FieldRef<"status_effect", 'String'>
  readonly tier: Prisma.FieldRef<"status_effect", 'StatusEffectTier'>
  readonly duration: Prisma.FieldRef<"status_effect", 'Int'>
  readonly modifier: Prisma.FieldRef<"status_effect", 'Float'>
  readonly modifierType: Prisma.FieldRef<"status_effect", 'StatusEffectModifierType'>
  readonly stackable: Prisma.FieldRef<"status_effect", 'Boolean'>
  readonly maxStacks: Prisma.FieldRef<"status_effect", 'Int'>
  readonly disabled: Prisma.FieldRef<"status_effect", 'Boolean'>
  readonly description: Prisma.FieldRef<"status_effect", 'String'>
}
    

// Custom InputTypes
/**
 * status_effect findUnique
 */
export type status_effectFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter, which status_effect to fetch.
   */
  where: Prisma.status_effectWhereUniqueInput
}

/**
 * status_effect findUniqueOrThrow
 */
export type status_effectFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter, which status_effect to fetch.
   */
  where: Prisma.status_effectWhereUniqueInput
}

/**
 * status_effect findFirst
 */
export type status_effectFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter, which status_effect to fetch.
   */
  where?: Prisma.status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of status_effects to fetch.
   */
  orderBy?: Prisma.status_effectOrderByWithRelationInput | Prisma.status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for status_effects.
   */
  cursor?: Prisma.status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of status_effects.
   */
  distinct?: Prisma.Status_effectScalarFieldEnum | Prisma.Status_effectScalarFieldEnum[]
}

/**
 * status_effect findFirstOrThrow
 */
export type status_effectFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter, which status_effect to fetch.
   */
  where?: Prisma.status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of status_effects to fetch.
   */
  orderBy?: Prisma.status_effectOrderByWithRelationInput | Prisma.status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for status_effects.
   */
  cursor?: Prisma.status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of status_effects.
   */
  distinct?: Prisma.Status_effectScalarFieldEnum | Prisma.Status_effectScalarFieldEnum[]
}

/**
 * status_effect findMany
 */
export type status_effectFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter, which status_effects to fetch.
   */
  where?: Prisma.status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of status_effects to fetch.
   */
  orderBy?: Prisma.status_effectOrderByWithRelationInput | Prisma.status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing status_effects.
   */
  cursor?: Prisma.status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` status_effects.
   */
  skip?: number
  distinct?: Prisma.Status_effectScalarFieldEnum | Prisma.Status_effectScalarFieldEnum[]
}

/**
 * status_effect create
 */
export type status_effectCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * The data needed to create a status_effect.
   */
  data: Prisma.XOR<Prisma.status_effectCreateInput, Prisma.status_effectUncheckedCreateInput>
}

/**
 * status_effect createMany
 */
export type status_effectCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many status_effects.
   */
  data: Prisma.status_effectCreateManyInput | Prisma.status_effectCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * status_effect update
 */
export type status_effectUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * The data needed to update a status_effect.
   */
  data: Prisma.XOR<Prisma.status_effectUpdateInput, Prisma.status_effectUncheckedUpdateInput>
  /**
   * Choose, which status_effect to update.
   */
  where: Prisma.status_effectWhereUniqueInput
}

/**
 * status_effect updateMany
 */
export type status_effectUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update status_effects.
   */
  data: Prisma.XOR<Prisma.status_effectUpdateManyMutationInput, Prisma.status_effectUncheckedUpdateManyInput>
  /**
   * Filter which status_effects to update
   */
  where?: Prisma.status_effectWhereInput
  /**
   * Limit how many status_effects to update.
   */
  limit?: number
}

/**
 * status_effect upsert
 */
export type status_effectUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * The filter to search for the status_effect to update in case it exists.
   */
  where: Prisma.status_effectWhereUniqueInput
  /**
   * In case the status_effect found by the `where` argument doesn't exist, create a new status_effect with this data.
   */
  create: Prisma.XOR<Prisma.status_effectCreateInput, Prisma.status_effectUncheckedCreateInput>
  /**
   * In case the status_effect was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.status_effectUpdateInput, Prisma.status_effectUncheckedUpdateInput>
}

/**
 * status_effect delete
 */
export type status_effectDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  /**
   * Filter which status_effect to delete.
   */
  where: Prisma.status_effectWhereUniqueInput
}

/**
 * status_effect deleteMany
 */
export type status_effectDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which status_effects to delete
   */
  where?: Prisma.status_effectWhereInput
  /**
   * Limit how many status_effects to delete.
   */
  limit?: number
}

/**
 * status_effect.user_status_effect
 */
export type status_effect$user_status_effectArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  where?: Prisma.user_status_effectWhereInput
  orderBy?: Prisma.user_status_effectOrderByWithRelationInput | Prisma.user_status_effectOrderByWithRelationInput[]
  cursor?: Prisma.user_status_effectWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_status_effectScalarFieldEnum | Prisma.User_status_effectScalarFieldEnum[]
}

/**
 * status_effect without action
 */
export type status_effectDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
}
