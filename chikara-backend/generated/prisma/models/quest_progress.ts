
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `quest_progress` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model quest_progress
 * 
 */
export type quest_progressModel = runtime.Types.Result.DefaultSelection<Prisma.$quest_progressPayload>

export type AggregateQuest_progress = {
  _count: Quest_progressCountAggregateOutputType | null
  _avg: Quest_progressAvgAggregateOutputType | null
  _sum: Quest_progressSumAggregateOutputType | null
  _min: Quest_progressMinAggregateOutputType | null
  _max: Quest_progressMaxAggregateOutputType | null
}

export type Quest_progressAvgAggregateOutputType = {
  id: number | null
  questId: number | null
  userId: number | null
}

export type Quest_progressSumAggregateOutputType = {
  id: number | null
  questId: number | null
  userId: number | null
}

export type Quest_progressMinAggregateOutputType = {
  id: number | null
  questStatus: $Enums.QuestProgressStatus | null
  createdAt: Date | null
  updatedAt: Date | null
  questId: number | null
  userId: number | null
}

export type Quest_progressMaxAggregateOutputType = {
  id: number | null
  questStatus: $Enums.QuestProgressStatus | null
  createdAt: Date | null
  updatedAt: Date | null
  questId: number | null
  userId: number | null
}

export type Quest_progressCountAggregateOutputType = {
  id: number
  questStatus: number
  createdAt: number
  updatedAt: number
  questId: number
  userId: number
  _all: number
}


export type Quest_progressAvgAggregateInputType = {
  id?: true
  questId?: true
  userId?: true
}

export type Quest_progressSumAggregateInputType = {
  id?: true
  questId?: true
  userId?: true
}

export type Quest_progressMinAggregateInputType = {
  id?: true
  questStatus?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  userId?: true
}

export type Quest_progressMaxAggregateInputType = {
  id?: true
  questStatus?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  userId?: true
}

export type Quest_progressCountAggregateInputType = {
  id?: true
  questStatus?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  userId?: true
  _all?: true
}

export type Quest_progressAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_progress to aggregate.
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_progresses to fetch.
   */
  orderBy?: Prisma.quest_progressOrderByWithRelationInput | Prisma.quest_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.quest_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned quest_progresses
  **/
  _count?: true | Quest_progressCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Quest_progressAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Quest_progressSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Quest_progressMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Quest_progressMaxAggregateInputType
}

export type GetQuest_progressAggregateType<T extends Quest_progressAggregateArgs> = {
      [P in keyof T & keyof AggregateQuest_progress]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateQuest_progress[P]>
    : Prisma.GetScalarType<T[P], AggregateQuest_progress[P]>
}




export type quest_progressGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_progressWhereInput
  orderBy?: Prisma.quest_progressOrderByWithAggregationInput | Prisma.quest_progressOrderByWithAggregationInput[]
  by: Prisma.Quest_progressScalarFieldEnum[] | Prisma.Quest_progressScalarFieldEnum
  having?: Prisma.quest_progressScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Quest_progressCountAggregateInputType | true
  _avg?: Quest_progressAvgAggregateInputType
  _sum?: Quest_progressSumAggregateInputType
  _min?: Quest_progressMinAggregateInputType
  _max?: Quest_progressMaxAggregateInputType
}

export type Quest_progressGroupByOutputType = {
  id: number
  questStatus: $Enums.QuestProgressStatus
  createdAt: Date
  updatedAt: Date
  questId: number | null
  userId: number | null
  _count: Quest_progressCountAggregateOutputType | null
  _avg: Quest_progressAvgAggregateOutputType | null
  _sum: Quest_progressSumAggregateOutputType | null
  _min: Quest_progressMinAggregateOutputType | null
  _max: Quest_progressMaxAggregateOutputType | null
}

type GetQuest_progressGroupByPayload<T extends quest_progressGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Quest_progressGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Quest_progressGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Quest_progressGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Quest_progressGroupByOutputType[P]>
      }
    >
  >



export type quest_progressWhereInput = {
  AND?: Prisma.quest_progressWhereInput | Prisma.quest_progressWhereInput[]
  OR?: Prisma.quest_progressWhereInput[]
  NOT?: Prisma.quest_progressWhereInput | Prisma.quest_progressWhereInput[]
  id?: Prisma.IntFilter<"quest_progress"> | number
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"quest_progress"> | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
  userId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type quest_progressOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  quest?: Prisma.questOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type quest_progressWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.quest_progressWhereInput | Prisma.quest_progressWhereInput[]
  OR?: Prisma.quest_progressWhereInput[]
  NOT?: Prisma.quest_progressWhereInput | Prisma.quest_progressWhereInput[]
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"quest_progress"> | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
  userId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type quest_progressOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.quest_progressCountOrderByAggregateInput
  _avg?: Prisma.quest_progressAvgOrderByAggregateInput
  _max?: Prisma.quest_progressMaxOrderByAggregateInput
  _min?: Prisma.quest_progressMinOrderByAggregateInput
  _sum?: Prisma.quest_progressSumOrderByAggregateInput
}

export type quest_progressScalarWhereWithAggregatesInput = {
  AND?: Prisma.quest_progressScalarWhereWithAggregatesInput | Prisma.quest_progressScalarWhereWithAggregatesInput[]
  OR?: Prisma.quest_progressScalarWhereWithAggregatesInput[]
  NOT?: Prisma.quest_progressScalarWhereWithAggregatesInput | Prisma.quest_progressScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"quest_progress"> | number
  questStatus?: Prisma.EnumQuestProgressStatusWithAggregatesFilter<"quest_progress"> | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"quest_progress"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"quest_progress"> | Date | string
  questId?: Prisma.IntNullableWithAggregatesFilter<"quest_progress"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"quest_progress"> | number | null
}

export type quest_progressCreateInput = {
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  quest?: Prisma.questCreateNestedOneWithoutQuest_progressInput
  user?: Prisma.userCreateNestedOneWithoutQuest_progressInput
}

export type quest_progressUncheckedCreateInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  userId?: number | null
}

export type quest_progressUpdateInput = {
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest?: Prisma.questUpdateOneWithoutQuest_progressNestedInput
  user?: Prisma.userUpdateOneWithoutQuest_progressNestedInput
}

export type quest_progressUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_progressCreateManyInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  userId?: number | null
}

export type quest_progressUpdateManyMutationInput = {
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type quest_progressUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Quest_progressListRelationFilter = {
  every?: Prisma.quest_progressWhereInput
  some?: Prisma.quest_progressWhereInput
  none?: Prisma.quest_progressWhereInput
}

export type quest_progressOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type quest_progressCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type quest_progressAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type quest_progressMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type quest_progressMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type quest_progressSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type quest_progressCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput> | Prisma.quest_progressCreateWithoutQuestInput[] | Prisma.quest_progressUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutQuestInput | Prisma.quest_progressCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_progressCreateManyQuestInputEnvelope
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
}

export type quest_progressUncheckedCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput> | Prisma.quest_progressCreateWithoutQuestInput[] | Prisma.quest_progressUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutQuestInput | Prisma.quest_progressCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_progressCreateManyQuestInputEnvelope
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
}

export type quest_progressUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput> | Prisma.quest_progressCreateWithoutQuestInput[] | Prisma.quest_progressUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutQuestInput | Prisma.quest_progressCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_progressUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_progressUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_progressCreateManyQuestInputEnvelope
  set?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  delete?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  update?: Prisma.quest_progressUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_progressUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_progressUpdateManyWithWhereWithoutQuestInput | Prisma.quest_progressUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
}

export type quest_progressUncheckedUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput> | Prisma.quest_progressCreateWithoutQuestInput[] | Prisma.quest_progressUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutQuestInput | Prisma.quest_progressCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_progressUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_progressUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_progressCreateManyQuestInputEnvelope
  set?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  delete?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  update?: Prisma.quest_progressUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_progressUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_progressUpdateManyWithWhereWithoutQuestInput | Prisma.quest_progressUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
}

export type quest_progressCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput> | Prisma.quest_progressCreateWithoutUserInput[] | Prisma.quest_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutUserInput | Prisma.quest_progressCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.quest_progressCreateManyUserInputEnvelope
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
}

export type quest_progressUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput> | Prisma.quest_progressCreateWithoutUserInput[] | Prisma.quest_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutUserInput | Prisma.quest_progressCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.quest_progressCreateManyUserInputEnvelope
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
}

export type quest_progressUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput> | Prisma.quest_progressCreateWithoutUserInput[] | Prisma.quest_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutUserInput | Prisma.quest_progressCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.quest_progressUpsertWithWhereUniqueWithoutUserInput | Prisma.quest_progressUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.quest_progressCreateManyUserInputEnvelope
  set?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  delete?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  update?: Prisma.quest_progressUpdateWithWhereUniqueWithoutUserInput | Prisma.quest_progressUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.quest_progressUpdateManyWithWhereWithoutUserInput | Prisma.quest_progressUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
}

export type quest_progressUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput> | Prisma.quest_progressCreateWithoutUserInput[] | Prisma.quest_progressUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.quest_progressCreateOrConnectWithoutUserInput | Prisma.quest_progressCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.quest_progressUpsertWithWhereUniqueWithoutUserInput | Prisma.quest_progressUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.quest_progressCreateManyUserInputEnvelope
  set?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  disconnect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  delete?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  connect?: Prisma.quest_progressWhereUniqueInput | Prisma.quest_progressWhereUniqueInput[]
  update?: Prisma.quest_progressUpdateWithWhereUniqueWithoutUserInput | Prisma.quest_progressUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.quest_progressUpdateManyWithWhereWithoutUserInput | Prisma.quest_progressUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
}

export type quest_progressCreateWithoutQuestInput = {
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutQuest_progressInput
}

export type quest_progressUncheckedCreateWithoutQuestInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type quest_progressCreateOrConnectWithoutQuestInput = {
  where: Prisma.quest_progressWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput>
}

export type quest_progressCreateManyQuestInputEnvelope = {
  data: Prisma.quest_progressCreateManyQuestInput | Prisma.quest_progressCreateManyQuestInput[]
  skipDuplicates?: boolean
}

export type quest_progressUpsertWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_progressWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_progressUpdateWithoutQuestInput, Prisma.quest_progressUncheckedUpdateWithoutQuestInput>
  create: Prisma.XOR<Prisma.quest_progressCreateWithoutQuestInput, Prisma.quest_progressUncheckedCreateWithoutQuestInput>
}

export type quest_progressUpdateWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_progressWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_progressUpdateWithoutQuestInput, Prisma.quest_progressUncheckedUpdateWithoutQuestInput>
}

export type quest_progressUpdateManyWithWhereWithoutQuestInput = {
  where: Prisma.quest_progressScalarWhereInput
  data: Prisma.XOR<Prisma.quest_progressUpdateManyMutationInput, Prisma.quest_progressUncheckedUpdateManyWithoutQuestInput>
}

export type quest_progressScalarWhereInput = {
  AND?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
  OR?: Prisma.quest_progressScalarWhereInput[]
  NOT?: Prisma.quest_progressScalarWhereInput | Prisma.quest_progressScalarWhereInput[]
  id?: Prisma.IntFilter<"quest_progress"> | number
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"quest_progress"> | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_progress"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
  userId?: Prisma.IntNullableFilter<"quest_progress"> | number | null
}

export type quest_progressCreateWithoutUserInput = {
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  quest?: Prisma.questCreateNestedOneWithoutQuest_progressInput
}

export type quest_progressUncheckedCreateWithoutUserInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
}

export type quest_progressCreateOrConnectWithoutUserInput = {
  where: Prisma.quest_progressWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput>
}

export type quest_progressCreateManyUserInputEnvelope = {
  data: Prisma.quest_progressCreateManyUserInput | Prisma.quest_progressCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type quest_progressUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.quest_progressWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_progressUpdateWithoutUserInput, Prisma.quest_progressUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.quest_progressCreateWithoutUserInput, Prisma.quest_progressUncheckedCreateWithoutUserInput>
}

export type quest_progressUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.quest_progressWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_progressUpdateWithoutUserInput, Prisma.quest_progressUncheckedUpdateWithoutUserInput>
}

export type quest_progressUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.quest_progressScalarWhereInput
  data: Prisma.XOR<Prisma.quest_progressUpdateManyMutationInput, Prisma.quest_progressUncheckedUpdateManyWithoutUserInput>
}

export type quest_progressCreateManyQuestInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type quest_progressUpdateWithoutQuestInput = {
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutQuest_progressNestedInput
}

export type quest_progressUncheckedUpdateWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_progressUncheckedUpdateManyWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_progressCreateManyUserInput = {
  id?: number
  questStatus?: $Enums.QuestProgressStatus
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
}

export type quest_progressUpdateWithoutUserInput = {
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  quest?: Prisma.questUpdateOneWithoutQuest_progressNestedInput
}

export type quest_progressUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_progressUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type quest_progressSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  questStatus?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questId?: boolean
  userId?: boolean
  quest?: boolean | Prisma.quest_progress$questArgs<ExtArgs>
  user?: boolean | Prisma.quest_progress$userArgs<ExtArgs>
}, ExtArgs["result"]["quest_progress"]>



export type quest_progressSelectScalar = {
  id?: boolean
  questStatus?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questId?: boolean
  userId?: boolean
}

export type quest_progressOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "questStatus" | "createdAt" | "updatedAt" | "questId" | "userId", ExtArgs["result"]["quest_progress"]>
export type quest_progressInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest?: boolean | Prisma.quest_progress$questArgs<ExtArgs>
  user?: boolean | Prisma.quest_progress$userArgs<ExtArgs>
}

export type $quest_progressPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "quest_progress"
  objects: {
    quest: Prisma.$questPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    questStatus: $Enums.QuestProgressStatus
    createdAt: Date
    updatedAt: Date
    questId: number | null
    userId: number | null
  }, ExtArgs["result"]["quest_progress"]>
  composites: {}
}

export type quest_progressGetPayload<S extends boolean | null | undefined | quest_progressDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$quest_progressPayload, S>

export type quest_progressCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<quest_progressFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Quest_progressCountAggregateInputType | true
  }

export interface quest_progressDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['quest_progress'], meta: { name: 'quest_progress' } }
  /**
   * Find zero or one Quest_progress that matches the filter.
   * @param {quest_progressFindUniqueArgs} args - Arguments to find a Quest_progress
   * @example
   * // Get one Quest_progress
   * const quest_progress = await prisma.quest_progress.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends quest_progressFindUniqueArgs>(args: Prisma.SelectSubset<T, quest_progressFindUniqueArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Quest_progress that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {quest_progressFindUniqueOrThrowArgs} args - Arguments to find a Quest_progress
   * @example
   * // Get one Quest_progress
   * const quest_progress = await prisma.quest_progress.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends quest_progressFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, quest_progressFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_progress that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressFindFirstArgs} args - Arguments to find a Quest_progress
   * @example
   * // Get one Quest_progress
   * const quest_progress = await prisma.quest_progress.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends quest_progressFindFirstArgs>(args?: Prisma.SelectSubset<T, quest_progressFindFirstArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_progress that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressFindFirstOrThrowArgs} args - Arguments to find a Quest_progress
   * @example
   * // Get one Quest_progress
   * const quest_progress = await prisma.quest_progress.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends quest_progressFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, quest_progressFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Quest_progresses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Quest_progresses
   * const quest_progresses = await prisma.quest_progress.findMany()
   * 
   * // Get first 10 Quest_progresses
   * const quest_progresses = await prisma.quest_progress.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const quest_progressWithIdOnly = await prisma.quest_progress.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends quest_progressFindManyArgs>(args?: Prisma.SelectSubset<T, quest_progressFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Quest_progress.
   * @param {quest_progressCreateArgs} args - Arguments to create a Quest_progress.
   * @example
   * // Create one Quest_progress
   * const Quest_progress = await prisma.quest_progress.create({
   *   data: {
   *     // ... data to create a Quest_progress
   *   }
   * })
   * 
   */
  create<T extends quest_progressCreateArgs>(args: Prisma.SelectSubset<T, quest_progressCreateArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Quest_progresses.
   * @param {quest_progressCreateManyArgs} args - Arguments to create many Quest_progresses.
   * @example
   * // Create many Quest_progresses
   * const quest_progress = await prisma.quest_progress.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends quest_progressCreateManyArgs>(args?: Prisma.SelectSubset<T, quest_progressCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Quest_progress.
   * @param {quest_progressDeleteArgs} args - Arguments to delete one Quest_progress.
   * @example
   * // Delete one Quest_progress
   * const Quest_progress = await prisma.quest_progress.delete({
   *   where: {
   *     // ... filter to delete one Quest_progress
   *   }
   * })
   * 
   */
  delete<T extends quest_progressDeleteArgs>(args: Prisma.SelectSubset<T, quest_progressDeleteArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Quest_progress.
   * @param {quest_progressUpdateArgs} args - Arguments to update one Quest_progress.
   * @example
   * // Update one Quest_progress
   * const quest_progress = await prisma.quest_progress.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends quest_progressUpdateArgs>(args: Prisma.SelectSubset<T, quest_progressUpdateArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Quest_progresses.
   * @param {quest_progressDeleteManyArgs} args - Arguments to filter Quest_progresses to delete.
   * @example
   * // Delete a few Quest_progresses
   * const { count } = await prisma.quest_progress.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends quest_progressDeleteManyArgs>(args?: Prisma.SelectSubset<T, quest_progressDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Quest_progresses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Quest_progresses
   * const quest_progress = await prisma.quest_progress.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends quest_progressUpdateManyArgs>(args: Prisma.SelectSubset<T, quest_progressUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Quest_progress.
   * @param {quest_progressUpsertArgs} args - Arguments to update or create a Quest_progress.
   * @example
   * // Update or create a Quest_progress
   * const quest_progress = await prisma.quest_progress.upsert({
   *   create: {
   *     // ... data to create a Quest_progress
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Quest_progress we want to update
   *   }
   * })
   */
  upsert<T extends quest_progressUpsertArgs>(args: Prisma.SelectSubset<T, quest_progressUpsertArgs<ExtArgs>>): Prisma.Prisma__quest_progressClient<runtime.Types.Result.GetResult<Prisma.$quest_progressPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Quest_progresses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressCountArgs} args - Arguments to filter Quest_progresses to count.
   * @example
   * // Count the number of Quest_progresses
   * const count = await prisma.quest_progress.count({
   *   where: {
   *     // ... the filter for the Quest_progresses we want to count
   *   }
   * })
  **/
  count<T extends quest_progressCountArgs>(
    args?: Prisma.Subset<T, quest_progressCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Quest_progressCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Quest_progress.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Quest_progressAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Quest_progressAggregateArgs>(args: Prisma.Subset<T, Quest_progressAggregateArgs>): Prisma.PrismaPromise<GetQuest_progressAggregateType<T>>

  /**
   * Group by Quest_progress.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_progressGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends quest_progressGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: quest_progressGroupByArgs['orderBy'] }
      : { orderBy?: quest_progressGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, quest_progressGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQuest_progressGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the quest_progress model
 */
readonly fields: quest_progressFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for quest_progress.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__quest_progressClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest<T extends Prisma.quest_progress$questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_progress$questArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.quest_progress$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_progress$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the quest_progress model
 */
export interface quest_progressFieldRefs {
  readonly id: Prisma.FieldRef<"quest_progress", 'Int'>
  readonly questStatus: Prisma.FieldRef<"quest_progress", 'QuestProgressStatus'>
  readonly createdAt: Prisma.FieldRef<"quest_progress", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"quest_progress", 'DateTime'>
  readonly questId: Prisma.FieldRef<"quest_progress", 'Int'>
  readonly userId: Prisma.FieldRef<"quest_progress", 'Int'>
}
    

// Custom InputTypes
/**
 * quest_progress findUnique
 */
export type quest_progressFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_progress to fetch.
   */
  where: Prisma.quest_progressWhereUniqueInput
}

/**
 * quest_progress findUniqueOrThrow
 */
export type quest_progressFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_progress to fetch.
   */
  where: Prisma.quest_progressWhereUniqueInput
}

/**
 * quest_progress findFirst
 */
export type quest_progressFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_progress to fetch.
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_progresses to fetch.
   */
  orderBy?: Prisma.quest_progressOrderByWithRelationInput | Prisma.quest_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_progresses.
   */
  cursor?: Prisma.quest_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_progresses.
   */
  distinct?: Prisma.Quest_progressScalarFieldEnum | Prisma.Quest_progressScalarFieldEnum[]
}

/**
 * quest_progress findFirstOrThrow
 */
export type quest_progressFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_progress to fetch.
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_progresses to fetch.
   */
  orderBy?: Prisma.quest_progressOrderByWithRelationInput | Prisma.quest_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_progresses.
   */
  cursor?: Prisma.quest_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_progresses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_progresses.
   */
  distinct?: Prisma.Quest_progressScalarFieldEnum | Prisma.Quest_progressScalarFieldEnum[]
}

/**
 * quest_progress findMany
 */
export type quest_progressFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter, which quest_progresses to fetch.
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_progresses to fetch.
   */
  orderBy?: Prisma.quest_progressOrderByWithRelationInput | Prisma.quest_progressOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing quest_progresses.
   */
  cursor?: Prisma.quest_progressWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_progresses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_progresses.
   */
  skip?: number
  distinct?: Prisma.Quest_progressScalarFieldEnum | Prisma.Quest_progressScalarFieldEnum[]
}

/**
 * quest_progress create
 */
export type quest_progressCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * The data needed to create a quest_progress.
   */
  data: Prisma.XOR<Prisma.quest_progressCreateInput, Prisma.quest_progressUncheckedCreateInput>
}

/**
 * quest_progress createMany
 */
export type quest_progressCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many quest_progresses.
   */
  data: Prisma.quest_progressCreateManyInput | Prisma.quest_progressCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * quest_progress update
 */
export type quest_progressUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * The data needed to update a quest_progress.
   */
  data: Prisma.XOR<Prisma.quest_progressUpdateInput, Prisma.quest_progressUncheckedUpdateInput>
  /**
   * Choose, which quest_progress to update.
   */
  where: Prisma.quest_progressWhereUniqueInput
}

/**
 * quest_progress updateMany
 */
export type quest_progressUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update quest_progresses.
   */
  data: Prisma.XOR<Prisma.quest_progressUpdateManyMutationInput, Prisma.quest_progressUncheckedUpdateManyInput>
  /**
   * Filter which quest_progresses to update
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * Limit how many quest_progresses to update.
   */
  limit?: number
}

/**
 * quest_progress upsert
 */
export type quest_progressUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * The filter to search for the quest_progress to update in case it exists.
   */
  where: Prisma.quest_progressWhereUniqueInput
  /**
   * In case the quest_progress found by the `where` argument doesn't exist, create a new quest_progress with this data.
   */
  create: Prisma.XOR<Prisma.quest_progressCreateInput, Prisma.quest_progressUncheckedCreateInput>
  /**
   * In case the quest_progress was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.quest_progressUpdateInput, Prisma.quest_progressUncheckedUpdateInput>
}

/**
 * quest_progress delete
 */
export type quest_progressDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
  /**
   * Filter which quest_progress to delete.
   */
  where: Prisma.quest_progressWhereUniqueInput
}

/**
 * quest_progress deleteMany
 */
export type quest_progressDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_progresses to delete
   */
  where?: Prisma.quest_progressWhereInput
  /**
   * Limit how many quest_progresses to delete.
   */
  limit?: number
}

/**
 * quest_progress.quest
 */
export type quest_progress$questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
}

/**
 * quest_progress.user
 */
export type quest_progress$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * quest_progress without action
 */
export type quest_progressDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_progress
   */
  select?: Prisma.quest_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_progress
   */
  omit?: Prisma.quest_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_progressInclude<ExtArgs> | null
}
