
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `story_season` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model story_season
 * 
 */
export type story_seasonModel = runtime.Types.Result.DefaultSelection<Prisma.$story_seasonPayload>

export type AggregateStory_season = {
  _count: Story_seasonCountAggregateOutputType | null
  _avg: Story_seasonAvgAggregateOutputType | null
  _sum: Story_seasonSumAggregateOutputType | null
  _min: Story_seasonMinAggregateOutputType | null
  _max: Story_seasonMaxAggregateOutputType | null
}

export type Story_seasonAvgAggregateOutputType = {
  id: number | null
  requiredLevel: number | null
}

export type Story_seasonSumAggregateOutputType = {
  id: number | null
  requiredLevel: number | null
}

export type Story_seasonMinAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  startDate: Date | null
  requiredLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_seasonMaxAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  startDate: Date | null
  requiredLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Story_seasonCountAggregateOutputType = {
  id: number
  name: number
  description: number
  startDate: number
  requiredLevel: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Story_seasonAvgAggregateInputType = {
  id?: true
  requiredLevel?: true
}

export type Story_seasonSumAggregateInputType = {
  id?: true
  requiredLevel?: true
}

export type Story_seasonMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  startDate?: true
  requiredLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_seasonMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  startDate?: true
  requiredLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type Story_seasonCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  startDate?: true
  requiredLevel?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Story_seasonAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_season to aggregate.
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_seasons to fetch.
   */
  orderBy?: Prisma.story_seasonOrderByWithRelationInput | Prisma.story_seasonOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.story_seasonWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_seasons from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_seasons.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned story_seasons
  **/
  _count?: true | Story_seasonCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Story_seasonAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Story_seasonSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Story_seasonMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Story_seasonMaxAggregateInputType
}

export type GetStory_seasonAggregateType<T extends Story_seasonAggregateArgs> = {
      [P in keyof T & keyof AggregateStory_season]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStory_season[P]>
    : Prisma.GetScalarType<T[P], AggregateStory_season[P]>
}




export type story_seasonGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.story_seasonWhereInput
  orderBy?: Prisma.story_seasonOrderByWithAggregationInput | Prisma.story_seasonOrderByWithAggregationInput[]
  by: Prisma.Story_seasonScalarFieldEnum[] | Prisma.Story_seasonScalarFieldEnum
  having?: Prisma.story_seasonScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Story_seasonCountAggregateInputType | true
  _avg?: Story_seasonAvgAggregateInputType
  _sum?: Story_seasonSumAggregateInputType
  _min?: Story_seasonMinAggregateInputType
  _max?: Story_seasonMaxAggregateInputType
}

export type Story_seasonGroupByOutputType = {
  id: number
  name: string
  description: string | null
  startDate: Date
  requiredLevel: number
  createdAt: Date
  updatedAt: Date
  _count: Story_seasonCountAggregateOutputType | null
  _avg: Story_seasonAvgAggregateOutputType | null
  _sum: Story_seasonSumAggregateOutputType | null
  _min: Story_seasonMinAggregateOutputType | null
  _max: Story_seasonMaxAggregateOutputType | null
}

type GetStory_seasonGroupByPayload<T extends story_seasonGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Story_seasonGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Story_seasonGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Story_seasonGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Story_seasonGroupByOutputType[P]>
      }
    >
  >



export type story_seasonWhereInput = {
  AND?: Prisma.story_seasonWhereInput | Prisma.story_seasonWhereInput[]
  OR?: Prisma.story_seasonWhereInput[]
  NOT?: Prisma.story_seasonWhereInput | Prisma.story_seasonWhereInput[]
  id?: Prisma.IntFilter<"story_season"> | number
  name?: Prisma.StringFilter<"story_season"> | string
  description?: Prisma.StringNullableFilter<"story_season"> | string | null
  startDate?: Prisma.DateTimeFilter<"story_season"> | Date | string
  requiredLevel?: Prisma.IntFilter<"story_season"> | number
  createdAt?: Prisma.DateTimeFilter<"story_season"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_season"> | Date | string
  story_chapter?: Prisma.Story_chapterListRelationFilter
}

export type story_seasonOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  startDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  story_chapter?: Prisma.story_chapterOrderByRelationAggregateInput
  _relevance?: Prisma.story_seasonOrderByRelevanceInput
}

export type story_seasonWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.story_seasonWhereInput | Prisma.story_seasonWhereInput[]
  OR?: Prisma.story_seasonWhereInput[]
  NOT?: Prisma.story_seasonWhereInput | Prisma.story_seasonWhereInput[]
  name?: Prisma.StringFilter<"story_season"> | string
  description?: Prisma.StringNullableFilter<"story_season"> | string | null
  startDate?: Prisma.DateTimeFilter<"story_season"> | Date | string
  requiredLevel?: Prisma.IntFilter<"story_season"> | number
  createdAt?: Prisma.DateTimeFilter<"story_season"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"story_season"> | Date | string
  story_chapter?: Prisma.Story_chapterListRelationFilter
}, "id">

export type story_seasonOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  startDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.story_seasonCountOrderByAggregateInput
  _avg?: Prisma.story_seasonAvgOrderByAggregateInput
  _max?: Prisma.story_seasonMaxOrderByAggregateInput
  _min?: Prisma.story_seasonMinOrderByAggregateInput
  _sum?: Prisma.story_seasonSumOrderByAggregateInput
}

export type story_seasonScalarWhereWithAggregatesInput = {
  AND?: Prisma.story_seasonScalarWhereWithAggregatesInput | Prisma.story_seasonScalarWhereWithAggregatesInput[]
  OR?: Prisma.story_seasonScalarWhereWithAggregatesInput[]
  NOT?: Prisma.story_seasonScalarWhereWithAggregatesInput | Prisma.story_seasonScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"story_season"> | number
  name?: Prisma.StringWithAggregatesFilter<"story_season"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"story_season"> | string | null
  startDate?: Prisma.DateTimeWithAggregatesFilter<"story_season"> | Date | string
  requiredLevel?: Prisma.IntWithAggregatesFilter<"story_season"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"story_season"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"story_season"> | Date | string
}

export type story_seasonCreateInput = {
  name: string
  description?: string | null
  startDate: Date | string
  requiredLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  story_chapter?: Prisma.story_chapterCreateNestedManyWithoutStory_seasonInput
}

export type story_seasonUncheckedCreateInput = {
  id?: number
  name: string
  description?: string | null
  startDate: Date | string
  requiredLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  story_chapter?: Prisma.story_chapterUncheckedCreateNestedManyWithoutStory_seasonInput
}

export type story_seasonUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  story_chapter?: Prisma.story_chapterUpdateManyWithoutStory_seasonNestedInput
}

export type story_seasonUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  story_chapter?: Prisma.story_chapterUncheckedUpdateManyWithoutStory_seasonNestedInput
}

export type story_seasonCreateManyInput = {
  id?: number
  name: string
  description?: string | null
  startDate: Date | string
  requiredLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_seasonUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_seasonUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_seasonOrderByRelevanceInput = {
  fields: Prisma.story_seasonOrderByRelevanceFieldEnum | Prisma.story_seasonOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type story_seasonCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  startDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_seasonAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
}

export type story_seasonMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  startDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_seasonMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  startDate?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type story_seasonSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  requiredLevel?: Prisma.SortOrder
}

export type Story_seasonScalarRelationFilter = {
  is?: Prisma.story_seasonWhereInput
  isNot?: Prisma.story_seasonWhereInput
}

export type story_seasonCreateNestedOneWithoutStory_chapterInput = {
  create?: Prisma.XOR<Prisma.story_seasonCreateWithoutStory_chapterInput, Prisma.story_seasonUncheckedCreateWithoutStory_chapterInput>
  connectOrCreate?: Prisma.story_seasonCreateOrConnectWithoutStory_chapterInput
  connect?: Prisma.story_seasonWhereUniqueInput
}

export type story_seasonUpdateOneRequiredWithoutStory_chapterNestedInput = {
  create?: Prisma.XOR<Prisma.story_seasonCreateWithoutStory_chapterInput, Prisma.story_seasonUncheckedCreateWithoutStory_chapterInput>
  connectOrCreate?: Prisma.story_seasonCreateOrConnectWithoutStory_chapterInput
  upsert?: Prisma.story_seasonUpsertWithoutStory_chapterInput
  connect?: Prisma.story_seasonWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.story_seasonUpdateToOneWithWhereWithoutStory_chapterInput, Prisma.story_seasonUpdateWithoutStory_chapterInput>, Prisma.story_seasonUncheckedUpdateWithoutStory_chapterInput>
}

export type story_seasonCreateWithoutStory_chapterInput = {
  name: string
  description?: string | null
  startDate: Date | string
  requiredLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_seasonUncheckedCreateWithoutStory_chapterInput = {
  id?: number
  name: string
  description?: string | null
  startDate: Date | string
  requiredLevel?: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type story_seasonCreateOrConnectWithoutStory_chapterInput = {
  where: Prisma.story_seasonWhereUniqueInput
  create: Prisma.XOR<Prisma.story_seasonCreateWithoutStory_chapterInput, Prisma.story_seasonUncheckedCreateWithoutStory_chapterInput>
}

export type story_seasonUpsertWithoutStory_chapterInput = {
  update: Prisma.XOR<Prisma.story_seasonUpdateWithoutStory_chapterInput, Prisma.story_seasonUncheckedUpdateWithoutStory_chapterInput>
  create: Prisma.XOR<Prisma.story_seasonCreateWithoutStory_chapterInput, Prisma.story_seasonUncheckedCreateWithoutStory_chapterInput>
  where?: Prisma.story_seasonWhereInput
}

export type story_seasonUpdateToOneWithWhereWithoutStory_chapterInput = {
  where?: Prisma.story_seasonWhereInput
  data: Prisma.XOR<Prisma.story_seasonUpdateWithoutStory_chapterInput, Prisma.story_seasonUncheckedUpdateWithoutStory_chapterInput>
}

export type story_seasonUpdateWithoutStory_chapterInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type story_seasonUncheckedUpdateWithoutStory_chapterInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  requiredLevel?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type Story_seasonCountOutputType
 */

export type Story_seasonCountOutputType = {
  story_chapter: number
}

export type Story_seasonCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  story_chapter?: boolean | Story_seasonCountOutputTypeCountStory_chapterArgs
}

/**
 * Story_seasonCountOutputType without action
 */
export type Story_seasonCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Story_seasonCountOutputType
   */
  select?: Prisma.Story_seasonCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Story_seasonCountOutputType without action
 */
export type Story_seasonCountOutputTypeCountStory_chapterArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.story_chapterWhereInput
}


export type story_seasonSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  startDate?: boolean
  requiredLevel?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  story_chapter?: boolean | Prisma.story_season$story_chapterArgs<ExtArgs>
  _count?: boolean | Prisma.Story_seasonCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["story_season"]>



export type story_seasonSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  startDate?: boolean
  requiredLevel?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type story_seasonOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "startDate" | "requiredLevel" | "createdAt" | "updatedAt", ExtArgs["result"]["story_season"]>
export type story_seasonInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  story_chapter?: boolean | Prisma.story_season$story_chapterArgs<ExtArgs>
  _count?: boolean | Prisma.Story_seasonCountOutputTypeDefaultArgs<ExtArgs>
}

export type $story_seasonPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "story_season"
  objects: {
    story_chapter: Prisma.$story_chapterPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    description: string | null
    startDate: Date
    requiredLevel: number
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["story_season"]>
  composites: {}
}

export type story_seasonGetPayload<S extends boolean | null | undefined | story_seasonDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$story_seasonPayload, S>

export type story_seasonCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<story_seasonFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Story_seasonCountAggregateInputType | true
  }

export interface story_seasonDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['story_season'], meta: { name: 'story_season' } }
  /**
   * Find zero or one Story_season that matches the filter.
   * @param {story_seasonFindUniqueArgs} args - Arguments to find a Story_season
   * @example
   * // Get one Story_season
   * const story_season = await prisma.story_season.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends story_seasonFindUniqueArgs>(args: Prisma.SelectSubset<T, story_seasonFindUniqueArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Story_season that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {story_seasonFindUniqueOrThrowArgs} args - Arguments to find a Story_season
   * @example
   * // Get one Story_season
   * const story_season = await prisma.story_season.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends story_seasonFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, story_seasonFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_season that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonFindFirstArgs} args - Arguments to find a Story_season
   * @example
   * // Get one Story_season
   * const story_season = await prisma.story_season.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends story_seasonFindFirstArgs>(args?: Prisma.SelectSubset<T, story_seasonFindFirstArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Story_season that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonFindFirstOrThrowArgs} args - Arguments to find a Story_season
   * @example
   * // Get one Story_season
   * const story_season = await prisma.story_season.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends story_seasonFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, story_seasonFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Story_seasons that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Story_seasons
   * const story_seasons = await prisma.story_season.findMany()
   * 
   * // Get first 10 Story_seasons
   * const story_seasons = await prisma.story_season.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const story_seasonWithIdOnly = await prisma.story_season.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends story_seasonFindManyArgs>(args?: Prisma.SelectSubset<T, story_seasonFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Story_season.
   * @param {story_seasonCreateArgs} args - Arguments to create a Story_season.
   * @example
   * // Create one Story_season
   * const Story_season = await prisma.story_season.create({
   *   data: {
   *     // ... data to create a Story_season
   *   }
   * })
   * 
   */
  create<T extends story_seasonCreateArgs>(args: Prisma.SelectSubset<T, story_seasonCreateArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Story_seasons.
   * @param {story_seasonCreateManyArgs} args - Arguments to create many Story_seasons.
   * @example
   * // Create many Story_seasons
   * const story_season = await prisma.story_season.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends story_seasonCreateManyArgs>(args?: Prisma.SelectSubset<T, story_seasonCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Story_season.
   * @param {story_seasonDeleteArgs} args - Arguments to delete one Story_season.
   * @example
   * // Delete one Story_season
   * const Story_season = await prisma.story_season.delete({
   *   where: {
   *     // ... filter to delete one Story_season
   *   }
   * })
   * 
   */
  delete<T extends story_seasonDeleteArgs>(args: Prisma.SelectSubset<T, story_seasonDeleteArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Story_season.
   * @param {story_seasonUpdateArgs} args - Arguments to update one Story_season.
   * @example
   * // Update one Story_season
   * const story_season = await prisma.story_season.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends story_seasonUpdateArgs>(args: Prisma.SelectSubset<T, story_seasonUpdateArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Story_seasons.
   * @param {story_seasonDeleteManyArgs} args - Arguments to filter Story_seasons to delete.
   * @example
   * // Delete a few Story_seasons
   * const { count } = await prisma.story_season.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends story_seasonDeleteManyArgs>(args?: Prisma.SelectSubset<T, story_seasonDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Story_seasons.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Story_seasons
   * const story_season = await prisma.story_season.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends story_seasonUpdateManyArgs>(args: Prisma.SelectSubset<T, story_seasonUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Story_season.
   * @param {story_seasonUpsertArgs} args - Arguments to update or create a Story_season.
   * @example
   * // Update or create a Story_season
   * const story_season = await prisma.story_season.upsert({
   *   create: {
   *     // ... data to create a Story_season
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Story_season we want to update
   *   }
   * })
   */
  upsert<T extends story_seasonUpsertArgs>(args: Prisma.SelectSubset<T, story_seasonUpsertArgs<ExtArgs>>): Prisma.Prisma__story_seasonClient<runtime.Types.Result.GetResult<Prisma.$story_seasonPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Story_seasons.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonCountArgs} args - Arguments to filter Story_seasons to count.
   * @example
   * // Count the number of Story_seasons
   * const count = await prisma.story_season.count({
   *   where: {
   *     // ... the filter for the Story_seasons we want to count
   *   }
   * })
  **/
  count<T extends story_seasonCountArgs>(
    args?: Prisma.Subset<T, story_seasonCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Story_seasonCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Story_season.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Story_seasonAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Story_seasonAggregateArgs>(args: Prisma.Subset<T, Story_seasonAggregateArgs>): Prisma.PrismaPromise<GetStory_seasonAggregateType<T>>

  /**
   * Group by Story_season.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {story_seasonGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends story_seasonGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: story_seasonGroupByArgs['orderBy'] }
      : { orderBy?: story_seasonGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, story_seasonGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStory_seasonGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the story_season model
 */
readonly fields: story_seasonFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for story_season.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__story_seasonClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  story_chapter<T extends Prisma.story_season$story_chapterArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.story_season$story_chapterArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$story_chapterPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the story_season model
 */
export interface story_seasonFieldRefs {
  readonly id: Prisma.FieldRef<"story_season", 'Int'>
  readonly name: Prisma.FieldRef<"story_season", 'String'>
  readonly description: Prisma.FieldRef<"story_season", 'String'>
  readonly startDate: Prisma.FieldRef<"story_season", 'DateTime'>
  readonly requiredLevel: Prisma.FieldRef<"story_season", 'Int'>
  readonly createdAt: Prisma.FieldRef<"story_season", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"story_season", 'DateTime'>
}
    

// Custom InputTypes
/**
 * story_season findUnique
 */
export type story_seasonFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter, which story_season to fetch.
   */
  where: Prisma.story_seasonWhereUniqueInput
}

/**
 * story_season findUniqueOrThrow
 */
export type story_seasonFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter, which story_season to fetch.
   */
  where: Prisma.story_seasonWhereUniqueInput
}

/**
 * story_season findFirst
 */
export type story_seasonFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter, which story_season to fetch.
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_seasons to fetch.
   */
  orderBy?: Prisma.story_seasonOrderByWithRelationInput | Prisma.story_seasonOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_seasons.
   */
  cursor?: Prisma.story_seasonWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_seasons from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_seasons.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_seasons.
   */
  distinct?: Prisma.Story_seasonScalarFieldEnum | Prisma.Story_seasonScalarFieldEnum[]
}

/**
 * story_season findFirstOrThrow
 */
export type story_seasonFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter, which story_season to fetch.
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_seasons to fetch.
   */
  orderBy?: Prisma.story_seasonOrderByWithRelationInput | Prisma.story_seasonOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for story_seasons.
   */
  cursor?: Prisma.story_seasonWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_seasons from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_seasons.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of story_seasons.
   */
  distinct?: Prisma.Story_seasonScalarFieldEnum | Prisma.Story_seasonScalarFieldEnum[]
}

/**
 * story_season findMany
 */
export type story_seasonFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter, which story_seasons to fetch.
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of story_seasons to fetch.
   */
  orderBy?: Prisma.story_seasonOrderByWithRelationInput | Prisma.story_seasonOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing story_seasons.
   */
  cursor?: Prisma.story_seasonWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` story_seasons from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` story_seasons.
   */
  skip?: number
  distinct?: Prisma.Story_seasonScalarFieldEnum | Prisma.Story_seasonScalarFieldEnum[]
}

/**
 * story_season create
 */
export type story_seasonCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * The data needed to create a story_season.
   */
  data: Prisma.XOR<Prisma.story_seasonCreateInput, Prisma.story_seasonUncheckedCreateInput>
}

/**
 * story_season createMany
 */
export type story_seasonCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many story_seasons.
   */
  data: Prisma.story_seasonCreateManyInput | Prisma.story_seasonCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * story_season update
 */
export type story_seasonUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * The data needed to update a story_season.
   */
  data: Prisma.XOR<Prisma.story_seasonUpdateInput, Prisma.story_seasonUncheckedUpdateInput>
  /**
   * Choose, which story_season to update.
   */
  where: Prisma.story_seasonWhereUniqueInput
}

/**
 * story_season updateMany
 */
export type story_seasonUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update story_seasons.
   */
  data: Prisma.XOR<Prisma.story_seasonUpdateManyMutationInput, Prisma.story_seasonUncheckedUpdateManyInput>
  /**
   * Filter which story_seasons to update
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * Limit how many story_seasons to update.
   */
  limit?: number
}

/**
 * story_season upsert
 */
export type story_seasonUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * The filter to search for the story_season to update in case it exists.
   */
  where: Prisma.story_seasonWhereUniqueInput
  /**
   * In case the story_season found by the `where` argument doesn't exist, create a new story_season with this data.
   */
  create: Prisma.XOR<Prisma.story_seasonCreateInput, Prisma.story_seasonUncheckedCreateInput>
  /**
   * In case the story_season was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.story_seasonUpdateInput, Prisma.story_seasonUncheckedUpdateInput>
}

/**
 * story_season delete
 */
export type story_seasonDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
  /**
   * Filter which story_season to delete.
   */
  where: Prisma.story_seasonWhereUniqueInput
}

/**
 * story_season deleteMany
 */
export type story_seasonDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which story_seasons to delete
   */
  where?: Prisma.story_seasonWhereInput
  /**
   * Limit how many story_seasons to delete.
   */
  limit?: number
}

/**
 * story_season.story_chapter
 */
export type story_season$story_chapterArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_chapter
   */
  select?: Prisma.story_chapterSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_chapter
   */
  omit?: Prisma.story_chapterOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_chapterInclude<ExtArgs> | null
  where?: Prisma.story_chapterWhereInput
  orderBy?: Prisma.story_chapterOrderByWithRelationInput | Prisma.story_chapterOrderByWithRelationInput[]
  cursor?: Prisma.story_chapterWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Story_chapterScalarFieldEnum | Prisma.Story_chapterScalarFieldEnum[]
}

/**
 * story_season without action
 */
export type story_seasonDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_season
   */
  select?: Prisma.story_seasonSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_season
   */
  omit?: Prisma.story_seasonOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_seasonInclude<ExtArgs> | null
}
