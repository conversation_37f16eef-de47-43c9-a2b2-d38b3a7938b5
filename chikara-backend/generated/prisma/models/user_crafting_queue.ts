
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_crafting_queue` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_crafting_queue
 * 
 */
export type user_crafting_queueModel = runtime.Types.Result.DefaultSelection<Prisma.$user_crafting_queuePayload>

export type AggregateUser_crafting_queue = {
  _count: User_crafting_queueCountAggregateOutputType | null
  _avg: User_crafting_queueAvgAggregateOutputType | null
  _sum: User_crafting_queueSumAggregateOutputType | null
  _min: User_crafting_queueMinAggregateOutputType | null
  _max: User_crafting_queueMaxAggregateOutputType | null
}

export type User_crafting_queueAvgAggregateOutputType = {
  id: number | null
  startedAt: number | null
  endsAt: number | null
  userId: number | null
  craftingRecipeId: number | null
}

export type User_crafting_queueSumAggregateOutputType = {
  id: number | null
  startedAt: bigint | null
  endsAt: bigint | null
  userId: number | null
  craftingRecipeId: number | null
}

export type User_crafting_queueMinAggregateOutputType = {
  id: number | null
  startedAt: bigint | null
  endsAt: bigint | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  craftingRecipeId: number | null
}

export type User_crafting_queueMaxAggregateOutputType = {
  id: number | null
  startedAt: bigint | null
  endsAt: bigint | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  craftingRecipeId: number | null
}

export type User_crafting_queueCountAggregateOutputType = {
  id: number
  startedAt: number
  endsAt: number
  createdAt: number
  updatedAt: number
  userId: number
  craftingRecipeId: number
  _all: number
}


export type User_crafting_queueAvgAggregateInputType = {
  id?: true
  startedAt?: true
  endsAt?: true
  userId?: true
  craftingRecipeId?: true
}

export type User_crafting_queueSumAggregateInputType = {
  id?: true
  startedAt?: true
  endsAt?: true
  userId?: true
  craftingRecipeId?: true
}

export type User_crafting_queueMinAggregateInputType = {
  id?: true
  startedAt?: true
  endsAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  craftingRecipeId?: true
}

export type User_crafting_queueMaxAggregateInputType = {
  id?: true
  startedAt?: true
  endsAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  craftingRecipeId?: true
}

export type User_crafting_queueCountAggregateInputType = {
  id?: true
  startedAt?: true
  endsAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  craftingRecipeId?: true
  _all?: true
}

export type User_crafting_queueAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_crafting_queue to aggregate.
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_crafting_queues to fetch.
   */
  orderBy?: Prisma.user_crafting_queueOrderByWithRelationInput | Prisma.user_crafting_queueOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_crafting_queueWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_crafting_queues from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_crafting_queues.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_crafting_queues
  **/
  _count?: true | User_crafting_queueCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_crafting_queueAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_crafting_queueSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_crafting_queueMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_crafting_queueMaxAggregateInputType
}

export type GetUser_crafting_queueAggregateType<T extends User_crafting_queueAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_crafting_queue]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_crafting_queue[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_crafting_queue[P]>
}




export type user_crafting_queueGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_crafting_queueWhereInput
  orderBy?: Prisma.user_crafting_queueOrderByWithAggregationInput | Prisma.user_crafting_queueOrderByWithAggregationInput[]
  by: Prisma.User_crafting_queueScalarFieldEnum[] | Prisma.User_crafting_queueScalarFieldEnum
  having?: Prisma.user_crafting_queueScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_crafting_queueCountAggregateInputType | true
  _avg?: User_crafting_queueAvgAggregateInputType
  _sum?: User_crafting_queueSumAggregateInputType
  _min?: User_crafting_queueMinAggregateInputType
  _max?: User_crafting_queueMaxAggregateInputType
}

export type User_crafting_queueGroupByOutputType = {
  id: number
  startedAt: bigint
  endsAt: bigint
  createdAt: Date
  updatedAt: Date
  userId: number | null
  craftingRecipeId: number | null
  _count: User_crafting_queueCountAggregateOutputType | null
  _avg: User_crafting_queueAvgAggregateOutputType | null
  _sum: User_crafting_queueSumAggregateOutputType | null
  _min: User_crafting_queueMinAggregateOutputType | null
  _max: User_crafting_queueMaxAggregateOutputType | null
}

type GetUser_crafting_queueGroupByPayload<T extends user_crafting_queueGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_crafting_queueGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_crafting_queueGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_crafting_queueGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_crafting_queueGroupByOutputType[P]>
      }
    >
  >



export type user_crafting_queueWhereInput = {
  AND?: Prisma.user_crafting_queueWhereInput | Prisma.user_crafting_queueWhereInput[]
  OR?: Prisma.user_crafting_queueWhereInput[]
  NOT?: Prisma.user_crafting_queueWhereInput | Prisma.user_crafting_queueWhereInput[]
  id?: Prisma.IntFilter<"user_crafting_queue"> | number
  startedAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  endsAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  createdAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
  craftingRecipeId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeNullableScalarRelationFilter, Prisma.crafting_recipeWhereInput> | null
}

export type user_crafting_queueOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  crafting_recipe?: Prisma.crafting_recipeOrderByWithRelationInput
}

export type user_crafting_queueWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.user_crafting_queueWhereInput | Prisma.user_crafting_queueWhereInput[]
  OR?: Prisma.user_crafting_queueWhereInput[]
  NOT?: Prisma.user_crafting_queueWhereInput | Prisma.user_crafting_queueWhereInput[]
  startedAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  endsAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  createdAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
  craftingRecipeId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeNullableScalarRelationFilter, Prisma.crafting_recipeWhereInput> | null
}, "id">

export type user_crafting_queueOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_crafting_queueCountOrderByAggregateInput
  _avg?: Prisma.user_crafting_queueAvgOrderByAggregateInput
  _max?: Prisma.user_crafting_queueMaxOrderByAggregateInput
  _min?: Prisma.user_crafting_queueMinOrderByAggregateInput
  _sum?: Prisma.user_crafting_queueSumOrderByAggregateInput
}

export type user_crafting_queueScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_crafting_queueScalarWhereWithAggregatesInput | Prisma.user_crafting_queueScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_crafting_queueScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_crafting_queueScalarWhereWithAggregatesInput | Prisma.user_crafting_queueScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_crafting_queue"> | number
  startedAt?: Prisma.BigIntWithAggregatesFilter<"user_crafting_queue"> | bigint | number
  endsAt?: Prisma.BigIntWithAggregatesFilter<"user_crafting_queue"> | bigint | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_crafting_queue"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_crafting_queue"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"user_crafting_queue"> | number | null
  craftingRecipeId?: Prisma.IntNullableWithAggregatesFilter<"user_crafting_queue"> | number | null
}

export type user_crafting_queueCreateInput = {
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutUser_crafting_queueInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutUser_crafting_queueInput
}

export type user_crafting_queueUncheckedCreateInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  craftingRecipeId?: number | null
}

export type user_crafting_queueUpdateInput = {
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutUser_crafting_queueNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutUser_crafting_queueNestedInput
}

export type user_crafting_queueUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftingRecipeId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_crafting_queueCreateManyInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  craftingRecipeId?: number | null
}

export type user_crafting_queueUpdateManyMutationInput = {
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_crafting_queueUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  craftingRecipeId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_crafting_queueListRelationFilter = {
  every?: Prisma.user_crafting_queueWhereInput
  some?: Prisma.user_crafting_queueWhereInput
  none?: Prisma.user_crafting_queueWhereInput
}

export type user_crafting_queueOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_crafting_queueCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
}

export type user_crafting_queueAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
}

export type user_crafting_queueMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
}

export type user_crafting_queueMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
}

export type user_crafting_queueSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  startedAt?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  craftingRecipeId?: Prisma.SortOrder
}

export type user_crafting_queueCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_crafting_queueCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
}

export type user_crafting_queueUncheckedCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_crafting_queueCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
}

export type user_crafting_queueUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_crafting_queueCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  disconnect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  delete?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  update?: Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.user_crafting_queueUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
}

export type user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput> | Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput | Prisma.user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.user_crafting_queueCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  disconnect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  delete?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  update?: Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.user_crafting_queueUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.user_crafting_queueUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
}

export type user_crafting_queueCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput> | Prisma.user_crafting_queueCreateWithoutUserInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutUserInput | Prisma.user_crafting_queueCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_crafting_queueCreateManyUserInputEnvelope
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
}

export type user_crafting_queueUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput> | Prisma.user_crafting_queueCreateWithoutUserInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutUserInput | Prisma.user_crafting_queueCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_crafting_queueCreateManyUserInputEnvelope
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
}

export type user_crafting_queueUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput> | Prisma.user_crafting_queueCreateWithoutUserInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutUserInput | Prisma.user_crafting_queueCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutUserInput | Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_crafting_queueCreateManyUserInputEnvelope
  set?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  disconnect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  delete?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  update?: Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutUserInput | Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_crafting_queueUpdateManyWithWhereWithoutUserInput | Prisma.user_crafting_queueUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
}

export type user_crafting_queueUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput> | Prisma.user_crafting_queueCreateWithoutUserInput[] | Prisma.user_crafting_queueUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_crafting_queueCreateOrConnectWithoutUserInput | Prisma.user_crafting_queueCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutUserInput | Prisma.user_crafting_queueUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_crafting_queueCreateManyUserInputEnvelope
  set?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  disconnect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  delete?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  connect?: Prisma.user_crafting_queueWhereUniqueInput | Prisma.user_crafting_queueWhereUniqueInput[]
  update?: Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutUserInput | Prisma.user_crafting_queueUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_crafting_queueUpdateManyWithWhereWithoutUserInput | Prisma.user_crafting_queueUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
}

export type BigIntFieldUpdateOperationsInput = {
  set?: bigint | number
  increment?: bigint | number
  decrement?: bigint | number
  multiply?: bigint | number
  divide?: bigint | number
}

export type user_crafting_queueCreateWithoutCrafting_recipeInput = {
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutUser_crafting_queueInput
}

export type user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_crafting_queueCreateOrConnectWithoutCrafting_recipeInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  create: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput>
}

export type user_crafting_queueCreateManyCrafting_recipeInputEnvelope = {
  data: Prisma.user_crafting_queueCreateManyCrafting_recipeInput | Prisma.user_crafting_queueCreateManyCrafting_recipeInput[]
  skipDuplicates?: boolean
}

export type user_crafting_queueUpsertWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  update: Prisma.XOR<Prisma.user_crafting_queueUpdateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedUpdateWithoutCrafting_recipeInput>
  create: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedCreateWithoutCrafting_recipeInput>
}

export type user_crafting_queueUpdateWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateWithoutCrafting_recipeInput, Prisma.user_crafting_queueUncheckedUpdateWithoutCrafting_recipeInput>
}

export type user_crafting_queueUpdateManyWithWhereWithoutCrafting_recipeInput = {
  where: Prisma.user_crafting_queueScalarWhereInput
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateManyMutationInput, Prisma.user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeInput>
}

export type user_crafting_queueScalarWhereInput = {
  AND?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
  OR?: Prisma.user_crafting_queueScalarWhereInput[]
  NOT?: Prisma.user_crafting_queueScalarWhereInput | Prisma.user_crafting_queueScalarWhereInput[]
  id?: Prisma.IntFilter<"user_crafting_queue"> | number
  startedAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  endsAt?: Prisma.BigIntFilter<"user_crafting_queue"> | bigint | number
  createdAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_crafting_queue"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
  craftingRecipeId?: Prisma.IntNullableFilter<"user_crafting_queue"> | number | null
}

export type user_crafting_queueCreateWithoutUserInput = {
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutUser_crafting_queueInput
}

export type user_crafting_queueUncheckedCreateWithoutUserInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId?: number | null
}

export type user_crafting_queueCreateOrConnectWithoutUserInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  create: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput>
}

export type user_crafting_queueCreateManyUserInputEnvelope = {
  data: Prisma.user_crafting_queueCreateManyUserInput | Prisma.user_crafting_queueCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_crafting_queueUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  update: Prisma.XOR<Prisma.user_crafting_queueUpdateWithoutUserInput, Prisma.user_crafting_queueUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_crafting_queueCreateWithoutUserInput, Prisma.user_crafting_queueUncheckedCreateWithoutUserInput>
}

export type user_crafting_queueUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_crafting_queueWhereUniqueInput
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateWithoutUserInput, Prisma.user_crafting_queueUncheckedUpdateWithoutUserInput>
}

export type user_crafting_queueUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_crafting_queueScalarWhereInput
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateManyMutationInput, Prisma.user_crafting_queueUncheckedUpdateManyWithoutUserInput>
}

export type user_crafting_queueCreateManyCrafting_recipeInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_crafting_queueUpdateWithoutCrafting_recipeInput = {
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutUser_crafting_queueNestedInput
}

export type user_crafting_queueUncheckedUpdateWithoutCrafting_recipeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_crafting_queueUncheckedUpdateManyWithoutCrafting_recipeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_crafting_queueCreateManyUserInput = {
  id?: number
  startedAt: bigint | number
  endsAt: bigint | number
  createdAt?: Date | string
  updatedAt?: Date | string
  craftingRecipeId?: number | null
}

export type user_crafting_queueUpdateWithoutUserInput = {
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutUser_crafting_queueNestedInput
}

export type user_crafting_queueUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_crafting_queueUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  startedAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  craftingRecipeId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type user_crafting_queueSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  startedAt?: boolean
  endsAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  craftingRecipeId?: boolean
  user?: boolean | Prisma.user_crafting_queue$userArgs<ExtArgs>
  crafting_recipe?: boolean | Prisma.user_crafting_queue$crafting_recipeArgs<ExtArgs>
}, ExtArgs["result"]["user_crafting_queue"]>



export type user_crafting_queueSelectScalar = {
  id?: boolean
  startedAt?: boolean
  endsAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  craftingRecipeId?: boolean
}

export type user_crafting_queueOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "startedAt" | "endsAt" | "createdAt" | "updatedAt" | "userId" | "craftingRecipeId", ExtArgs["result"]["user_crafting_queue"]>
export type user_crafting_queueInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.user_crafting_queue$userArgs<ExtArgs>
  crafting_recipe?: boolean | Prisma.user_crafting_queue$crafting_recipeArgs<ExtArgs>
}

export type $user_crafting_queuePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_crafting_queue"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    crafting_recipe: Prisma.$crafting_recipePayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    startedAt: bigint
    endsAt: bigint
    createdAt: Date
    updatedAt: Date
    userId: number | null
    craftingRecipeId: number | null
  }, ExtArgs["result"]["user_crafting_queue"]>
  composites: {}
}

export type user_crafting_queueGetPayload<S extends boolean | null | undefined | user_crafting_queueDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload, S>

export type user_crafting_queueCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_crafting_queueFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_crafting_queueCountAggregateInputType | true
  }

export interface user_crafting_queueDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_crafting_queue'], meta: { name: 'user_crafting_queue' } }
  /**
   * Find zero or one User_crafting_queue that matches the filter.
   * @param {user_crafting_queueFindUniqueArgs} args - Arguments to find a User_crafting_queue
   * @example
   * // Get one User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_crafting_queueFindUniqueArgs>(args: Prisma.SelectSubset<T, user_crafting_queueFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_crafting_queue that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_crafting_queueFindUniqueOrThrowArgs} args - Arguments to find a User_crafting_queue
   * @example
   * // Get one User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_crafting_queueFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_crafting_queueFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_crafting_queue that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueFindFirstArgs} args - Arguments to find a User_crafting_queue
   * @example
   * // Get one User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_crafting_queueFindFirstArgs>(args?: Prisma.SelectSubset<T, user_crafting_queueFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_crafting_queue that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueFindFirstOrThrowArgs} args - Arguments to find a User_crafting_queue
   * @example
   * // Get one User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_crafting_queueFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_crafting_queueFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_crafting_queues that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_crafting_queues
   * const user_crafting_queues = await prisma.user_crafting_queue.findMany()
   * 
   * // Get first 10 User_crafting_queues
   * const user_crafting_queues = await prisma.user_crafting_queue.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_crafting_queueWithIdOnly = await prisma.user_crafting_queue.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_crafting_queueFindManyArgs>(args?: Prisma.SelectSubset<T, user_crafting_queueFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_crafting_queue.
   * @param {user_crafting_queueCreateArgs} args - Arguments to create a User_crafting_queue.
   * @example
   * // Create one User_crafting_queue
   * const User_crafting_queue = await prisma.user_crafting_queue.create({
   *   data: {
   *     // ... data to create a User_crafting_queue
   *   }
   * })
   * 
   */
  create<T extends user_crafting_queueCreateArgs>(args: Prisma.SelectSubset<T, user_crafting_queueCreateArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_crafting_queues.
   * @param {user_crafting_queueCreateManyArgs} args - Arguments to create many User_crafting_queues.
   * @example
   * // Create many User_crafting_queues
   * const user_crafting_queue = await prisma.user_crafting_queue.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_crafting_queueCreateManyArgs>(args?: Prisma.SelectSubset<T, user_crafting_queueCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_crafting_queue.
   * @param {user_crafting_queueDeleteArgs} args - Arguments to delete one User_crafting_queue.
   * @example
   * // Delete one User_crafting_queue
   * const User_crafting_queue = await prisma.user_crafting_queue.delete({
   *   where: {
   *     // ... filter to delete one User_crafting_queue
   *   }
   * })
   * 
   */
  delete<T extends user_crafting_queueDeleteArgs>(args: Prisma.SelectSubset<T, user_crafting_queueDeleteArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_crafting_queue.
   * @param {user_crafting_queueUpdateArgs} args - Arguments to update one User_crafting_queue.
   * @example
   * // Update one User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_crafting_queueUpdateArgs>(args: Prisma.SelectSubset<T, user_crafting_queueUpdateArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_crafting_queues.
   * @param {user_crafting_queueDeleteManyArgs} args - Arguments to filter User_crafting_queues to delete.
   * @example
   * // Delete a few User_crafting_queues
   * const { count } = await prisma.user_crafting_queue.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_crafting_queueDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_crafting_queueDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_crafting_queues.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_crafting_queues
   * const user_crafting_queue = await prisma.user_crafting_queue.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_crafting_queueUpdateManyArgs>(args: Prisma.SelectSubset<T, user_crafting_queueUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_crafting_queue.
   * @param {user_crafting_queueUpsertArgs} args - Arguments to update or create a User_crafting_queue.
   * @example
   * // Update or create a User_crafting_queue
   * const user_crafting_queue = await prisma.user_crafting_queue.upsert({
   *   create: {
   *     // ... data to create a User_crafting_queue
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_crafting_queue we want to update
   *   }
   * })
   */
  upsert<T extends user_crafting_queueUpsertArgs>(args: Prisma.SelectSubset<T, user_crafting_queueUpsertArgs<ExtArgs>>): Prisma.Prisma__user_crafting_queueClient<runtime.Types.Result.GetResult<Prisma.$user_crafting_queuePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_crafting_queues.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueCountArgs} args - Arguments to filter User_crafting_queues to count.
   * @example
   * // Count the number of User_crafting_queues
   * const count = await prisma.user_crafting_queue.count({
   *   where: {
   *     // ... the filter for the User_crafting_queues we want to count
   *   }
   * })
  **/
  count<T extends user_crafting_queueCountArgs>(
    args?: Prisma.Subset<T, user_crafting_queueCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_crafting_queueCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_crafting_queue.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_crafting_queueAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_crafting_queueAggregateArgs>(args: Prisma.Subset<T, User_crafting_queueAggregateArgs>): Prisma.PrismaPromise<GetUser_crafting_queueAggregateType<T>>

  /**
   * Group by User_crafting_queue.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_crafting_queueGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_crafting_queueGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_crafting_queueGroupByArgs['orderBy'] }
      : { orderBy?: user_crafting_queueGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_crafting_queueGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_crafting_queueGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_crafting_queue model
 */
readonly fields: user_crafting_queueFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_crafting_queue.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_crafting_queueClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.user_crafting_queue$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_crafting_queue$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  crafting_recipe<T extends Prisma.user_crafting_queue$crafting_recipeArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_crafting_queue$crafting_recipeArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_crafting_queue model
 */
export interface user_crafting_queueFieldRefs {
  readonly id: Prisma.FieldRef<"user_crafting_queue", 'Int'>
  readonly startedAt: Prisma.FieldRef<"user_crafting_queue", 'BigInt'>
  readonly endsAt: Prisma.FieldRef<"user_crafting_queue", 'BigInt'>
  readonly createdAt: Prisma.FieldRef<"user_crafting_queue", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_crafting_queue", 'DateTime'>
  readonly userId: Prisma.FieldRef<"user_crafting_queue", 'Int'>
  readonly craftingRecipeId: Prisma.FieldRef<"user_crafting_queue", 'Int'>
}
    

// Custom InputTypes
/**
 * user_crafting_queue findUnique
 */
export type user_crafting_queueFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter, which user_crafting_queue to fetch.
   */
  where: Prisma.user_crafting_queueWhereUniqueInput
}

/**
 * user_crafting_queue findUniqueOrThrow
 */
export type user_crafting_queueFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter, which user_crafting_queue to fetch.
   */
  where: Prisma.user_crafting_queueWhereUniqueInput
}

/**
 * user_crafting_queue findFirst
 */
export type user_crafting_queueFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter, which user_crafting_queue to fetch.
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_crafting_queues to fetch.
   */
  orderBy?: Prisma.user_crafting_queueOrderByWithRelationInput | Prisma.user_crafting_queueOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_crafting_queues.
   */
  cursor?: Prisma.user_crafting_queueWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_crafting_queues from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_crafting_queues.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_crafting_queues.
   */
  distinct?: Prisma.User_crafting_queueScalarFieldEnum | Prisma.User_crafting_queueScalarFieldEnum[]
}

/**
 * user_crafting_queue findFirstOrThrow
 */
export type user_crafting_queueFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter, which user_crafting_queue to fetch.
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_crafting_queues to fetch.
   */
  orderBy?: Prisma.user_crafting_queueOrderByWithRelationInput | Prisma.user_crafting_queueOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_crafting_queues.
   */
  cursor?: Prisma.user_crafting_queueWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_crafting_queues from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_crafting_queues.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_crafting_queues.
   */
  distinct?: Prisma.User_crafting_queueScalarFieldEnum | Prisma.User_crafting_queueScalarFieldEnum[]
}

/**
 * user_crafting_queue findMany
 */
export type user_crafting_queueFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter, which user_crafting_queues to fetch.
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_crafting_queues to fetch.
   */
  orderBy?: Prisma.user_crafting_queueOrderByWithRelationInput | Prisma.user_crafting_queueOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_crafting_queues.
   */
  cursor?: Prisma.user_crafting_queueWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_crafting_queues from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_crafting_queues.
   */
  skip?: number
  distinct?: Prisma.User_crafting_queueScalarFieldEnum | Prisma.User_crafting_queueScalarFieldEnum[]
}

/**
 * user_crafting_queue create
 */
export type user_crafting_queueCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * The data needed to create a user_crafting_queue.
   */
  data: Prisma.XOR<Prisma.user_crafting_queueCreateInput, Prisma.user_crafting_queueUncheckedCreateInput>
}

/**
 * user_crafting_queue createMany
 */
export type user_crafting_queueCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_crafting_queues.
   */
  data: Prisma.user_crafting_queueCreateManyInput | Prisma.user_crafting_queueCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_crafting_queue update
 */
export type user_crafting_queueUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * The data needed to update a user_crafting_queue.
   */
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateInput, Prisma.user_crafting_queueUncheckedUpdateInput>
  /**
   * Choose, which user_crafting_queue to update.
   */
  where: Prisma.user_crafting_queueWhereUniqueInput
}

/**
 * user_crafting_queue updateMany
 */
export type user_crafting_queueUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_crafting_queues.
   */
  data: Prisma.XOR<Prisma.user_crafting_queueUpdateManyMutationInput, Prisma.user_crafting_queueUncheckedUpdateManyInput>
  /**
   * Filter which user_crafting_queues to update
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * Limit how many user_crafting_queues to update.
   */
  limit?: number
}

/**
 * user_crafting_queue upsert
 */
export type user_crafting_queueUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * The filter to search for the user_crafting_queue to update in case it exists.
   */
  where: Prisma.user_crafting_queueWhereUniqueInput
  /**
   * In case the user_crafting_queue found by the `where` argument doesn't exist, create a new user_crafting_queue with this data.
   */
  create: Prisma.XOR<Prisma.user_crafting_queueCreateInput, Prisma.user_crafting_queueUncheckedCreateInput>
  /**
   * In case the user_crafting_queue was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_crafting_queueUpdateInput, Prisma.user_crafting_queueUncheckedUpdateInput>
}

/**
 * user_crafting_queue delete
 */
export type user_crafting_queueDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
  /**
   * Filter which user_crafting_queue to delete.
   */
  where: Prisma.user_crafting_queueWhereUniqueInput
}

/**
 * user_crafting_queue deleteMany
 */
export type user_crafting_queueDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_crafting_queues to delete
   */
  where?: Prisma.user_crafting_queueWhereInput
  /**
   * Limit how many user_crafting_queues to delete.
   */
  limit?: number
}

/**
 * user_crafting_queue.user
 */
export type user_crafting_queue$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * user_crafting_queue.crafting_recipe
 */
export type user_crafting_queue$crafting_recipeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  where?: Prisma.crafting_recipeWhereInput
}

/**
 * user_crafting_queue without action
 */
export type user_crafting_queueDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_crafting_queue
   */
  select?: Prisma.user_crafting_queueSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_crafting_queue
   */
  omit?: Prisma.user_crafting_queueOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_crafting_queueInclude<ExtArgs> | null
}
