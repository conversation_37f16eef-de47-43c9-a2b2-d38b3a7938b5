
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `quest_reward` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model quest_reward
 * 
 */
export type quest_rewardModel = runtime.Types.Result.DefaultSelection<Prisma.$quest_rewardPayload>

export type AggregateQuest_reward = {
  _count: Quest_rewardCountAggregateOutputType | null
  _avg: Quest_rewardAvgAggregateOutputType | null
  _sum: Quest_rewardSumAggregateOutputType | null
  _min: Quest_rewardMinAggregateOutputType | null
  _max: Quest_rewardMaxAggregateOutputType | null
}

export type Quest_rewardAvgAggregateOutputType = {
  id: number | null
  quantity: number | null
  questId: number | null
  dailyQuestId: number | null
  itemId: number | null
}

export type Quest_rewardSumAggregateOutputType = {
  id: number | null
  quantity: number | null
  questId: number | null
  dailyQuestId: number | null
  itemId: number | null
}

export type Quest_rewardMinAggregateOutputType = {
  id: number | null
  rewardType: $Enums.QuestRewardType | null
  quantity: number | null
  isChoice: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  questId: number | null
  dailyQuestId: number | null
  itemId: number | null
}

export type Quest_rewardMaxAggregateOutputType = {
  id: number | null
  rewardType: $Enums.QuestRewardType | null
  quantity: number | null
  isChoice: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  questId: number | null
  dailyQuestId: number | null
  itemId: number | null
}

export type Quest_rewardCountAggregateOutputType = {
  id: number
  rewardType: number
  quantity: number
  isChoice: number
  createdAt: number
  updatedAt: number
  questId: number
  dailyQuestId: number
  itemId: number
  _all: number
}


export type Quest_rewardAvgAggregateInputType = {
  id?: true
  quantity?: true
  questId?: true
  dailyQuestId?: true
  itemId?: true
}

export type Quest_rewardSumAggregateInputType = {
  id?: true
  quantity?: true
  questId?: true
  dailyQuestId?: true
  itemId?: true
}

export type Quest_rewardMinAggregateInputType = {
  id?: true
  rewardType?: true
  quantity?: true
  isChoice?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  dailyQuestId?: true
  itemId?: true
}

export type Quest_rewardMaxAggregateInputType = {
  id?: true
  rewardType?: true
  quantity?: true
  isChoice?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  dailyQuestId?: true
  itemId?: true
}

export type Quest_rewardCountAggregateInputType = {
  id?: true
  rewardType?: true
  quantity?: true
  isChoice?: true
  createdAt?: true
  updatedAt?: true
  questId?: true
  dailyQuestId?: true
  itemId?: true
  _all?: true
}

export type Quest_rewardAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_reward to aggregate.
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_rewards to fetch.
   */
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.quest_rewardWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_rewards from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_rewards.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned quest_rewards
  **/
  _count?: true | Quest_rewardCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Quest_rewardAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Quest_rewardSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Quest_rewardMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Quest_rewardMaxAggregateInputType
}

export type GetQuest_rewardAggregateType<T extends Quest_rewardAggregateArgs> = {
      [P in keyof T & keyof AggregateQuest_reward]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateQuest_reward[P]>
    : Prisma.GetScalarType<T[P], AggregateQuest_reward[P]>
}




export type quest_rewardGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_rewardWhereInput
  orderBy?: Prisma.quest_rewardOrderByWithAggregationInput | Prisma.quest_rewardOrderByWithAggregationInput[]
  by: Prisma.Quest_rewardScalarFieldEnum[] | Prisma.Quest_rewardScalarFieldEnum
  having?: Prisma.quest_rewardScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Quest_rewardCountAggregateInputType | true
  _avg?: Quest_rewardAvgAggregateInputType
  _sum?: Quest_rewardSumAggregateInputType
  _min?: Quest_rewardMinAggregateInputType
  _max?: Quest_rewardMaxAggregateInputType
}

export type Quest_rewardGroupByOutputType = {
  id: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice: boolean
  createdAt: Date
  updatedAt: Date
  questId: number | null
  dailyQuestId: number | null
  itemId: number | null
  _count: Quest_rewardCountAggregateOutputType | null
  _avg: Quest_rewardAvgAggregateOutputType | null
  _sum: Quest_rewardSumAggregateOutputType | null
  _min: Quest_rewardMinAggregateOutputType | null
  _max: Quest_rewardMaxAggregateOutputType | null
}

type GetQuest_rewardGroupByPayload<T extends quest_rewardGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Quest_rewardGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Quest_rewardGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Quest_rewardGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Quest_rewardGroupByOutputType[P]>
      }
    >
  >



export type quest_rewardWhereInput = {
  AND?: Prisma.quest_rewardWhereInput | Prisma.quest_rewardWhereInput[]
  OR?: Prisma.quest_rewardWhereInput[]
  NOT?: Prisma.quest_rewardWhereInput | Prisma.quest_rewardWhereInput[]
  id?: Prisma.IntFilter<"quest_reward"> | number
  rewardType?: Prisma.EnumQuestRewardTypeFilter<"quest_reward"> | $Enums.QuestRewardType
  quantity?: Prisma.IntFilter<"quest_reward"> | number
  isChoice?: Prisma.BoolFilter<"quest_reward"> | boolean
  createdAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  dailyQuestId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}

export type quest_rewardOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  rewardType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  isChoice?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  quest?: Prisma.questOrderByWithRelationInput
  item?: Prisma.itemOrderByWithRelationInput
}

export type quest_rewardWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.quest_rewardWhereInput | Prisma.quest_rewardWhereInput[]
  OR?: Prisma.quest_rewardWhereInput[]
  NOT?: Prisma.quest_rewardWhereInput | Prisma.quest_rewardWhereInput[]
  rewardType?: Prisma.EnumQuestRewardTypeFilter<"quest_reward"> | $Enums.QuestRewardType
  quantity?: Prisma.IntFilter<"quest_reward"> | number
  isChoice?: Prisma.BoolFilter<"quest_reward"> | boolean
  createdAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  dailyQuestId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}, "id">

export type quest_rewardOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  rewardType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  isChoice?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.quest_rewardCountOrderByAggregateInput
  _avg?: Prisma.quest_rewardAvgOrderByAggregateInput
  _max?: Prisma.quest_rewardMaxOrderByAggregateInput
  _min?: Prisma.quest_rewardMinOrderByAggregateInput
  _sum?: Prisma.quest_rewardSumOrderByAggregateInput
}

export type quest_rewardScalarWhereWithAggregatesInput = {
  AND?: Prisma.quest_rewardScalarWhereWithAggregatesInput | Prisma.quest_rewardScalarWhereWithAggregatesInput[]
  OR?: Prisma.quest_rewardScalarWhereWithAggregatesInput[]
  NOT?: Prisma.quest_rewardScalarWhereWithAggregatesInput | Prisma.quest_rewardScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"quest_reward"> | number
  rewardType?: Prisma.EnumQuestRewardTypeWithAggregatesFilter<"quest_reward"> | $Enums.QuestRewardType
  quantity?: Prisma.IntWithAggregatesFilter<"quest_reward"> | number
  isChoice?: Prisma.BoolWithAggregatesFilter<"quest_reward"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"quest_reward"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"quest_reward"> | Date | string
  questId?: Prisma.IntNullableWithAggregatesFilter<"quest_reward"> | number | null
  dailyQuestId?: Prisma.IntNullableWithAggregatesFilter<"quest_reward"> | number | null
  itemId?: Prisma.IntNullableWithAggregatesFilter<"quest_reward"> | number | null
}

export type quest_rewardCreateInput = {
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  dailyQuestId?: number | null
  quest?: Prisma.questCreateNestedOneWithoutQuest_rewardInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_rewardInput
}

export type quest_rewardUncheckedCreateInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  dailyQuestId?: number | null
  itemId?: number | null
}

export type quest_rewardUpdateInput = {
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest?: Prisma.questUpdateOneWithoutQuest_rewardNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_rewardNestedInput
}

export type quest_rewardUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_rewardCreateManyInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  dailyQuestId?: number | null
  itemId?: number | null
}

export type quest_rewardUpdateManyMutationInput = {
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_rewardUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Quest_rewardListRelationFilter = {
  every?: Prisma.quest_rewardWhereInput
  some?: Prisma.quest_rewardWhereInput
  none?: Prisma.quest_rewardWhereInput
}

export type quest_rewardOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type quest_rewardCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rewardType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  isChoice?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_rewardAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_rewardMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rewardType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  isChoice?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_rewardMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  rewardType?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  isChoice?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_rewardSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  dailyQuestId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_rewardCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput> | Prisma.quest_rewardCreateWithoutItemInput[] | Prisma.quest_rewardUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutItemInput | Prisma.quest_rewardCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.quest_rewardCreateManyItemInputEnvelope
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
}

export type quest_rewardUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput> | Prisma.quest_rewardCreateWithoutItemInput[] | Prisma.quest_rewardUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutItemInput | Prisma.quest_rewardCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.quest_rewardCreateManyItemInputEnvelope
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
}

export type quest_rewardUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput> | Prisma.quest_rewardCreateWithoutItemInput[] | Prisma.quest_rewardUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutItemInput | Prisma.quest_rewardCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.quest_rewardUpsertWithWhereUniqueWithoutItemInput | Prisma.quest_rewardUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.quest_rewardCreateManyItemInputEnvelope
  set?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  disconnect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  delete?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  update?: Prisma.quest_rewardUpdateWithWhereUniqueWithoutItemInput | Prisma.quest_rewardUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.quest_rewardUpdateManyWithWhereWithoutItemInput | Prisma.quest_rewardUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
}

export type quest_rewardUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput> | Prisma.quest_rewardCreateWithoutItemInput[] | Prisma.quest_rewardUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutItemInput | Prisma.quest_rewardCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.quest_rewardUpsertWithWhereUniqueWithoutItemInput | Prisma.quest_rewardUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.quest_rewardCreateManyItemInputEnvelope
  set?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  disconnect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  delete?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  update?: Prisma.quest_rewardUpdateWithWhereUniqueWithoutItemInput | Prisma.quest_rewardUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.quest_rewardUpdateManyWithWhereWithoutItemInput | Prisma.quest_rewardUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
}

export type quest_rewardCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput> | Prisma.quest_rewardCreateWithoutQuestInput[] | Prisma.quest_rewardUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutQuestInput | Prisma.quest_rewardCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_rewardCreateManyQuestInputEnvelope
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
}

export type quest_rewardUncheckedCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput> | Prisma.quest_rewardCreateWithoutQuestInput[] | Prisma.quest_rewardUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutQuestInput | Prisma.quest_rewardCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_rewardCreateManyQuestInputEnvelope
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
}

export type quest_rewardUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput> | Prisma.quest_rewardCreateWithoutQuestInput[] | Prisma.quest_rewardUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutQuestInput | Prisma.quest_rewardCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_rewardUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_rewardUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_rewardCreateManyQuestInputEnvelope
  set?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  disconnect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  delete?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  update?: Prisma.quest_rewardUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_rewardUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_rewardUpdateManyWithWhereWithoutQuestInput | Prisma.quest_rewardUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
}

export type quest_rewardUncheckedUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput> | Prisma.quest_rewardCreateWithoutQuestInput[] | Prisma.quest_rewardUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_rewardCreateOrConnectWithoutQuestInput | Prisma.quest_rewardCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_rewardUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_rewardUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_rewardCreateManyQuestInputEnvelope
  set?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  disconnect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  delete?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  connect?: Prisma.quest_rewardWhereUniqueInput | Prisma.quest_rewardWhereUniqueInput[]
  update?: Prisma.quest_rewardUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_rewardUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_rewardUpdateManyWithWhereWithoutQuestInput | Prisma.quest_rewardUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
}

export type EnumQuestRewardTypeFieldUpdateOperationsInput = {
  set?: $Enums.QuestRewardType
}

export type quest_rewardCreateWithoutItemInput = {
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  dailyQuestId?: number | null
  quest?: Prisma.questCreateNestedOneWithoutQuest_rewardInput
}

export type quest_rewardUncheckedCreateWithoutItemInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  dailyQuestId?: number | null
}

export type quest_rewardCreateOrConnectWithoutItemInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput>
}

export type quest_rewardCreateManyItemInputEnvelope = {
  data: Prisma.quest_rewardCreateManyItemInput | Prisma.quest_rewardCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type quest_rewardUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_rewardUpdateWithoutItemInput, Prisma.quest_rewardUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.quest_rewardCreateWithoutItemInput, Prisma.quest_rewardUncheckedCreateWithoutItemInput>
}

export type quest_rewardUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_rewardUpdateWithoutItemInput, Prisma.quest_rewardUncheckedUpdateWithoutItemInput>
}

export type quest_rewardUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.quest_rewardScalarWhereInput
  data: Prisma.XOR<Prisma.quest_rewardUpdateManyMutationInput, Prisma.quest_rewardUncheckedUpdateManyWithoutItemInput>
}

export type quest_rewardScalarWhereInput = {
  AND?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
  OR?: Prisma.quest_rewardScalarWhereInput[]
  NOT?: Prisma.quest_rewardScalarWhereInput | Prisma.quest_rewardScalarWhereInput[]
  id?: Prisma.IntFilter<"quest_reward"> | number
  rewardType?: Prisma.EnumQuestRewardTypeFilter<"quest_reward"> | $Enums.QuestRewardType
  quantity?: Prisma.IntFilter<"quest_reward"> | number
  isChoice?: Prisma.BoolFilter<"quest_reward"> | boolean
  createdAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"quest_reward"> | Date | string
  questId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  dailyQuestId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_reward"> | number | null
}

export type quest_rewardCreateWithoutQuestInput = {
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  dailyQuestId?: number | null
  item?: Prisma.itemCreateNestedOneWithoutQuest_rewardInput
}

export type quest_rewardUncheckedCreateWithoutQuestInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  dailyQuestId?: number | null
  itemId?: number | null
}

export type quest_rewardCreateOrConnectWithoutQuestInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput>
}

export type quest_rewardCreateManyQuestInputEnvelope = {
  data: Prisma.quest_rewardCreateManyQuestInput | Prisma.quest_rewardCreateManyQuestInput[]
  skipDuplicates?: boolean
}

export type quest_rewardUpsertWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_rewardUpdateWithoutQuestInput, Prisma.quest_rewardUncheckedUpdateWithoutQuestInput>
  create: Prisma.XOR<Prisma.quest_rewardCreateWithoutQuestInput, Prisma.quest_rewardUncheckedCreateWithoutQuestInput>
}

export type quest_rewardUpdateWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_rewardWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_rewardUpdateWithoutQuestInput, Prisma.quest_rewardUncheckedUpdateWithoutQuestInput>
}

export type quest_rewardUpdateManyWithWhereWithoutQuestInput = {
  where: Prisma.quest_rewardScalarWhereInput
  data: Prisma.XOR<Prisma.quest_rewardUpdateManyMutationInput, Prisma.quest_rewardUncheckedUpdateManyWithoutQuestInput>
}

export type quest_rewardCreateManyItemInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  questId?: number | null
  dailyQuestId?: number | null
}

export type quest_rewardUpdateWithoutItemInput = {
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest?: Prisma.questUpdateOneWithoutQuest_rewardNestedInput
}

export type quest_rewardUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_rewardUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_rewardCreateManyQuestInput = {
  id?: number
  rewardType: $Enums.QuestRewardType
  quantity: number
  isChoice?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  dailyQuestId?: number | null
  itemId?: number | null
}

export type quest_rewardUpdateWithoutQuestInput = {
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  item?: Prisma.itemUpdateOneWithoutQuest_rewardNestedInput
}

export type quest_rewardUncheckedUpdateWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_rewardUncheckedUpdateManyWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  rewardType?: Prisma.EnumQuestRewardTypeFieldUpdateOperationsInput | $Enums.QuestRewardType
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  isChoice?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dailyQuestId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type quest_rewardSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  rewardType?: boolean
  quantity?: boolean
  isChoice?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questId?: boolean
  dailyQuestId?: boolean
  itemId?: boolean
  quest?: boolean | Prisma.quest_reward$questArgs<ExtArgs>
  item?: boolean | Prisma.quest_reward$itemArgs<ExtArgs>
}, ExtArgs["result"]["quest_reward"]>



export type quest_rewardSelectScalar = {
  id?: boolean
  rewardType?: boolean
  quantity?: boolean
  isChoice?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  questId?: boolean
  dailyQuestId?: boolean
  itemId?: boolean
}

export type quest_rewardOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "rewardType" | "quantity" | "isChoice" | "createdAt" | "updatedAt" | "questId" | "dailyQuestId" | "itemId", ExtArgs["result"]["quest_reward"]>
export type quest_rewardInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest?: boolean | Prisma.quest_reward$questArgs<ExtArgs>
  item?: boolean | Prisma.quest_reward$itemArgs<ExtArgs>
}

export type $quest_rewardPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "quest_reward"
  objects: {
    quest: Prisma.$questPayload<ExtArgs> | null
    item: Prisma.$itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    rewardType: $Enums.QuestRewardType
    quantity: number
    isChoice: boolean
    createdAt: Date
    updatedAt: Date
    questId: number | null
    dailyQuestId: number | null
    itemId: number | null
  }, ExtArgs["result"]["quest_reward"]>
  composites: {}
}

export type quest_rewardGetPayload<S extends boolean | null | undefined | quest_rewardDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload, S>

export type quest_rewardCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<quest_rewardFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Quest_rewardCountAggregateInputType | true
  }

export interface quest_rewardDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['quest_reward'], meta: { name: 'quest_reward' } }
  /**
   * Find zero or one Quest_reward that matches the filter.
   * @param {quest_rewardFindUniqueArgs} args - Arguments to find a Quest_reward
   * @example
   * // Get one Quest_reward
   * const quest_reward = await prisma.quest_reward.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends quest_rewardFindUniqueArgs>(args: Prisma.SelectSubset<T, quest_rewardFindUniqueArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Quest_reward that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {quest_rewardFindUniqueOrThrowArgs} args - Arguments to find a Quest_reward
   * @example
   * // Get one Quest_reward
   * const quest_reward = await prisma.quest_reward.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends quest_rewardFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, quest_rewardFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_reward that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardFindFirstArgs} args - Arguments to find a Quest_reward
   * @example
   * // Get one Quest_reward
   * const quest_reward = await prisma.quest_reward.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends quest_rewardFindFirstArgs>(args?: Prisma.SelectSubset<T, quest_rewardFindFirstArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_reward that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardFindFirstOrThrowArgs} args - Arguments to find a Quest_reward
   * @example
   * // Get one Quest_reward
   * const quest_reward = await prisma.quest_reward.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends quest_rewardFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, quest_rewardFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Quest_rewards that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Quest_rewards
   * const quest_rewards = await prisma.quest_reward.findMany()
   * 
   * // Get first 10 Quest_rewards
   * const quest_rewards = await prisma.quest_reward.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const quest_rewardWithIdOnly = await prisma.quest_reward.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends quest_rewardFindManyArgs>(args?: Prisma.SelectSubset<T, quest_rewardFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Quest_reward.
   * @param {quest_rewardCreateArgs} args - Arguments to create a Quest_reward.
   * @example
   * // Create one Quest_reward
   * const Quest_reward = await prisma.quest_reward.create({
   *   data: {
   *     // ... data to create a Quest_reward
   *   }
   * })
   * 
   */
  create<T extends quest_rewardCreateArgs>(args: Prisma.SelectSubset<T, quest_rewardCreateArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Quest_rewards.
   * @param {quest_rewardCreateManyArgs} args - Arguments to create many Quest_rewards.
   * @example
   * // Create many Quest_rewards
   * const quest_reward = await prisma.quest_reward.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends quest_rewardCreateManyArgs>(args?: Prisma.SelectSubset<T, quest_rewardCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Quest_reward.
   * @param {quest_rewardDeleteArgs} args - Arguments to delete one Quest_reward.
   * @example
   * // Delete one Quest_reward
   * const Quest_reward = await prisma.quest_reward.delete({
   *   where: {
   *     // ... filter to delete one Quest_reward
   *   }
   * })
   * 
   */
  delete<T extends quest_rewardDeleteArgs>(args: Prisma.SelectSubset<T, quest_rewardDeleteArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Quest_reward.
   * @param {quest_rewardUpdateArgs} args - Arguments to update one Quest_reward.
   * @example
   * // Update one Quest_reward
   * const quest_reward = await prisma.quest_reward.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends quest_rewardUpdateArgs>(args: Prisma.SelectSubset<T, quest_rewardUpdateArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Quest_rewards.
   * @param {quest_rewardDeleteManyArgs} args - Arguments to filter Quest_rewards to delete.
   * @example
   * // Delete a few Quest_rewards
   * const { count } = await prisma.quest_reward.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends quest_rewardDeleteManyArgs>(args?: Prisma.SelectSubset<T, quest_rewardDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Quest_rewards.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Quest_rewards
   * const quest_reward = await prisma.quest_reward.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends quest_rewardUpdateManyArgs>(args: Prisma.SelectSubset<T, quest_rewardUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Quest_reward.
   * @param {quest_rewardUpsertArgs} args - Arguments to update or create a Quest_reward.
   * @example
   * // Update or create a Quest_reward
   * const quest_reward = await prisma.quest_reward.upsert({
   *   create: {
   *     // ... data to create a Quest_reward
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Quest_reward we want to update
   *   }
   * })
   */
  upsert<T extends quest_rewardUpsertArgs>(args: Prisma.SelectSubset<T, quest_rewardUpsertArgs<ExtArgs>>): Prisma.Prisma__quest_rewardClient<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Quest_rewards.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardCountArgs} args - Arguments to filter Quest_rewards to count.
   * @example
   * // Count the number of Quest_rewards
   * const count = await prisma.quest_reward.count({
   *   where: {
   *     // ... the filter for the Quest_rewards we want to count
   *   }
   * })
  **/
  count<T extends quest_rewardCountArgs>(
    args?: Prisma.Subset<T, quest_rewardCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Quest_rewardCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Quest_reward.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Quest_rewardAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Quest_rewardAggregateArgs>(args: Prisma.Subset<T, Quest_rewardAggregateArgs>): Prisma.PrismaPromise<GetQuest_rewardAggregateType<T>>

  /**
   * Group by Quest_reward.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_rewardGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends quest_rewardGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: quest_rewardGroupByArgs['orderBy'] }
      : { orderBy?: quest_rewardGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, quest_rewardGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQuest_rewardGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the quest_reward model
 */
readonly fields: quest_rewardFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for quest_reward.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__quest_rewardClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest<T extends Prisma.quest_reward$questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_reward$questArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  item<T extends Prisma.quest_reward$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_reward$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the quest_reward model
 */
export interface quest_rewardFieldRefs {
  readonly id: Prisma.FieldRef<"quest_reward", 'Int'>
  readonly rewardType: Prisma.FieldRef<"quest_reward", 'QuestRewardType'>
  readonly quantity: Prisma.FieldRef<"quest_reward", 'Int'>
  readonly isChoice: Prisma.FieldRef<"quest_reward", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"quest_reward", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"quest_reward", 'DateTime'>
  readonly questId: Prisma.FieldRef<"quest_reward", 'Int'>
  readonly dailyQuestId: Prisma.FieldRef<"quest_reward", 'Int'>
  readonly itemId: Prisma.FieldRef<"quest_reward", 'Int'>
}
    

// Custom InputTypes
/**
 * quest_reward findUnique
 */
export type quest_rewardFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter, which quest_reward to fetch.
   */
  where: Prisma.quest_rewardWhereUniqueInput
}

/**
 * quest_reward findUniqueOrThrow
 */
export type quest_rewardFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter, which quest_reward to fetch.
   */
  where: Prisma.quest_rewardWhereUniqueInput
}

/**
 * quest_reward findFirst
 */
export type quest_rewardFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter, which quest_reward to fetch.
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_rewards to fetch.
   */
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_rewards.
   */
  cursor?: Prisma.quest_rewardWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_rewards from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_rewards.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_rewards.
   */
  distinct?: Prisma.Quest_rewardScalarFieldEnum | Prisma.Quest_rewardScalarFieldEnum[]
}

/**
 * quest_reward findFirstOrThrow
 */
export type quest_rewardFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter, which quest_reward to fetch.
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_rewards to fetch.
   */
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_rewards.
   */
  cursor?: Prisma.quest_rewardWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_rewards from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_rewards.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_rewards.
   */
  distinct?: Prisma.Quest_rewardScalarFieldEnum | Prisma.Quest_rewardScalarFieldEnum[]
}

/**
 * quest_reward findMany
 */
export type quest_rewardFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter, which quest_rewards to fetch.
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_rewards to fetch.
   */
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing quest_rewards.
   */
  cursor?: Prisma.quest_rewardWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_rewards from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_rewards.
   */
  skip?: number
  distinct?: Prisma.Quest_rewardScalarFieldEnum | Prisma.Quest_rewardScalarFieldEnum[]
}

/**
 * quest_reward create
 */
export type quest_rewardCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * The data needed to create a quest_reward.
   */
  data: Prisma.XOR<Prisma.quest_rewardCreateInput, Prisma.quest_rewardUncheckedCreateInput>
}

/**
 * quest_reward createMany
 */
export type quest_rewardCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many quest_rewards.
   */
  data: Prisma.quest_rewardCreateManyInput | Prisma.quest_rewardCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * quest_reward update
 */
export type quest_rewardUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * The data needed to update a quest_reward.
   */
  data: Prisma.XOR<Prisma.quest_rewardUpdateInput, Prisma.quest_rewardUncheckedUpdateInput>
  /**
   * Choose, which quest_reward to update.
   */
  where: Prisma.quest_rewardWhereUniqueInput
}

/**
 * quest_reward updateMany
 */
export type quest_rewardUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update quest_rewards.
   */
  data: Prisma.XOR<Prisma.quest_rewardUpdateManyMutationInput, Prisma.quest_rewardUncheckedUpdateManyInput>
  /**
   * Filter which quest_rewards to update
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * Limit how many quest_rewards to update.
   */
  limit?: number
}

/**
 * quest_reward upsert
 */
export type quest_rewardUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * The filter to search for the quest_reward to update in case it exists.
   */
  where: Prisma.quest_rewardWhereUniqueInput
  /**
   * In case the quest_reward found by the `where` argument doesn't exist, create a new quest_reward with this data.
   */
  create: Prisma.XOR<Prisma.quest_rewardCreateInput, Prisma.quest_rewardUncheckedCreateInput>
  /**
   * In case the quest_reward was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.quest_rewardUpdateInput, Prisma.quest_rewardUncheckedUpdateInput>
}

/**
 * quest_reward delete
 */
export type quest_rewardDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  /**
   * Filter which quest_reward to delete.
   */
  where: Prisma.quest_rewardWhereUniqueInput
}

/**
 * quest_reward deleteMany
 */
export type quest_rewardDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_rewards to delete
   */
  where?: Prisma.quest_rewardWhereInput
  /**
   * Limit how many quest_rewards to delete.
   */
  limit?: number
}

/**
 * quest_reward.quest
 */
export type quest_reward$questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
}

/**
 * quest_reward.item
 */
export type quest_reward$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * quest_reward without action
 */
export type quest_rewardDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
}
