
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_property` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_property
 * 
 */
export type user_propertyModel = runtime.Types.Result.DefaultSelection<Prisma.$user_propertyPayload>

export type AggregateUser_property = {
  _count: User_propertyCountAggregateOutputType | null
  _avg: User_propertyAvgAggregateOutputType | null
  _sum: User_propertySumAggregateOutputType | null
  _min: User_propertyMinAggregateOutputType | null
  _max: User_propertyMaxAggregateOutputType | null
}

export type User_propertyAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  propertyId: number | null
}

export type User_propertySumAggregateOutputType = {
  id: number | null
  userId: number | null
  propertyId: number | null
}

export type User_propertyMinAggregateOutputType = {
  id: number | null
  purchaseDate: Date | null
  lastUpkeepPaid: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  propertyId: number | null
}

export type User_propertyMaxAggregateOutputType = {
  id: number | null
  purchaseDate: Date | null
  lastUpkeepPaid: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  propertyId: number | null
}

export type User_propertyCountAggregateOutputType = {
  id: number
  purchaseDate: number
  lastUpkeepPaid: number
  furniture: number
  customization: number
  createdAt: number
  updatedAt: number
  userId: number
  propertyId: number
  _all: number
}


export type User_propertyAvgAggregateInputType = {
  id?: true
  userId?: true
  propertyId?: true
}

export type User_propertySumAggregateInputType = {
  id?: true
  userId?: true
  propertyId?: true
}

export type User_propertyMinAggregateInputType = {
  id?: true
  purchaseDate?: true
  lastUpkeepPaid?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  propertyId?: true
}

export type User_propertyMaxAggregateInputType = {
  id?: true
  purchaseDate?: true
  lastUpkeepPaid?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  propertyId?: true
}

export type User_propertyCountAggregateInputType = {
  id?: true
  purchaseDate?: true
  lastUpkeepPaid?: true
  furniture?: true
  customization?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  propertyId?: true
  _all?: true
}

export type User_propertyAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_property to aggregate.
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_properties to fetch.
   */
  orderBy?: Prisma.user_propertyOrderByWithRelationInput | Prisma.user_propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_properties
  **/
  _count?: true | User_propertyCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_propertyAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_propertySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_propertyMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_propertyMaxAggregateInputType
}

export type GetUser_propertyAggregateType<T extends User_propertyAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_property]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_property[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_property[P]>
}




export type user_propertyGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_propertyWhereInput
  orderBy?: Prisma.user_propertyOrderByWithAggregationInput | Prisma.user_propertyOrderByWithAggregationInput[]
  by: Prisma.User_propertyScalarFieldEnum[] | Prisma.User_propertyScalarFieldEnum
  having?: Prisma.user_propertyScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_propertyCountAggregateInputType | true
  _avg?: User_propertyAvgAggregateInputType
  _sum?: User_propertySumAggregateInputType
  _min?: User_propertyMinAggregateInputType
  _max?: User_propertyMaxAggregateInputType
}

export type User_propertyGroupByOutputType = {
  id: number
  purchaseDate: Date
  lastUpkeepPaid: Date
  furniture: runtime.JsonValue
  customization: runtime.JsonValue
  createdAt: Date
  updatedAt: Date
  userId: number
  propertyId: number
  _count: User_propertyCountAggregateOutputType | null
  _avg: User_propertyAvgAggregateOutputType | null
  _sum: User_propertySumAggregateOutputType | null
  _min: User_propertyMinAggregateOutputType | null
  _max: User_propertyMaxAggregateOutputType | null
}

type GetUser_propertyGroupByPayload<T extends user_propertyGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_propertyGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_propertyGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_propertyGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_propertyGroupByOutputType[P]>
      }
    >
  >



export type user_propertyWhereInput = {
  AND?: Prisma.user_propertyWhereInput | Prisma.user_propertyWhereInput[]
  OR?: Prisma.user_propertyWhereInput[]
  NOT?: Prisma.user_propertyWhereInput | Prisma.user_propertyWhereInput[]
  id?: Prisma.IntFilter<"user_property"> | number
  purchaseDate?: Prisma.DateTimeFilter<"user_property"> | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFilter<"user_property"> | Date | string
  furniture?: Prisma.JsonFilter<"user_property">
  customization?: Prisma.JsonFilter<"user_property">
  createdAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  userId?: Prisma.IntFilter<"user_property"> | number
  propertyId?: Prisma.IntFilter<"user_property"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  property?: Prisma.XOR<Prisma.PropertyScalarRelationFilter, Prisma.propertyWhereInput>
}

export type user_propertyOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  purchaseDate?: Prisma.SortOrder
  lastUpkeepPaid?: Prisma.SortOrder
  furniture?: Prisma.SortOrder
  customization?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  property?: Prisma.propertyOrderByWithRelationInput
}

export type user_propertyWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.user_propertyWhereInput | Prisma.user_propertyWhereInput[]
  OR?: Prisma.user_propertyWhereInput[]
  NOT?: Prisma.user_propertyWhereInput | Prisma.user_propertyWhereInput[]
  purchaseDate?: Prisma.DateTimeFilter<"user_property"> | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFilter<"user_property"> | Date | string
  furniture?: Prisma.JsonFilter<"user_property">
  customization?: Prisma.JsonFilter<"user_property">
  createdAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  userId?: Prisma.IntFilter<"user_property"> | number
  propertyId?: Prisma.IntFilter<"user_property"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  property?: Prisma.XOR<Prisma.PropertyScalarRelationFilter, Prisma.propertyWhereInput>
}, "id">

export type user_propertyOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  purchaseDate?: Prisma.SortOrder
  lastUpkeepPaid?: Prisma.SortOrder
  furniture?: Prisma.SortOrder
  customization?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
  _count?: Prisma.user_propertyCountOrderByAggregateInput
  _avg?: Prisma.user_propertyAvgOrderByAggregateInput
  _max?: Prisma.user_propertyMaxOrderByAggregateInput
  _min?: Prisma.user_propertyMinOrderByAggregateInput
  _sum?: Prisma.user_propertySumOrderByAggregateInput
}

export type user_propertyScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_propertyScalarWhereWithAggregatesInput | Prisma.user_propertyScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_propertyScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_propertyScalarWhereWithAggregatesInput | Prisma.user_propertyScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_property"> | number
  purchaseDate?: Prisma.DateTimeWithAggregatesFilter<"user_property"> | Date | string
  lastUpkeepPaid?: Prisma.DateTimeWithAggregatesFilter<"user_property"> | Date | string
  furniture?: Prisma.JsonWithAggregatesFilter<"user_property">
  customization?: Prisma.JsonWithAggregatesFilter<"user_property">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_property"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_property"> | Date | string
  userId?: Prisma.IntWithAggregatesFilter<"user_property"> | number
  propertyId?: Prisma.IntWithAggregatesFilter<"user_property"> | number
}

export type user_propertyCreateInput = {
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_propertyInput
  property: Prisma.propertyCreateNestedOneWithoutUser_propertyInput
}

export type user_propertyUncheckedCreateInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  propertyId: number
}

export type user_propertyUpdateInput = {
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_propertyNestedInput
  property?: Prisma.propertyUpdateOneRequiredWithoutUser_propertyNestedInput
}

export type user_propertyUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  propertyId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_propertyCreateManyInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  propertyId: number
}

export type user_propertyUpdateManyMutationInput = {
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_propertyUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  propertyId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type User_propertyListRelationFilter = {
  every?: Prisma.user_propertyWhereInput
  some?: Prisma.user_propertyWhereInput
  none?: Prisma.user_propertyWhereInput
}

export type user_propertyOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_propertyCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  purchaseDate?: Prisma.SortOrder
  lastUpkeepPaid?: Prisma.SortOrder
  furniture?: Prisma.SortOrder
  customization?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
}

export type user_propertyAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
}

export type user_propertyMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  purchaseDate?: Prisma.SortOrder
  lastUpkeepPaid?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
}

export type user_propertyMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  purchaseDate?: Prisma.SortOrder
  lastUpkeepPaid?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
}

export type user_propertySumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  propertyId?: Prisma.SortOrder
}

export type user_propertyCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput> | Prisma.user_propertyCreateWithoutUserInput[] | Prisma.user_propertyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutUserInput | Prisma.user_propertyCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_propertyCreateManyUserInputEnvelope
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
}

export type user_propertyUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput> | Prisma.user_propertyCreateWithoutUserInput[] | Prisma.user_propertyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutUserInput | Prisma.user_propertyCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_propertyCreateManyUserInputEnvelope
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
}

export type user_propertyUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput> | Prisma.user_propertyCreateWithoutUserInput[] | Prisma.user_propertyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutUserInput | Prisma.user_propertyCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_propertyUpsertWithWhereUniqueWithoutUserInput | Prisma.user_propertyUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_propertyCreateManyUserInputEnvelope
  set?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  disconnect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  delete?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  update?: Prisma.user_propertyUpdateWithWhereUniqueWithoutUserInput | Prisma.user_propertyUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_propertyUpdateManyWithWhereWithoutUserInput | Prisma.user_propertyUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
}

export type user_propertyUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput> | Prisma.user_propertyCreateWithoutUserInput[] | Prisma.user_propertyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutUserInput | Prisma.user_propertyCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_propertyUpsertWithWhereUniqueWithoutUserInput | Prisma.user_propertyUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_propertyCreateManyUserInputEnvelope
  set?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  disconnect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  delete?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  update?: Prisma.user_propertyUpdateWithWhereUniqueWithoutUserInput | Prisma.user_propertyUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_propertyUpdateManyWithWhereWithoutUserInput | Prisma.user_propertyUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
}

export type user_propertyCreateNestedManyWithoutPropertyInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput> | Prisma.user_propertyCreateWithoutPropertyInput[] | Prisma.user_propertyUncheckedCreateWithoutPropertyInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutPropertyInput | Prisma.user_propertyCreateOrConnectWithoutPropertyInput[]
  createMany?: Prisma.user_propertyCreateManyPropertyInputEnvelope
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
}

export type user_propertyUncheckedCreateNestedManyWithoutPropertyInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput> | Prisma.user_propertyCreateWithoutPropertyInput[] | Prisma.user_propertyUncheckedCreateWithoutPropertyInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutPropertyInput | Prisma.user_propertyCreateOrConnectWithoutPropertyInput[]
  createMany?: Prisma.user_propertyCreateManyPropertyInputEnvelope
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
}

export type user_propertyUpdateManyWithoutPropertyNestedInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput> | Prisma.user_propertyCreateWithoutPropertyInput[] | Prisma.user_propertyUncheckedCreateWithoutPropertyInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutPropertyInput | Prisma.user_propertyCreateOrConnectWithoutPropertyInput[]
  upsert?: Prisma.user_propertyUpsertWithWhereUniqueWithoutPropertyInput | Prisma.user_propertyUpsertWithWhereUniqueWithoutPropertyInput[]
  createMany?: Prisma.user_propertyCreateManyPropertyInputEnvelope
  set?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  disconnect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  delete?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  update?: Prisma.user_propertyUpdateWithWhereUniqueWithoutPropertyInput | Prisma.user_propertyUpdateWithWhereUniqueWithoutPropertyInput[]
  updateMany?: Prisma.user_propertyUpdateManyWithWhereWithoutPropertyInput | Prisma.user_propertyUpdateManyWithWhereWithoutPropertyInput[]
  deleteMany?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
}

export type user_propertyUncheckedUpdateManyWithoutPropertyNestedInput = {
  create?: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput> | Prisma.user_propertyCreateWithoutPropertyInput[] | Prisma.user_propertyUncheckedCreateWithoutPropertyInput[]
  connectOrCreate?: Prisma.user_propertyCreateOrConnectWithoutPropertyInput | Prisma.user_propertyCreateOrConnectWithoutPropertyInput[]
  upsert?: Prisma.user_propertyUpsertWithWhereUniqueWithoutPropertyInput | Prisma.user_propertyUpsertWithWhereUniqueWithoutPropertyInput[]
  createMany?: Prisma.user_propertyCreateManyPropertyInputEnvelope
  set?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  disconnect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  delete?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  connect?: Prisma.user_propertyWhereUniqueInput | Prisma.user_propertyWhereUniqueInput[]
  update?: Prisma.user_propertyUpdateWithWhereUniqueWithoutPropertyInput | Prisma.user_propertyUpdateWithWhereUniqueWithoutPropertyInput[]
  updateMany?: Prisma.user_propertyUpdateManyWithWhereWithoutPropertyInput | Prisma.user_propertyUpdateManyWithWhereWithoutPropertyInput[]
  deleteMany?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
}

export type user_propertyCreateWithoutUserInput = {
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  property: Prisma.propertyCreateNestedOneWithoutUser_propertyInput
}

export type user_propertyUncheckedCreateWithoutUserInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  propertyId: number
}

export type user_propertyCreateOrConnectWithoutUserInput = {
  where: Prisma.user_propertyWhereUniqueInput
  create: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput>
}

export type user_propertyCreateManyUserInputEnvelope = {
  data: Prisma.user_propertyCreateManyUserInput | Prisma.user_propertyCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_propertyUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_propertyWhereUniqueInput
  update: Prisma.XOR<Prisma.user_propertyUpdateWithoutUserInput, Prisma.user_propertyUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_propertyCreateWithoutUserInput, Prisma.user_propertyUncheckedCreateWithoutUserInput>
}

export type user_propertyUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_propertyWhereUniqueInput
  data: Prisma.XOR<Prisma.user_propertyUpdateWithoutUserInput, Prisma.user_propertyUncheckedUpdateWithoutUserInput>
}

export type user_propertyUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_propertyScalarWhereInput
  data: Prisma.XOR<Prisma.user_propertyUpdateManyMutationInput, Prisma.user_propertyUncheckedUpdateManyWithoutUserInput>
}

export type user_propertyScalarWhereInput = {
  AND?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
  OR?: Prisma.user_propertyScalarWhereInput[]
  NOT?: Prisma.user_propertyScalarWhereInput | Prisma.user_propertyScalarWhereInput[]
  id?: Prisma.IntFilter<"user_property"> | number
  purchaseDate?: Prisma.DateTimeFilter<"user_property"> | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFilter<"user_property"> | Date | string
  furniture?: Prisma.JsonFilter<"user_property">
  customization?: Prisma.JsonFilter<"user_property">
  createdAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_property"> | Date | string
  userId?: Prisma.IntFilter<"user_property"> | number
  propertyId?: Prisma.IntFilter<"user_property"> | number
}

export type user_propertyCreateWithoutPropertyInput = {
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_propertyInput
}

export type user_propertyUncheckedCreateWithoutPropertyInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_propertyCreateOrConnectWithoutPropertyInput = {
  where: Prisma.user_propertyWhereUniqueInput
  create: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput>
}

export type user_propertyCreateManyPropertyInputEnvelope = {
  data: Prisma.user_propertyCreateManyPropertyInput | Prisma.user_propertyCreateManyPropertyInput[]
  skipDuplicates?: boolean
}

export type user_propertyUpsertWithWhereUniqueWithoutPropertyInput = {
  where: Prisma.user_propertyWhereUniqueInput
  update: Prisma.XOR<Prisma.user_propertyUpdateWithoutPropertyInput, Prisma.user_propertyUncheckedUpdateWithoutPropertyInput>
  create: Prisma.XOR<Prisma.user_propertyCreateWithoutPropertyInput, Prisma.user_propertyUncheckedCreateWithoutPropertyInput>
}

export type user_propertyUpdateWithWhereUniqueWithoutPropertyInput = {
  where: Prisma.user_propertyWhereUniqueInput
  data: Prisma.XOR<Prisma.user_propertyUpdateWithoutPropertyInput, Prisma.user_propertyUncheckedUpdateWithoutPropertyInput>
}

export type user_propertyUpdateManyWithWhereWithoutPropertyInput = {
  where: Prisma.user_propertyScalarWhereInput
  data: Prisma.XOR<Prisma.user_propertyUpdateManyMutationInput, Prisma.user_propertyUncheckedUpdateManyWithoutPropertyInput>
}

export type user_propertyCreateManyUserInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  propertyId: number
}

export type user_propertyUpdateWithoutUserInput = {
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  property?: Prisma.propertyUpdateOneRequiredWithoutUser_propertyNestedInput
}

export type user_propertyUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  propertyId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_propertyUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  propertyId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_propertyCreateManyPropertyInput = {
  id?: number
  purchaseDate?: Date | string
  lastUpkeepPaid?: Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_propertyUpdateWithoutPropertyInput = {
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_propertyNestedInput
}

export type user_propertyUncheckedUpdateWithoutPropertyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_propertyUncheckedUpdateManyWithoutPropertyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  purchaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastUpkeepPaid?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  furniture?:unknown
  customization?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type user_propertySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  purchaseDate?: boolean
  lastUpkeepPaid?: boolean
  furniture?: boolean
  customization?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  propertyId?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  property?: boolean | Prisma.propertyDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_property"]>



export type user_propertySelectScalar = {
  id?: boolean
  purchaseDate?: boolean
  lastUpkeepPaid?: boolean
  furniture?: boolean
  customization?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  propertyId?: boolean
}

export type user_propertyOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "purchaseDate" | "lastUpkeepPaid" | "furniture" | "customization" | "createdAt" | "updatedAt" | "userId" | "propertyId", ExtArgs["result"]["user_property"]>
export type user_propertyInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  property?: boolean | Prisma.propertyDefaultArgs<ExtArgs>
}

export type $user_propertyPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_property"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    property: Prisma.$propertyPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    purchaseDate: Date
    lastUpkeepPaid: Date
    furniture:unknown
    customization:unknown
    createdAt: Date
    updatedAt: Date
    userId: number
    propertyId: number
  }, ExtArgs["result"]["user_property"]>
  composites: {}
}

export type user_propertyGetPayload<S extends boolean | null | undefined | user_propertyDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_propertyPayload, S>

export type user_propertyCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_propertyFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_propertyCountAggregateInputType | true
  }

export interface user_propertyDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_property'], meta: { name: 'user_property' } }
  /**
   * Find zero or one User_property that matches the filter.
   * @param {user_propertyFindUniqueArgs} args - Arguments to find a User_property
   * @example
   * // Get one User_property
   * const user_property = await prisma.user_property.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_propertyFindUniqueArgs>(args: Prisma.SelectSubset<T, user_propertyFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_property that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_propertyFindUniqueOrThrowArgs} args - Arguments to find a User_property
   * @example
   * // Get one User_property
   * const user_property = await prisma.user_property.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_propertyFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_propertyFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_property that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyFindFirstArgs} args - Arguments to find a User_property
   * @example
   * // Get one User_property
   * const user_property = await prisma.user_property.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_propertyFindFirstArgs>(args?: Prisma.SelectSubset<T, user_propertyFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_property that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyFindFirstOrThrowArgs} args - Arguments to find a User_property
   * @example
   * // Get one User_property
   * const user_property = await prisma.user_property.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_propertyFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_propertyFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_properties that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_properties
   * const user_properties = await prisma.user_property.findMany()
   * 
   * // Get first 10 User_properties
   * const user_properties = await prisma.user_property.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_propertyWithIdOnly = await prisma.user_property.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_propertyFindManyArgs>(args?: Prisma.SelectSubset<T, user_propertyFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_property.
   * @param {user_propertyCreateArgs} args - Arguments to create a User_property.
   * @example
   * // Create one User_property
   * const User_property = await prisma.user_property.create({
   *   data: {
   *     // ... data to create a User_property
   *   }
   * })
   * 
   */
  create<T extends user_propertyCreateArgs>(args: Prisma.SelectSubset<T, user_propertyCreateArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_properties.
   * @param {user_propertyCreateManyArgs} args - Arguments to create many User_properties.
   * @example
   * // Create many User_properties
   * const user_property = await prisma.user_property.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_propertyCreateManyArgs>(args?: Prisma.SelectSubset<T, user_propertyCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_property.
   * @param {user_propertyDeleteArgs} args - Arguments to delete one User_property.
   * @example
   * // Delete one User_property
   * const User_property = await prisma.user_property.delete({
   *   where: {
   *     // ... filter to delete one User_property
   *   }
   * })
   * 
   */
  delete<T extends user_propertyDeleteArgs>(args: Prisma.SelectSubset<T, user_propertyDeleteArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_property.
   * @param {user_propertyUpdateArgs} args - Arguments to update one User_property.
   * @example
   * // Update one User_property
   * const user_property = await prisma.user_property.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_propertyUpdateArgs>(args: Prisma.SelectSubset<T, user_propertyUpdateArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_properties.
   * @param {user_propertyDeleteManyArgs} args - Arguments to filter User_properties to delete.
   * @example
   * // Delete a few User_properties
   * const { count } = await prisma.user_property.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_propertyDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_propertyDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_properties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_properties
   * const user_property = await prisma.user_property.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_propertyUpdateManyArgs>(args: Prisma.SelectSubset<T, user_propertyUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_property.
   * @param {user_propertyUpsertArgs} args - Arguments to update or create a User_property.
   * @example
   * // Update or create a User_property
   * const user_property = await prisma.user_property.upsert({
   *   create: {
   *     // ... data to create a User_property
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_property we want to update
   *   }
   * })
   */
  upsert<T extends user_propertyUpsertArgs>(args: Prisma.SelectSubset<T, user_propertyUpsertArgs<ExtArgs>>): Prisma.Prisma__user_propertyClient<runtime.Types.Result.GetResult<Prisma.$user_propertyPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_properties.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyCountArgs} args - Arguments to filter User_properties to count.
   * @example
   * // Count the number of User_properties
   * const count = await prisma.user_property.count({
   *   where: {
   *     // ... the filter for the User_properties we want to count
   *   }
   * })
  **/
  count<T extends user_propertyCountArgs>(
    args?: Prisma.Subset<T, user_propertyCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_propertyCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_property.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_propertyAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_propertyAggregateArgs>(args: Prisma.Subset<T, User_propertyAggregateArgs>): Prisma.PrismaPromise<GetUser_propertyAggregateType<T>>

  /**
   * Group by User_property.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_propertyGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_propertyGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_propertyGroupByArgs['orderBy'] }
      : { orderBy?: user_propertyGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_propertyGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_propertyGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_property model
 */
readonly fields: user_propertyFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_property.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_propertyClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  property<T extends Prisma.propertyDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.propertyDefaultArgs<ExtArgs>>): Prisma.Prisma__propertyClient<runtime.Types.Result.GetResult<Prisma.$propertyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_property model
 */
export interface user_propertyFieldRefs {
  readonly id: Prisma.FieldRef<"user_property", 'Int'>
  readonly purchaseDate: Prisma.FieldRef<"user_property", 'DateTime'>
  readonly lastUpkeepPaid: Prisma.FieldRef<"user_property", 'DateTime'>
  readonly furniture: Prisma.FieldRef<"user_property", 'Json'>
  readonly customization: Prisma.FieldRef<"user_property", 'Json'>
  readonly createdAt: Prisma.FieldRef<"user_property", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_property", 'DateTime'>
  readonly userId: Prisma.FieldRef<"user_property", 'Int'>
  readonly propertyId: Prisma.FieldRef<"user_property", 'Int'>
}
    

// Custom InputTypes
/**
 * user_property findUnique
 */
export type user_propertyFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter, which user_property to fetch.
   */
  where: Prisma.user_propertyWhereUniqueInput
}

/**
 * user_property findUniqueOrThrow
 */
export type user_propertyFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter, which user_property to fetch.
   */
  where: Prisma.user_propertyWhereUniqueInput
}

/**
 * user_property findFirst
 */
export type user_propertyFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter, which user_property to fetch.
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_properties to fetch.
   */
  orderBy?: Prisma.user_propertyOrderByWithRelationInput | Prisma.user_propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_properties.
   */
  cursor?: Prisma.user_propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_properties.
   */
  distinct?: Prisma.User_propertyScalarFieldEnum | Prisma.User_propertyScalarFieldEnum[]
}

/**
 * user_property findFirstOrThrow
 */
export type user_propertyFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter, which user_property to fetch.
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_properties to fetch.
   */
  orderBy?: Prisma.user_propertyOrderByWithRelationInput | Prisma.user_propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_properties.
   */
  cursor?: Prisma.user_propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_properties.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_properties.
   */
  distinct?: Prisma.User_propertyScalarFieldEnum | Prisma.User_propertyScalarFieldEnum[]
}

/**
 * user_property findMany
 */
export type user_propertyFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter, which user_properties to fetch.
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_properties to fetch.
   */
  orderBy?: Prisma.user_propertyOrderByWithRelationInput | Prisma.user_propertyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_properties.
   */
  cursor?: Prisma.user_propertyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_properties from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_properties.
   */
  skip?: number
  distinct?: Prisma.User_propertyScalarFieldEnum | Prisma.User_propertyScalarFieldEnum[]
}

/**
 * user_property create
 */
export type user_propertyCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * The data needed to create a user_property.
   */
  data: Prisma.XOR<Prisma.user_propertyCreateInput, Prisma.user_propertyUncheckedCreateInput>
}

/**
 * user_property createMany
 */
export type user_propertyCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_properties.
   */
  data: Prisma.user_propertyCreateManyInput | Prisma.user_propertyCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_property update
 */
export type user_propertyUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * The data needed to update a user_property.
   */
  data: Prisma.XOR<Prisma.user_propertyUpdateInput, Prisma.user_propertyUncheckedUpdateInput>
  /**
   * Choose, which user_property to update.
   */
  where: Prisma.user_propertyWhereUniqueInput
}

/**
 * user_property updateMany
 */
export type user_propertyUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_properties.
   */
  data: Prisma.XOR<Prisma.user_propertyUpdateManyMutationInput, Prisma.user_propertyUncheckedUpdateManyInput>
  /**
   * Filter which user_properties to update
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * Limit how many user_properties to update.
   */
  limit?: number
}

/**
 * user_property upsert
 */
export type user_propertyUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * The filter to search for the user_property to update in case it exists.
   */
  where: Prisma.user_propertyWhereUniqueInput
  /**
   * In case the user_property found by the `where` argument doesn't exist, create a new user_property with this data.
   */
  create: Prisma.XOR<Prisma.user_propertyCreateInput, Prisma.user_propertyUncheckedCreateInput>
  /**
   * In case the user_property was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_propertyUpdateInput, Prisma.user_propertyUncheckedUpdateInput>
}

/**
 * user_property delete
 */
export type user_propertyDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
  /**
   * Filter which user_property to delete.
   */
  where: Prisma.user_propertyWhereUniqueInput
}

/**
 * user_property deleteMany
 */
export type user_propertyDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_properties to delete
   */
  where?: Prisma.user_propertyWhereInput
  /**
   * Limit how many user_properties to delete.
   */
  limit?: number
}

/**
 * user_property without action
 */
export type user_propertyDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_property
   */
  select?: Prisma.user_propertySelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_property
   */
  omit?: Prisma.user_propertyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_propertyInclude<ExtArgs> | null
}
