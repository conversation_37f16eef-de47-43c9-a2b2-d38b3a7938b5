
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `lottery` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model lottery
 * 
 */
export type lotteryModel = runtime.Types.Result.DefaultSelection<Prisma.$lotteryPayload>

export type AggregateLottery = {
  _count: LotteryCountAggregateOutputType | null
  _avg: LotteryAvgAggregateOutputType | null
  _sum: LotterySumAggregateOutputType | null
  _min: LotteryMinAggregateOutputType | null
  _max: LotteryMaxAggregateOutputType | null
}

export type LotteryAvgAggregateOutputType = {
  id: number | null
  prizeAmount: number | null
  entries: number | null
  winnerId: number | null
}

export type LotterySumAggregateOutputType = {
  id: number | null
  prizeAmount: number | null
  entries: number | null
  winnerId: number | null
}

export type LotteryMinAggregateOutputType = {
  id: number | null
  drawDate: Date | null
  prizeAmount: number | null
  entries: number | null
  createdAt: Date | null
  updatedAt: Date | null
  winnerId: number | null
}

export type LotteryMaxAggregateOutputType = {
  id: number | null
  drawDate: Date | null
  prizeAmount: number | null
  entries: number | null
  createdAt: Date | null
  updatedAt: Date | null
  winnerId: number | null
}

export type LotteryCountAggregateOutputType = {
  id: number
  drawDate: number
  prizeAmount: number
  entries: number
  createdAt: number
  updatedAt: number
  winnerId: number
  _all: number
}


export type LotteryAvgAggregateInputType = {
  id?: true
  prizeAmount?: true
  entries?: true
  winnerId?: true
}

export type LotterySumAggregateInputType = {
  id?: true
  prizeAmount?: true
  entries?: true
  winnerId?: true
}

export type LotteryMinAggregateInputType = {
  id?: true
  drawDate?: true
  prizeAmount?: true
  entries?: true
  createdAt?: true
  updatedAt?: true
  winnerId?: true
}

export type LotteryMaxAggregateInputType = {
  id?: true
  drawDate?: true
  prizeAmount?: true
  entries?: true
  createdAt?: true
  updatedAt?: true
  winnerId?: true
}

export type LotteryCountAggregateInputType = {
  id?: true
  drawDate?: true
  prizeAmount?: true
  entries?: true
  createdAt?: true
  updatedAt?: true
  winnerId?: true
  _all?: true
}

export type LotteryAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which lottery to aggregate.
   */
  where?: Prisma.lotteryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lotteries to fetch.
   */
  orderBy?: Prisma.lotteryOrderByWithRelationInput | Prisma.lotteryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.lotteryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lotteries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lotteries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned lotteries
  **/
  _count?: true | LotteryCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: LotteryAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: LotterySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LotteryMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LotteryMaxAggregateInputType
}

export type GetLotteryAggregateType<T extends LotteryAggregateArgs> = {
      [P in keyof T & keyof AggregateLottery]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLottery[P]>
    : Prisma.GetScalarType<T[P], AggregateLottery[P]>
}




export type lotteryGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.lotteryWhereInput
  orderBy?: Prisma.lotteryOrderByWithAggregationInput | Prisma.lotteryOrderByWithAggregationInput[]
  by: Prisma.LotteryScalarFieldEnum[] | Prisma.LotteryScalarFieldEnum
  having?: Prisma.lotteryScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LotteryCountAggregateInputType | true
  _avg?: LotteryAvgAggregateInputType
  _sum?: LotterySumAggregateInputType
  _min?: LotteryMinAggregateInputType
  _max?: LotteryMaxAggregateInputType
}

export type LotteryGroupByOutputType = {
  id: number
  drawDate: Date
  prizeAmount: number
  entries: number
  createdAt: Date
  updatedAt: Date
  winnerId: number | null
  _count: LotteryCountAggregateOutputType | null
  _avg: LotteryAvgAggregateOutputType | null
  _sum: LotterySumAggregateOutputType | null
  _min: LotteryMinAggregateOutputType | null
  _max: LotteryMaxAggregateOutputType | null
}

type GetLotteryGroupByPayload<T extends lotteryGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LotteryGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LotteryGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LotteryGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LotteryGroupByOutputType[P]>
      }
    >
  >



export type lotteryWhereInput = {
  AND?: Prisma.lotteryWhereInput | Prisma.lotteryWhereInput[]
  OR?: Prisma.lotteryWhereInput[]
  NOT?: Prisma.lotteryWhereInput | Prisma.lotteryWhereInput[]
  id?: Prisma.IntFilter<"lottery"> | number
  drawDate?: Prisma.DateTimeFilter<"lottery"> | Date | string
  prizeAmount?: Prisma.IntFilter<"lottery"> | number
  entries?: Prisma.IntFilter<"lottery"> | number
  createdAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  winnerId?: Prisma.IntNullableFilter<"lottery"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  lottery_entry?: Prisma.Lottery_entryListRelationFilter
}

export type lotteryOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  drawDate?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  winnerId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  lottery_entry?: Prisma.lottery_entryOrderByRelationAggregateInput
}

export type lotteryWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  drawDate?: Date | string
  AND?: Prisma.lotteryWhereInput | Prisma.lotteryWhereInput[]
  OR?: Prisma.lotteryWhereInput[]
  NOT?: Prisma.lotteryWhereInput | Prisma.lotteryWhereInput[]
  prizeAmount?: Prisma.IntFilter<"lottery"> | number
  entries?: Prisma.IntFilter<"lottery"> | number
  createdAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  winnerId?: Prisma.IntNullableFilter<"lottery"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  lottery_entry?: Prisma.Lottery_entryListRelationFilter
}, "id" | "drawDate">

export type lotteryOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  drawDate?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  winnerId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.lotteryCountOrderByAggregateInput
  _avg?: Prisma.lotteryAvgOrderByAggregateInput
  _max?: Prisma.lotteryMaxOrderByAggregateInput
  _min?: Prisma.lotteryMinOrderByAggregateInput
  _sum?: Prisma.lotterySumOrderByAggregateInput
}

export type lotteryScalarWhereWithAggregatesInput = {
  AND?: Prisma.lotteryScalarWhereWithAggregatesInput | Prisma.lotteryScalarWhereWithAggregatesInput[]
  OR?: Prisma.lotteryScalarWhereWithAggregatesInput[]
  NOT?: Prisma.lotteryScalarWhereWithAggregatesInput | Prisma.lotteryScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"lottery"> | number
  drawDate?: Prisma.DateTimeWithAggregatesFilter<"lottery"> | Date | string
  prizeAmount?: Prisma.IntWithAggregatesFilter<"lottery"> | number
  entries?: Prisma.IntWithAggregatesFilter<"lottery"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"lottery"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"lottery"> | Date | string
  winnerId?: Prisma.IntNullableWithAggregatesFilter<"lottery"> | number | null
}

export type lotteryCreateInput = {
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutLotteryInput
  lottery_entry?: Prisma.lottery_entryCreateNestedManyWithoutLotteryInput
}

export type lotteryUncheckedCreateInput = {
  id?: number
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  winnerId?: number | null
  lottery_entry?: Prisma.lottery_entryUncheckedCreateNestedManyWithoutLotteryInput
}

export type lotteryUpdateInput = {
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutLotteryNestedInput
  lottery_entry?: Prisma.lottery_entryUpdateManyWithoutLotteryNestedInput
}

export type lotteryUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  winnerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  lottery_entry?: Prisma.lottery_entryUncheckedUpdateManyWithoutLotteryNestedInput
}

export type lotteryCreateManyInput = {
  id?: number
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  winnerId?: number | null
}

export type lotteryUpdateManyMutationInput = {
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type lotteryUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  winnerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lotteryCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  drawDate?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  winnerId?: Prisma.SortOrder
}

export type lotteryAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  winnerId?: Prisma.SortOrder
}

export type lotteryMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  drawDate?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  winnerId?: Prisma.SortOrder
}

export type lotteryMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  drawDate?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  winnerId?: Prisma.SortOrder
}

export type lotterySumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  prizeAmount?: Prisma.SortOrder
  entries?: Prisma.SortOrder
  winnerId?: Prisma.SortOrder
}

export type LotteryNullableScalarRelationFilter = {
  is?: Prisma.lotteryWhereInput | null
  isNot?: Prisma.lotteryWhereInput | null
}

export type LotteryListRelationFilter = {
  every?: Prisma.lotteryWhereInput
  some?: Prisma.lotteryWhereInput
  none?: Prisma.lotteryWhereInput
}

export type lotteryOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type lotteryCreateNestedOneWithoutLottery_entryInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutLottery_entryInput, Prisma.lotteryUncheckedCreateWithoutLottery_entryInput>
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutLottery_entryInput
  connect?: Prisma.lotteryWhereUniqueInput
}

export type lotteryUpdateOneWithoutLottery_entryNestedInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutLottery_entryInput, Prisma.lotteryUncheckedCreateWithoutLottery_entryInput>
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutLottery_entryInput
  upsert?: Prisma.lotteryUpsertWithoutLottery_entryInput
  disconnect?: Prisma.lotteryWhereInput | boolean
  delete?: Prisma.lotteryWhereInput | boolean
  connect?: Prisma.lotteryWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.lotteryUpdateToOneWithWhereWithoutLottery_entryInput, Prisma.lotteryUpdateWithoutLottery_entryInput>, Prisma.lotteryUncheckedUpdateWithoutLottery_entryInput>
}

export type lotteryCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput> | Prisma.lotteryCreateWithoutUserInput[] | Prisma.lotteryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutUserInput | Prisma.lotteryCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.lotteryCreateManyUserInputEnvelope
  connect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
}

export type lotteryUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput> | Prisma.lotteryCreateWithoutUserInput[] | Prisma.lotteryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutUserInput | Prisma.lotteryCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.lotteryCreateManyUserInputEnvelope
  connect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
}

export type lotteryUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput> | Prisma.lotteryCreateWithoutUserInput[] | Prisma.lotteryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutUserInput | Prisma.lotteryCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.lotteryUpsertWithWhereUniqueWithoutUserInput | Prisma.lotteryUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.lotteryCreateManyUserInputEnvelope
  set?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  disconnect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  delete?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  connect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  update?: Prisma.lotteryUpdateWithWhereUniqueWithoutUserInput | Prisma.lotteryUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.lotteryUpdateManyWithWhereWithoutUserInput | Prisma.lotteryUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.lotteryScalarWhereInput | Prisma.lotteryScalarWhereInput[]
}

export type lotteryUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput> | Prisma.lotteryCreateWithoutUserInput[] | Prisma.lotteryUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.lotteryCreateOrConnectWithoutUserInput | Prisma.lotteryCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.lotteryUpsertWithWhereUniqueWithoutUserInput | Prisma.lotteryUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.lotteryCreateManyUserInputEnvelope
  set?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  disconnect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  delete?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  connect?: Prisma.lotteryWhereUniqueInput | Prisma.lotteryWhereUniqueInput[]
  update?: Prisma.lotteryUpdateWithWhereUniqueWithoutUserInput | Prisma.lotteryUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.lotteryUpdateManyWithWhereWithoutUserInput | Prisma.lotteryUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.lotteryScalarWhereInput | Prisma.lotteryScalarWhereInput[]
}

export type lotteryCreateWithoutLottery_entryInput = {
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutLotteryInput
}

export type lotteryUncheckedCreateWithoutLottery_entryInput = {
  id?: number
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  winnerId?: number | null
}

export type lotteryCreateOrConnectWithoutLottery_entryInput = {
  where: Prisma.lotteryWhereUniqueInput
  create: Prisma.XOR<Prisma.lotteryCreateWithoutLottery_entryInput, Prisma.lotteryUncheckedCreateWithoutLottery_entryInput>
}

export type lotteryUpsertWithoutLottery_entryInput = {
  update: Prisma.XOR<Prisma.lotteryUpdateWithoutLottery_entryInput, Prisma.lotteryUncheckedUpdateWithoutLottery_entryInput>
  create: Prisma.XOR<Prisma.lotteryCreateWithoutLottery_entryInput, Prisma.lotteryUncheckedCreateWithoutLottery_entryInput>
  where?: Prisma.lotteryWhereInput
}

export type lotteryUpdateToOneWithWhereWithoutLottery_entryInput = {
  where?: Prisma.lotteryWhereInput
  data: Prisma.XOR<Prisma.lotteryUpdateWithoutLottery_entryInput, Prisma.lotteryUncheckedUpdateWithoutLottery_entryInput>
}

export type lotteryUpdateWithoutLottery_entryInput = {
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutLotteryNestedInput
}

export type lotteryUncheckedUpdateWithoutLottery_entryInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  winnerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type lotteryCreateWithoutUserInput = {
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lottery_entry?: Prisma.lottery_entryCreateNestedManyWithoutLotteryInput
}

export type lotteryUncheckedCreateWithoutUserInput = {
  id?: number
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  lottery_entry?: Prisma.lottery_entryUncheckedCreateNestedManyWithoutLotteryInput
}

export type lotteryCreateOrConnectWithoutUserInput = {
  where: Prisma.lotteryWhereUniqueInput
  create: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput>
}

export type lotteryCreateManyUserInputEnvelope = {
  data: Prisma.lotteryCreateManyUserInput | Prisma.lotteryCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type lotteryUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.lotteryWhereUniqueInput
  update: Prisma.XOR<Prisma.lotteryUpdateWithoutUserInput, Prisma.lotteryUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.lotteryCreateWithoutUserInput, Prisma.lotteryUncheckedCreateWithoutUserInput>
}

export type lotteryUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.lotteryWhereUniqueInput
  data: Prisma.XOR<Prisma.lotteryUpdateWithoutUserInput, Prisma.lotteryUncheckedUpdateWithoutUserInput>
}

export type lotteryUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.lotteryScalarWhereInput
  data: Prisma.XOR<Prisma.lotteryUpdateManyMutationInput, Prisma.lotteryUncheckedUpdateManyWithoutUserInput>
}

export type lotteryScalarWhereInput = {
  AND?: Prisma.lotteryScalarWhereInput | Prisma.lotteryScalarWhereInput[]
  OR?: Prisma.lotteryScalarWhereInput[]
  NOT?: Prisma.lotteryScalarWhereInput | Prisma.lotteryScalarWhereInput[]
  id?: Prisma.IntFilter<"lottery"> | number
  drawDate?: Prisma.DateTimeFilter<"lottery"> | Date | string
  prizeAmount?: Prisma.IntFilter<"lottery"> | number
  entries?: Prisma.IntFilter<"lottery"> | number
  createdAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"lottery"> | Date | string
  winnerId?: Prisma.IntNullableFilter<"lottery"> | number | null
}

export type lotteryCreateManyUserInput = {
  id?: number
  drawDate: Date | string
  prizeAmount?: number
  entries?: number
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type lotteryUpdateWithoutUserInput = {
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lottery_entry?: Prisma.lottery_entryUpdateManyWithoutLotteryNestedInput
}

export type lotteryUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lottery_entry?: Prisma.lottery_entryUncheckedUpdateManyWithoutLotteryNestedInput
}

export type lotteryUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  drawDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  prizeAmount?: Prisma.IntFieldUpdateOperationsInput | number
  entries?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type LotteryCountOutputType
 */

export type LotteryCountOutputType = {
  lottery_entry: number
}

export type LotteryCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  lottery_entry?: boolean | LotteryCountOutputTypeCountLottery_entryArgs
}

/**
 * LotteryCountOutputType without action
 */
export type LotteryCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LotteryCountOutputType
   */
  select?: Prisma.LotteryCountOutputTypeSelect<ExtArgs> | null
}

/**
 * LotteryCountOutputType without action
 */
export type LotteryCountOutputTypeCountLottery_entryArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.lottery_entryWhereInput
}


export type lotterySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  drawDate?: boolean
  prizeAmount?: boolean
  entries?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  winnerId?: boolean
  user?: boolean | Prisma.lottery$userArgs<ExtArgs>
  lottery_entry?: boolean | Prisma.lottery$lottery_entryArgs<ExtArgs>
  _count?: boolean | Prisma.LotteryCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["lottery"]>



export type lotterySelectScalar = {
  id?: boolean
  drawDate?: boolean
  prizeAmount?: boolean
  entries?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  winnerId?: boolean
}

export type lotteryOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "drawDate" | "prizeAmount" | "entries" | "createdAt" | "updatedAt" | "winnerId", ExtArgs["result"]["lottery"]>
export type lotteryInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.lottery$userArgs<ExtArgs>
  lottery_entry?: boolean | Prisma.lottery$lottery_entryArgs<ExtArgs>
  _count?: boolean | Prisma.LotteryCountOutputTypeDefaultArgs<ExtArgs>
}

export type $lotteryPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "lottery"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    lottery_entry: Prisma.$lottery_entryPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    drawDate: Date
    prizeAmount: number
    entries: number
    createdAt: Date
    updatedAt: Date
    winnerId: number | null
  }, ExtArgs["result"]["lottery"]>
  composites: {}
}

export type lotteryGetPayload<S extends boolean | null | undefined | lotteryDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$lotteryPayload, S>

export type lotteryCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<lotteryFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: LotteryCountAggregateInputType | true
  }

export interface lotteryDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['lottery'], meta: { name: 'lottery' } }
  /**
   * Find zero or one Lottery that matches the filter.
   * @param {lotteryFindUniqueArgs} args - Arguments to find a Lottery
   * @example
   * // Get one Lottery
   * const lottery = await prisma.lottery.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends lotteryFindUniqueArgs>(args: Prisma.SelectSubset<T, lotteryFindUniqueArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Lottery that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {lotteryFindUniqueOrThrowArgs} args - Arguments to find a Lottery
   * @example
   * // Get one Lottery
   * const lottery = await prisma.lottery.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends lotteryFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, lotteryFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Lottery that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryFindFirstArgs} args - Arguments to find a Lottery
   * @example
   * // Get one Lottery
   * const lottery = await prisma.lottery.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends lotteryFindFirstArgs>(args?: Prisma.SelectSubset<T, lotteryFindFirstArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Lottery that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryFindFirstOrThrowArgs} args - Arguments to find a Lottery
   * @example
   * // Get one Lottery
   * const lottery = await prisma.lottery.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends lotteryFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, lotteryFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Lotteries that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Lotteries
   * const lotteries = await prisma.lottery.findMany()
   * 
   * // Get first 10 Lotteries
   * const lotteries = await prisma.lottery.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const lotteryWithIdOnly = await prisma.lottery.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends lotteryFindManyArgs>(args?: Prisma.SelectSubset<T, lotteryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Lottery.
   * @param {lotteryCreateArgs} args - Arguments to create a Lottery.
   * @example
   * // Create one Lottery
   * const Lottery = await prisma.lottery.create({
   *   data: {
   *     // ... data to create a Lottery
   *   }
   * })
   * 
   */
  create<T extends lotteryCreateArgs>(args: Prisma.SelectSubset<T, lotteryCreateArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Lotteries.
   * @param {lotteryCreateManyArgs} args - Arguments to create many Lotteries.
   * @example
   * // Create many Lotteries
   * const lottery = await prisma.lottery.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends lotteryCreateManyArgs>(args?: Prisma.SelectSubset<T, lotteryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Lottery.
   * @param {lotteryDeleteArgs} args - Arguments to delete one Lottery.
   * @example
   * // Delete one Lottery
   * const Lottery = await prisma.lottery.delete({
   *   where: {
   *     // ... filter to delete one Lottery
   *   }
   * })
   * 
   */
  delete<T extends lotteryDeleteArgs>(args: Prisma.SelectSubset<T, lotteryDeleteArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Lottery.
   * @param {lotteryUpdateArgs} args - Arguments to update one Lottery.
   * @example
   * // Update one Lottery
   * const lottery = await prisma.lottery.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends lotteryUpdateArgs>(args: Prisma.SelectSubset<T, lotteryUpdateArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Lotteries.
   * @param {lotteryDeleteManyArgs} args - Arguments to filter Lotteries to delete.
   * @example
   * // Delete a few Lotteries
   * const { count } = await prisma.lottery.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends lotteryDeleteManyArgs>(args?: Prisma.SelectSubset<T, lotteryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Lotteries.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Lotteries
   * const lottery = await prisma.lottery.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends lotteryUpdateManyArgs>(args: Prisma.SelectSubset<T, lotteryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Lottery.
   * @param {lotteryUpsertArgs} args - Arguments to update or create a Lottery.
   * @example
   * // Update or create a Lottery
   * const lottery = await prisma.lottery.upsert({
   *   create: {
   *     // ... data to create a Lottery
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Lottery we want to update
   *   }
   * })
   */
  upsert<T extends lotteryUpsertArgs>(args: Prisma.SelectSubset<T, lotteryUpsertArgs<ExtArgs>>): Prisma.Prisma__lotteryClient<runtime.Types.Result.GetResult<Prisma.$lotteryPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Lotteries.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryCountArgs} args - Arguments to filter Lotteries to count.
   * @example
   * // Count the number of Lotteries
   * const count = await prisma.lottery.count({
   *   where: {
   *     // ... the filter for the Lotteries we want to count
   *   }
   * })
  **/
  count<T extends lotteryCountArgs>(
    args?: Prisma.Subset<T, lotteryCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LotteryCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Lottery.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LotteryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LotteryAggregateArgs>(args: Prisma.Subset<T, LotteryAggregateArgs>): Prisma.PrismaPromise<GetLotteryAggregateType<T>>

  /**
   * Group by Lottery.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {lotteryGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends lotteryGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: lotteryGroupByArgs['orderBy'] }
      : { orderBy?: lotteryGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, lotteryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLotteryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the lottery model
 */
readonly fields: lotteryFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for lottery.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__lotteryClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.lottery$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.lottery$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  lottery_entry<T extends Prisma.lottery$lottery_entryArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.lottery$lottery_entryArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$lottery_entryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the lottery model
 */
export interface lotteryFieldRefs {
  readonly id: Prisma.FieldRef<"lottery", 'Int'>
  readonly drawDate: Prisma.FieldRef<"lottery", 'DateTime'>
  readonly prizeAmount: Prisma.FieldRef<"lottery", 'Int'>
  readonly entries: Prisma.FieldRef<"lottery", 'Int'>
  readonly createdAt: Prisma.FieldRef<"lottery", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"lottery", 'DateTime'>
  readonly winnerId: Prisma.FieldRef<"lottery", 'Int'>
}
    

// Custom InputTypes
/**
 * lottery findUnique
 */
export type lotteryFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter, which lottery to fetch.
   */
  where: Prisma.lotteryWhereUniqueInput
}

/**
 * lottery findUniqueOrThrow
 */
export type lotteryFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter, which lottery to fetch.
   */
  where: Prisma.lotteryWhereUniqueInput
}

/**
 * lottery findFirst
 */
export type lotteryFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter, which lottery to fetch.
   */
  where?: Prisma.lotteryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lotteries to fetch.
   */
  orderBy?: Prisma.lotteryOrderByWithRelationInput | Prisma.lotteryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for lotteries.
   */
  cursor?: Prisma.lotteryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lotteries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lotteries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of lotteries.
   */
  distinct?: Prisma.LotteryScalarFieldEnum | Prisma.LotteryScalarFieldEnum[]
}

/**
 * lottery findFirstOrThrow
 */
export type lotteryFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter, which lottery to fetch.
   */
  where?: Prisma.lotteryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lotteries to fetch.
   */
  orderBy?: Prisma.lotteryOrderByWithRelationInput | Prisma.lotteryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for lotteries.
   */
  cursor?: Prisma.lotteryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lotteries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lotteries.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of lotteries.
   */
  distinct?: Prisma.LotteryScalarFieldEnum | Prisma.LotteryScalarFieldEnum[]
}

/**
 * lottery findMany
 */
export type lotteryFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter, which lotteries to fetch.
   */
  where?: Prisma.lotteryWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of lotteries to fetch.
   */
  orderBy?: Prisma.lotteryOrderByWithRelationInput | Prisma.lotteryOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing lotteries.
   */
  cursor?: Prisma.lotteryWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` lotteries from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` lotteries.
   */
  skip?: number
  distinct?: Prisma.LotteryScalarFieldEnum | Prisma.LotteryScalarFieldEnum[]
}

/**
 * lottery create
 */
export type lotteryCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * The data needed to create a lottery.
   */
  data: Prisma.XOR<Prisma.lotteryCreateInput, Prisma.lotteryUncheckedCreateInput>
}

/**
 * lottery createMany
 */
export type lotteryCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many lotteries.
   */
  data: Prisma.lotteryCreateManyInput | Prisma.lotteryCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * lottery update
 */
export type lotteryUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * The data needed to update a lottery.
   */
  data: Prisma.XOR<Prisma.lotteryUpdateInput, Prisma.lotteryUncheckedUpdateInput>
  /**
   * Choose, which lottery to update.
   */
  where: Prisma.lotteryWhereUniqueInput
}

/**
 * lottery updateMany
 */
export type lotteryUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update lotteries.
   */
  data: Prisma.XOR<Prisma.lotteryUpdateManyMutationInput, Prisma.lotteryUncheckedUpdateManyInput>
  /**
   * Filter which lotteries to update
   */
  where?: Prisma.lotteryWhereInput
  /**
   * Limit how many lotteries to update.
   */
  limit?: number
}

/**
 * lottery upsert
 */
export type lotteryUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * The filter to search for the lottery to update in case it exists.
   */
  where: Prisma.lotteryWhereUniqueInput
  /**
   * In case the lottery found by the `where` argument doesn't exist, create a new lottery with this data.
   */
  create: Prisma.XOR<Prisma.lotteryCreateInput, Prisma.lotteryUncheckedCreateInput>
  /**
   * In case the lottery was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.lotteryUpdateInput, Prisma.lotteryUncheckedUpdateInput>
}

/**
 * lottery delete
 */
export type lotteryDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
  /**
   * Filter which lottery to delete.
   */
  where: Prisma.lotteryWhereUniqueInput
}

/**
 * lottery deleteMany
 */
export type lotteryDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which lotteries to delete
   */
  where?: Prisma.lotteryWhereInput
  /**
   * Limit how many lotteries to delete.
   */
  limit?: number
}

/**
 * lottery.user
 */
export type lottery$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * lottery.lottery_entry
 */
export type lottery$lottery_entryArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery_entry
   */
  select?: Prisma.lottery_entrySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery_entry
   */
  omit?: Prisma.lottery_entryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lottery_entryInclude<ExtArgs> | null
  where?: Prisma.lottery_entryWhereInput
  orderBy?: Prisma.lottery_entryOrderByWithRelationInput | Prisma.lottery_entryOrderByWithRelationInput[]
  cursor?: Prisma.lottery_entryWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Lottery_entryScalarFieldEnum | Prisma.Lottery_entryScalarFieldEnum[]
}

/**
 * lottery without action
 */
export type lotteryDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the lottery
   */
  select?: Prisma.lotterySelect<ExtArgs> | null
  /**
   * Omit specific fields from the lottery
   */
  omit?: Prisma.lotteryOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.lotteryInclude<ExtArgs> | null
}
