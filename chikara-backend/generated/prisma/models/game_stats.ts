
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `game_stats` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model game_stats
 * 
 */
export type game_statsModel = runtime.Types.Result.DefaultSelection<Prisma.$game_statsPayload>

export type AggregateGame_stats = {
  _count: Game_statsCountAggregateOutputType | null
  _avg: Game_statsAvgAggregateOutputType | null
  _sum: Game_statsSumAggregateOutputType | null
  _min: Game_statsMinAggregateOutputType | null
  _max: Game_statsMaxAggregateOutputType | null
}

export type Game_statsAvgAggregateOutputType = {
  id: number | null
  playerId: number | null
}

export type Game_statsSumAggregateOutputType = {
  id: number | null
  playerId: number | null
}

export type Game_statsMinAggregateOutputType = {
  id: number | null
  stats_type: string | null
  info: string | null
  createdAt: Date | null
  updatedAt: Date | null
  playerId: number | null
}

export type Game_statsMaxAggregateOutputType = {
  id: number | null
  stats_type: string | null
  info: string | null
  createdAt: Date | null
  updatedAt: Date | null
  playerId: number | null
}

export type Game_statsCountAggregateOutputType = {
  id: number
  stats_type: number
  info: number
  createdAt: number
  updatedAt: number
  playerId: number
  _all: number
}


export type Game_statsAvgAggregateInputType = {
  id?: true
  playerId?: true
}

export type Game_statsSumAggregateInputType = {
  id?: true
  playerId?: true
}

export type Game_statsMinAggregateInputType = {
  id?: true
  stats_type?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
}

export type Game_statsMaxAggregateInputType = {
  id?: true
  stats_type?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
}

export type Game_statsCountAggregateInputType = {
  id?: true
  stats_type?: true
  info?: true
  createdAt?: true
  updatedAt?: true
  playerId?: true
  _all?: true
}

export type Game_statsAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which game_stats to aggregate.
   */
  where?: Prisma.game_statsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_stats to fetch.
   */
  orderBy?: Prisma.game_statsOrderByWithRelationInput | Prisma.game_statsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.game_statsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_stats from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_stats.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned game_stats
  **/
  _count?: true | Game_statsCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Game_statsAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Game_statsSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Game_statsMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Game_statsMaxAggregateInputType
}

export type GetGame_statsAggregateType<T extends Game_statsAggregateArgs> = {
      [P in keyof T & keyof AggregateGame_stats]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGame_stats[P]>
    : Prisma.GetScalarType<T[P], AggregateGame_stats[P]>
}




export type game_statsGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.game_statsWhereInput
  orderBy?: Prisma.game_statsOrderByWithAggregationInput | Prisma.game_statsOrderByWithAggregationInput[]
  by: Prisma.Game_statsScalarFieldEnum[] | Prisma.Game_statsScalarFieldEnum
  having?: Prisma.game_statsScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Game_statsCountAggregateInputType | true
  _avg?: Game_statsAvgAggregateInputType
  _sum?: Game_statsSumAggregateInputType
  _min?: Game_statsMinAggregateInputType
  _max?: Game_statsMaxAggregateInputType
}

export type Game_statsGroupByOutputType = {
  id: number
  stats_type: string
  info: string
  createdAt: Date
  updatedAt: Date
  playerId: number | null
  _count: Game_statsCountAggregateOutputType | null
  _avg: Game_statsAvgAggregateOutputType | null
  _sum: Game_statsSumAggregateOutputType | null
  _min: Game_statsMinAggregateOutputType | null
  _max: Game_statsMaxAggregateOutputType | null
}

type GetGame_statsGroupByPayload<T extends game_statsGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Game_statsGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Game_statsGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Game_statsGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Game_statsGroupByOutputType[P]>
      }
    >
  >



export type game_statsWhereInput = {
  AND?: Prisma.game_statsWhereInput | Prisma.game_statsWhereInput[]
  OR?: Prisma.game_statsWhereInput[]
  NOT?: Prisma.game_statsWhereInput | Prisma.game_statsWhereInput[]
  id?: Prisma.IntFilter<"game_stats"> | number
  stats_type?: Prisma.StringFilter<"game_stats"> | string
  info?: Prisma.StringFilter<"game_stats"> | string
  createdAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  playerId?: Prisma.IntNullableFilter<"game_stats"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type game_statsOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  stats_type?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.game_statsOrderByRelevanceInput
}

export type game_statsWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.game_statsWhereInput | Prisma.game_statsWhereInput[]
  OR?: Prisma.game_statsWhereInput[]
  NOT?: Prisma.game_statsWhereInput | Prisma.game_statsWhereInput[]
  stats_type?: Prisma.StringFilter<"game_stats"> | string
  info?: Prisma.StringFilter<"game_stats"> | string
  createdAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  playerId?: Prisma.IntNullableFilter<"game_stats"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type game_statsOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  stats_type?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.game_statsCountOrderByAggregateInput
  _avg?: Prisma.game_statsAvgOrderByAggregateInput
  _max?: Prisma.game_statsMaxOrderByAggregateInput
  _min?: Prisma.game_statsMinOrderByAggregateInput
  _sum?: Prisma.game_statsSumOrderByAggregateInput
}

export type game_statsScalarWhereWithAggregatesInput = {
  AND?: Prisma.game_statsScalarWhereWithAggregatesInput | Prisma.game_statsScalarWhereWithAggregatesInput[]
  OR?: Prisma.game_statsScalarWhereWithAggregatesInput[]
  NOT?: Prisma.game_statsScalarWhereWithAggregatesInput | Prisma.game_statsScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"game_stats"> | number
  stats_type?: Prisma.StringWithAggregatesFilter<"game_stats"> | string
  info?: Prisma.StringWithAggregatesFilter<"game_stats"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"game_stats"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"game_stats"> | Date | string
  playerId?: Prisma.IntNullableWithAggregatesFilter<"game_stats"> | number | null
}

export type game_statsCreateInput = {
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutGame_statsInput
}

export type game_statsUncheckedCreateInput = {
  id?: number
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
}

export type game_statsUpdateInput = {
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutGame_statsNestedInput
}

export type game_statsUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type game_statsCreateManyInput = {
  id?: number
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
  playerId?: number | null
}

export type game_statsUpdateManyMutationInput = {
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_statsUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  playerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type game_statsOrderByRelevanceInput = {
  fields: Prisma.game_statsOrderByRelevanceFieldEnum | Prisma.game_statsOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type game_statsCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stats_type?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
}

export type game_statsAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
}

export type game_statsMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stats_type?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
}

export type game_statsMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stats_type?: Prisma.SortOrder
  info?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
}

export type game_statsSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  playerId?: Prisma.SortOrder
}

export type Game_statsListRelationFilter = {
  every?: Prisma.game_statsWhereInput
  some?: Prisma.game_statsWhereInput
  none?: Prisma.game_statsWhereInput
}

export type game_statsOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type game_statsCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput> | Prisma.game_statsCreateWithoutUserInput[] | Prisma.game_statsUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.game_statsCreateOrConnectWithoutUserInput | Prisma.game_statsCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.game_statsCreateManyUserInputEnvelope
  connect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
}

export type game_statsUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput> | Prisma.game_statsCreateWithoutUserInput[] | Prisma.game_statsUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.game_statsCreateOrConnectWithoutUserInput | Prisma.game_statsCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.game_statsCreateManyUserInputEnvelope
  connect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
}

export type game_statsUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput> | Prisma.game_statsCreateWithoutUserInput[] | Prisma.game_statsUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.game_statsCreateOrConnectWithoutUserInput | Prisma.game_statsCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.game_statsUpsertWithWhereUniqueWithoutUserInput | Prisma.game_statsUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.game_statsCreateManyUserInputEnvelope
  set?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  disconnect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  delete?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  connect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  update?: Prisma.game_statsUpdateWithWhereUniqueWithoutUserInput | Prisma.game_statsUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.game_statsUpdateManyWithWhereWithoutUserInput | Prisma.game_statsUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.game_statsScalarWhereInput | Prisma.game_statsScalarWhereInput[]
}

export type game_statsUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput> | Prisma.game_statsCreateWithoutUserInput[] | Prisma.game_statsUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.game_statsCreateOrConnectWithoutUserInput | Prisma.game_statsCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.game_statsUpsertWithWhereUniqueWithoutUserInput | Prisma.game_statsUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.game_statsCreateManyUserInputEnvelope
  set?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  disconnect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  delete?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  connect?: Prisma.game_statsWhereUniqueInput | Prisma.game_statsWhereUniqueInput[]
  update?: Prisma.game_statsUpdateWithWhereUniqueWithoutUserInput | Prisma.game_statsUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.game_statsUpdateManyWithWhereWithoutUserInput | Prisma.game_statsUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.game_statsScalarWhereInput | Prisma.game_statsScalarWhereInput[]
}

export type game_statsCreateWithoutUserInput = {
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_statsUncheckedCreateWithoutUserInput = {
  id?: number
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_statsCreateOrConnectWithoutUserInput = {
  where: Prisma.game_statsWhereUniqueInput
  create: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput>
}

export type game_statsCreateManyUserInputEnvelope = {
  data: Prisma.game_statsCreateManyUserInput | Prisma.game_statsCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type game_statsUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.game_statsWhereUniqueInput
  update: Prisma.XOR<Prisma.game_statsUpdateWithoutUserInput, Prisma.game_statsUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.game_statsCreateWithoutUserInput, Prisma.game_statsUncheckedCreateWithoutUserInput>
}

export type game_statsUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.game_statsWhereUniqueInput
  data: Prisma.XOR<Prisma.game_statsUpdateWithoutUserInput, Prisma.game_statsUncheckedUpdateWithoutUserInput>
}

export type game_statsUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.game_statsScalarWhereInput
  data: Prisma.XOR<Prisma.game_statsUpdateManyMutationInput, Prisma.game_statsUncheckedUpdateManyWithoutUserInput>
}

export type game_statsScalarWhereInput = {
  AND?: Prisma.game_statsScalarWhereInput | Prisma.game_statsScalarWhereInput[]
  OR?: Prisma.game_statsScalarWhereInput[]
  NOT?: Prisma.game_statsScalarWhereInput | Prisma.game_statsScalarWhereInput[]
  id?: Prisma.IntFilter<"game_stats"> | number
  stats_type?: Prisma.StringFilter<"game_stats"> | string
  info?: Prisma.StringFilter<"game_stats"> | string
  createdAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"game_stats"> | Date | string
  playerId?: Prisma.IntNullableFilter<"game_stats"> | number | null
}

export type game_statsCreateManyUserInput = {
  id?: number
  stats_type: string
  info: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type game_statsUpdateWithoutUserInput = {
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_statsUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type game_statsUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stats_type?: Prisma.StringFieldUpdateOperationsInput | string
  info?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type game_statsSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stats_type?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  playerId?: boolean
  user?: boolean | Prisma.game_stats$userArgs<ExtArgs>
}, ExtArgs["result"]["game_stats"]>



export type game_statsSelectScalar = {
  id?: boolean
  stats_type?: boolean
  info?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  playerId?: boolean
}

export type game_statsOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "stats_type" | "info" | "createdAt" | "updatedAt" | "playerId", ExtArgs["result"]["game_stats"]>
export type game_statsInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.game_stats$userArgs<ExtArgs>
}

export type $game_statsPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "game_stats"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    stats_type: string
    info: string
    createdAt: Date
    updatedAt: Date
    playerId: number | null
  }, ExtArgs["result"]["game_stats"]>
  composites: {}
}

export type game_statsGetPayload<S extends boolean | null | undefined | game_statsDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$game_statsPayload, S>

export type game_statsCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<game_statsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Game_statsCountAggregateInputType | true
  }

export interface game_statsDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['game_stats'], meta: { name: 'game_stats' } }
  /**
   * Find zero or one Game_stats that matches the filter.
   * @param {game_statsFindUniqueArgs} args - Arguments to find a Game_stats
   * @example
   * // Get one Game_stats
   * const game_stats = await prisma.game_stats.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends game_statsFindUniqueArgs>(args: Prisma.SelectSubset<T, game_statsFindUniqueArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Game_stats that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {game_statsFindUniqueOrThrowArgs} args - Arguments to find a Game_stats
   * @example
   * // Get one Game_stats
   * const game_stats = await prisma.game_stats.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends game_statsFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, game_statsFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Game_stats that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsFindFirstArgs} args - Arguments to find a Game_stats
   * @example
   * // Get one Game_stats
   * const game_stats = await prisma.game_stats.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends game_statsFindFirstArgs>(args?: Prisma.SelectSubset<T, game_statsFindFirstArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Game_stats that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsFindFirstOrThrowArgs} args - Arguments to find a Game_stats
   * @example
   * // Get one Game_stats
   * const game_stats = await prisma.game_stats.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends game_statsFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, game_statsFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Game_stats that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Game_stats
   * const game_stats = await prisma.game_stats.findMany()
   * 
   * // Get first 10 Game_stats
   * const game_stats = await prisma.game_stats.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const game_statsWithIdOnly = await prisma.game_stats.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends game_statsFindManyArgs>(args?: Prisma.SelectSubset<T, game_statsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Game_stats.
   * @param {game_statsCreateArgs} args - Arguments to create a Game_stats.
   * @example
   * // Create one Game_stats
   * const Game_stats = await prisma.game_stats.create({
   *   data: {
   *     // ... data to create a Game_stats
   *   }
   * })
   * 
   */
  create<T extends game_statsCreateArgs>(args: Prisma.SelectSubset<T, game_statsCreateArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Game_stats.
   * @param {game_statsCreateManyArgs} args - Arguments to create many Game_stats.
   * @example
   * // Create many Game_stats
   * const game_stats = await prisma.game_stats.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends game_statsCreateManyArgs>(args?: Prisma.SelectSubset<T, game_statsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Game_stats.
   * @param {game_statsDeleteArgs} args - Arguments to delete one Game_stats.
   * @example
   * // Delete one Game_stats
   * const Game_stats = await prisma.game_stats.delete({
   *   where: {
   *     // ... filter to delete one Game_stats
   *   }
   * })
   * 
   */
  delete<T extends game_statsDeleteArgs>(args: Prisma.SelectSubset<T, game_statsDeleteArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Game_stats.
   * @param {game_statsUpdateArgs} args - Arguments to update one Game_stats.
   * @example
   * // Update one Game_stats
   * const game_stats = await prisma.game_stats.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends game_statsUpdateArgs>(args: Prisma.SelectSubset<T, game_statsUpdateArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Game_stats.
   * @param {game_statsDeleteManyArgs} args - Arguments to filter Game_stats to delete.
   * @example
   * // Delete a few Game_stats
   * const { count } = await prisma.game_stats.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends game_statsDeleteManyArgs>(args?: Prisma.SelectSubset<T, game_statsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Game_stats.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Game_stats
   * const game_stats = await prisma.game_stats.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends game_statsUpdateManyArgs>(args: Prisma.SelectSubset<T, game_statsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Game_stats.
   * @param {game_statsUpsertArgs} args - Arguments to update or create a Game_stats.
   * @example
   * // Update or create a Game_stats
   * const game_stats = await prisma.game_stats.upsert({
   *   create: {
   *     // ... data to create a Game_stats
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Game_stats we want to update
   *   }
   * })
   */
  upsert<T extends game_statsUpsertArgs>(args: Prisma.SelectSubset<T, game_statsUpsertArgs<ExtArgs>>): Prisma.Prisma__game_statsClient<runtime.Types.Result.GetResult<Prisma.$game_statsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Game_stats.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsCountArgs} args - Arguments to filter Game_stats to count.
   * @example
   * // Count the number of Game_stats
   * const count = await prisma.game_stats.count({
   *   where: {
   *     // ... the filter for the Game_stats we want to count
   *   }
   * })
  **/
  count<T extends game_statsCountArgs>(
    args?: Prisma.Subset<T, game_statsCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Game_statsCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Game_stats.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Game_statsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Game_statsAggregateArgs>(args: Prisma.Subset<T, Game_statsAggregateArgs>): Prisma.PrismaPromise<GetGame_statsAggregateType<T>>

  /**
   * Group by Game_stats.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {game_statsGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends game_statsGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: game_statsGroupByArgs['orderBy'] }
      : { orderBy?: game_statsGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, game_statsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGame_statsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the game_stats model
 */
readonly fields: game_statsFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for game_stats.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__game_statsClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.game_stats$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.game_stats$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the game_stats model
 */
export interface game_statsFieldRefs {
  readonly id: Prisma.FieldRef<"game_stats", 'Int'>
  readonly stats_type: Prisma.FieldRef<"game_stats", 'String'>
  readonly info: Prisma.FieldRef<"game_stats", 'String'>
  readonly createdAt: Prisma.FieldRef<"game_stats", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"game_stats", 'DateTime'>
  readonly playerId: Prisma.FieldRef<"game_stats", 'Int'>
}
    

// Custom InputTypes
/**
 * game_stats findUnique
 */
export type game_statsFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter, which game_stats to fetch.
   */
  where: Prisma.game_statsWhereUniqueInput
}

/**
 * game_stats findUniqueOrThrow
 */
export type game_statsFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter, which game_stats to fetch.
   */
  where: Prisma.game_statsWhereUniqueInput
}

/**
 * game_stats findFirst
 */
export type game_statsFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter, which game_stats to fetch.
   */
  where?: Prisma.game_statsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_stats to fetch.
   */
  orderBy?: Prisma.game_statsOrderByWithRelationInput | Prisma.game_statsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for game_stats.
   */
  cursor?: Prisma.game_statsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_stats from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_stats.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of game_stats.
   */
  distinct?: Prisma.Game_statsScalarFieldEnum | Prisma.Game_statsScalarFieldEnum[]
}

/**
 * game_stats findFirstOrThrow
 */
export type game_statsFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter, which game_stats to fetch.
   */
  where?: Prisma.game_statsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_stats to fetch.
   */
  orderBy?: Prisma.game_statsOrderByWithRelationInput | Prisma.game_statsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for game_stats.
   */
  cursor?: Prisma.game_statsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_stats from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_stats.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of game_stats.
   */
  distinct?: Prisma.Game_statsScalarFieldEnum | Prisma.Game_statsScalarFieldEnum[]
}

/**
 * game_stats findMany
 */
export type game_statsFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter, which game_stats to fetch.
   */
  where?: Prisma.game_statsWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of game_stats to fetch.
   */
  orderBy?: Prisma.game_statsOrderByWithRelationInput | Prisma.game_statsOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing game_stats.
   */
  cursor?: Prisma.game_statsWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` game_stats from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` game_stats.
   */
  skip?: number
  distinct?: Prisma.Game_statsScalarFieldEnum | Prisma.Game_statsScalarFieldEnum[]
}

/**
 * game_stats create
 */
export type game_statsCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * The data needed to create a game_stats.
   */
  data: Prisma.XOR<Prisma.game_statsCreateInput, Prisma.game_statsUncheckedCreateInput>
}

/**
 * game_stats createMany
 */
export type game_statsCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many game_stats.
   */
  data: Prisma.game_statsCreateManyInput | Prisma.game_statsCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * game_stats update
 */
export type game_statsUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * The data needed to update a game_stats.
   */
  data: Prisma.XOR<Prisma.game_statsUpdateInput, Prisma.game_statsUncheckedUpdateInput>
  /**
   * Choose, which game_stats to update.
   */
  where: Prisma.game_statsWhereUniqueInput
}

/**
 * game_stats updateMany
 */
export type game_statsUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update game_stats.
   */
  data: Prisma.XOR<Prisma.game_statsUpdateManyMutationInput, Prisma.game_statsUncheckedUpdateManyInput>
  /**
   * Filter which game_stats to update
   */
  where?: Prisma.game_statsWhereInput
  /**
   * Limit how many game_stats to update.
   */
  limit?: number
}

/**
 * game_stats upsert
 */
export type game_statsUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * The filter to search for the game_stats to update in case it exists.
   */
  where: Prisma.game_statsWhereUniqueInput
  /**
   * In case the game_stats found by the `where` argument doesn't exist, create a new game_stats with this data.
   */
  create: Prisma.XOR<Prisma.game_statsCreateInput, Prisma.game_statsUncheckedCreateInput>
  /**
   * In case the game_stats was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.game_statsUpdateInput, Prisma.game_statsUncheckedUpdateInput>
}

/**
 * game_stats delete
 */
export type game_statsDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
  /**
   * Filter which game_stats to delete.
   */
  where: Prisma.game_statsWhereUniqueInput
}

/**
 * game_stats deleteMany
 */
export type game_statsDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which game_stats to delete
   */
  where?: Prisma.game_statsWhereInput
  /**
   * Limit how many game_stats to delete.
   */
  limit?: number
}

/**
 * game_stats.user
 */
export type game_stats$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * game_stats without action
 */
export type game_statsDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the game_stats
   */
  select?: Prisma.game_statsSelect<ExtArgs> | null
  /**
   * Omit specific fields from the game_stats
   */
  omit?: Prisma.game_statsOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.game_statsInclude<ExtArgs> | null
}
