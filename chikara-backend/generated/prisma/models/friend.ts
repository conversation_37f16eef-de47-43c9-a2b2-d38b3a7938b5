
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `friend` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model friend
 * 
 */
export type friendModel = runtime.Types.Result.DefaultSelection<Prisma.$friendPayload>

export type AggregateFriend = {
  _count: FriendCountAggregateOutputType | null
  _avg: FriendAvgAggregateOutputType | null
  _sum: FriendSumAggregateOutputType | null
  _min: FriendMinAggregateOutputType | null
  _max: FriendMaxAggregateOutputType | null
}

export type FriendAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  friendId: number | null
}

export type FriendSumAggregateOutputType = {
  id: number | null
  userId: number | null
  friendId: number | null
}

export type FriendMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  friendId: number | null
  note: string | null
}

export type FriendMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  friendId: number | null
  note: string | null
}

export type FriendCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  userId: number
  friendId: number
  note: number
  _all: number
}


export type FriendAvgAggregateInputType = {
  id?: true
  userId?: true
  friendId?: true
}

export type FriendSumAggregateInputType = {
  id?: true
  userId?: true
  friendId?: true
}

export type FriendMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  friendId?: true
  note?: true
}

export type FriendMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  friendId?: true
  note?: true
}

export type FriendCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  friendId?: true
  note?: true
  _all?: true
}

export type FriendAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which friend to aggregate.
   */
  where?: Prisma.friendWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friends to fetch.
   */
  orderBy?: Prisma.friendOrderByWithRelationInput | Prisma.friendOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.friendWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friends from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friends.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned friends
  **/
  _count?: true | FriendCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: FriendAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: FriendSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: FriendMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: FriendMaxAggregateInputType
}

export type GetFriendAggregateType<T extends FriendAggregateArgs> = {
      [P in keyof T & keyof AggregateFriend]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateFriend[P]>
    : Prisma.GetScalarType<T[P], AggregateFriend[P]>
}




export type friendGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.friendWhereInput
  orderBy?: Prisma.friendOrderByWithAggregationInput | Prisma.friendOrderByWithAggregationInput[]
  by: Prisma.FriendScalarFieldEnum[] | Prisma.FriendScalarFieldEnum
  having?: Prisma.friendScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: FriendCountAggregateInputType | true
  _avg?: FriendAvgAggregateInputType
  _sum?: FriendSumAggregateInputType
  _min?: FriendMinAggregateInputType
  _max?: FriendMaxAggregateInputType
}

export type FriendGroupByOutputType = {
  id: number
  createdAt: Date
  updatedAt: Date
  userId: number
  friendId: number
  note: string | null
  _count: FriendCountAggregateOutputType | null
  _avg: FriendAvgAggregateOutputType | null
  _sum: FriendSumAggregateOutputType | null
  _min: FriendMinAggregateOutputType | null
  _max: FriendMaxAggregateOutputType | null
}

type GetFriendGroupByPayload<T extends friendGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<FriendGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof FriendGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], FriendGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], FriendGroupByOutputType[P]>
      }
    >
  >



export type friendWhereInput = {
  AND?: Prisma.friendWhereInput | Prisma.friendWhereInput[]
  OR?: Prisma.friendWhereInput[]
  NOT?: Prisma.friendWhereInput | Prisma.friendWhereInput[]
  id?: Prisma.IntFilter<"friend"> | number
  createdAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  userId?: Prisma.IntFilter<"friend"> | number
  friendId?: Prisma.IntFilter<"friend"> | number
  note?: Prisma.StringNullableFilter<"friend"> | string | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  friend?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type friendOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
  note?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  friend?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.friendOrderByRelevanceInput
}

export type friendWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  userId_friendId?: Prisma.friendUserIdFriendIdCompoundUniqueInput
  AND?: Prisma.friendWhereInput | Prisma.friendWhereInput[]
  OR?: Prisma.friendWhereInput[]
  NOT?: Prisma.friendWhereInput | Prisma.friendWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  userId?: Prisma.IntFilter<"friend"> | number
  friendId?: Prisma.IntFilter<"friend"> | number
  note?: Prisma.StringNullableFilter<"friend"> | string | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  friend?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id" | "userId_friendId">

export type friendOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
  note?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.friendCountOrderByAggregateInput
  _avg?: Prisma.friendAvgOrderByAggregateInput
  _max?: Prisma.friendMaxOrderByAggregateInput
  _min?: Prisma.friendMinOrderByAggregateInput
  _sum?: Prisma.friendSumOrderByAggregateInput
}

export type friendScalarWhereWithAggregatesInput = {
  AND?: Prisma.friendScalarWhereWithAggregatesInput | Prisma.friendScalarWhereWithAggregatesInput[]
  OR?: Prisma.friendScalarWhereWithAggregatesInput[]
  NOT?: Prisma.friendScalarWhereWithAggregatesInput | Prisma.friendScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"friend"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"friend"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"friend"> | Date | string
  userId?: Prisma.IntWithAggregatesFilter<"friend"> | number
  friendId?: Prisma.IntWithAggregatesFilter<"friend"> | number
  note?: Prisma.StringNullableWithAggregatesFilter<"friend"> | string | null
}

export type friendCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  user: Prisma.userCreateNestedOneWithoutFriendsInput
  friend: Prisma.userCreateNestedOneWithoutFriendOfInput
}

export type friendUncheckedCreateInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  friendId: number
  note?: string | null
}

export type friendUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user?: Prisma.userUpdateOneRequiredWithoutFriendsNestedInput
  friend?: Prisma.userUpdateOneRequiredWithoutFriendOfNestedInput
}

export type friendUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  friendId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type friendCreateManyInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  friendId: number
  note?: string | null
}

export type friendUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type friendUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  friendId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type FriendListRelationFilter = {
  every?: Prisma.friendWhereInput
  some?: Prisma.friendWhereInput
  none?: Prisma.friendWhereInput
}

export type friendOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type friendOrderByRelevanceInput = {
  fields: Prisma.friendOrderByRelevanceFieldEnum | Prisma.friendOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type friendUserIdFriendIdCompoundUniqueInput = {
  userId: number
  friendId: number
}

export type friendCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type friendAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
}

export type friendMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type friendMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type friendSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  friendId?: Prisma.SortOrder
}

export type friendCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput> | Prisma.friendCreateWithoutUserInput[] | Prisma.friendUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutUserInput | Prisma.friendCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.friendCreateManyUserInputEnvelope
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
}

export type friendCreateNestedManyWithoutFriendInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput> | Prisma.friendCreateWithoutFriendInput[] | Prisma.friendUncheckedCreateWithoutFriendInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutFriendInput | Prisma.friendCreateOrConnectWithoutFriendInput[]
  createMany?: Prisma.friendCreateManyFriendInputEnvelope
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
}

export type friendUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput> | Prisma.friendCreateWithoutUserInput[] | Prisma.friendUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutUserInput | Prisma.friendCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.friendCreateManyUserInputEnvelope
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
}

export type friendUncheckedCreateNestedManyWithoutFriendInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput> | Prisma.friendCreateWithoutFriendInput[] | Prisma.friendUncheckedCreateWithoutFriendInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutFriendInput | Prisma.friendCreateOrConnectWithoutFriendInput[]
  createMany?: Prisma.friendCreateManyFriendInputEnvelope
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
}

export type friendUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput> | Prisma.friendCreateWithoutUserInput[] | Prisma.friendUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutUserInput | Prisma.friendCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.friendUpsertWithWhereUniqueWithoutUserInput | Prisma.friendUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.friendCreateManyUserInputEnvelope
  set?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  disconnect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  delete?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  update?: Prisma.friendUpdateWithWhereUniqueWithoutUserInput | Prisma.friendUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.friendUpdateManyWithWhereWithoutUserInput | Prisma.friendUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
}

export type friendUpdateManyWithoutFriendNestedInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput> | Prisma.friendCreateWithoutFriendInput[] | Prisma.friendUncheckedCreateWithoutFriendInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutFriendInput | Prisma.friendCreateOrConnectWithoutFriendInput[]
  upsert?: Prisma.friendUpsertWithWhereUniqueWithoutFriendInput | Prisma.friendUpsertWithWhereUniqueWithoutFriendInput[]
  createMany?: Prisma.friendCreateManyFriendInputEnvelope
  set?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  disconnect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  delete?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  update?: Prisma.friendUpdateWithWhereUniqueWithoutFriendInput | Prisma.friendUpdateWithWhereUniqueWithoutFriendInput[]
  updateMany?: Prisma.friendUpdateManyWithWhereWithoutFriendInput | Prisma.friendUpdateManyWithWhereWithoutFriendInput[]
  deleteMany?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
}

export type friendUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput> | Prisma.friendCreateWithoutUserInput[] | Prisma.friendUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutUserInput | Prisma.friendCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.friendUpsertWithWhereUniqueWithoutUserInput | Prisma.friendUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.friendCreateManyUserInputEnvelope
  set?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  disconnect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  delete?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  update?: Prisma.friendUpdateWithWhereUniqueWithoutUserInput | Prisma.friendUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.friendUpdateManyWithWhereWithoutUserInput | Prisma.friendUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
}

export type friendUncheckedUpdateManyWithoutFriendNestedInput = {
  create?: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput> | Prisma.friendCreateWithoutFriendInput[] | Prisma.friendUncheckedCreateWithoutFriendInput[]
  connectOrCreate?: Prisma.friendCreateOrConnectWithoutFriendInput | Prisma.friendCreateOrConnectWithoutFriendInput[]
  upsert?: Prisma.friendUpsertWithWhereUniqueWithoutFriendInput | Prisma.friendUpsertWithWhereUniqueWithoutFriendInput[]
  createMany?: Prisma.friendCreateManyFriendInputEnvelope
  set?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  disconnect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  delete?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  connect?: Prisma.friendWhereUniqueInput | Prisma.friendWhereUniqueInput[]
  update?: Prisma.friendUpdateWithWhereUniqueWithoutFriendInput | Prisma.friendUpdateWithWhereUniqueWithoutFriendInput[]
  updateMany?: Prisma.friendUpdateManyWithWhereWithoutFriendInput | Prisma.friendUpdateManyWithWhereWithoutFriendInput[]
  deleteMany?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
}

export type friendCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  friend: Prisma.userCreateNestedOneWithoutFriendOfInput
}

export type friendUncheckedCreateWithoutUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  friendId: number
  note?: string | null
}

export type friendCreateOrConnectWithoutUserInput = {
  where: Prisma.friendWhereUniqueInput
  create: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput>
}

export type friendCreateManyUserInputEnvelope = {
  data: Prisma.friendCreateManyUserInput | Prisma.friendCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type friendCreateWithoutFriendInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  user: Prisma.userCreateNestedOneWithoutFriendsInput
}

export type friendUncheckedCreateWithoutFriendInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  note?: string | null
}

export type friendCreateOrConnectWithoutFriendInput = {
  where: Prisma.friendWhereUniqueInput
  create: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput>
}

export type friendCreateManyFriendInputEnvelope = {
  data: Prisma.friendCreateManyFriendInput | Prisma.friendCreateManyFriendInput[]
  skipDuplicates?: boolean
}

export type friendUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.friendWhereUniqueInput
  update: Prisma.XOR<Prisma.friendUpdateWithoutUserInput, Prisma.friendUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.friendCreateWithoutUserInput, Prisma.friendUncheckedCreateWithoutUserInput>
}

export type friendUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.friendWhereUniqueInput
  data: Prisma.XOR<Prisma.friendUpdateWithoutUserInput, Prisma.friendUncheckedUpdateWithoutUserInput>
}

export type friendUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.friendScalarWhereInput
  data: Prisma.XOR<Prisma.friendUpdateManyMutationInput, Prisma.friendUncheckedUpdateManyWithoutUserInput>
}

export type friendScalarWhereInput = {
  AND?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
  OR?: Prisma.friendScalarWhereInput[]
  NOT?: Prisma.friendScalarWhereInput | Prisma.friendScalarWhereInput[]
  id?: Prisma.IntFilter<"friend"> | number
  createdAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"friend"> | Date | string
  userId?: Prisma.IntFilter<"friend"> | number
  friendId?: Prisma.IntFilter<"friend"> | number
  note?: Prisma.StringNullableFilter<"friend"> | string | null
}

export type friendUpsertWithWhereUniqueWithoutFriendInput = {
  where: Prisma.friendWhereUniqueInput
  update: Prisma.XOR<Prisma.friendUpdateWithoutFriendInput, Prisma.friendUncheckedUpdateWithoutFriendInput>
  create: Prisma.XOR<Prisma.friendCreateWithoutFriendInput, Prisma.friendUncheckedCreateWithoutFriendInput>
}

export type friendUpdateWithWhereUniqueWithoutFriendInput = {
  where: Prisma.friendWhereUniqueInput
  data: Prisma.XOR<Prisma.friendUpdateWithoutFriendInput, Prisma.friendUncheckedUpdateWithoutFriendInput>
}

export type friendUpdateManyWithWhereWithoutFriendInput = {
  where: Prisma.friendScalarWhereInput
  data: Prisma.XOR<Prisma.friendUpdateManyMutationInput, Prisma.friendUncheckedUpdateManyWithoutFriendInput>
}

export type friendCreateManyUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  friendId: number
  note?: string | null
}

export type friendCreateManyFriendInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  note?: string | null
}

export type friendUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  friend?: Prisma.userUpdateOneRequiredWithoutFriendOfNestedInput
}

export type friendUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  friendId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type friendUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  friendId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type friendUpdateWithoutFriendInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user?: Prisma.userUpdateOneRequiredWithoutFriendsNestedInput
}

export type friendUncheckedUpdateWithoutFriendInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type friendUncheckedUpdateManyWithoutFriendInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}



export type friendSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  friendId?: boolean
  note?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  friend?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["friend"]>



export type friendSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  friendId?: boolean
  note?: boolean
}

export type friendOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "createdAt" | "updatedAt" | "userId" | "friendId" | "note", ExtArgs["result"]["friend"]>
export type friendInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  friend?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $friendPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "friend"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    friend: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    createdAt: Date
    updatedAt: Date
    userId: number
    friendId: number
    note: string | null
  }, ExtArgs["result"]["friend"]>
  composites: {}
}

export type friendGetPayload<S extends boolean | null | undefined | friendDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$friendPayload, S>

export type friendCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<friendFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: FriendCountAggregateInputType | true
  }

export interface friendDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['friend'], meta: { name: 'friend' } }
  /**
   * Find zero or one Friend that matches the filter.
   * @param {friendFindUniqueArgs} args - Arguments to find a Friend
   * @example
   * // Get one Friend
   * const friend = await prisma.friend.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends friendFindUniqueArgs>(args: Prisma.SelectSubset<T, friendFindUniqueArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Friend that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {friendFindUniqueOrThrowArgs} args - Arguments to find a Friend
   * @example
   * // Get one Friend
   * const friend = await prisma.friend.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends friendFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, friendFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Friend that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendFindFirstArgs} args - Arguments to find a Friend
   * @example
   * // Get one Friend
   * const friend = await prisma.friend.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends friendFindFirstArgs>(args?: Prisma.SelectSubset<T, friendFindFirstArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Friend that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendFindFirstOrThrowArgs} args - Arguments to find a Friend
   * @example
   * // Get one Friend
   * const friend = await prisma.friend.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends friendFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, friendFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Friends that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Friends
   * const friends = await prisma.friend.findMany()
   * 
   * // Get first 10 Friends
   * const friends = await prisma.friend.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const friendWithIdOnly = await prisma.friend.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends friendFindManyArgs>(args?: Prisma.SelectSubset<T, friendFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Friend.
   * @param {friendCreateArgs} args - Arguments to create a Friend.
   * @example
   * // Create one Friend
   * const Friend = await prisma.friend.create({
   *   data: {
   *     // ... data to create a Friend
   *   }
   * })
   * 
   */
  create<T extends friendCreateArgs>(args: Prisma.SelectSubset<T, friendCreateArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Friends.
   * @param {friendCreateManyArgs} args - Arguments to create many Friends.
   * @example
   * // Create many Friends
   * const friend = await prisma.friend.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends friendCreateManyArgs>(args?: Prisma.SelectSubset<T, friendCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Friend.
   * @param {friendDeleteArgs} args - Arguments to delete one Friend.
   * @example
   * // Delete one Friend
   * const Friend = await prisma.friend.delete({
   *   where: {
   *     // ... filter to delete one Friend
   *   }
   * })
   * 
   */
  delete<T extends friendDeleteArgs>(args: Prisma.SelectSubset<T, friendDeleteArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Friend.
   * @param {friendUpdateArgs} args - Arguments to update one Friend.
   * @example
   * // Update one Friend
   * const friend = await prisma.friend.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends friendUpdateArgs>(args: Prisma.SelectSubset<T, friendUpdateArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Friends.
   * @param {friendDeleteManyArgs} args - Arguments to filter Friends to delete.
   * @example
   * // Delete a few Friends
   * const { count } = await prisma.friend.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends friendDeleteManyArgs>(args?: Prisma.SelectSubset<T, friendDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Friends.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Friends
   * const friend = await prisma.friend.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends friendUpdateManyArgs>(args: Prisma.SelectSubset<T, friendUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Friend.
   * @param {friendUpsertArgs} args - Arguments to update or create a Friend.
   * @example
   * // Update or create a Friend
   * const friend = await prisma.friend.upsert({
   *   create: {
   *     // ... data to create a Friend
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Friend we want to update
   *   }
   * })
   */
  upsert<T extends friendUpsertArgs>(args: Prisma.SelectSubset<T, friendUpsertArgs<ExtArgs>>): Prisma.Prisma__friendClient<runtime.Types.Result.GetResult<Prisma.$friendPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Friends.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendCountArgs} args - Arguments to filter Friends to count.
   * @example
   * // Count the number of Friends
   * const count = await prisma.friend.count({
   *   where: {
   *     // ... the filter for the Friends we want to count
   *   }
   * })
  **/
  count<T extends friendCountArgs>(
    args?: Prisma.Subset<T, friendCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], FriendCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Friend.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FriendAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends FriendAggregateArgs>(args: Prisma.Subset<T, FriendAggregateArgs>): Prisma.PrismaPromise<GetFriendAggregateType<T>>

  /**
   * Group by Friend.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {friendGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends friendGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: friendGroupByArgs['orderBy'] }
      : { orderBy?: friendGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, friendGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetFriendGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the friend model
 */
readonly fields: friendFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for friend.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__friendClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  friend<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the friend model
 */
export interface friendFieldRefs {
  readonly id: Prisma.FieldRef<"friend", 'Int'>
  readonly createdAt: Prisma.FieldRef<"friend", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"friend", 'DateTime'>
  readonly userId: Prisma.FieldRef<"friend", 'Int'>
  readonly friendId: Prisma.FieldRef<"friend", 'Int'>
  readonly note: Prisma.FieldRef<"friend", 'String'>
}
    

// Custom InputTypes
/**
 * friend findUnique
 */
export type friendFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter, which friend to fetch.
   */
  where: Prisma.friendWhereUniqueInput
}

/**
 * friend findUniqueOrThrow
 */
export type friendFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter, which friend to fetch.
   */
  where: Prisma.friendWhereUniqueInput
}

/**
 * friend findFirst
 */
export type friendFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter, which friend to fetch.
   */
  where?: Prisma.friendWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friends to fetch.
   */
  orderBy?: Prisma.friendOrderByWithRelationInput | Prisma.friendOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for friends.
   */
  cursor?: Prisma.friendWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friends from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friends.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of friends.
   */
  distinct?: Prisma.FriendScalarFieldEnum | Prisma.FriendScalarFieldEnum[]
}

/**
 * friend findFirstOrThrow
 */
export type friendFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter, which friend to fetch.
   */
  where?: Prisma.friendWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friends to fetch.
   */
  orderBy?: Prisma.friendOrderByWithRelationInput | Prisma.friendOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for friends.
   */
  cursor?: Prisma.friendWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friends from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friends.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of friends.
   */
  distinct?: Prisma.FriendScalarFieldEnum | Prisma.FriendScalarFieldEnum[]
}

/**
 * friend findMany
 */
export type friendFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter, which friends to fetch.
   */
  where?: Prisma.friendWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of friends to fetch.
   */
  orderBy?: Prisma.friendOrderByWithRelationInput | Prisma.friendOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing friends.
   */
  cursor?: Prisma.friendWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` friends from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` friends.
   */
  skip?: number
  distinct?: Prisma.FriendScalarFieldEnum | Prisma.FriendScalarFieldEnum[]
}

/**
 * friend create
 */
export type friendCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * The data needed to create a friend.
   */
  data: Prisma.XOR<Prisma.friendCreateInput, Prisma.friendUncheckedCreateInput>
}

/**
 * friend createMany
 */
export type friendCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many friends.
   */
  data: Prisma.friendCreateManyInput | Prisma.friendCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * friend update
 */
export type friendUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * The data needed to update a friend.
   */
  data: Prisma.XOR<Prisma.friendUpdateInput, Prisma.friendUncheckedUpdateInput>
  /**
   * Choose, which friend to update.
   */
  where: Prisma.friendWhereUniqueInput
}

/**
 * friend updateMany
 */
export type friendUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update friends.
   */
  data: Prisma.XOR<Prisma.friendUpdateManyMutationInput, Prisma.friendUncheckedUpdateManyInput>
  /**
   * Filter which friends to update
   */
  where?: Prisma.friendWhereInput
  /**
   * Limit how many friends to update.
   */
  limit?: number
}

/**
 * friend upsert
 */
export type friendUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * The filter to search for the friend to update in case it exists.
   */
  where: Prisma.friendWhereUniqueInput
  /**
   * In case the friend found by the `where` argument doesn't exist, create a new friend with this data.
   */
  create: Prisma.XOR<Prisma.friendCreateInput, Prisma.friendUncheckedCreateInput>
  /**
   * In case the friend was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.friendUpdateInput, Prisma.friendUncheckedUpdateInput>
}

/**
 * friend delete
 */
export type friendDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
  /**
   * Filter which friend to delete.
   */
  where: Prisma.friendWhereUniqueInput
}

/**
 * friend deleteMany
 */
export type friendDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which friends to delete
   */
  where?: Prisma.friendWhereInput
  /**
   * Limit how many friends to delete.
   */
  limit?: number
}

/**
 * friend without action
 */
export type friendDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the friend
   */
  select?: Prisma.friendSelect<ExtArgs> | null
  /**
   * Omit specific fields from the friend
   */
  omit?: Prisma.friendOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.friendInclude<ExtArgs> | null
}
