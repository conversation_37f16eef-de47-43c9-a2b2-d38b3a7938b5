
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_item` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_item
 * 
 */
export type user_itemModel = runtime.Types.Result.DefaultSelection<Prisma.$user_itemPayload>

export type AggregateUser_item = {
  _count: User_itemCountAggregateOutputType | null
  _avg: User_itemAvgAggregateOutputType | null
  _sum: User_itemSumAggregateOutputType | null
  _min: User_itemMinAggregateOutputType | null
  _max: User_itemMaxAggregateOutputType | null
}

export type User_itemAvgAggregateOutputType = {
  id: number | null
  count: number | null
  upgradeLevel: number | null
  userId: number | null
  itemId: number | null
}

export type User_itemSumAggregateOutputType = {
  id: number | null
  count: number | null
  upgradeLevel: number | null
  userId: number | null
  itemId: number | null
}

export type User_itemMinAggregateOutputType = {
  id: number | null
  count: number | null
  upgradeLevel: number | null
  quality: $Enums.ItemQuality | null
  isTradeable: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  itemId: number | null
}

export type User_itemMaxAggregateOutputType = {
  id: number | null
  count: number | null
  upgradeLevel: number | null
  quality: $Enums.ItemQuality | null
  isTradeable: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  itemId: number | null
}

export type User_itemCountAggregateOutputType = {
  id: number
  count: number
  upgradeLevel: number
  quality: number
  isTradeable: number
  createdAt: number
  updatedAt: number
  userId: number
  itemId: number
  _all: number
}


export type User_itemAvgAggregateInputType = {
  id?: true
  count?: true
  upgradeLevel?: true
  userId?: true
  itemId?: true
}

export type User_itemSumAggregateInputType = {
  id?: true
  count?: true
  upgradeLevel?: true
  userId?: true
  itemId?: true
}

export type User_itemMinAggregateInputType = {
  id?: true
  count?: true
  upgradeLevel?: true
  quality?: true
  isTradeable?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  itemId?: true
}

export type User_itemMaxAggregateInputType = {
  id?: true
  count?: true
  upgradeLevel?: true
  quality?: true
  isTradeable?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  itemId?: true
}

export type User_itemCountAggregateInputType = {
  id?: true
  count?: true
  upgradeLevel?: true
  quality?: true
  isTradeable?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  itemId?: true
  _all?: true
}

export type User_itemAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_item to aggregate.
   */
  where?: Prisma.user_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_items to fetch.
   */
  orderBy?: Prisma.user_itemOrderByWithRelationInput | Prisma.user_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_items
  **/
  _count?: true | User_itemCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_itemAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_itemSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_itemMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_itemMaxAggregateInputType
}

export type GetUser_itemAggregateType<T extends User_itemAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_item]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_item[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_item[P]>
}




export type user_itemGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_itemWhereInput
  orderBy?: Prisma.user_itemOrderByWithAggregationInput | Prisma.user_itemOrderByWithAggregationInput[]
  by: Prisma.User_itemScalarFieldEnum[] | Prisma.User_itemScalarFieldEnum
  having?: Prisma.user_itemScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_itemCountAggregateInputType | true
  _avg?: User_itemAvgAggregateInputType
  _sum?: User_itemSumAggregateInputType
  _min?: User_itemMinAggregateInputType
  _max?: User_itemMaxAggregateInputType
}

export type User_itemGroupByOutputType = {
  id: number
  count: number
  upgradeLevel: number
  quality: $Enums.ItemQuality
  isTradeable: boolean
  createdAt: Date
  updatedAt: Date
  userId: number | null
  itemId: number | null
  _count: User_itemCountAggregateOutputType | null
  _avg: User_itemAvgAggregateOutputType | null
  _sum: User_itemSumAggregateOutputType | null
  _min: User_itemMinAggregateOutputType | null
  _max: User_itemMaxAggregateOutputType | null
}

type GetUser_itemGroupByPayload<T extends user_itemGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_itemGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_itemGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_itemGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_itemGroupByOutputType[P]>
      }
    >
  >



export type user_itemWhereInput = {
  AND?: Prisma.user_itemWhereInput | Prisma.user_itemWhereInput[]
  OR?: Prisma.user_itemWhereInput[]
  NOT?: Prisma.user_itemWhereInput | Prisma.user_itemWhereInput[]
  id?: Prisma.IntFilter<"user_item"> | number
  count?: Prisma.IntFilter<"user_item"> | number
  upgradeLevel?: Prisma.IntFilter<"user_item"> | number
  quality?: Prisma.EnumItemQualityFilter<"user_item"> | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFilter<"user_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_item"> | number | null
  itemId?: Prisma.IntNullableFilter<"user_item"> | number | null
  equipped_items?: Prisma.Equipped_itemListRelationFilter
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}

export type user_itemOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  quality?: Prisma.SortOrder
  isTradeable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  equipped_items?: Prisma.equipped_itemOrderByRelationAggregateInput
  user?: Prisma.userOrderByWithRelationInput
  item?: Prisma.itemOrderByWithRelationInput
}

export type user_itemWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.user_itemWhereInput | Prisma.user_itemWhereInput[]
  OR?: Prisma.user_itemWhereInput[]
  NOT?: Prisma.user_itemWhereInput | Prisma.user_itemWhereInput[]
  count?: Prisma.IntFilter<"user_item"> | number
  upgradeLevel?: Prisma.IntFilter<"user_item"> | number
  quality?: Prisma.EnumItemQualityFilter<"user_item"> | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFilter<"user_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_item"> | number | null
  itemId?: Prisma.IntNullableFilter<"user_item"> | number | null
  equipped_items?: Prisma.Equipped_itemListRelationFilter
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}, "id">

export type user_itemOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  quality?: Prisma.SortOrder
  isTradeable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_itemCountOrderByAggregateInput
  _avg?: Prisma.user_itemAvgOrderByAggregateInput
  _max?: Prisma.user_itemMaxOrderByAggregateInput
  _min?: Prisma.user_itemMinOrderByAggregateInput
  _sum?: Prisma.user_itemSumOrderByAggregateInput
}

export type user_itemScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_itemScalarWhereWithAggregatesInput | Prisma.user_itemScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_itemScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_itemScalarWhereWithAggregatesInput | Prisma.user_itemScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_item"> | number
  count?: Prisma.IntWithAggregatesFilter<"user_item"> | number
  upgradeLevel?: Prisma.IntWithAggregatesFilter<"user_item"> | number
  quality?: Prisma.EnumItemQualityWithAggregatesFilter<"user_item"> | $Enums.ItemQuality
  isTradeable?: Prisma.BoolWithAggregatesFilter<"user_item"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_item"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_item"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"user_item"> | number | null
  itemId?: Prisma.IntNullableWithAggregatesFilter<"user_item"> | number | null
}

export type user_itemCreateInput = {
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  equipped_items?: Prisma.equipped_itemCreateNestedManyWithoutUser_itemInput
  user?: Prisma.userCreateNestedOneWithoutUser_itemInput
  item?: Prisma.itemCreateNestedOneWithoutUser_itemInput
}

export type user_itemUncheckedCreateInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  itemId?: number | null
  equipped_items?: Prisma.equipped_itemUncheckedCreateNestedManyWithoutUser_itemInput
}

export type user_itemUpdateInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  equipped_items?: Prisma.equipped_itemUpdateManyWithoutUser_itemNestedInput
  user?: Prisma.userUpdateOneWithoutUser_itemNestedInput
  item?: Prisma.itemUpdateOneWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equipped_items?: Prisma.equipped_itemUncheckedUpdateManyWithoutUser_itemNestedInput
}

export type user_itemCreateManyInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  itemId?: number | null
}

export type user_itemUpdateManyMutationInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_itemUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_itemNullableScalarRelationFilter = {
  is?: Prisma.user_itemWhereInput | null
  isNot?: Prisma.user_itemWhereInput | null
}

export type User_itemListRelationFilter = {
  every?: Prisma.user_itemWhereInput
  some?: Prisma.user_itemWhereInput
  none?: Prisma.user_itemWhereInput
}

export type user_itemOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_itemCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  quality?: Prisma.SortOrder
  isTradeable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type user_itemAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type user_itemMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  quality?: Prisma.SortOrder
  isTradeable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type user_itemMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  quality?: Prisma.SortOrder
  isTradeable?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type user_itemSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  count?: Prisma.SortOrder
  upgradeLevel?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type user_itemCreateNestedOneWithoutEquipped_itemsInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedCreateWithoutEquipped_itemsInput>
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutEquipped_itemsInput
  connect?: Prisma.user_itemWhereUniqueInput
}

export type user_itemUpdateOneWithoutEquipped_itemsNestedInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedCreateWithoutEquipped_itemsInput>
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutEquipped_itemsInput
  upsert?: Prisma.user_itemUpsertWithoutEquipped_itemsInput
  disconnect?: Prisma.user_itemWhereInput | boolean
  delete?: Prisma.user_itemWhereInput | boolean
  connect?: Prisma.user_itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.user_itemUpdateToOneWithWhereWithoutEquipped_itemsInput, Prisma.user_itemUpdateWithoutEquipped_itemsInput>, Prisma.user_itemUncheckedUpdateWithoutEquipped_itemsInput>
}

export type user_itemCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput> | Prisma.user_itemCreateWithoutItemInput[] | Prisma.user_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutItemInput | Prisma.user_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.user_itemCreateManyItemInputEnvelope
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
}

export type user_itemUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput> | Prisma.user_itemCreateWithoutItemInput[] | Prisma.user_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutItemInput | Prisma.user_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.user_itemCreateManyItemInputEnvelope
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
}

export type user_itemUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput> | Prisma.user_itemCreateWithoutItemInput[] | Prisma.user_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutItemInput | Prisma.user_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.user_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.user_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.user_itemCreateManyItemInputEnvelope
  set?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  disconnect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  delete?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  update?: Prisma.user_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.user_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.user_itemUpdateManyWithWhereWithoutItemInput | Prisma.user_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
}

export type user_itemUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput> | Prisma.user_itemCreateWithoutItemInput[] | Prisma.user_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutItemInput | Prisma.user_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.user_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.user_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.user_itemCreateManyItemInputEnvelope
  set?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  disconnect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  delete?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  update?: Prisma.user_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.user_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.user_itemUpdateManyWithWhereWithoutItemInput | Prisma.user_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
}

export type user_itemCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput> | Prisma.user_itemCreateWithoutUserInput[] | Prisma.user_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutUserInput | Prisma.user_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_itemCreateManyUserInputEnvelope
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
}

export type user_itemUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput> | Prisma.user_itemCreateWithoutUserInput[] | Prisma.user_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutUserInput | Prisma.user_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_itemCreateManyUserInputEnvelope
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
}

export type user_itemUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput> | Prisma.user_itemCreateWithoutUserInput[] | Prisma.user_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutUserInput | Prisma.user_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.user_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_itemCreateManyUserInputEnvelope
  set?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  disconnect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  delete?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  update?: Prisma.user_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.user_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_itemUpdateManyWithWhereWithoutUserInput | Prisma.user_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
}

export type user_itemUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput> | Prisma.user_itemCreateWithoutUserInput[] | Prisma.user_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_itemCreateOrConnectWithoutUserInput | Prisma.user_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.user_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_itemCreateManyUserInputEnvelope
  set?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  disconnect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  delete?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  connect?: Prisma.user_itemWhereUniqueInput | Prisma.user_itemWhereUniqueInput[]
  update?: Prisma.user_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.user_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_itemUpdateManyWithWhereWithoutUserInput | Prisma.user_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
}

export type EnumItemQualityFieldUpdateOperationsInput = {
  set?: $Enums.ItemQuality
}

export type user_itemCreateWithoutEquipped_itemsInput = {
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutUser_itemInput
  item?: Prisma.itemCreateNestedOneWithoutUser_itemInput
}

export type user_itemUncheckedCreateWithoutEquipped_itemsInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  itemId?: number | null
}

export type user_itemCreateOrConnectWithoutEquipped_itemsInput = {
  where: Prisma.user_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.user_itemCreateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedCreateWithoutEquipped_itemsInput>
}

export type user_itemUpsertWithoutEquipped_itemsInput = {
  update: Prisma.XOR<Prisma.user_itemUpdateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedUpdateWithoutEquipped_itemsInput>
  create: Prisma.XOR<Prisma.user_itemCreateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedCreateWithoutEquipped_itemsInput>
  where?: Prisma.user_itemWhereInput
}

export type user_itemUpdateToOneWithWhereWithoutEquipped_itemsInput = {
  where?: Prisma.user_itemWhereInput
  data: Prisma.XOR<Prisma.user_itemUpdateWithoutEquipped_itemsInput, Prisma.user_itemUncheckedUpdateWithoutEquipped_itemsInput>
}

export type user_itemUpdateWithoutEquipped_itemsInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutUser_itemNestedInput
  item?: Prisma.itemUpdateOneWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateWithoutEquipped_itemsInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_itemCreateWithoutItemInput = {
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  equipped_items?: Prisma.equipped_itemCreateNestedManyWithoutUser_itemInput
  user?: Prisma.userCreateNestedOneWithoutUser_itemInput
}

export type user_itemUncheckedCreateWithoutItemInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  equipped_items?: Prisma.equipped_itemUncheckedCreateNestedManyWithoutUser_itemInput
}

export type user_itemCreateOrConnectWithoutItemInput = {
  where: Prisma.user_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput>
}

export type user_itemCreateManyItemInputEnvelope = {
  data: Prisma.user_itemCreateManyItemInput | Prisma.user_itemCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type user_itemUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.user_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.user_itemUpdateWithoutItemInput, Prisma.user_itemUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.user_itemCreateWithoutItemInput, Prisma.user_itemUncheckedCreateWithoutItemInput>
}

export type user_itemUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.user_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.user_itemUpdateWithoutItemInput, Prisma.user_itemUncheckedUpdateWithoutItemInput>
}

export type user_itemUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.user_itemScalarWhereInput
  data: Prisma.XOR<Prisma.user_itemUpdateManyMutationInput, Prisma.user_itemUncheckedUpdateManyWithoutItemInput>
}

export type user_itemScalarWhereInput = {
  AND?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
  OR?: Prisma.user_itemScalarWhereInput[]
  NOT?: Prisma.user_itemScalarWhereInput | Prisma.user_itemScalarWhereInput[]
  id?: Prisma.IntFilter<"user_item"> | number
  count?: Prisma.IntFilter<"user_item"> | number
  upgradeLevel?: Prisma.IntFilter<"user_item"> | number
  quality?: Prisma.EnumItemQualityFilter<"user_item"> | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFilter<"user_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_item"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_item"> | number | null
  itemId?: Prisma.IntNullableFilter<"user_item"> | number | null
}

export type user_itemCreateWithoutUserInput = {
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  equipped_items?: Prisma.equipped_itemCreateNestedManyWithoutUser_itemInput
  item?: Prisma.itemCreateNestedOneWithoutUser_itemInput
}

export type user_itemUncheckedCreateWithoutUserInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
  equipped_items?: Prisma.equipped_itemUncheckedCreateNestedManyWithoutUser_itemInput
}

export type user_itemCreateOrConnectWithoutUserInput = {
  where: Prisma.user_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput>
}

export type user_itemCreateManyUserInputEnvelope = {
  data: Prisma.user_itemCreateManyUserInput | Prisma.user_itemCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_itemUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.user_itemUpdateWithoutUserInput, Prisma.user_itemUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_itemCreateWithoutUserInput, Prisma.user_itemUncheckedCreateWithoutUserInput>
}

export type user_itemUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.user_itemUpdateWithoutUserInput, Prisma.user_itemUncheckedUpdateWithoutUserInput>
}

export type user_itemUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_itemScalarWhereInput
  data: Prisma.XOR<Prisma.user_itemUpdateManyMutationInput, Prisma.user_itemUncheckedUpdateManyWithoutUserInput>
}

export type user_itemCreateManyItemInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_itemUpdateWithoutItemInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  equipped_items?: Prisma.equipped_itemUpdateManyWithoutUser_itemNestedInput
  user?: Prisma.userUpdateOneWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equipped_items?: Prisma.equipped_itemUncheckedUpdateManyWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_itemCreateManyUserInput = {
  id?: number
  count: number
  upgradeLevel?: number
  quality?: $Enums.ItemQuality
  isTradeable?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
}

export type user_itemUpdateWithoutUserInput = {
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  equipped_items?: Prisma.equipped_itemUpdateManyWithoutUser_itemNestedInput
  item?: Prisma.itemUpdateOneWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  equipped_items?: Prisma.equipped_itemUncheckedUpdateManyWithoutUser_itemNestedInput
}

export type user_itemUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  count?: Prisma.IntFieldUpdateOperationsInput | number
  upgradeLevel?: Prisma.IntFieldUpdateOperationsInput | number
  quality?: Prisma.EnumItemQualityFieldUpdateOperationsInput | $Enums.ItemQuality
  isTradeable?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}


/**
 * Count Type User_itemCountOutputType
 */

export type User_itemCountOutputType = {
  equipped_items: number
}

export type User_itemCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  equipped_items?: boolean | User_itemCountOutputTypeCountEquipped_itemsArgs
}

/**
 * User_itemCountOutputType without action
 */
export type User_itemCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User_itemCountOutputType
   */
  select?: Prisma.User_itemCountOutputTypeSelect<ExtArgs> | null
}

/**
 * User_itemCountOutputType without action
 */
export type User_itemCountOutputTypeCountEquipped_itemsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.equipped_itemWhereInput
}


export type user_itemSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  count?: boolean
  upgradeLevel?: boolean
  quality?: boolean
  isTradeable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  itemId?: boolean
  equipped_items?: boolean | Prisma.user_item$equipped_itemsArgs<ExtArgs>
  user?: boolean | Prisma.user_item$userArgs<ExtArgs>
  item?: boolean | Prisma.user_item$itemArgs<ExtArgs>
  _count?: boolean | Prisma.User_itemCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_item"]>



export type user_itemSelectScalar = {
  id?: boolean
  count?: boolean
  upgradeLevel?: boolean
  quality?: boolean
  isTradeable?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  itemId?: boolean
}

export type user_itemOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "count" | "upgradeLevel" | "quality" | "isTradeable" | "createdAt" | "updatedAt" | "userId" | "itemId", ExtArgs["result"]["user_item"]>
export type user_itemInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  equipped_items?: boolean | Prisma.user_item$equipped_itemsArgs<ExtArgs>
  user?: boolean | Prisma.user_item$userArgs<ExtArgs>
  item?: boolean | Prisma.user_item$itemArgs<ExtArgs>
  _count?: boolean | Prisma.User_itemCountOutputTypeDefaultArgs<ExtArgs>
}

export type $user_itemPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_item"
  objects: {
    equipped_items: Prisma.$equipped_itemPayload<ExtArgs>[]
    user: Prisma.$userPayload<ExtArgs> | null
    item: Prisma.$itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    count: number
    upgradeLevel: number
    quality: $Enums.ItemQuality
    isTradeable: boolean
    createdAt: Date
    updatedAt: Date
    userId: number | null
    itemId: number | null
  }, ExtArgs["result"]["user_item"]>
  composites: {}
}

export type user_itemGetPayload<S extends boolean | null | undefined | user_itemDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_itemPayload, S>

export type user_itemCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_itemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_itemCountAggregateInputType | true
  }

export interface user_itemDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_item'], meta: { name: 'user_item' } }
  /**
   * Find zero or one User_item that matches the filter.
   * @param {user_itemFindUniqueArgs} args - Arguments to find a User_item
   * @example
   * // Get one User_item
   * const user_item = await prisma.user_item.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_itemFindUniqueArgs>(args: Prisma.SelectSubset<T, user_itemFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_item that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_itemFindUniqueOrThrowArgs} args - Arguments to find a User_item
   * @example
   * // Get one User_item
   * const user_item = await prisma.user_item.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_itemFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_itemFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_item that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemFindFirstArgs} args - Arguments to find a User_item
   * @example
   * // Get one User_item
   * const user_item = await prisma.user_item.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_itemFindFirstArgs>(args?: Prisma.SelectSubset<T, user_itemFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_item that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemFindFirstOrThrowArgs} args - Arguments to find a User_item
   * @example
   * // Get one User_item
   * const user_item = await prisma.user_item.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_itemFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_itemFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_items that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_items
   * const user_items = await prisma.user_item.findMany()
   * 
   * // Get first 10 User_items
   * const user_items = await prisma.user_item.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_itemWithIdOnly = await prisma.user_item.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_itemFindManyArgs>(args?: Prisma.SelectSubset<T, user_itemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_item.
   * @param {user_itemCreateArgs} args - Arguments to create a User_item.
   * @example
   * // Create one User_item
   * const User_item = await prisma.user_item.create({
   *   data: {
   *     // ... data to create a User_item
   *   }
   * })
   * 
   */
  create<T extends user_itemCreateArgs>(args: Prisma.SelectSubset<T, user_itemCreateArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_items.
   * @param {user_itemCreateManyArgs} args - Arguments to create many User_items.
   * @example
   * // Create many User_items
   * const user_item = await prisma.user_item.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_itemCreateManyArgs>(args?: Prisma.SelectSubset<T, user_itemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_item.
   * @param {user_itemDeleteArgs} args - Arguments to delete one User_item.
   * @example
   * // Delete one User_item
   * const User_item = await prisma.user_item.delete({
   *   where: {
   *     // ... filter to delete one User_item
   *   }
   * })
   * 
   */
  delete<T extends user_itemDeleteArgs>(args: Prisma.SelectSubset<T, user_itemDeleteArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_item.
   * @param {user_itemUpdateArgs} args - Arguments to update one User_item.
   * @example
   * // Update one User_item
   * const user_item = await prisma.user_item.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_itemUpdateArgs>(args: Prisma.SelectSubset<T, user_itemUpdateArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_items.
   * @param {user_itemDeleteManyArgs} args - Arguments to filter User_items to delete.
   * @example
   * // Delete a few User_items
   * const { count } = await prisma.user_item.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_itemDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_itemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_items
   * const user_item = await prisma.user_item.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_itemUpdateManyArgs>(args: Prisma.SelectSubset<T, user_itemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_item.
   * @param {user_itemUpsertArgs} args - Arguments to update or create a User_item.
   * @example
   * // Update or create a User_item
   * const user_item = await prisma.user_item.upsert({
   *   create: {
   *     // ... data to create a User_item
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_item we want to update
   *   }
   * })
   */
  upsert<T extends user_itemUpsertArgs>(args: Prisma.SelectSubset<T, user_itemUpsertArgs<ExtArgs>>): Prisma.Prisma__user_itemClient<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemCountArgs} args - Arguments to filter User_items to count.
   * @example
   * // Count the number of User_items
   * const count = await prisma.user_item.count({
   *   where: {
   *     // ... the filter for the User_items we want to count
   *   }
   * })
  **/
  count<T extends user_itemCountArgs>(
    args?: Prisma.Subset<T, user_itemCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_itemCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_itemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_itemAggregateArgs>(args: Prisma.Subset<T, User_itemAggregateArgs>): Prisma.PrismaPromise<GetUser_itemAggregateType<T>>

  /**
   * Group by User_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_itemGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_itemGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_itemGroupByArgs['orderBy'] }
      : { orderBy?: user_itemGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_itemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_itemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_item model
 */
readonly fields: user_itemFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_item.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_itemClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  equipped_items<T extends Prisma.user_item$equipped_itemsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_item$equipped_itemsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$equipped_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user<T extends Prisma.user_item$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_item$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  item<T extends Prisma.user_item$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_item$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_item model
 */
export interface user_itemFieldRefs {
  readonly id: Prisma.FieldRef<"user_item", 'Int'>
  readonly count: Prisma.FieldRef<"user_item", 'Int'>
  readonly upgradeLevel: Prisma.FieldRef<"user_item", 'Int'>
  readonly quality: Prisma.FieldRef<"user_item", 'ItemQuality'>
  readonly isTradeable: Prisma.FieldRef<"user_item", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"user_item", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_item", 'DateTime'>
  readonly userId: Prisma.FieldRef<"user_item", 'Int'>
  readonly itemId: Prisma.FieldRef<"user_item", 'Int'>
}
    

// Custom InputTypes
/**
 * user_item findUnique
 */
export type user_itemFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter, which user_item to fetch.
   */
  where: Prisma.user_itemWhereUniqueInput
}

/**
 * user_item findUniqueOrThrow
 */
export type user_itemFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter, which user_item to fetch.
   */
  where: Prisma.user_itemWhereUniqueInput
}

/**
 * user_item findFirst
 */
export type user_itemFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter, which user_item to fetch.
   */
  where?: Prisma.user_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_items to fetch.
   */
  orderBy?: Prisma.user_itemOrderByWithRelationInput | Prisma.user_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_items.
   */
  cursor?: Prisma.user_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_items.
   */
  distinct?: Prisma.User_itemScalarFieldEnum | Prisma.User_itemScalarFieldEnum[]
}

/**
 * user_item findFirstOrThrow
 */
export type user_itemFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter, which user_item to fetch.
   */
  where?: Prisma.user_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_items to fetch.
   */
  orderBy?: Prisma.user_itemOrderByWithRelationInput | Prisma.user_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_items.
   */
  cursor?: Prisma.user_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_items.
   */
  distinct?: Prisma.User_itemScalarFieldEnum | Prisma.User_itemScalarFieldEnum[]
}

/**
 * user_item findMany
 */
export type user_itemFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter, which user_items to fetch.
   */
  where?: Prisma.user_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_items to fetch.
   */
  orderBy?: Prisma.user_itemOrderByWithRelationInput | Prisma.user_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_items.
   */
  cursor?: Prisma.user_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_items.
   */
  skip?: number
  distinct?: Prisma.User_itemScalarFieldEnum | Prisma.User_itemScalarFieldEnum[]
}

/**
 * user_item create
 */
export type user_itemCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * The data needed to create a user_item.
   */
  data: Prisma.XOR<Prisma.user_itemCreateInput, Prisma.user_itemUncheckedCreateInput>
}

/**
 * user_item createMany
 */
export type user_itemCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_items.
   */
  data: Prisma.user_itemCreateManyInput | Prisma.user_itemCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_item update
 */
export type user_itemUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * The data needed to update a user_item.
   */
  data: Prisma.XOR<Prisma.user_itemUpdateInput, Prisma.user_itemUncheckedUpdateInput>
  /**
   * Choose, which user_item to update.
   */
  where: Prisma.user_itemWhereUniqueInput
}

/**
 * user_item updateMany
 */
export type user_itemUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_items.
   */
  data: Prisma.XOR<Prisma.user_itemUpdateManyMutationInput, Prisma.user_itemUncheckedUpdateManyInput>
  /**
   * Filter which user_items to update
   */
  where?: Prisma.user_itemWhereInput
  /**
   * Limit how many user_items to update.
   */
  limit?: number
}

/**
 * user_item upsert
 */
export type user_itemUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * The filter to search for the user_item to update in case it exists.
   */
  where: Prisma.user_itemWhereUniqueInput
  /**
   * In case the user_item found by the `where` argument doesn't exist, create a new user_item with this data.
   */
  create: Prisma.XOR<Prisma.user_itemCreateInput, Prisma.user_itemUncheckedCreateInput>
  /**
   * In case the user_item was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_itemUpdateInput, Prisma.user_itemUncheckedUpdateInput>
}

/**
 * user_item delete
 */
export type user_itemDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  /**
   * Filter which user_item to delete.
   */
  where: Prisma.user_itemWhereUniqueInput
}

/**
 * user_item deleteMany
 */
export type user_itemDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_items to delete
   */
  where?: Prisma.user_itemWhereInput
  /**
   * Limit how many user_items to delete.
   */
  limit?: number
}

/**
 * user_item.equipped_items
 */
export type user_item$equipped_itemsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the equipped_item
   */
  select?: Prisma.equipped_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the equipped_item
   */
  omit?: Prisma.equipped_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.equipped_itemInclude<ExtArgs> | null
  where?: Prisma.equipped_itemWhereInput
  orderBy?: Prisma.equipped_itemOrderByWithRelationInput | Prisma.equipped_itemOrderByWithRelationInput[]
  cursor?: Prisma.equipped_itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Equipped_itemScalarFieldEnum | Prisma.Equipped_itemScalarFieldEnum[]
}

/**
 * user_item.user
 */
export type user_item$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * user_item.item
 */
export type user_item$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * user_item without action
 */
export type user_itemDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
}
