
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `rival` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model rival
 * 
 */
export type rivalModel = runtime.Types.Result.DefaultSelection<Prisma.$rivalPayload>

export type AggregateRival = {
  _count: RivalCountAggregateOutputType | null
  _avg: RivalAvgAggregateOutputType | null
  _sum: RivalSumAggregateOutputType | null
  _min: RivalMinAggregateOutputType | null
  _max: RivalMaxAggregateOutputType | null
}

export type RivalAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  rivalId: number | null
}

export type RivalSumAggregateOutputType = {
  id: number | null
  userId: number | null
  rivalId: number | null
}

export type RivalMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  rivalId: number | null
  note: string | null
}

export type RivalMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  rivalId: number | null
  note: string | null
}

export type RivalCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  userId: number
  rivalId: number
  note: number
  _all: number
}


export type RivalAvgAggregateInputType = {
  id?: true
  userId?: true
  rivalId?: true
}

export type RivalSumAggregateInputType = {
  id?: true
  userId?: true
  rivalId?: true
}

export type RivalMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  rivalId?: true
  note?: true
}

export type RivalMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  rivalId?: true
  note?: true
}

export type RivalCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  rivalId?: true
  note?: true
  _all?: true
}

export type RivalAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which rival to aggregate.
   */
  where?: Prisma.rivalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of rivals to fetch.
   */
  orderBy?: Prisma.rivalOrderByWithRelationInput | Prisma.rivalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.rivalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` rivals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` rivals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned rivals
  **/
  _count?: true | RivalCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: RivalAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: RivalSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RivalMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RivalMaxAggregateInputType
}

export type GetRivalAggregateType<T extends RivalAggregateArgs> = {
      [P in keyof T & keyof AggregateRival]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRival[P]>
    : Prisma.GetScalarType<T[P], AggregateRival[P]>
}




export type rivalGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.rivalWhereInput
  orderBy?: Prisma.rivalOrderByWithAggregationInput | Prisma.rivalOrderByWithAggregationInput[]
  by: Prisma.RivalScalarFieldEnum[] | Prisma.RivalScalarFieldEnum
  having?: Prisma.rivalScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RivalCountAggregateInputType | true
  _avg?: RivalAvgAggregateInputType
  _sum?: RivalSumAggregateInputType
  _min?: RivalMinAggregateInputType
  _max?: RivalMaxAggregateInputType
}

export type RivalGroupByOutputType = {
  id: number
  createdAt: Date
  updatedAt: Date
  userId: number
  rivalId: number
  note: string | null
  _count: RivalCountAggregateOutputType | null
  _avg: RivalAvgAggregateOutputType | null
  _sum: RivalSumAggregateOutputType | null
  _min: RivalMinAggregateOutputType | null
  _max: RivalMaxAggregateOutputType | null
}

type GetRivalGroupByPayload<T extends rivalGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RivalGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RivalGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RivalGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RivalGroupByOutputType[P]>
      }
    >
  >



export type rivalWhereInput = {
  AND?: Prisma.rivalWhereInput | Prisma.rivalWhereInput[]
  OR?: Prisma.rivalWhereInput[]
  NOT?: Prisma.rivalWhereInput | Prisma.rivalWhereInput[]
  id?: Prisma.IntFilter<"rival"> | number
  createdAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  userId?: Prisma.IntFilter<"rival"> | number
  rivalId?: Prisma.IntFilter<"rival"> | number
  note?: Prisma.StringNullableFilter<"rival"> | string | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  rival?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type rivalOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
  note?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  rival?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.rivalOrderByRelevanceInput
}

export type rivalWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  userId_rivalId?: Prisma.rivalUserIdRivalIdCompoundUniqueInput
  AND?: Prisma.rivalWhereInput | Prisma.rivalWhereInput[]
  OR?: Prisma.rivalWhereInput[]
  NOT?: Prisma.rivalWhereInput | Prisma.rivalWhereInput[]
  createdAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  userId?: Prisma.IntFilter<"rival"> | number
  rivalId?: Prisma.IntFilter<"rival"> | number
  note?: Prisma.StringNullableFilter<"rival"> | string | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
  rival?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id" | "userId_rivalId">

export type rivalOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
  note?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.rivalCountOrderByAggregateInput
  _avg?: Prisma.rivalAvgOrderByAggregateInput
  _max?: Prisma.rivalMaxOrderByAggregateInput
  _min?: Prisma.rivalMinOrderByAggregateInput
  _sum?: Prisma.rivalSumOrderByAggregateInput
}

export type rivalScalarWhereWithAggregatesInput = {
  AND?: Prisma.rivalScalarWhereWithAggregatesInput | Prisma.rivalScalarWhereWithAggregatesInput[]
  OR?: Prisma.rivalScalarWhereWithAggregatesInput[]
  NOT?: Prisma.rivalScalarWhereWithAggregatesInput | Prisma.rivalScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"rival"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"rival"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"rival"> | Date | string
  userId?: Prisma.IntWithAggregatesFilter<"rival"> | number
  rivalId?: Prisma.IntWithAggregatesFilter<"rival"> | number
  note?: Prisma.StringNullableWithAggregatesFilter<"rival"> | string | null
}

export type rivalCreateInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  user: Prisma.userCreateNestedOneWithoutRivalsInput
  rival: Prisma.userCreateNestedOneWithoutRivalOfInput
}

export type rivalUncheckedCreateInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  rivalId: number
  note?: string | null
}

export type rivalUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user?: Prisma.userUpdateOneRequiredWithoutRivalsNestedInput
  rival?: Prisma.userUpdateOneRequiredWithoutRivalOfNestedInput
}

export type rivalUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  rivalId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type rivalCreateManyInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  rivalId: number
  note?: string | null
}

export type rivalUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type rivalUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  rivalId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type RivalListRelationFilter = {
  every?: Prisma.rivalWhereInput
  some?: Prisma.rivalWhereInput
  none?: Prisma.rivalWhereInput
}

export type rivalOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type rivalOrderByRelevanceInput = {
  fields: Prisma.rivalOrderByRelevanceFieldEnum | Prisma.rivalOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type rivalUserIdRivalIdCompoundUniqueInput = {
  userId: number
  rivalId: number
}

export type rivalCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type rivalAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
}

export type rivalMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type rivalMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
  note?: Prisma.SortOrder
}

export type rivalSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  rivalId?: Prisma.SortOrder
}

export type rivalCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput> | Prisma.rivalCreateWithoutUserInput[] | Prisma.rivalUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutUserInput | Prisma.rivalCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.rivalCreateManyUserInputEnvelope
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
}

export type rivalCreateNestedManyWithoutRivalInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput> | Prisma.rivalCreateWithoutRivalInput[] | Prisma.rivalUncheckedCreateWithoutRivalInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutRivalInput | Prisma.rivalCreateOrConnectWithoutRivalInput[]
  createMany?: Prisma.rivalCreateManyRivalInputEnvelope
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
}

export type rivalUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput> | Prisma.rivalCreateWithoutUserInput[] | Prisma.rivalUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutUserInput | Prisma.rivalCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.rivalCreateManyUserInputEnvelope
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
}

export type rivalUncheckedCreateNestedManyWithoutRivalInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput> | Prisma.rivalCreateWithoutRivalInput[] | Prisma.rivalUncheckedCreateWithoutRivalInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutRivalInput | Prisma.rivalCreateOrConnectWithoutRivalInput[]
  createMany?: Prisma.rivalCreateManyRivalInputEnvelope
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
}

export type rivalUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput> | Prisma.rivalCreateWithoutUserInput[] | Prisma.rivalUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutUserInput | Prisma.rivalCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.rivalUpsertWithWhereUniqueWithoutUserInput | Prisma.rivalUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.rivalCreateManyUserInputEnvelope
  set?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  disconnect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  delete?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  update?: Prisma.rivalUpdateWithWhereUniqueWithoutUserInput | Prisma.rivalUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.rivalUpdateManyWithWhereWithoutUserInput | Prisma.rivalUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
}

export type rivalUpdateManyWithoutRivalNestedInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput> | Prisma.rivalCreateWithoutRivalInput[] | Prisma.rivalUncheckedCreateWithoutRivalInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutRivalInput | Prisma.rivalCreateOrConnectWithoutRivalInput[]
  upsert?: Prisma.rivalUpsertWithWhereUniqueWithoutRivalInput | Prisma.rivalUpsertWithWhereUniqueWithoutRivalInput[]
  createMany?: Prisma.rivalCreateManyRivalInputEnvelope
  set?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  disconnect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  delete?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  update?: Prisma.rivalUpdateWithWhereUniqueWithoutRivalInput | Prisma.rivalUpdateWithWhereUniqueWithoutRivalInput[]
  updateMany?: Prisma.rivalUpdateManyWithWhereWithoutRivalInput | Prisma.rivalUpdateManyWithWhereWithoutRivalInput[]
  deleteMany?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
}

export type rivalUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput> | Prisma.rivalCreateWithoutUserInput[] | Prisma.rivalUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutUserInput | Prisma.rivalCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.rivalUpsertWithWhereUniqueWithoutUserInput | Prisma.rivalUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.rivalCreateManyUserInputEnvelope
  set?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  disconnect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  delete?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  update?: Prisma.rivalUpdateWithWhereUniqueWithoutUserInput | Prisma.rivalUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.rivalUpdateManyWithWhereWithoutUserInput | Prisma.rivalUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
}

export type rivalUncheckedUpdateManyWithoutRivalNestedInput = {
  create?: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput> | Prisma.rivalCreateWithoutRivalInput[] | Prisma.rivalUncheckedCreateWithoutRivalInput[]
  connectOrCreate?: Prisma.rivalCreateOrConnectWithoutRivalInput | Prisma.rivalCreateOrConnectWithoutRivalInput[]
  upsert?: Prisma.rivalUpsertWithWhereUniqueWithoutRivalInput | Prisma.rivalUpsertWithWhereUniqueWithoutRivalInput[]
  createMany?: Prisma.rivalCreateManyRivalInputEnvelope
  set?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  disconnect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  delete?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  connect?: Prisma.rivalWhereUniqueInput | Prisma.rivalWhereUniqueInput[]
  update?: Prisma.rivalUpdateWithWhereUniqueWithoutRivalInput | Prisma.rivalUpdateWithWhereUniqueWithoutRivalInput[]
  updateMany?: Prisma.rivalUpdateManyWithWhereWithoutRivalInput | Prisma.rivalUpdateManyWithWhereWithoutRivalInput[]
  deleteMany?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
}

export type rivalCreateWithoutUserInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  rival: Prisma.userCreateNestedOneWithoutRivalOfInput
}

export type rivalUncheckedCreateWithoutUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  rivalId: number
  note?: string | null
}

export type rivalCreateOrConnectWithoutUserInput = {
  where: Prisma.rivalWhereUniqueInput
  create: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput>
}

export type rivalCreateManyUserInputEnvelope = {
  data: Prisma.rivalCreateManyUserInput | Prisma.rivalCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type rivalCreateWithoutRivalInput = {
  createdAt?: Date | string
  updatedAt?: Date | string
  note?: string | null
  user: Prisma.userCreateNestedOneWithoutRivalsInput
}

export type rivalUncheckedCreateWithoutRivalInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  note?: string | null
}

export type rivalCreateOrConnectWithoutRivalInput = {
  where: Prisma.rivalWhereUniqueInput
  create: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput>
}

export type rivalCreateManyRivalInputEnvelope = {
  data: Prisma.rivalCreateManyRivalInput | Prisma.rivalCreateManyRivalInput[]
  skipDuplicates?: boolean
}

export type rivalUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.rivalWhereUniqueInput
  update: Prisma.XOR<Prisma.rivalUpdateWithoutUserInput, Prisma.rivalUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.rivalCreateWithoutUserInput, Prisma.rivalUncheckedCreateWithoutUserInput>
}

export type rivalUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.rivalWhereUniqueInput
  data: Prisma.XOR<Prisma.rivalUpdateWithoutUserInput, Prisma.rivalUncheckedUpdateWithoutUserInput>
}

export type rivalUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.rivalScalarWhereInput
  data: Prisma.XOR<Prisma.rivalUpdateManyMutationInput, Prisma.rivalUncheckedUpdateManyWithoutUserInput>
}

export type rivalScalarWhereInput = {
  AND?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
  OR?: Prisma.rivalScalarWhereInput[]
  NOT?: Prisma.rivalScalarWhereInput | Prisma.rivalScalarWhereInput[]
  id?: Prisma.IntFilter<"rival"> | number
  createdAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"rival"> | Date | string
  userId?: Prisma.IntFilter<"rival"> | number
  rivalId?: Prisma.IntFilter<"rival"> | number
  note?: Prisma.StringNullableFilter<"rival"> | string | null
}

export type rivalUpsertWithWhereUniqueWithoutRivalInput = {
  where: Prisma.rivalWhereUniqueInput
  update: Prisma.XOR<Prisma.rivalUpdateWithoutRivalInput, Prisma.rivalUncheckedUpdateWithoutRivalInput>
  create: Prisma.XOR<Prisma.rivalCreateWithoutRivalInput, Prisma.rivalUncheckedCreateWithoutRivalInput>
}

export type rivalUpdateWithWhereUniqueWithoutRivalInput = {
  where: Prisma.rivalWhereUniqueInput
  data: Prisma.XOR<Prisma.rivalUpdateWithoutRivalInput, Prisma.rivalUncheckedUpdateWithoutRivalInput>
}

export type rivalUpdateManyWithWhereWithoutRivalInput = {
  where: Prisma.rivalScalarWhereInput
  data: Prisma.XOR<Prisma.rivalUpdateManyMutationInput, Prisma.rivalUncheckedUpdateManyWithoutRivalInput>
}

export type rivalCreateManyUserInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  rivalId: number
  note?: string | null
}

export type rivalCreateManyRivalInput = {
  id?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
  note?: string | null
}

export type rivalUpdateWithoutUserInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rival?: Prisma.userUpdateOneRequiredWithoutRivalOfNestedInput
}

export type rivalUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  rivalId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type rivalUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  rivalId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type rivalUpdateWithoutRivalInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user?: Prisma.userUpdateOneRequiredWithoutRivalsNestedInput
}

export type rivalUncheckedUpdateWithoutRivalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type rivalUncheckedUpdateManyWithoutRivalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
  note?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}



export type rivalSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  rivalId?: boolean
  note?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  rival?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["rival"]>



export type rivalSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  rivalId?: boolean
  note?: boolean
}

export type rivalOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "createdAt" | "updatedAt" | "userId" | "rivalId" | "note", ExtArgs["result"]["rival"]>
export type rivalInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
  rival?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $rivalPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "rival"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
    rival: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    createdAt: Date
    updatedAt: Date
    userId: number
    rivalId: number
    note: string | null
  }, ExtArgs["result"]["rival"]>
  composites: {}
}

export type rivalGetPayload<S extends boolean | null | undefined | rivalDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$rivalPayload, S>

export type rivalCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<rivalFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: RivalCountAggregateInputType | true
  }

export interface rivalDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['rival'], meta: { name: 'rival' } }
  /**
   * Find zero or one Rival that matches the filter.
   * @param {rivalFindUniqueArgs} args - Arguments to find a Rival
   * @example
   * // Get one Rival
   * const rival = await prisma.rival.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends rivalFindUniqueArgs>(args: Prisma.SelectSubset<T, rivalFindUniqueArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Rival that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {rivalFindUniqueOrThrowArgs} args - Arguments to find a Rival
   * @example
   * // Get one Rival
   * const rival = await prisma.rival.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends rivalFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, rivalFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Rival that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalFindFirstArgs} args - Arguments to find a Rival
   * @example
   * // Get one Rival
   * const rival = await prisma.rival.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends rivalFindFirstArgs>(args?: Prisma.SelectSubset<T, rivalFindFirstArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Rival that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalFindFirstOrThrowArgs} args - Arguments to find a Rival
   * @example
   * // Get one Rival
   * const rival = await prisma.rival.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends rivalFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, rivalFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Rivals that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Rivals
   * const rivals = await prisma.rival.findMany()
   * 
   * // Get first 10 Rivals
   * const rivals = await prisma.rival.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const rivalWithIdOnly = await prisma.rival.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends rivalFindManyArgs>(args?: Prisma.SelectSubset<T, rivalFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Rival.
   * @param {rivalCreateArgs} args - Arguments to create a Rival.
   * @example
   * // Create one Rival
   * const Rival = await prisma.rival.create({
   *   data: {
   *     // ... data to create a Rival
   *   }
   * })
   * 
   */
  create<T extends rivalCreateArgs>(args: Prisma.SelectSubset<T, rivalCreateArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Rivals.
   * @param {rivalCreateManyArgs} args - Arguments to create many Rivals.
   * @example
   * // Create many Rivals
   * const rival = await prisma.rival.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends rivalCreateManyArgs>(args?: Prisma.SelectSubset<T, rivalCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Rival.
   * @param {rivalDeleteArgs} args - Arguments to delete one Rival.
   * @example
   * // Delete one Rival
   * const Rival = await prisma.rival.delete({
   *   where: {
   *     // ... filter to delete one Rival
   *   }
   * })
   * 
   */
  delete<T extends rivalDeleteArgs>(args: Prisma.SelectSubset<T, rivalDeleteArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Rival.
   * @param {rivalUpdateArgs} args - Arguments to update one Rival.
   * @example
   * // Update one Rival
   * const rival = await prisma.rival.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends rivalUpdateArgs>(args: Prisma.SelectSubset<T, rivalUpdateArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Rivals.
   * @param {rivalDeleteManyArgs} args - Arguments to filter Rivals to delete.
   * @example
   * // Delete a few Rivals
   * const { count } = await prisma.rival.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends rivalDeleteManyArgs>(args?: Prisma.SelectSubset<T, rivalDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Rivals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Rivals
   * const rival = await prisma.rival.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends rivalUpdateManyArgs>(args: Prisma.SelectSubset<T, rivalUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Rival.
   * @param {rivalUpsertArgs} args - Arguments to update or create a Rival.
   * @example
   * // Update or create a Rival
   * const rival = await prisma.rival.upsert({
   *   create: {
   *     // ... data to create a Rival
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Rival we want to update
   *   }
   * })
   */
  upsert<T extends rivalUpsertArgs>(args: Prisma.SelectSubset<T, rivalUpsertArgs<ExtArgs>>): Prisma.Prisma__rivalClient<runtime.Types.Result.GetResult<Prisma.$rivalPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Rivals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalCountArgs} args - Arguments to filter Rivals to count.
   * @example
   * // Count the number of Rivals
   * const count = await prisma.rival.count({
   *   where: {
   *     // ... the filter for the Rivals we want to count
   *   }
   * })
  **/
  count<T extends rivalCountArgs>(
    args?: Prisma.Subset<T, rivalCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RivalCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Rival.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RivalAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RivalAggregateArgs>(args: Prisma.Subset<T, RivalAggregateArgs>): Prisma.PrismaPromise<GetRivalAggregateType<T>>

  /**
   * Group by Rival.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {rivalGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends rivalGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: rivalGroupByArgs['orderBy'] }
      : { orderBy?: rivalGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, rivalGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRivalGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the rival model
 */
readonly fields: rivalFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for rival.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__rivalClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  rival<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the rival model
 */
export interface rivalFieldRefs {
  readonly id: Prisma.FieldRef<"rival", 'Int'>
  readonly createdAt: Prisma.FieldRef<"rival", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"rival", 'DateTime'>
  readonly userId: Prisma.FieldRef<"rival", 'Int'>
  readonly rivalId: Prisma.FieldRef<"rival", 'Int'>
  readonly note: Prisma.FieldRef<"rival", 'String'>
}
    

// Custom InputTypes
/**
 * rival findUnique
 */
export type rivalFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter, which rival to fetch.
   */
  where: Prisma.rivalWhereUniqueInput
}

/**
 * rival findUniqueOrThrow
 */
export type rivalFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter, which rival to fetch.
   */
  where: Prisma.rivalWhereUniqueInput
}

/**
 * rival findFirst
 */
export type rivalFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter, which rival to fetch.
   */
  where?: Prisma.rivalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of rivals to fetch.
   */
  orderBy?: Prisma.rivalOrderByWithRelationInput | Prisma.rivalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for rivals.
   */
  cursor?: Prisma.rivalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` rivals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` rivals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of rivals.
   */
  distinct?: Prisma.RivalScalarFieldEnum | Prisma.RivalScalarFieldEnum[]
}

/**
 * rival findFirstOrThrow
 */
export type rivalFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter, which rival to fetch.
   */
  where?: Prisma.rivalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of rivals to fetch.
   */
  orderBy?: Prisma.rivalOrderByWithRelationInput | Prisma.rivalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for rivals.
   */
  cursor?: Prisma.rivalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` rivals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` rivals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of rivals.
   */
  distinct?: Prisma.RivalScalarFieldEnum | Prisma.RivalScalarFieldEnum[]
}

/**
 * rival findMany
 */
export type rivalFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter, which rivals to fetch.
   */
  where?: Prisma.rivalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of rivals to fetch.
   */
  orderBy?: Prisma.rivalOrderByWithRelationInput | Prisma.rivalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing rivals.
   */
  cursor?: Prisma.rivalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` rivals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` rivals.
   */
  skip?: number
  distinct?: Prisma.RivalScalarFieldEnum | Prisma.RivalScalarFieldEnum[]
}

/**
 * rival create
 */
export type rivalCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * The data needed to create a rival.
   */
  data: Prisma.XOR<Prisma.rivalCreateInput, Prisma.rivalUncheckedCreateInput>
}

/**
 * rival createMany
 */
export type rivalCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many rivals.
   */
  data: Prisma.rivalCreateManyInput | Prisma.rivalCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * rival update
 */
export type rivalUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * The data needed to update a rival.
   */
  data: Prisma.XOR<Prisma.rivalUpdateInput, Prisma.rivalUncheckedUpdateInput>
  /**
   * Choose, which rival to update.
   */
  where: Prisma.rivalWhereUniqueInput
}

/**
 * rival updateMany
 */
export type rivalUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update rivals.
   */
  data: Prisma.XOR<Prisma.rivalUpdateManyMutationInput, Prisma.rivalUncheckedUpdateManyInput>
  /**
   * Filter which rivals to update
   */
  where?: Prisma.rivalWhereInput
  /**
   * Limit how many rivals to update.
   */
  limit?: number
}

/**
 * rival upsert
 */
export type rivalUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * The filter to search for the rival to update in case it exists.
   */
  where: Prisma.rivalWhereUniqueInput
  /**
   * In case the rival found by the `where` argument doesn't exist, create a new rival with this data.
   */
  create: Prisma.XOR<Prisma.rivalCreateInput, Prisma.rivalUncheckedCreateInput>
  /**
   * In case the rival was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.rivalUpdateInput, Prisma.rivalUncheckedUpdateInput>
}

/**
 * rival delete
 */
export type rivalDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
  /**
   * Filter which rival to delete.
   */
  where: Prisma.rivalWhereUniqueInput
}

/**
 * rival deleteMany
 */
export type rivalDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which rivals to delete
   */
  where?: Prisma.rivalWhereInput
  /**
   * Limit how many rivals to delete.
   */
  limit?: number
}

/**
 * rival without action
 */
export type rivalDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the rival
   */
  select?: Prisma.rivalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the rival
   */
  omit?: Prisma.rivalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.rivalInclude<ExtArgs> | null
}
