
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `shrine_goal` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model shrine_goal
 * 
 */
export type shrine_goalModel = runtime.Types.Result.DefaultSelection<Prisma.$shrine_goalPayload>

export type AggregateShrine_goal = {
  _count: Shrine_goalCountAggregateOutputType | null
  _avg: Shrine_goalAvgAggregateOutputType | null
  _sum: Shrine_goalSumAggregateOutputType | null
  _min: Shrine_goalMinAggregateOutputType | null
  _max: Shrine_goalMaxAggregateOutputType | null
}

export type Shrine_goalAvgAggregateOutputType = {
  id: number | null
  donationGoal: number | null
  donationAmount: number | null
}

export type Shrine_goalSumAggregateOutputType = {
  id: number | null
  donationGoal: number | null
  donationAmount: number | null
}

export type Shrine_goalMinAggregateOutputType = {
  id: number | null
  donationGoal: number | null
  donationAmount: number | null
  goalReached: boolean | null
  goalDate: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Shrine_goalMaxAggregateOutputType = {
  id: number | null
  donationGoal: number | null
  donationAmount: number | null
  goalReached: boolean | null
  goalDate: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type Shrine_goalCountAggregateOutputType = {
  id: number
  donationGoal: number
  donationAmount: number
  goalReached: number
  goalDate: number
  buffRewards: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type Shrine_goalAvgAggregateInputType = {
  id?: true
  donationGoal?: true
  donationAmount?: true
}

export type Shrine_goalSumAggregateInputType = {
  id?: true
  donationGoal?: true
  donationAmount?: true
}

export type Shrine_goalMinAggregateInputType = {
  id?: true
  donationGoal?: true
  donationAmount?: true
  goalReached?: true
  goalDate?: true
  createdAt?: true
  updatedAt?: true
}

export type Shrine_goalMaxAggregateInputType = {
  id?: true
  donationGoal?: true
  donationAmount?: true
  goalReached?: true
  goalDate?: true
  createdAt?: true
  updatedAt?: true
}

export type Shrine_goalCountAggregateInputType = {
  id?: true
  donationGoal?: true
  donationAmount?: true
  goalReached?: true
  goalDate?: true
  buffRewards?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type Shrine_goalAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shrine_goal to aggregate.
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_goals to fetch.
   */
  orderBy?: Prisma.shrine_goalOrderByWithRelationInput | Prisma.shrine_goalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.shrine_goalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_goals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_goals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned shrine_goals
  **/
  _count?: true | Shrine_goalCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Shrine_goalAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Shrine_goalSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Shrine_goalMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Shrine_goalMaxAggregateInputType
}

export type GetShrine_goalAggregateType<T extends Shrine_goalAggregateArgs> = {
      [P in keyof T & keyof AggregateShrine_goal]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateShrine_goal[P]>
    : Prisma.GetScalarType<T[P], AggregateShrine_goal[P]>
}




export type shrine_goalGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shrine_goalWhereInput
  orderBy?: Prisma.shrine_goalOrderByWithAggregationInput | Prisma.shrine_goalOrderByWithAggregationInput[]
  by: Prisma.Shrine_goalScalarFieldEnum[] | Prisma.Shrine_goalScalarFieldEnum
  having?: Prisma.shrine_goalScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Shrine_goalCountAggregateInputType | true
  _avg?: Shrine_goalAvgAggregateInputType
  _sum?: Shrine_goalSumAggregateInputType
  _min?: Shrine_goalMinAggregateInputType
  _max?: Shrine_goalMaxAggregateInputType
}

export type Shrine_goalGroupByOutputType = {
  id: number
  donationGoal: number
  donationAmount: number
  goalReached: boolean
  goalDate: Date
  buffRewards: runtime.JsonValue
  createdAt: Date
  updatedAt: Date
  _count: Shrine_goalCountAggregateOutputType | null
  _avg: Shrine_goalAvgAggregateOutputType | null
  _sum: Shrine_goalSumAggregateOutputType | null
  _min: Shrine_goalMinAggregateOutputType | null
  _max: Shrine_goalMaxAggregateOutputType | null
}

type GetShrine_goalGroupByPayload<T extends shrine_goalGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Shrine_goalGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Shrine_goalGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Shrine_goalGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Shrine_goalGroupByOutputType[P]>
      }
    >
  >



export type shrine_goalWhereInput = {
  AND?: Prisma.shrine_goalWhereInput | Prisma.shrine_goalWhereInput[]
  OR?: Prisma.shrine_goalWhereInput[]
  NOT?: Prisma.shrine_goalWhereInput | Prisma.shrine_goalWhereInput[]
  id?: Prisma.IntFilter<"shrine_goal"> | number
  donationGoal?: Prisma.IntFilter<"shrine_goal"> | number
  donationAmount?: Prisma.IntFilter<"shrine_goal"> | number
  goalReached?: Prisma.BoolFilter<"shrine_goal"> | boolean
  goalDate?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  buffRewards?: Prisma.JsonFilter<"shrine_goal">
  createdAt?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  shrine_donation?: Prisma.Shrine_donationListRelationFilter
}

export type shrine_goalOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
  goalReached?: Prisma.SortOrder
  goalDate?: Prisma.SortOrder
  buffRewards?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrine_donation?: Prisma.shrine_donationOrderByRelationAggregateInput
}

export type shrine_goalWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.shrine_goalWhereInput | Prisma.shrine_goalWhereInput[]
  OR?: Prisma.shrine_goalWhereInput[]
  NOT?: Prisma.shrine_goalWhereInput | Prisma.shrine_goalWhereInput[]
  donationGoal?: Prisma.IntFilter<"shrine_goal"> | number
  donationAmount?: Prisma.IntFilter<"shrine_goal"> | number
  goalReached?: Prisma.BoolFilter<"shrine_goal"> | boolean
  goalDate?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  buffRewards?: Prisma.JsonFilter<"shrine_goal">
  createdAt?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"shrine_goal"> | Date | string
  shrine_donation?: Prisma.Shrine_donationListRelationFilter
}, "id">

export type shrine_goalOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
  goalReached?: Prisma.SortOrder
  goalDate?: Prisma.SortOrder
  buffRewards?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.shrine_goalCountOrderByAggregateInput
  _avg?: Prisma.shrine_goalAvgOrderByAggregateInput
  _max?: Prisma.shrine_goalMaxOrderByAggregateInput
  _min?: Prisma.shrine_goalMinOrderByAggregateInput
  _sum?: Prisma.shrine_goalSumOrderByAggregateInput
}

export type shrine_goalScalarWhereWithAggregatesInput = {
  AND?: Prisma.shrine_goalScalarWhereWithAggregatesInput | Prisma.shrine_goalScalarWhereWithAggregatesInput[]
  OR?: Prisma.shrine_goalScalarWhereWithAggregatesInput[]
  NOT?: Prisma.shrine_goalScalarWhereWithAggregatesInput | Prisma.shrine_goalScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"shrine_goal"> | number
  donationGoal?: Prisma.IntWithAggregatesFilter<"shrine_goal"> | number
  donationAmount?: Prisma.IntWithAggregatesFilter<"shrine_goal"> | number
  goalReached?: Prisma.BoolWithAggregatesFilter<"shrine_goal"> | boolean
  goalDate?: Prisma.DateTimeWithAggregatesFilter<"shrine_goal"> | Date | string
  buffRewards?: Prisma.JsonWithAggregatesFilter<"shrine_goal">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"shrine_goal"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"shrine_goal"> | Date | string
}

export type shrine_goalCreateInput = {
  donationGoal: number
  donationAmount?: number
  goalReached?: boolean
  goalDate: Date | string
  buffRewards:PrismaJson.ShrineBuffRewards
  createdAt?: Date | string
  updatedAt?: Date | string
  shrine_donation?: Prisma.shrine_donationCreateNestedManyWithoutShrine_goalInput
}

export type shrine_goalUncheckedCreateInput = {
  id?: number
  donationGoal: number
  donationAmount?: number
  goalReached?: boolean
  goalDate: Date | string
  buffRewards:PrismaJson.ShrineBuffRewards
  createdAt?: Date | string
  updatedAt?: Date | string
  shrine_donation?: Prisma.shrine_donationUncheckedCreateNestedManyWithoutShrine_goalInput
}

export type shrine_goalUpdateInput = {
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrine_donation?: Prisma.shrine_donationUpdateManyWithoutShrine_goalNestedInput
}

export type shrine_goalUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrine_donation?: Prisma.shrine_donationUncheckedUpdateManyWithoutShrine_goalNestedInput
}

export type shrine_goalCreateManyInput = {
  id?: number
  donationGoal: number
  donationAmount?: number
  goalReached?: boolean
  goalDate: Date | string
  buffRewards:PrismaJson.ShrineBuffRewards
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type shrine_goalUpdateManyMutationInput = {
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type shrine_goalUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type Shrine_goalNullableScalarRelationFilter = {
  is?: Prisma.shrine_goalWhereInput | null
  isNot?: Prisma.shrine_goalWhereInput | null
}

export type shrine_goalCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
  goalReached?: Prisma.SortOrder
  goalDate?: Prisma.SortOrder
  buffRewards?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type shrine_goalAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
}

export type shrine_goalMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
  goalReached?: Prisma.SortOrder
  goalDate?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type shrine_goalMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
  goalReached?: Prisma.SortOrder
  goalDate?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type shrine_goalSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donationGoal?: Prisma.SortOrder
  donationAmount?: Prisma.SortOrder
}

export type shrine_goalCreateNestedOneWithoutShrine_donationInput = {
  create?: Prisma.XOR<Prisma.shrine_goalCreateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedCreateWithoutShrine_donationInput>
  connectOrCreate?: Prisma.shrine_goalCreateOrConnectWithoutShrine_donationInput
  connect?: Prisma.shrine_goalWhereUniqueInput
}

export type shrine_goalUpdateOneWithoutShrine_donationNestedInput = {
  create?: Prisma.XOR<Prisma.shrine_goalCreateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedCreateWithoutShrine_donationInput>
  connectOrCreate?: Prisma.shrine_goalCreateOrConnectWithoutShrine_donationInput
  upsert?: Prisma.shrine_goalUpsertWithoutShrine_donationInput
  disconnect?: Prisma.shrine_goalWhereInput | boolean
  delete?: Prisma.shrine_goalWhereInput | boolean
  connect?: Prisma.shrine_goalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.shrine_goalUpdateToOneWithWhereWithoutShrine_donationInput, Prisma.shrine_goalUpdateWithoutShrine_donationInput>, Prisma.shrine_goalUncheckedUpdateWithoutShrine_donationInput>
}

export type shrine_goalCreateWithoutShrine_donationInput = {
  donationGoal: number
  donationAmount?: number
  goalReached?: boolean
  goalDate: Date | string
  buffRewards:PrismaJson.ShrineBuffRewards
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type shrine_goalUncheckedCreateWithoutShrine_donationInput = {
  id?: number
  donationGoal: number
  donationAmount?: number
  goalReached?: boolean
  goalDate: Date | string
  buffRewards:PrismaJson.ShrineBuffRewards
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type shrine_goalCreateOrConnectWithoutShrine_donationInput = {
  where: Prisma.shrine_goalWhereUniqueInput
  create: Prisma.XOR<Prisma.shrine_goalCreateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedCreateWithoutShrine_donationInput>
}

export type shrine_goalUpsertWithoutShrine_donationInput = {
  update: Prisma.XOR<Prisma.shrine_goalUpdateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedUpdateWithoutShrine_donationInput>
  create: Prisma.XOR<Prisma.shrine_goalCreateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedCreateWithoutShrine_donationInput>
  where?: Prisma.shrine_goalWhereInput
}

export type shrine_goalUpdateToOneWithWhereWithoutShrine_donationInput = {
  where?: Prisma.shrine_goalWhereInput
  data: Prisma.XOR<Prisma.shrine_goalUpdateWithoutShrine_donationInput, Prisma.shrine_goalUncheckedUpdateWithoutShrine_donationInput>
}

export type shrine_goalUpdateWithoutShrine_donationInput = {
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type shrine_goalUncheckedUpdateWithoutShrine_donationInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  donationGoal?: Prisma.IntFieldUpdateOperationsInput | number
  donationAmount?: Prisma.IntFieldUpdateOperationsInput | number
  goalReached?: Prisma.BoolFieldUpdateOperationsInput | boolean
  goalDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  buffRewards?:PrismaJson.ShrineBuffRewards
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type Shrine_goalCountOutputType
 */

export type Shrine_goalCountOutputType = {
  shrine_donation: number
}

export type Shrine_goalCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shrine_donation?: boolean | Shrine_goalCountOutputTypeCountShrine_donationArgs
}

/**
 * Shrine_goalCountOutputType without action
 */
export type Shrine_goalCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Shrine_goalCountOutputType
   */
  select?: Prisma.Shrine_goalCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Shrine_goalCountOutputType without action
 */
export type Shrine_goalCountOutputTypeCountShrine_donationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shrine_donationWhereInput
}


export type shrine_goalSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  donationGoal?: boolean
  donationAmount?: boolean
  goalReached?: boolean
  goalDate?: boolean
  buffRewards?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shrine_donation?: boolean | Prisma.shrine_goal$shrine_donationArgs<ExtArgs>
  _count?: boolean | Prisma.Shrine_goalCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["shrine_goal"]>



export type shrine_goalSelectScalar = {
  id?: boolean
  donationGoal?: boolean
  donationAmount?: boolean
  goalReached?: boolean
  goalDate?: boolean
  buffRewards?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type shrine_goalOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "donationGoal" | "donationAmount" | "goalReached" | "goalDate" | "buffRewards" | "createdAt" | "updatedAt", ExtArgs["result"]["shrine_goal"]>
export type shrine_goalInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shrine_donation?: boolean | Prisma.shrine_goal$shrine_donationArgs<ExtArgs>
  _count?: boolean | Prisma.Shrine_goalCountOutputTypeDefaultArgs<ExtArgs>
}

export type $shrine_goalPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "shrine_goal"
  objects: {
    shrine_donation: Prisma.$shrine_donationPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    donationGoal: number
    donationAmount: number
    goalReached: boolean
    goalDate: Date
    /**
     * [ShrineBuffRewards]
     */
    buffRewards:PrismaJson.ShrineBuffRewards
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["shrine_goal"]>
  composites: {}
}

export type shrine_goalGetPayload<S extends boolean | null | undefined | shrine_goalDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload, S>

export type shrine_goalCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<shrine_goalFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Shrine_goalCountAggregateInputType | true
  }

export interface shrine_goalDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['shrine_goal'], meta: { name: 'shrine_goal' } }
  /**
   * Find zero or one Shrine_goal that matches the filter.
   * @param {shrine_goalFindUniqueArgs} args - Arguments to find a Shrine_goal
   * @example
   * // Get one Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends shrine_goalFindUniqueArgs>(args: Prisma.SelectSubset<T, shrine_goalFindUniqueArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Shrine_goal that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {shrine_goalFindUniqueOrThrowArgs} args - Arguments to find a Shrine_goal
   * @example
   * // Get one Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends shrine_goalFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, shrine_goalFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shrine_goal that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalFindFirstArgs} args - Arguments to find a Shrine_goal
   * @example
   * // Get one Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends shrine_goalFindFirstArgs>(args?: Prisma.SelectSubset<T, shrine_goalFindFirstArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shrine_goal that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalFindFirstOrThrowArgs} args - Arguments to find a Shrine_goal
   * @example
   * // Get one Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends shrine_goalFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, shrine_goalFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Shrine_goals that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Shrine_goals
   * const shrine_goals = await prisma.shrine_goal.findMany()
   * 
   * // Get first 10 Shrine_goals
   * const shrine_goals = await prisma.shrine_goal.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const shrine_goalWithIdOnly = await prisma.shrine_goal.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends shrine_goalFindManyArgs>(args?: Prisma.SelectSubset<T, shrine_goalFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Shrine_goal.
   * @param {shrine_goalCreateArgs} args - Arguments to create a Shrine_goal.
   * @example
   * // Create one Shrine_goal
   * const Shrine_goal = await prisma.shrine_goal.create({
   *   data: {
   *     // ... data to create a Shrine_goal
   *   }
   * })
   * 
   */
  create<T extends shrine_goalCreateArgs>(args: Prisma.SelectSubset<T, shrine_goalCreateArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Shrine_goals.
   * @param {shrine_goalCreateManyArgs} args - Arguments to create many Shrine_goals.
   * @example
   * // Create many Shrine_goals
   * const shrine_goal = await prisma.shrine_goal.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends shrine_goalCreateManyArgs>(args?: Prisma.SelectSubset<T, shrine_goalCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Shrine_goal.
   * @param {shrine_goalDeleteArgs} args - Arguments to delete one Shrine_goal.
   * @example
   * // Delete one Shrine_goal
   * const Shrine_goal = await prisma.shrine_goal.delete({
   *   where: {
   *     // ... filter to delete one Shrine_goal
   *   }
   * })
   * 
   */
  delete<T extends shrine_goalDeleteArgs>(args: Prisma.SelectSubset<T, shrine_goalDeleteArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Shrine_goal.
   * @param {shrine_goalUpdateArgs} args - Arguments to update one Shrine_goal.
   * @example
   * // Update one Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends shrine_goalUpdateArgs>(args: Prisma.SelectSubset<T, shrine_goalUpdateArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Shrine_goals.
   * @param {shrine_goalDeleteManyArgs} args - Arguments to filter Shrine_goals to delete.
   * @example
   * // Delete a few Shrine_goals
   * const { count } = await prisma.shrine_goal.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends shrine_goalDeleteManyArgs>(args?: Prisma.SelectSubset<T, shrine_goalDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Shrine_goals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Shrine_goals
   * const shrine_goal = await prisma.shrine_goal.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends shrine_goalUpdateManyArgs>(args: Prisma.SelectSubset<T, shrine_goalUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Shrine_goal.
   * @param {shrine_goalUpsertArgs} args - Arguments to update or create a Shrine_goal.
   * @example
   * // Update or create a Shrine_goal
   * const shrine_goal = await prisma.shrine_goal.upsert({
   *   create: {
   *     // ... data to create a Shrine_goal
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Shrine_goal we want to update
   *   }
   * })
   */
  upsert<T extends shrine_goalUpsertArgs>(args: Prisma.SelectSubset<T, shrine_goalUpsertArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Shrine_goals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalCountArgs} args - Arguments to filter Shrine_goals to count.
   * @example
   * // Count the number of Shrine_goals
   * const count = await prisma.shrine_goal.count({
   *   where: {
   *     // ... the filter for the Shrine_goals we want to count
   *   }
   * })
  **/
  count<T extends shrine_goalCountArgs>(
    args?: Prisma.Subset<T, shrine_goalCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Shrine_goalCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Shrine_goal.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Shrine_goalAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Shrine_goalAggregateArgs>(args: Prisma.Subset<T, Shrine_goalAggregateArgs>): Prisma.PrismaPromise<GetShrine_goalAggregateType<T>>

  /**
   * Group by Shrine_goal.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_goalGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends shrine_goalGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: shrine_goalGroupByArgs['orderBy'] }
      : { orderBy?: shrine_goalGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, shrine_goalGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetShrine_goalGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the shrine_goal model
 */
readonly fields: shrine_goalFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for shrine_goal.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__shrine_goalClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  shrine_donation<T extends Prisma.shrine_goal$shrine_donationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shrine_goal$shrine_donationArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the shrine_goal model
 */
export interface shrine_goalFieldRefs {
  readonly id: Prisma.FieldRef<"shrine_goal", 'Int'>
  readonly donationGoal: Prisma.FieldRef<"shrine_goal", 'Int'>
  readonly donationAmount: Prisma.FieldRef<"shrine_goal", 'Int'>
  readonly goalReached: Prisma.FieldRef<"shrine_goal", 'Boolean'>
  readonly goalDate: Prisma.FieldRef<"shrine_goal", 'DateTime'>
  readonly buffRewards: Prisma.FieldRef<"shrine_goal", 'Json'>
  readonly createdAt: Prisma.FieldRef<"shrine_goal", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"shrine_goal", 'DateTime'>
}
    

// Custom InputTypes
/**
 * shrine_goal findUnique
 */
export type shrine_goalFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter, which shrine_goal to fetch.
   */
  where: Prisma.shrine_goalWhereUniqueInput
}

/**
 * shrine_goal findUniqueOrThrow
 */
export type shrine_goalFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter, which shrine_goal to fetch.
   */
  where: Prisma.shrine_goalWhereUniqueInput
}

/**
 * shrine_goal findFirst
 */
export type shrine_goalFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter, which shrine_goal to fetch.
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_goals to fetch.
   */
  orderBy?: Prisma.shrine_goalOrderByWithRelationInput | Prisma.shrine_goalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shrine_goals.
   */
  cursor?: Prisma.shrine_goalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_goals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_goals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shrine_goals.
   */
  distinct?: Prisma.Shrine_goalScalarFieldEnum | Prisma.Shrine_goalScalarFieldEnum[]
}

/**
 * shrine_goal findFirstOrThrow
 */
export type shrine_goalFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter, which shrine_goal to fetch.
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_goals to fetch.
   */
  orderBy?: Prisma.shrine_goalOrderByWithRelationInput | Prisma.shrine_goalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shrine_goals.
   */
  cursor?: Prisma.shrine_goalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_goals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_goals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shrine_goals.
   */
  distinct?: Prisma.Shrine_goalScalarFieldEnum | Prisma.Shrine_goalScalarFieldEnum[]
}

/**
 * shrine_goal findMany
 */
export type shrine_goalFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter, which shrine_goals to fetch.
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_goals to fetch.
   */
  orderBy?: Prisma.shrine_goalOrderByWithRelationInput | Prisma.shrine_goalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing shrine_goals.
   */
  cursor?: Prisma.shrine_goalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_goals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_goals.
   */
  skip?: number
  distinct?: Prisma.Shrine_goalScalarFieldEnum | Prisma.Shrine_goalScalarFieldEnum[]
}

/**
 * shrine_goal create
 */
export type shrine_goalCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * The data needed to create a shrine_goal.
   */
  data: Prisma.XOR<Prisma.shrine_goalCreateInput, Prisma.shrine_goalUncheckedCreateInput>
}

/**
 * shrine_goal createMany
 */
export type shrine_goalCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many shrine_goals.
   */
  data: Prisma.shrine_goalCreateManyInput | Prisma.shrine_goalCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * shrine_goal update
 */
export type shrine_goalUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * The data needed to update a shrine_goal.
   */
  data: Prisma.XOR<Prisma.shrine_goalUpdateInput, Prisma.shrine_goalUncheckedUpdateInput>
  /**
   * Choose, which shrine_goal to update.
   */
  where: Prisma.shrine_goalWhereUniqueInput
}

/**
 * shrine_goal updateMany
 */
export type shrine_goalUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update shrine_goals.
   */
  data: Prisma.XOR<Prisma.shrine_goalUpdateManyMutationInput, Prisma.shrine_goalUncheckedUpdateManyInput>
  /**
   * Filter which shrine_goals to update
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * Limit how many shrine_goals to update.
   */
  limit?: number
}

/**
 * shrine_goal upsert
 */
export type shrine_goalUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * The filter to search for the shrine_goal to update in case it exists.
   */
  where: Prisma.shrine_goalWhereUniqueInput
  /**
   * In case the shrine_goal found by the `where` argument doesn't exist, create a new shrine_goal with this data.
   */
  create: Prisma.XOR<Prisma.shrine_goalCreateInput, Prisma.shrine_goalUncheckedCreateInput>
  /**
   * In case the shrine_goal was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.shrine_goalUpdateInput, Prisma.shrine_goalUncheckedUpdateInput>
}

/**
 * shrine_goal delete
 */
export type shrine_goalDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  /**
   * Filter which shrine_goal to delete.
   */
  where: Prisma.shrine_goalWhereUniqueInput
}

/**
 * shrine_goal deleteMany
 */
export type shrine_goalDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shrine_goals to delete
   */
  where?: Prisma.shrine_goalWhereInput
  /**
   * Limit how many shrine_goals to delete.
   */
  limit?: number
}

/**
 * shrine_goal.shrine_donation
 */
export type shrine_goal$shrine_donationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  where?: Prisma.shrine_donationWhereInput
  orderBy?: Prisma.shrine_donationOrderByWithRelationInput | Prisma.shrine_donationOrderByWithRelationInput[]
  cursor?: Prisma.shrine_donationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Shrine_donationScalarFieldEnum | Prisma.Shrine_donationScalarFieldEnum[]
}

/**
 * shrine_goal without action
 */
export type shrine_goalDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
}
