
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `daily_mission` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model daily_mission
 * 
 */
export type daily_missionModel = runtime.Types.Result.DefaultSelection<Prisma.$daily_missionPayload>

export type AggregateDaily_mission = {
  _count: Daily_missionCountAggregateOutputType | null
  _avg: Daily_missionAvgAggregateOutputType | null
  _sum: Daily_missionSumAggregateOutputType | null
  _min: Daily_missionMinAggregateOutputType | null
  _max: Daily_missionMaxAggregateOutputType | null
}

export type Daily_missionAvgAggregateOutputType = {
  id: number | null
  tier: number | null
  duration: number | null
  minCashReward: number | null
  maxCashReward: number | null
  minExpReward: number | null
  maxExpReward: number | null
  levelReq: number | null
  hoursReq: number | null
  itemRewardQuantity: number | null
  itemRewardId: number | null
}

export type Daily_missionSumAggregateOutputType = {
  id: number | null
  tier: number | null
  duration: number | null
  minCashReward: number | null
  maxCashReward: number | null
  minExpReward: number | null
  maxExpReward: number | null
  levelReq: number | null
  hoursReq: number | null
  itemRewardQuantity: number | null
  itemRewardId: number | null
}

export type Daily_missionMinAggregateOutputType = {
  id: number | null
  tier: number | null
  missionName: string | null
  description: string | null
  missionDate: Date | null
  duration: number | null
  minCashReward: number | null
  maxCashReward: number | null
  minExpReward: number | null
  maxExpReward: number | null
  levelReq: number | null
  hoursReq: number | null
  itemRewardQuantity: number | null
  createdAt: Date | null
  updatedAt: Date | null
  itemRewardId: number | null
}

export type Daily_missionMaxAggregateOutputType = {
  id: number | null
  tier: number | null
  missionName: string | null
  description: string | null
  missionDate: Date | null
  duration: number | null
  minCashReward: number | null
  maxCashReward: number | null
  minExpReward: number | null
  maxExpReward: number | null
  levelReq: number | null
  hoursReq: number | null
  itemRewardQuantity: number | null
  createdAt: Date | null
  updatedAt: Date | null
  itemRewardId: number | null
}

export type Daily_missionCountAggregateOutputType = {
  id: number
  tier: number
  missionName: number
  description: number
  missionDate: number
  duration: number
  minCashReward: number
  maxCashReward: number
  minExpReward: number
  maxExpReward: number
  levelReq: number
  hoursReq: number
  itemRewardQuantity: number
  createdAt: number
  updatedAt: number
  itemRewardId: number
  _all: number
}


export type Daily_missionAvgAggregateInputType = {
  id?: true
  tier?: true
  duration?: true
  minCashReward?: true
  maxCashReward?: true
  minExpReward?: true
  maxExpReward?: true
  levelReq?: true
  hoursReq?: true
  itemRewardQuantity?: true
  itemRewardId?: true
}

export type Daily_missionSumAggregateInputType = {
  id?: true
  tier?: true
  duration?: true
  minCashReward?: true
  maxCashReward?: true
  minExpReward?: true
  maxExpReward?: true
  levelReq?: true
  hoursReq?: true
  itemRewardQuantity?: true
  itemRewardId?: true
}

export type Daily_missionMinAggregateInputType = {
  id?: true
  tier?: true
  missionName?: true
  description?: true
  missionDate?: true
  duration?: true
  minCashReward?: true
  maxCashReward?: true
  minExpReward?: true
  maxExpReward?: true
  levelReq?: true
  hoursReq?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
}

export type Daily_missionMaxAggregateInputType = {
  id?: true
  tier?: true
  missionName?: true
  description?: true
  missionDate?: true
  duration?: true
  minCashReward?: true
  maxCashReward?: true
  minExpReward?: true
  maxExpReward?: true
  levelReq?: true
  hoursReq?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
}

export type Daily_missionCountAggregateInputType = {
  id?: true
  tier?: true
  missionName?: true
  description?: true
  missionDate?: true
  duration?: true
  minCashReward?: true
  maxCashReward?: true
  minExpReward?: true
  maxExpReward?: true
  levelReq?: true
  hoursReq?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
  _all?: true
}

export type Daily_missionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which daily_mission to aggregate.
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_missions to fetch.
   */
  orderBy?: Prisma.daily_missionOrderByWithRelationInput | Prisma.daily_missionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.daily_missionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_missions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_missions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned daily_missions
  **/
  _count?: true | Daily_missionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Daily_missionAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Daily_missionSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Daily_missionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Daily_missionMaxAggregateInputType
}

export type GetDaily_missionAggregateType<T extends Daily_missionAggregateArgs> = {
      [P in keyof T & keyof AggregateDaily_mission]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDaily_mission[P]>
    : Prisma.GetScalarType<T[P], AggregateDaily_mission[P]>
}




export type daily_missionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.daily_missionWhereInput
  orderBy?: Prisma.daily_missionOrderByWithAggregationInput | Prisma.daily_missionOrderByWithAggregationInput[]
  by: Prisma.Daily_missionScalarFieldEnum[] | Prisma.Daily_missionScalarFieldEnum
  having?: Prisma.daily_missionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Daily_missionCountAggregateInputType | true
  _avg?: Daily_missionAvgAggregateInputType
  _sum?: Daily_missionSumAggregateInputType
  _min?: Daily_missionMinAggregateInputType
  _max?: Daily_missionMaxAggregateInputType
}

export type Daily_missionGroupByOutputType = {
  id: number
  tier: number
  missionName: string
  description: string
  missionDate: Date
  duration: number
  minCashReward: number
  maxCashReward: number
  minExpReward: number
  maxExpReward: number
  levelReq: number
  hoursReq: number
  itemRewardQuantity: number | null
  createdAt: Date
  updatedAt: Date
  itemRewardId: number | null
  _count: Daily_missionCountAggregateOutputType | null
  _avg: Daily_missionAvgAggregateOutputType | null
  _sum: Daily_missionSumAggregateOutputType | null
  _min: Daily_missionMinAggregateOutputType | null
  _max: Daily_missionMaxAggregateOutputType | null
}

type GetDaily_missionGroupByPayload<T extends daily_missionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Daily_missionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Daily_missionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Daily_missionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Daily_missionGroupByOutputType[P]>
      }
    >
  >



export type daily_missionWhereInput = {
  AND?: Prisma.daily_missionWhereInput | Prisma.daily_missionWhereInput[]
  OR?: Prisma.daily_missionWhereInput[]
  NOT?: Prisma.daily_missionWhereInput | Prisma.daily_missionWhereInput[]
  id?: Prisma.IntFilter<"daily_mission"> | number
  tier?: Prisma.IntFilter<"daily_mission"> | number
  missionName?: Prisma.StringFilter<"daily_mission"> | string
  description?: Prisma.StringFilter<"daily_mission"> | string
  missionDate?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  duration?: Prisma.IntFilter<"daily_mission"> | number
  minCashReward?: Prisma.IntFilter<"daily_mission"> | number
  maxCashReward?: Prisma.IntFilter<"daily_mission"> | number
  minExpReward?: Prisma.IntFilter<"daily_mission"> | number
  maxExpReward?: Prisma.IntFilter<"daily_mission"> | number
  levelReq?: Prisma.IntFilter<"daily_mission"> | number
  hoursReq?: Prisma.IntFilter<"daily_mission"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_mission"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_mission"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}

export type daily_missionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  missionName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  missionDate?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrderInput | Prisma.SortOrder
  item?: Prisma.itemOrderByWithRelationInput
  _relevance?: Prisma.daily_missionOrderByRelevanceInput
}

export type daily_missionWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.daily_missionWhereInput | Prisma.daily_missionWhereInput[]
  OR?: Prisma.daily_missionWhereInput[]
  NOT?: Prisma.daily_missionWhereInput | Prisma.daily_missionWhereInput[]
  tier?: Prisma.IntFilter<"daily_mission"> | number
  missionName?: Prisma.StringFilter<"daily_mission"> | string
  description?: Prisma.StringFilter<"daily_mission"> | string
  missionDate?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  duration?: Prisma.IntFilter<"daily_mission"> | number
  minCashReward?: Prisma.IntFilter<"daily_mission"> | number
  maxCashReward?: Prisma.IntFilter<"daily_mission"> | number
  minExpReward?: Prisma.IntFilter<"daily_mission"> | number
  maxExpReward?: Prisma.IntFilter<"daily_mission"> | number
  levelReq?: Prisma.IntFilter<"daily_mission"> | number
  hoursReq?: Prisma.IntFilter<"daily_mission"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_mission"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_mission"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
}, "id">

export type daily_missionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  missionName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  missionDate?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.daily_missionCountOrderByAggregateInput
  _avg?: Prisma.daily_missionAvgOrderByAggregateInput
  _max?: Prisma.daily_missionMaxOrderByAggregateInput
  _min?: Prisma.daily_missionMinOrderByAggregateInput
  _sum?: Prisma.daily_missionSumOrderByAggregateInput
}

export type daily_missionScalarWhereWithAggregatesInput = {
  AND?: Prisma.daily_missionScalarWhereWithAggregatesInput | Prisma.daily_missionScalarWhereWithAggregatesInput[]
  OR?: Prisma.daily_missionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.daily_missionScalarWhereWithAggregatesInput | Prisma.daily_missionScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  tier?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  missionName?: Prisma.StringWithAggregatesFilter<"daily_mission"> | string
  description?: Prisma.StringWithAggregatesFilter<"daily_mission"> | string
  missionDate?: Prisma.DateTimeWithAggregatesFilter<"daily_mission"> | Date | string
  duration?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  minCashReward?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  maxCashReward?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  minExpReward?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  maxExpReward?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  levelReq?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  hoursReq?: Prisma.IntWithAggregatesFilter<"daily_mission"> | number
  itemRewardQuantity?: Prisma.IntNullableWithAggregatesFilter<"daily_mission"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"daily_mission"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"daily_mission"> | Date | string
  itemRewardId?: Prisma.IntNullableWithAggregatesFilter<"daily_mission"> | number | null
}

export type daily_missionCreateInput = {
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutDaily_missionInput
}

export type daily_missionUncheckedCreateInput = {
  id?: number
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
}

export type daily_missionUpdateInput = {
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutDaily_missionNestedInput
}

export type daily_missionUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_missionCreateManyInput = {
  id?: number
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
}

export type daily_missionUpdateManyMutationInput = {
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type daily_missionUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_missionOrderByRelevanceInput = {
  fields: Prisma.daily_missionOrderByRelevanceFieldEnum | Prisma.daily_missionOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type daily_missionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  missionName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  missionDate?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
}

export type daily_missionAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
}

export type daily_missionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  missionName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  missionDate?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
}

export type daily_missionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  missionName?: Prisma.SortOrder
  description?: Prisma.SortOrder
  missionDate?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
}

export type daily_missionSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  tier?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  minCashReward?: Prisma.SortOrder
  maxCashReward?: Prisma.SortOrder
  minExpReward?: Prisma.SortOrder
  maxExpReward?: Prisma.SortOrder
  levelReq?: Prisma.SortOrder
  hoursReq?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
}

export type Daily_missionListRelationFilter = {
  every?: Prisma.daily_missionWhereInput
  some?: Prisma.daily_missionWhereInput
  none?: Prisma.daily_missionWhereInput
}

export type daily_missionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type daily_missionCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput> | Prisma.daily_missionCreateWithoutItemInput[] | Prisma.daily_missionUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_missionCreateOrConnectWithoutItemInput | Prisma.daily_missionCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.daily_missionCreateManyItemInputEnvelope
  connect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
}

export type daily_missionUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput> | Prisma.daily_missionCreateWithoutItemInput[] | Prisma.daily_missionUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_missionCreateOrConnectWithoutItemInput | Prisma.daily_missionCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.daily_missionCreateManyItemInputEnvelope
  connect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
}

export type daily_missionUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput> | Prisma.daily_missionCreateWithoutItemInput[] | Prisma.daily_missionUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_missionCreateOrConnectWithoutItemInput | Prisma.daily_missionCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.daily_missionUpsertWithWhereUniqueWithoutItemInput | Prisma.daily_missionUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.daily_missionCreateManyItemInputEnvelope
  set?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  disconnect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  delete?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  connect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  update?: Prisma.daily_missionUpdateWithWhereUniqueWithoutItemInput | Prisma.daily_missionUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.daily_missionUpdateManyWithWhereWithoutItemInput | Prisma.daily_missionUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.daily_missionScalarWhereInput | Prisma.daily_missionScalarWhereInput[]
}

export type daily_missionUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput> | Prisma.daily_missionCreateWithoutItemInput[] | Prisma.daily_missionUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_missionCreateOrConnectWithoutItemInput | Prisma.daily_missionCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.daily_missionUpsertWithWhereUniqueWithoutItemInput | Prisma.daily_missionUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.daily_missionCreateManyItemInputEnvelope
  set?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  disconnect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  delete?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  connect?: Prisma.daily_missionWhereUniqueInput | Prisma.daily_missionWhereUniqueInput[]
  update?: Prisma.daily_missionUpdateWithWhereUniqueWithoutItemInput | Prisma.daily_missionUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.daily_missionUpdateManyWithWhereWithoutItemInput | Prisma.daily_missionUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.daily_missionScalarWhereInput | Prisma.daily_missionScalarWhereInput[]
}

export type daily_missionCreateWithoutItemInput = {
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type daily_missionUncheckedCreateWithoutItemInput = {
  id?: number
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type daily_missionCreateOrConnectWithoutItemInput = {
  where: Prisma.daily_missionWhereUniqueInput
  create: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput>
}

export type daily_missionCreateManyItemInputEnvelope = {
  data: Prisma.daily_missionCreateManyItemInput | Prisma.daily_missionCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type daily_missionUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.daily_missionWhereUniqueInput
  update: Prisma.XOR<Prisma.daily_missionUpdateWithoutItemInput, Prisma.daily_missionUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.daily_missionCreateWithoutItemInput, Prisma.daily_missionUncheckedCreateWithoutItemInput>
}

export type daily_missionUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.daily_missionWhereUniqueInput
  data: Prisma.XOR<Prisma.daily_missionUpdateWithoutItemInput, Prisma.daily_missionUncheckedUpdateWithoutItemInput>
}

export type daily_missionUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.daily_missionScalarWhereInput
  data: Prisma.XOR<Prisma.daily_missionUpdateManyMutationInput, Prisma.daily_missionUncheckedUpdateManyWithoutItemInput>
}

export type daily_missionScalarWhereInput = {
  AND?: Prisma.daily_missionScalarWhereInput | Prisma.daily_missionScalarWhereInput[]
  OR?: Prisma.daily_missionScalarWhereInput[]
  NOT?: Prisma.daily_missionScalarWhereInput | Prisma.daily_missionScalarWhereInput[]
  id?: Prisma.IntFilter<"daily_mission"> | number
  tier?: Prisma.IntFilter<"daily_mission"> | number
  missionName?: Prisma.StringFilter<"daily_mission"> | string
  description?: Prisma.StringFilter<"daily_mission"> | string
  missionDate?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  duration?: Prisma.IntFilter<"daily_mission"> | number
  minCashReward?: Prisma.IntFilter<"daily_mission"> | number
  maxCashReward?: Prisma.IntFilter<"daily_mission"> | number
  minExpReward?: Prisma.IntFilter<"daily_mission"> | number
  maxExpReward?: Prisma.IntFilter<"daily_mission"> | number
  levelReq?: Prisma.IntFilter<"daily_mission"> | number
  hoursReq?: Prisma.IntFilter<"daily_mission"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_mission"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_mission"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_mission"> | number | null
}

export type daily_missionCreateManyItemInput = {
  id?: number
  tier: number
  missionName: string
  description: string
  missionDate: Date | string
  duration: number
  minCashReward?: number
  maxCashReward?: number
  minExpReward?: number
  maxExpReward?: number
  levelReq?: number
  hoursReq?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type daily_missionUpdateWithoutItemInput = {
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type daily_missionUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type daily_missionUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  tier?: Prisma.IntFieldUpdateOperationsInput | number
  missionName?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  missionDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  duration?: Prisma.IntFieldUpdateOperationsInput | number
  minCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxCashReward?: Prisma.IntFieldUpdateOperationsInput | number
  minExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  maxExpReward?: Prisma.IntFieldUpdateOperationsInput | number
  levelReq?: Prisma.IntFieldUpdateOperationsInput | number
  hoursReq?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type daily_missionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  tier?: boolean
  missionName?: boolean
  description?: boolean
  missionDate?: boolean
  duration?: boolean
  minCashReward?: boolean
  maxCashReward?: boolean
  minExpReward?: boolean
  maxExpReward?: boolean
  levelReq?: boolean
  hoursReq?: boolean
  itemRewardQuantity?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemRewardId?: boolean
  item?: boolean | Prisma.daily_mission$itemArgs<ExtArgs>
}, ExtArgs["result"]["daily_mission"]>



export type daily_missionSelectScalar = {
  id?: boolean
  tier?: boolean
  missionName?: boolean
  description?: boolean
  missionDate?: boolean
  duration?: boolean
  minCashReward?: boolean
  maxCashReward?: boolean
  minExpReward?: boolean
  maxExpReward?: boolean
  levelReq?: boolean
  hoursReq?: boolean
  itemRewardQuantity?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemRewardId?: boolean
}

export type daily_missionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "tier" | "missionName" | "description" | "missionDate" | "duration" | "minCashReward" | "maxCashReward" | "minExpReward" | "maxExpReward" | "levelReq" | "hoursReq" | "itemRewardQuantity" | "createdAt" | "updatedAt" | "itemRewardId", ExtArgs["result"]["daily_mission"]>
export type daily_missionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Prisma.daily_mission$itemArgs<ExtArgs>
}

export type $daily_missionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "daily_mission"
  objects: {
    item: Prisma.$itemPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    tier: number
    missionName: string
    description: string
    missionDate: Date
    duration: number
    minCashReward: number
    maxCashReward: number
    minExpReward: number
    maxExpReward: number
    levelReq: number
    hoursReq: number
    itemRewardQuantity: number | null
    createdAt: Date
    updatedAt: Date
    itemRewardId: number | null
  }, ExtArgs["result"]["daily_mission"]>
  composites: {}
}

export type daily_missionGetPayload<S extends boolean | null | undefined | daily_missionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$daily_missionPayload, S>

export type daily_missionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<daily_missionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Daily_missionCountAggregateInputType | true
  }

export interface daily_missionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['daily_mission'], meta: { name: 'daily_mission' } }
  /**
   * Find zero or one Daily_mission that matches the filter.
   * @param {daily_missionFindUniqueArgs} args - Arguments to find a Daily_mission
   * @example
   * // Get one Daily_mission
   * const daily_mission = await prisma.daily_mission.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends daily_missionFindUniqueArgs>(args: Prisma.SelectSubset<T, daily_missionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Daily_mission that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {daily_missionFindUniqueOrThrowArgs} args - Arguments to find a Daily_mission
   * @example
   * // Get one Daily_mission
   * const daily_mission = await prisma.daily_mission.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends daily_missionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, daily_missionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Daily_mission that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionFindFirstArgs} args - Arguments to find a Daily_mission
   * @example
   * // Get one Daily_mission
   * const daily_mission = await prisma.daily_mission.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends daily_missionFindFirstArgs>(args?: Prisma.SelectSubset<T, daily_missionFindFirstArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Daily_mission that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionFindFirstOrThrowArgs} args - Arguments to find a Daily_mission
   * @example
   * // Get one Daily_mission
   * const daily_mission = await prisma.daily_mission.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends daily_missionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, daily_missionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Daily_missions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Daily_missions
   * const daily_missions = await prisma.daily_mission.findMany()
   * 
   * // Get first 10 Daily_missions
   * const daily_missions = await prisma.daily_mission.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const daily_missionWithIdOnly = await prisma.daily_mission.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends daily_missionFindManyArgs>(args?: Prisma.SelectSubset<T, daily_missionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Daily_mission.
   * @param {daily_missionCreateArgs} args - Arguments to create a Daily_mission.
   * @example
   * // Create one Daily_mission
   * const Daily_mission = await prisma.daily_mission.create({
   *   data: {
   *     // ... data to create a Daily_mission
   *   }
   * })
   * 
   */
  create<T extends daily_missionCreateArgs>(args: Prisma.SelectSubset<T, daily_missionCreateArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Daily_missions.
   * @param {daily_missionCreateManyArgs} args - Arguments to create many Daily_missions.
   * @example
   * // Create many Daily_missions
   * const daily_mission = await prisma.daily_mission.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends daily_missionCreateManyArgs>(args?: Prisma.SelectSubset<T, daily_missionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Daily_mission.
   * @param {daily_missionDeleteArgs} args - Arguments to delete one Daily_mission.
   * @example
   * // Delete one Daily_mission
   * const Daily_mission = await prisma.daily_mission.delete({
   *   where: {
   *     // ... filter to delete one Daily_mission
   *   }
   * })
   * 
   */
  delete<T extends daily_missionDeleteArgs>(args: Prisma.SelectSubset<T, daily_missionDeleteArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Daily_mission.
   * @param {daily_missionUpdateArgs} args - Arguments to update one Daily_mission.
   * @example
   * // Update one Daily_mission
   * const daily_mission = await prisma.daily_mission.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends daily_missionUpdateArgs>(args: Prisma.SelectSubset<T, daily_missionUpdateArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Daily_missions.
   * @param {daily_missionDeleteManyArgs} args - Arguments to filter Daily_missions to delete.
   * @example
   * // Delete a few Daily_missions
   * const { count } = await prisma.daily_mission.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends daily_missionDeleteManyArgs>(args?: Prisma.SelectSubset<T, daily_missionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Daily_missions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Daily_missions
   * const daily_mission = await prisma.daily_mission.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends daily_missionUpdateManyArgs>(args: Prisma.SelectSubset<T, daily_missionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Daily_mission.
   * @param {daily_missionUpsertArgs} args - Arguments to update or create a Daily_mission.
   * @example
   * // Update or create a Daily_mission
   * const daily_mission = await prisma.daily_mission.upsert({
   *   create: {
   *     // ... data to create a Daily_mission
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Daily_mission we want to update
   *   }
   * })
   */
  upsert<T extends daily_missionUpsertArgs>(args: Prisma.SelectSubset<T, daily_missionUpsertArgs<ExtArgs>>): Prisma.Prisma__daily_missionClient<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Daily_missions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionCountArgs} args - Arguments to filter Daily_missions to count.
   * @example
   * // Count the number of Daily_missions
   * const count = await prisma.daily_mission.count({
   *   where: {
   *     // ... the filter for the Daily_missions we want to count
   *   }
   * })
  **/
  count<T extends daily_missionCountArgs>(
    args?: Prisma.Subset<T, daily_missionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Daily_missionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Daily_mission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Daily_missionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Daily_missionAggregateArgs>(args: Prisma.Subset<T, Daily_missionAggregateArgs>): Prisma.PrismaPromise<GetDaily_missionAggregateType<T>>

  /**
   * Group by Daily_mission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_missionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends daily_missionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: daily_missionGroupByArgs['orderBy'] }
      : { orderBy?: daily_missionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, daily_missionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDaily_missionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the daily_mission model
 */
readonly fields: daily_missionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for daily_mission.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__daily_missionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  item<T extends Prisma.daily_mission$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.daily_mission$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the daily_mission model
 */
export interface daily_missionFieldRefs {
  readonly id: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly tier: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly missionName: Prisma.FieldRef<"daily_mission", 'String'>
  readonly description: Prisma.FieldRef<"daily_mission", 'String'>
  readonly missionDate: Prisma.FieldRef<"daily_mission", 'DateTime'>
  readonly duration: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly minCashReward: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly maxCashReward: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly minExpReward: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly maxExpReward: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly levelReq: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly hoursReq: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly itemRewardQuantity: Prisma.FieldRef<"daily_mission", 'Int'>
  readonly createdAt: Prisma.FieldRef<"daily_mission", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"daily_mission", 'DateTime'>
  readonly itemRewardId: Prisma.FieldRef<"daily_mission", 'Int'>
}
    

// Custom InputTypes
/**
 * daily_mission findUnique
 */
export type daily_missionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter, which daily_mission to fetch.
   */
  where: Prisma.daily_missionWhereUniqueInput
}

/**
 * daily_mission findUniqueOrThrow
 */
export type daily_missionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter, which daily_mission to fetch.
   */
  where: Prisma.daily_missionWhereUniqueInput
}

/**
 * daily_mission findFirst
 */
export type daily_missionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter, which daily_mission to fetch.
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_missions to fetch.
   */
  orderBy?: Prisma.daily_missionOrderByWithRelationInput | Prisma.daily_missionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for daily_missions.
   */
  cursor?: Prisma.daily_missionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_missions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_missions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of daily_missions.
   */
  distinct?: Prisma.Daily_missionScalarFieldEnum | Prisma.Daily_missionScalarFieldEnum[]
}

/**
 * daily_mission findFirstOrThrow
 */
export type daily_missionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter, which daily_mission to fetch.
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_missions to fetch.
   */
  orderBy?: Prisma.daily_missionOrderByWithRelationInput | Prisma.daily_missionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for daily_missions.
   */
  cursor?: Prisma.daily_missionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_missions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_missions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of daily_missions.
   */
  distinct?: Prisma.Daily_missionScalarFieldEnum | Prisma.Daily_missionScalarFieldEnum[]
}

/**
 * daily_mission findMany
 */
export type daily_missionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter, which daily_missions to fetch.
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_missions to fetch.
   */
  orderBy?: Prisma.daily_missionOrderByWithRelationInput | Prisma.daily_missionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing daily_missions.
   */
  cursor?: Prisma.daily_missionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_missions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_missions.
   */
  skip?: number
  distinct?: Prisma.Daily_missionScalarFieldEnum | Prisma.Daily_missionScalarFieldEnum[]
}

/**
 * daily_mission create
 */
export type daily_missionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * The data needed to create a daily_mission.
   */
  data: Prisma.XOR<Prisma.daily_missionCreateInput, Prisma.daily_missionUncheckedCreateInput>
}

/**
 * daily_mission createMany
 */
export type daily_missionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many daily_missions.
   */
  data: Prisma.daily_missionCreateManyInput | Prisma.daily_missionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * daily_mission update
 */
export type daily_missionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * The data needed to update a daily_mission.
   */
  data: Prisma.XOR<Prisma.daily_missionUpdateInput, Prisma.daily_missionUncheckedUpdateInput>
  /**
   * Choose, which daily_mission to update.
   */
  where: Prisma.daily_missionWhereUniqueInput
}

/**
 * daily_mission updateMany
 */
export type daily_missionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update daily_missions.
   */
  data: Prisma.XOR<Prisma.daily_missionUpdateManyMutationInput, Prisma.daily_missionUncheckedUpdateManyInput>
  /**
   * Filter which daily_missions to update
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * Limit how many daily_missions to update.
   */
  limit?: number
}

/**
 * daily_mission upsert
 */
export type daily_missionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * The filter to search for the daily_mission to update in case it exists.
   */
  where: Prisma.daily_missionWhereUniqueInput
  /**
   * In case the daily_mission found by the `where` argument doesn't exist, create a new daily_mission with this data.
   */
  create: Prisma.XOR<Prisma.daily_missionCreateInput, Prisma.daily_missionUncheckedCreateInput>
  /**
   * In case the daily_mission was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.daily_missionUpdateInput, Prisma.daily_missionUncheckedUpdateInput>
}

/**
 * daily_mission delete
 */
export type daily_missionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  /**
   * Filter which daily_mission to delete.
   */
  where: Prisma.daily_missionWhereUniqueInput
}

/**
 * daily_mission deleteMany
 */
export type daily_missionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which daily_missions to delete
   */
  where?: Prisma.daily_missionWhereInput
  /**
   * Limit how many daily_missions to delete.
   */
  limit?: number
}

/**
 * daily_mission.item
 */
export type daily_mission$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * daily_mission without action
 */
export type daily_missionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
}
