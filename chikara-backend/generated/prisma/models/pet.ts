
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `pet` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model pet
 * 
 */
export type petModel = runtime.Types.Result.DefaultSelection<Prisma.$petPayload>

export type AggregatePet = {
  _count: PetCountAggregateOutputType | null
  _avg: PetAvgAggregateOutputType | null
  _sum: PetSumAggregateOutputType | null
  _min: PetMinAggregateOutputType | null
  _max: PetMaxAggregateOutputType | null
}

export type PetAvgAggregateOutputType = {
  id: number | null
  maxLevel: number | null
}

export type PetSumAggregateOutputType = {
  id: number | null
  maxLevel: number | null
}

export type PetMinAggregateOutputType = {
  id: number | null
  name: string | null
  species: string | null
  maxLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PetMaxAggregateOutputType = {
  id: number | null
  name: string | null
  species: string | null
  maxLevel: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PetCountAggregateOutputType = {
  id: number
  name: number
  species: number
  maxLevel: number
  evolution_stages: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type PetAvgAggregateInputType = {
  id?: true
  maxLevel?: true
}

export type PetSumAggregateInputType = {
  id?: true
  maxLevel?: true
}

export type PetMinAggregateInputType = {
  id?: true
  name?: true
  species?: true
  maxLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type PetMaxAggregateInputType = {
  id?: true
  name?: true
  species?: true
  maxLevel?: true
  createdAt?: true
  updatedAt?: true
}

export type PetCountAggregateInputType = {
  id?: true
  name?: true
  species?: true
  maxLevel?: true
  evolution_stages?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PetAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which pet to aggregate.
   */
  where?: Prisma.petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of pets to fetch.
   */
  orderBy?: Prisma.petOrderByWithRelationInput | Prisma.petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned pets
  **/
  _count?: true | PetCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PetAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PetSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PetMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PetMaxAggregateInputType
}

export type GetPetAggregateType<T extends PetAggregateArgs> = {
      [P in keyof T & keyof AggregatePet]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePet[P]>
    : Prisma.GetScalarType<T[P], AggregatePet[P]>
}




export type petGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.petWhereInput
  orderBy?: Prisma.petOrderByWithAggregationInput | Prisma.petOrderByWithAggregationInput[]
  by: Prisma.PetScalarFieldEnum[] | Prisma.PetScalarFieldEnum
  having?: Prisma.petScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PetCountAggregateInputType | true
  _avg?: PetAvgAggregateInputType
  _sum?: PetSumAggregateInputType
  _min?: PetMinAggregateInputType
  _max?: PetMaxAggregateInputType
}

export type PetGroupByOutputType = {
  id: number
  name: string
  species: string
  maxLevel: number
  evolution_stages: runtime.JsonValue
  createdAt: Date
  updatedAt: Date
  _count: PetCountAggregateOutputType | null
  _avg: PetAvgAggregateOutputType | null
  _sum: PetSumAggregateOutputType | null
  _min: PetMinAggregateOutputType | null
  _max: PetMaxAggregateOutputType | null
}

type GetPetGroupByPayload<T extends petGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PetGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PetGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PetGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PetGroupByOutputType[P]>
      }
    >
  >



export type petWhereInput = {
  AND?: Prisma.petWhereInput | Prisma.petWhereInput[]
  OR?: Prisma.petWhereInput[]
  NOT?: Prisma.petWhereInput | Prisma.petWhereInput[]
  id?: Prisma.IntFilter<"pet"> | number
  name?: Prisma.StringFilter<"pet"> | string
  species?: Prisma.StringFilter<"pet"> | string
  maxLevel?: Prisma.IntFilter<"pet"> | number
  evolution_stages?: Prisma.JsonFilter<"pet">
  createdAt?: Prisma.DateTimeFilter<"pet"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"pet"> | Date | string
  user_pet?: Prisma.User_petListRelationFilter
  item?: Prisma.ItemListRelationFilter
}

export type petOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  species?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  evolution_stages?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user_pet?: Prisma.user_petOrderByRelationAggregateInput
  item?: Prisma.itemOrderByRelationAggregateInput
  _relevance?: Prisma.petOrderByRelevanceInput
}

export type petWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.petWhereInput | Prisma.petWhereInput[]
  OR?: Prisma.petWhereInput[]
  NOT?: Prisma.petWhereInput | Prisma.petWhereInput[]
  name?: Prisma.StringFilter<"pet"> | string
  species?: Prisma.StringFilter<"pet"> | string
  maxLevel?: Prisma.IntFilter<"pet"> | number
  evolution_stages?: Prisma.JsonFilter<"pet">
  createdAt?: Prisma.DateTimeFilter<"pet"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"pet"> | Date | string
  user_pet?: Prisma.User_petListRelationFilter
  item?: Prisma.ItemListRelationFilter
}, "id">

export type petOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  species?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  evolution_stages?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.petCountOrderByAggregateInput
  _avg?: Prisma.petAvgOrderByAggregateInput
  _max?: Prisma.petMaxOrderByAggregateInput
  _min?: Prisma.petMinOrderByAggregateInput
  _sum?: Prisma.petSumOrderByAggregateInput
}

export type petScalarWhereWithAggregatesInput = {
  AND?: Prisma.petScalarWhereWithAggregatesInput | Prisma.petScalarWhereWithAggregatesInput[]
  OR?: Prisma.petScalarWhereWithAggregatesInput[]
  NOT?: Prisma.petScalarWhereWithAggregatesInput | Prisma.petScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"pet"> | number
  name?: Prisma.StringWithAggregatesFilter<"pet"> | string
  species?: Prisma.StringWithAggregatesFilter<"pet"> | string
  maxLevel?: Prisma.IntWithAggregatesFilter<"pet"> | number
  evolution_stages?: Prisma.JsonWithAggregatesFilter<"pet">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"pet"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"pet"> | Date | string
}

export type petCreateInput = {
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  user_pet?: Prisma.user_petCreateNestedManyWithoutPetInput
  item?: Prisma.itemCreateNestedManyWithoutPetInput
}

export type petUncheckedCreateInput = {
  id?: number
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  user_pet?: Prisma.user_petUncheckedCreateNestedManyWithoutPetInput
  item?: Prisma.itemUncheckedCreateNestedManyWithoutPetInput
}

export type petUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_pet?: Prisma.user_petUpdateManyWithoutPetNestedInput
  item?: Prisma.itemUpdateManyWithoutPetNestedInput
}

export type petUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_pet?: Prisma.user_petUncheckedUpdateManyWithoutPetNestedInput
  item?: Prisma.itemUncheckedUpdateManyWithoutPetNestedInput
}

export type petCreateManyInput = {
  id?: number
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type petUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type petUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PetNullableScalarRelationFilter = {
  is?: Prisma.petWhereInput | null
  isNot?: Prisma.petWhereInput | null
}

export type petOrderByRelevanceInput = {
  fields: Prisma.petOrderByRelevanceFieldEnum | Prisma.petOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type petCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  species?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  evolution_stages?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type petAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
}

export type petMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  species?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type petMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  species?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type petSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  maxLevel?: Prisma.SortOrder
}

export type PetScalarRelationFilter = {
  is?: Prisma.petWhereInput
  isNot?: Prisma.petWhereInput
}

export type petCreateNestedOneWithoutItemInput = {
  create?: Prisma.XOR<Prisma.petCreateWithoutItemInput, Prisma.petUncheckedCreateWithoutItemInput>
  connectOrCreate?: Prisma.petCreateOrConnectWithoutItemInput
  connect?: Prisma.petWhereUniqueInput
}

export type petUpdateOneWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.petCreateWithoutItemInput, Prisma.petUncheckedCreateWithoutItemInput>
  connectOrCreate?: Prisma.petCreateOrConnectWithoutItemInput
  upsert?: Prisma.petUpsertWithoutItemInput
  disconnect?: Prisma.petWhereInput | boolean
  delete?: Prisma.petWhereInput | boolean
  connect?: Prisma.petWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.petUpdateToOneWithWhereWithoutItemInput, Prisma.petUpdateWithoutItemInput>, Prisma.petUncheckedUpdateWithoutItemInput>
}

export type petCreateNestedOneWithoutUser_petInput = {
  create?: Prisma.XOR<Prisma.petCreateWithoutUser_petInput, Prisma.petUncheckedCreateWithoutUser_petInput>
  connectOrCreate?: Prisma.petCreateOrConnectWithoutUser_petInput
  connect?: Prisma.petWhereUniqueInput
}

export type petUpdateOneRequiredWithoutUser_petNestedInput = {
  create?: Prisma.XOR<Prisma.petCreateWithoutUser_petInput, Prisma.petUncheckedCreateWithoutUser_petInput>
  connectOrCreate?: Prisma.petCreateOrConnectWithoutUser_petInput
  upsert?: Prisma.petUpsertWithoutUser_petInput
  connect?: Prisma.petWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.petUpdateToOneWithWhereWithoutUser_petInput, Prisma.petUpdateWithoutUser_petInput>, Prisma.petUncheckedUpdateWithoutUser_petInput>
}

export type petCreateWithoutItemInput = {
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  user_pet?: Prisma.user_petCreateNestedManyWithoutPetInput
}

export type petUncheckedCreateWithoutItemInput = {
  id?: number
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  user_pet?: Prisma.user_petUncheckedCreateNestedManyWithoutPetInput
}

export type petCreateOrConnectWithoutItemInput = {
  where: Prisma.petWhereUniqueInput
  create: Prisma.XOR<Prisma.petCreateWithoutItemInput, Prisma.petUncheckedCreateWithoutItemInput>
}

export type petUpsertWithoutItemInput = {
  update: Prisma.XOR<Prisma.petUpdateWithoutItemInput, Prisma.petUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.petCreateWithoutItemInput, Prisma.petUncheckedCreateWithoutItemInput>
  where?: Prisma.petWhereInput
}

export type petUpdateToOneWithWhereWithoutItemInput = {
  where?: Prisma.petWhereInput
  data: Prisma.XOR<Prisma.petUpdateWithoutItemInput, Prisma.petUncheckedUpdateWithoutItemInput>
}

export type petUpdateWithoutItemInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_pet?: Prisma.user_petUpdateManyWithoutPetNestedInput
}

export type petUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_pet?: Prisma.user_petUncheckedUpdateManyWithoutPetNestedInput
}

export type petCreateWithoutUser_petInput = {
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedManyWithoutPetInput
}

export type petUncheckedCreateWithoutUser_petInput = {
  id?: number
  name: string
  species: string
  maxLevel?: number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemUncheckedCreateNestedManyWithoutPetInput
}

export type petCreateOrConnectWithoutUser_petInput = {
  where: Prisma.petWhereUniqueInput
  create: Prisma.XOR<Prisma.petCreateWithoutUser_petInput, Prisma.petUncheckedCreateWithoutUser_petInput>
}

export type petUpsertWithoutUser_petInput = {
  update: Prisma.XOR<Prisma.petUpdateWithoutUser_petInput, Prisma.petUncheckedUpdateWithoutUser_petInput>
  create: Prisma.XOR<Prisma.petCreateWithoutUser_petInput, Prisma.petUncheckedCreateWithoutUser_petInput>
  where?: Prisma.petWhereInput
}

export type petUpdateToOneWithWhereWithoutUser_petInput = {
  where?: Prisma.petWhereInput
  data: Prisma.XOR<Prisma.petUpdateWithoutUser_petInput, Prisma.petUncheckedUpdateWithoutUser_petInput>
}

export type petUpdateWithoutUser_petInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateManyWithoutPetNestedInput
}

export type petUncheckedUpdateWithoutUser_petInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  species?: Prisma.StringFieldUpdateOperationsInput | string
  maxLevel?: Prisma.IntFieldUpdateOperationsInput | number
  evolution_stages?:PrismaJson.EvolutionStages
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUncheckedUpdateManyWithoutPetNestedInput
}


/**
 * Count Type PetCountOutputType
 */

export type PetCountOutputType = {
  user_pet: number
  item: number
}

export type PetCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_pet?: boolean | PetCountOutputTypeCountUser_petArgs
  item?: boolean | PetCountOutputTypeCountItemArgs
}

/**
 * PetCountOutputType without action
 */
export type PetCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PetCountOutputType
   */
  select?: Prisma.PetCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PetCountOutputType without action
 */
export type PetCountOutputTypeCountUser_petArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_petWhereInput
}

/**
 * PetCountOutputType without action
 */
export type PetCountOutputTypeCountItemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.itemWhereInput
}


export type petSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  species?: boolean
  maxLevel?: boolean
  evolution_stages?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user_pet?: boolean | Prisma.pet$user_petArgs<ExtArgs>
  item?: boolean | Prisma.pet$itemArgs<ExtArgs>
  _count?: boolean | Prisma.PetCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["pet"]>



export type petSelectScalar = {
  id?: boolean
  name?: boolean
  species?: boolean
  maxLevel?: boolean
  evolution_stages?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type petOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "species" | "maxLevel" | "evolution_stages" | "createdAt" | "updatedAt", ExtArgs["result"]["pet"]>
export type petInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_pet?: boolean | Prisma.pet$user_petArgs<ExtArgs>
  item?: boolean | Prisma.pet$itemArgs<ExtArgs>
  _count?: boolean | Prisma.PetCountOutputTypeDefaultArgs<ExtArgs>
}

export type $petPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "pet"
  objects: {
    user_pet: Prisma.$user_petPayload<ExtArgs>[]
    item: Prisma.$itemPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    species: string
    maxLevel: number
    /**
     * [EvolutionStages]
     */
    evolution_stages:PrismaJson.EvolutionStages
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["pet"]>
  composites: {}
}

export type petGetPayload<S extends boolean | null | undefined | petDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$petPayload, S>

export type petCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<petFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PetCountAggregateInputType | true
  }

export interface petDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['pet'], meta: { name: 'pet' } }
  /**
   * Find zero or one Pet that matches the filter.
   * @param {petFindUniqueArgs} args - Arguments to find a Pet
   * @example
   * // Get one Pet
   * const pet = await prisma.pet.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends petFindUniqueArgs>(args: Prisma.SelectSubset<T, petFindUniqueArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Pet that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {petFindUniqueOrThrowArgs} args - Arguments to find a Pet
   * @example
   * // Get one Pet
   * const pet = await prisma.pet.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends petFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, petFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Pet that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petFindFirstArgs} args - Arguments to find a Pet
   * @example
   * // Get one Pet
   * const pet = await prisma.pet.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends petFindFirstArgs>(args?: Prisma.SelectSubset<T, petFindFirstArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Pet that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petFindFirstOrThrowArgs} args - Arguments to find a Pet
   * @example
   * // Get one Pet
   * const pet = await prisma.pet.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends petFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, petFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Pets that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Pets
   * const pets = await prisma.pet.findMany()
   * 
   * // Get first 10 Pets
   * const pets = await prisma.pet.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const petWithIdOnly = await prisma.pet.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends petFindManyArgs>(args?: Prisma.SelectSubset<T, petFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Pet.
   * @param {petCreateArgs} args - Arguments to create a Pet.
   * @example
   * // Create one Pet
   * const Pet = await prisma.pet.create({
   *   data: {
   *     // ... data to create a Pet
   *   }
   * })
   * 
   */
  create<T extends petCreateArgs>(args: Prisma.SelectSubset<T, petCreateArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Pets.
   * @param {petCreateManyArgs} args - Arguments to create many Pets.
   * @example
   * // Create many Pets
   * const pet = await prisma.pet.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends petCreateManyArgs>(args?: Prisma.SelectSubset<T, petCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Pet.
   * @param {petDeleteArgs} args - Arguments to delete one Pet.
   * @example
   * // Delete one Pet
   * const Pet = await prisma.pet.delete({
   *   where: {
   *     // ... filter to delete one Pet
   *   }
   * })
   * 
   */
  delete<T extends petDeleteArgs>(args: Prisma.SelectSubset<T, petDeleteArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Pet.
   * @param {petUpdateArgs} args - Arguments to update one Pet.
   * @example
   * // Update one Pet
   * const pet = await prisma.pet.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends petUpdateArgs>(args: Prisma.SelectSubset<T, petUpdateArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Pets.
   * @param {petDeleteManyArgs} args - Arguments to filter Pets to delete.
   * @example
   * // Delete a few Pets
   * const { count } = await prisma.pet.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends petDeleteManyArgs>(args?: Prisma.SelectSubset<T, petDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Pets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Pets
   * const pet = await prisma.pet.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends petUpdateManyArgs>(args: Prisma.SelectSubset<T, petUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Pet.
   * @param {petUpsertArgs} args - Arguments to update or create a Pet.
   * @example
   * // Update or create a Pet
   * const pet = await prisma.pet.upsert({
   *   create: {
   *     // ... data to create a Pet
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Pet we want to update
   *   }
   * })
   */
  upsert<T extends petUpsertArgs>(args: Prisma.SelectSubset<T, petUpsertArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Pets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petCountArgs} args - Arguments to filter Pets to count.
   * @example
   * // Count the number of Pets
   * const count = await prisma.pet.count({
   *   where: {
   *     // ... the filter for the Pets we want to count
   *   }
   * })
  **/
  count<T extends petCountArgs>(
    args?: Prisma.Subset<T, petCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PetCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Pet.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PetAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PetAggregateArgs>(args: Prisma.Subset<T, PetAggregateArgs>): Prisma.PrismaPromise<GetPetAggregateType<T>>

  /**
   * Group by Pet.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {petGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends petGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: petGroupByArgs['orderBy'] }
      : { orderBy?: petGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, petGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPetGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the pet model
 */
readonly fields: petFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for pet.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__petClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_pet<T extends Prisma.pet$user_petArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.pet$user_petArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_petPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  item<T extends Prisma.pet$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.pet$itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the pet model
 */
export interface petFieldRefs {
  readonly id: Prisma.FieldRef<"pet", 'Int'>
  readonly name: Prisma.FieldRef<"pet", 'String'>
  readonly species: Prisma.FieldRef<"pet", 'String'>
  readonly maxLevel: Prisma.FieldRef<"pet", 'Int'>
  readonly evolution_stages: Prisma.FieldRef<"pet", 'Json'>
  readonly createdAt: Prisma.FieldRef<"pet", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"pet", 'DateTime'>
}
    

// Custom InputTypes
/**
 * pet findUnique
 */
export type petFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter, which pet to fetch.
   */
  where: Prisma.petWhereUniqueInput
}

/**
 * pet findUniqueOrThrow
 */
export type petFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter, which pet to fetch.
   */
  where: Prisma.petWhereUniqueInput
}

/**
 * pet findFirst
 */
export type petFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter, which pet to fetch.
   */
  where?: Prisma.petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of pets to fetch.
   */
  orderBy?: Prisma.petOrderByWithRelationInput | Prisma.petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for pets.
   */
  cursor?: Prisma.petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of pets.
   */
  distinct?: Prisma.PetScalarFieldEnum | Prisma.PetScalarFieldEnum[]
}

/**
 * pet findFirstOrThrow
 */
export type petFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter, which pet to fetch.
   */
  where?: Prisma.petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of pets to fetch.
   */
  orderBy?: Prisma.petOrderByWithRelationInput | Prisma.petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for pets.
   */
  cursor?: Prisma.petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` pets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of pets.
   */
  distinct?: Prisma.PetScalarFieldEnum | Prisma.PetScalarFieldEnum[]
}

/**
 * pet findMany
 */
export type petFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter, which pets to fetch.
   */
  where?: Prisma.petWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of pets to fetch.
   */
  orderBy?: Prisma.petOrderByWithRelationInput | Prisma.petOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing pets.
   */
  cursor?: Prisma.petWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` pets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` pets.
   */
  skip?: number
  distinct?: Prisma.PetScalarFieldEnum | Prisma.PetScalarFieldEnum[]
}

/**
 * pet create
 */
export type petCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * The data needed to create a pet.
   */
  data: Prisma.XOR<Prisma.petCreateInput, Prisma.petUncheckedCreateInput>
}

/**
 * pet createMany
 */
export type petCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many pets.
   */
  data: Prisma.petCreateManyInput | Prisma.petCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * pet update
 */
export type petUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * The data needed to update a pet.
   */
  data: Prisma.XOR<Prisma.petUpdateInput, Prisma.petUncheckedUpdateInput>
  /**
   * Choose, which pet to update.
   */
  where: Prisma.petWhereUniqueInput
}

/**
 * pet updateMany
 */
export type petUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update pets.
   */
  data: Prisma.XOR<Prisma.petUpdateManyMutationInput, Prisma.petUncheckedUpdateManyInput>
  /**
   * Filter which pets to update
   */
  where?: Prisma.petWhereInput
  /**
   * Limit how many pets to update.
   */
  limit?: number
}

/**
 * pet upsert
 */
export type petUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * The filter to search for the pet to update in case it exists.
   */
  where: Prisma.petWhereUniqueInput
  /**
   * In case the pet found by the `where` argument doesn't exist, create a new pet with this data.
   */
  create: Prisma.XOR<Prisma.petCreateInput, Prisma.petUncheckedCreateInput>
  /**
   * In case the pet was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.petUpdateInput, Prisma.petUncheckedUpdateInput>
}

/**
 * pet delete
 */
export type petDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  /**
   * Filter which pet to delete.
   */
  where: Prisma.petWhereUniqueInput
}

/**
 * pet deleteMany
 */
export type petDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which pets to delete
   */
  where?: Prisma.petWhereInput
  /**
   * Limit how many pets to delete.
   */
  limit?: number
}

/**
 * pet.user_pet
 */
export type pet$user_petArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_pet
   */
  select?: Prisma.user_petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_pet
   */
  omit?: Prisma.user_petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_petInclude<ExtArgs> | null
  where?: Prisma.user_petWhereInput
  orderBy?: Prisma.user_petOrderByWithRelationInput | Prisma.user_petOrderByWithRelationInput[]
  cursor?: Prisma.user_petWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_petScalarFieldEnum | Prisma.User_petScalarFieldEnum[]
}

/**
 * pet.item
 */
export type pet$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  cursor?: Prisma.itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ItemScalarFieldEnum | Prisma.ItemScalarFieldEnum[]
}

/**
 * pet without action
 */
export type petDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
}
