
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `daily_quest` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model daily_quest
 * 
 */
export type daily_questModel = runtime.Types.Result.DefaultSelection<Prisma.$daily_questPayload>

export type AggregateDaily_quest = {
  _count: Daily_questCountAggregateOutputType | null
  _avg: Daily_questAvgAggregateOutputType | null
  _sum: Daily_questSumAggregateOutputType | null
  _min: Daily_questMinAggregateOutputType | null
  _max: Daily_questMaxAggregateOutputType | null
}

export type Daily_questAvgAggregateOutputType = {
  id: number | null
  target: number | null
  quantity: number | null
  count: number | null
  cashReward: number | null
  xpReward: number | null
  itemRewardQuantity: number | null
  itemRewardId: number | null
  userId: number | null
}

export type Daily_questSumAggregateOutputType = {
  id: number | null
  target: number | null
  quantity: number | null
  count: number | null
  cashReward: number | null
  xpReward: number | null
  itemRewardQuantity: number | null
  itemRewardId: number | null
  userId: number | null
}

export type Daily_questMinAggregateOutputType = {
  id: number | null
  objectiveType: $Enums.QuestObjectiveTypes | null
  target: number | null
  targetAction: string | null
  quantity: number | null
  questStatus: $Enums.QuestProgressStatus | null
  count: number | null
  cashReward: number | null
  xpReward: number | null
  itemRewardQuantity: number | null
  createdAt: Date | null
  updatedAt: Date | null
  itemRewardId: number | null
  userId: number | null
}

export type Daily_questMaxAggregateOutputType = {
  id: number | null
  objectiveType: $Enums.QuestObjectiveTypes | null
  target: number | null
  targetAction: string | null
  quantity: number | null
  questStatus: $Enums.QuestProgressStatus | null
  count: number | null
  cashReward: number | null
  xpReward: number | null
  itemRewardQuantity: number | null
  createdAt: Date | null
  updatedAt: Date | null
  itemRewardId: number | null
  userId: number | null
}

export type Daily_questCountAggregateOutputType = {
  id: number
  objectiveType: number
  target: number
  targetAction: number
  quantity: number
  questStatus: number
  count: number
  cashReward: number
  xpReward: number
  itemRewardQuantity: number
  createdAt: number
  updatedAt: number
  itemRewardId: number
  userId: number
  _all: number
}


export type Daily_questAvgAggregateInputType = {
  id?: true
  target?: true
  quantity?: true
  count?: true
  cashReward?: true
  xpReward?: true
  itemRewardQuantity?: true
  itemRewardId?: true
  userId?: true
}

export type Daily_questSumAggregateInputType = {
  id?: true
  target?: true
  quantity?: true
  count?: true
  cashReward?: true
  xpReward?: true
  itemRewardQuantity?: true
  itemRewardId?: true
  userId?: true
}

export type Daily_questMinAggregateInputType = {
  id?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  questStatus?: true
  count?: true
  cashReward?: true
  xpReward?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
  userId?: true
}

export type Daily_questMaxAggregateInputType = {
  id?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  questStatus?: true
  count?: true
  cashReward?: true
  xpReward?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
  userId?: true
}

export type Daily_questCountAggregateInputType = {
  id?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  questStatus?: true
  count?: true
  cashReward?: true
  xpReward?: true
  itemRewardQuantity?: true
  createdAt?: true
  updatedAt?: true
  itemRewardId?: true
  userId?: true
  _all?: true
}

export type Daily_questAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which daily_quest to aggregate.
   */
  where?: Prisma.daily_questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_quests to fetch.
   */
  orderBy?: Prisma.daily_questOrderByWithRelationInput | Prisma.daily_questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.daily_questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned daily_quests
  **/
  _count?: true | Daily_questCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Daily_questAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Daily_questSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Daily_questMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Daily_questMaxAggregateInputType
}

export type GetDaily_questAggregateType<T extends Daily_questAggregateArgs> = {
      [P in keyof T & keyof AggregateDaily_quest]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDaily_quest[P]>
    : Prisma.GetScalarType<T[P], AggregateDaily_quest[P]>
}




export type daily_questGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.daily_questWhereInput
  orderBy?: Prisma.daily_questOrderByWithAggregationInput | Prisma.daily_questOrderByWithAggregationInput[]
  by: Prisma.Daily_questScalarFieldEnum[] | Prisma.Daily_questScalarFieldEnum
  having?: Prisma.daily_questScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Daily_questCountAggregateInputType | true
  _avg?: Daily_questAvgAggregateInputType
  _sum?: Daily_questSumAggregateInputType
  _min?: Daily_questMinAggregateInputType
  _max?: Daily_questMaxAggregateInputType
}

export type Daily_questGroupByOutputType = {
  id: number
  objectiveType: $Enums.QuestObjectiveTypes
  target: number | null
  targetAction: string | null
  quantity: number | null
  questStatus: $Enums.QuestProgressStatus
  count: number
  cashReward: number
  xpReward: number
  itemRewardQuantity: number | null
  createdAt: Date
  updatedAt: Date
  itemRewardId: number | null
  userId: number | null
  _count: Daily_questCountAggregateOutputType | null
  _avg: Daily_questAvgAggregateOutputType | null
  _sum: Daily_questSumAggregateOutputType | null
  _min: Daily_questMinAggregateOutputType | null
  _max: Daily_questMaxAggregateOutputType | null
}

type GetDaily_questGroupByPayload<T extends daily_questGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Daily_questGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Daily_questGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Daily_questGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Daily_questGroupByOutputType[P]>
      }
    >
  >



export type daily_questWhereInput = {
  AND?: Prisma.daily_questWhereInput | Prisma.daily_questWhereInput[]
  OR?: Prisma.daily_questWhereInput[]
  NOT?: Prisma.daily_questWhereInput | Prisma.daily_questWhereInput[]
  id?: Prisma.IntFilter<"daily_quest"> | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"daily_quest"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  targetAction?: Prisma.StringNullableFilter<"daily_quest"> | string | null
  quantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"daily_quest"> | $Enums.QuestProgressStatus
  count?: Prisma.IntFilter<"daily_quest"> | number
  cashReward?: Prisma.IntFilter<"daily_quest"> | number
  xpReward?: Prisma.IntFilter<"daily_quest"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  userId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type daily_questOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrderInput | Prisma.SortOrder
  targetAction?: Prisma.SortOrderInput | Prisma.SortOrder
  quantity?: Prisma.SortOrderInput | Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  item?: Prisma.itemOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.daily_questOrderByRelevanceInput
}

export type daily_questWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.daily_questWhereInput | Prisma.daily_questWhereInput[]
  OR?: Prisma.daily_questWhereInput[]
  NOT?: Prisma.daily_questWhereInput | Prisma.daily_questWhereInput[]
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"daily_quest"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  targetAction?: Prisma.StringNullableFilter<"daily_quest"> | string | null
  quantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"daily_quest"> | $Enums.QuestProgressStatus
  count?: Prisma.IntFilter<"daily_quest"> | number
  cashReward?: Prisma.IntFilter<"daily_quest"> | number
  xpReward?: Prisma.IntFilter<"daily_quest"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  userId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type daily_questOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrderInput | Prisma.SortOrder
  targetAction?: Prisma.SortOrderInput | Prisma.SortOrder
  quantity?: Prisma.SortOrderInput | Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.daily_questCountOrderByAggregateInput
  _avg?: Prisma.daily_questAvgOrderByAggregateInput
  _max?: Prisma.daily_questMaxOrderByAggregateInput
  _min?: Prisma.daily_questMinOrderByAggregateInput
  _sum?: Prisma.daily_questSumOrderByAggregateInput
}

export type daily_questScalarWhereWithAggregatesInput = {
  AND?: Prisma.daily_questScalarWhereWithAggregatesInput | Prisma.daily_questScalarWhereWithAggregatesInput[]
  OR?: Prisma.daily_questScalarWhereWithAggregatesInput[]
  NOT?: Prisma.daily_questScalarWhereWithAggregatesInput | Prisma.daily_questScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"daily_quest"> | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesWithAggregatesFilter<"daily_quest"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableWithAggregatesFilter<"daily_quest"> | number | null
  targetAction?: Prisma.StringNullableWithAggregatesFilter<"daily_quest"> | string | null
  quantity?: Prisma.IntNullableWithAggregatesFilter<"daily_quest"> | number | null
  questStatus?: Prisma.EnumQuestProgressStatusWithAggregatesFilter<"daily_quest"> | $Enums.QuestProgressStatus
  count?: Prisma.IntWithAggregatesFilter<"daily_quest"> | number
  cashReward?: Prisma.IntWithAggregatesFilter<"daily_quest"> | number
  xpReward?: Prisma.IntWithAggregatesFilter<"daily_quest"> | number
  itemRewardQuantity?: Prisma.IntNullableWithAggregatesFilter<"daily_quest"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"daily_quest"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"daily_quest"> | Date | string
  itemRewardId?: Prisma.IntNullableWithAggregatesFilter<"daily_quest"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"daily_quest"> | number | null
}

export type daily_questCreateInput = {
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutDaily_questInput
  user?: Prisma.userCreateNestedOneWithoutDaily_questInput
}

export type daily_questUncheckedCreateInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
  userId?: number | null
}

export type daily_questUpdateInput = {
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutDaily_questNestedInput
  user?: Prisma.userUpdateOneWithoutDaily_questNestedInput
}

export type daily_questUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_questCreateManyInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
  userId?: number | null
}

export type daily_questUpdateManyMutationInput = {
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type daily_questUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Daily_questListRelationFilter = {
  every?: Prisma.daily_questWhereInput
  some?: Prisma.daily_questWhereInput
  none?: Prisma.daily_questWhereInput
}

export type daily_questOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type daily_questOrderByRelevanceInput = {
  fields: Prisma.daily_questOrderByRelevanceFieldEnum | Prisma.daily_questOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type daily_questCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type daily_questAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  target?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type daily_questMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type daily_questMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questStatus?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type daily_questSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  target?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  count?: Prisma.SortOrder
  cashReward?: Prisma.SortOrder
  xpReward?: Prisma.SortOrder
  itemRewardQuantity?: Prisma.SortOrder
  itemRewardId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type daily_questCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput> | Prisma.daily_questCreateWithoutItemInput[] | Prisma.daily_questUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutItemInput | Prisma.daily_questCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.daily_questCreateManyItemInputEnvelope
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
}

export type daily_questUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput> | Prisma.daily_questCreateWithoutItemInput[] | Prisma.daily_questUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutItemInput | Prisma.daily_questCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.daily_questCreateManyItemInputEnvelope
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
}

export type daily_questUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput> | Prisma.daily_questCreateWithoutItemInput[] | Prisma.daily_questUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutItemInput | Prisma.daily_questCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.daily_questUpsertWithWhereUniqueWithoutItemInput | Prisma.daily_questUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.daily_questCreateManyItemInputEnvelope
  set?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  disconnect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  delete?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  update?: Prisma.daily_questUpdateWithWhereUniqueWithoutItemInput | Prisma.daily_questUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.daily_questUpdateManyWithWhereWithoutItemInput | Prisma.daily_questUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
}

export type daily_questUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput> | Prisma.daily_questCreateWithoutItemInput[] | Prisma.daily_questUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutItemInput | Prisma.daily_questCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.daily_questUpsertWithWhereUniqueWithoutItemInput | Prisma.daily_questUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.daily_questCreateManyItemInputEnvelope
  set?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  disconnect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  delete?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  update?: Prisma.daily_questUpdateWithWhereUniqueWithoutItemInput | Prisma.daily_questUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.daily_questUpdateManyWithWhereWithoutItemInput | Prisma.daily_questUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
}

export type EnumQuestObjectiveTypesFieldUpdateOperationsInput = {
  set?: $Enums.QuestObjectiveTypes
}

export type EnumQuestProgressStatusFieldUpdateOperationsInput = {
  set?: $Enums.QuestProgressStatus
}

export type daily_questCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput> | Prisma.daily_questCreateWithoutUserInput[] | Prisma.daily_questUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutUserInput | Prisma.daily_questCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.daily_questCreateManyUserInputEnvelope
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
}

export type daily_questUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput> | Prisma.daily_questCreateWithoutUserInput[] | Prisma.daily_questUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutUserInput | Prisma.daily_questCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.daily_questCreateManyUserInputEnvelope
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
}

export type daily_questUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput> | Prisma.daily_questCreateWithoutUserInput[] | Prisma.daily_questUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutUserInput | Prisma.daily_questCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.daily_questUpsertWithWhereUniqueWithoutUserInput | Prisma.daily_questUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.daily_questCreateManyUserInputEnvelope
  set?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  disconnect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  delete?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  update?: Prisma.daily_questUpdateWithWhereUniqueWithoutUserInput | Prisma.daily_questUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.daily_questUpdateManyWithWhereWithoutUserInput | Prisma.daily_questUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
}

export type daily_questUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput> | Prisma.daily_questCreateWithoutUserInput[] | Prisma.daily_questUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.daily_questCreateOrConnectWithoutUserInput | Prisma.daily_questCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.daily_questUpsertWithWhereUniqueWithoutUserInput | Prisma.daily_questUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.daily_questCreateManyUserInputEnvelope
  set?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  disconnect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  delete?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  connect?: Prisma.daily_questWhereUniqueInput | Prisma.daily_questWhereUniqueInput[]
  update?: Prisma.daily_questUpdateWithWhereUniqueWithoutUserInput | Prisma.daily_questUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.daily_questUpdateManyWithWhereWithoutUserInput | Prisma.daily_questUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
}

export type daily_questCreateWithoutItemInput = {
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutDaily_questInput
}

export type daily_questUncheckedCreateWithoutItemInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type daily_questCreateOrConnectWithoutItemInput = {
  where: Prisma.daily_questWhereUniqueInput
  create: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput>
}

export type daily_questCreateManyItemInputEnvelope = {
  data: Prisma.daily_questCreateManyItemInput | Prisma.daily_questCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type daily_questUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.daily_questWhereUniqueInput
  update: Prisma.XOR<Prisma.daily_questUpdateWithoutItemInput, Prisma.daily_questUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.daily_questCreateWithoutItemInput, Prisma.daily_questUncheckedCreateWithoutItemInput>
}

export type daily_questUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.daily_questWhereUniqueInput
  data: Prisma.XOR<Prisma.daily_questUpdateWithoutItemInput, Prisma.daily_questUncheckedUpdateWithoutItemInput>
}

export type daily_questUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.daily_questScalarWhereInput
  data: Prisma.XOR<Prisma.daily_questUpdateManyMutationInput, Prisma.daily_questUncheckedUpdateManyWithoutItemInput>
}

export type daily_questScalarWhereInput = {
  AND?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
  OR?: Prisma.daily_questScalarWhereInput[]
  NOT?: Prisma.daily_questScalarWhereInput | Prisma.daily_questScalarWhereInput[]
  id?: Prisma.IntFilter<"daily_quest"> | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"daily_quest"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  targetAction?: Prisma.StringNullableFilter<"daily_quest"> | string | null
  quantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFilter<"daily_quest"> | $Enums.QuestProgressStatus
  count?: Prisma.IntFilter<"daily_quest"> | number
  cashReward?: Prisma.IntFilter<"daily_quest"> | number
  xpReward?: Prisma.IntFilter<"daily_quest"> | number
  itemRewardQuantity?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  createdAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"daily_quest"> | Date | string
  itemRewardId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
  userId?: Prisma.IntNullableFilter<"daily_quest"> | number | null
}

export type daily_questCreateWithoutUserInput = {
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutDaily_questInput
}

export type daily_questUncheckedCreateWithoutUserInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
}

export type daily_questCreateOrConnectWithoutUserInput = {
  where: Prisma.daily_questWhereUniqueInput
  create: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput>
}

export type daily_questCreateManyUserInputEnvelope = {
  data: Prisma.daily_questCreateManyUserInput | Prisma.daily_questCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type daily_questUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.daily_questWhereUniqueInput
  update: Prisma.XOR<Prisma.daily_questUpdateWithoutUserInput, Prisma.daily_questUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.daily_questCreateWithoutUserInput, Prisma.daily_questUncheckedCreateWithoutUserInput>
}

export type daily_questUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.daily_questWhereUniqueInput
  data: Prisma.XOR<Prisma.daily_questUpdateWithoutUserInput, Prisma.daily_questUncheckedUpdateWithoutUserInput>
}

export type daily_questUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.daily_questScalarWhereInput
  data: Prisma.XOR<Prisma.daily_questUpdateManyMutationInput, Prisma.daily_questUncheckedUpdateManyWithoutUserInput>
}

export type daily_questCreateManyItemInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type daily_questUpdateWithoutItemInput = {
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutDaily_questNestedInput
}

export type daily_questUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_questUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_questCreateManyUserInput = {
  id?: number
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  questStatus?: $Enums.QuestProgressStatus
  count?: number
  cashReward?: number
  xpReward?: number
  itemRewardQuantity?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  itemRewardId?: number | null
}

export type daily_questUpdateWithoutUserInput = {
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutDaily_questNestedInput
}

export type daily_questUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type daily_questUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  questStatus?: Prisma.EnumQuestProgressStatusFieldUpdateOperationsInput | $Enums.QuestProgressStatus
  count?: Prisma.IntFieldUpdateOperationsInput | number
  cashReward?: Prisma.IntFieldUpdateOperationsInput | number
  xpReward?: Prisma.IntFieldUpdateOperationsInput | number
  itemRewardQuantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemRewardId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type daily_questSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  objectiveType?: boolean
  target?: boolean
  targetAction?: boolean
  quantity?: boolean
  questStatus?: boolean
  count?: boolean
  cashReward?: boolean
  xpReward?: boolean
  itemRewardQuantity?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemRewardId?: boolean
  userId?: boolean
  item?: boolean | Prisma.daily_quest$itemArgs<ExtArgs>
  user?: boolean | Prisma.daily_quest$userArgs<ExtArgs>
}, ExtArgs["result"]["daily_quest"]>



export type daily_questSelectScalar = {
  id?: boolean
  objectiveType?: boolean
  target?: boolean
  targetAction?: boolean
  quantity?: boolean
  questStatus?: boolean
  count?: boolean
  cashReward?: boolean
  xpReward?: boolean
  itemRewardQuantity?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemRewardId?: boolean
  userId?: boolean
}

export type daily_questOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "objectiveType" | "target" | "targetAction" | "quantity" | "questStatus" | "count" | "cashReward" | "xpReward" | "itemRewardQuantity" | "createdAt" | "updatedAt" | "itemRewardId" | "userId", ExtArgs["result"]["daily_quest"]>
export type daily_questInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Prisma.daily_quest$itemArgs<ExtArgs>
  user?: boolean | Prisma.daily_quest$userArgs<ExtArgs>
}

export type $daily_questPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "daily_quest"
  objects: {
    item: Prisma.$itemPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    objectiveType: $Enums.QuestObjectiveTypes
    target: number | null
    targetAction: string | null
    quantity: number | null
    questStatus: $Enums.QuestProgressStatus
    count: number
    cashReward: number
    xpReward: number
    itemRewardQuantity: number | null
    createdAt: Date
    updatedAt: Date
    itemRewardId: number | null
    userId: number | null
  }, ExtArgs["result"]["daily_quest"]>
  composites: {}
}

export type daily_questGetPayload<S extends boolean | null | undefined | daily_questDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$daily_questPayload, S>

export type daily_questCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<daily_questFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Daily_questCountAggregateInputType | true
  }

export interface daily_questDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['daily_quest'], meta: { name: 'daily_quest' } }
  /**
   * Find zero or one Daily_quest that matches the filter.
   * @param {daily_questFindUniqueArgs} args - Arguments to find a Daily_quest
   * @example
   * // Get one Daily_quest
   * const daily_quest = await prisma.daily_quest.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends daily_questFindUniqueArgs>(args: Prisma.SelectSubset<T, daily_questFindUniqueArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Daily_quest that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {daily_questFindUniqueOrThrowArgs} args - Arguments to find a Daily_quest
   * @example
   * // Get one Daily_quest
   * const daily_quest = await prisma.daily_quest.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends daily_questFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, daily_questFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Daily_quest that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questFindFirstArgs} args - Arguments to find a Daily_quest
   * @example
   * // Get one Daily_quest
   * const daily_quest = await prisma.daily_quest.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends daily_questFindFirstArgs>(args?: Prisma.SelectSubset<T, daily_questFindFirstArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Daily_quest that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questFindFirstOrThrowArgs} args - Arguments to find a Daily_quest
   * @example
   * // Get one Daily_quest
   * const daily_quest = await prisma.daily_quest.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends daily_questFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, daily_questFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Daily_quests that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Daily_quests
   * const daily_quests = await prisma.daily_quest.findMany()
   * 
   * // Get first 10 Daily_quests
   * const daily_quests = await prisma.daily_quest.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const daily_questWithIdOnly = await prisma.daily_quest.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends daily_questFindManyArgs>(args?: Prisma.SelectSubset<T, daily_questFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Daily_quest.
   * @param {daily_questCreateArgs} args - Arguments to create a Daily_quest.
   * @example
   * // Create one Daily_quest
   * const Daily_quest = await prisma.daily_quest.create({
   *   data: {
   *     // ... data to create a Daily_quest
   *   }
   * })
   * 
   */
  create<T extends daily_questCreateArgs>(args: Prisma.SelectSubset<T, daily_questCreateArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Daily_quests.
   * @param {daily_questCreateManyArgs} args - Arguments to create many Daily_quests.
   * @example
   * // Create many Daily_quests
   * const daily_quest = await prisma.daily_quest.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends daily_questCreateManyArgs>(args?: Prisma.SelectSubset<T, daily_questCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Daily_quest.
   * @param {daily_questDeleteArgs} args - Arguments to delete one Daily_quest.
   * @example
   * // Delete one Daily_quest
   * const Daily_quest = await prisma.daily_quest.delete({
   *   where: {
   *     // ... filter to delete one Daily_quest
   *   }
   * })
   * 
   */
  delete<T extends daily_questDeleteArgs>(args: Prisma.SelectSubset<T, daily_questDeleteArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Daily_quest.
   * @param {daily_questUpdateArgs} args - Arguments to update one Daily_quest.
   * @example
   * // Update one Daily_quest
   * const daily_quest = await prisma.daily_quest.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends daily_questUpdateArgs>(args: Prisma.SelectSubset<T, daily_questUpdateArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Daily_quests.
   * @param {daily_questDeleteManyArgs} args - Arguments to filter Daily_quests to delete.
   * @example
   * // Delete a few Daily_quests
   * const { count } = await prisma.daily_quest.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends daily_questDeleteManyArgs>(args?: Prisma.SelectSubset<T, daily_questDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Daily_quests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Daily_quests
   * const daily_quest = await prisma.daily_quest.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends daily_questUpdateManyArgs>(args: Prisma.SelectSubset<T, daily_questUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Daily_quest.
   * @param {daily_questUpsertArgs} args - Arguments to update or create a Daily_quest.
   * @example
   * // Update or create a Daily_quest
   * const daily_quest = await prisma.daily_quest.upsert({
   *   create: {
   *     // ... data to create a Daily_quest
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Daily_quest we want to update
   *   }
   * })
   */
  upsert<T extends daily_questUpsertArgs>(args: Prisma.SelectSubset<T, daily_questUpsertArgs<ExtArgs>>): Prisma.Prisma__daily_questClient<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Daily_quests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questCountArgs} args - Arguments to filter Daily_quests to count.
   * @example
   * // Count the number of Daily_quests
   * const count = await prisma.daily_quest.count({
   *   where: {
   *     // ... the filter for the Daily_quests we want to count
   *   }
   * })
  **/
  count<T extends daily_questCountArgs>(
    args?: Prisma.Subset<T, daily_questCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Daily_questCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Daily_quest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Daily_questAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Daily_questAggregateArgs>(args: Prisma.Subset<T, Daily_questAggregateArgs>): Prisma.PrismaPromise<GetDaily_questAggregateType<T>>

  /**
   * Group by Daily_quest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {daily_questGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends daily_questGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: daily_questGroupByArgs['orderBy'] }
      : { orderBy?: daily_questGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, daily_questGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDaily_questGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the daily_quest model
 */
readonly fields: daily_questFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for daily_quest.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__daily_questClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  item<T extends Prisma.daily_quest$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.daily_quest$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.daily_quest$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.daily_quest$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the daily_quest model
 */
export interface daily_questFieldRefs {
  readonly id: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly objectiveType: Prisma.FieldRef<"daily_quest", 'QuestObjectiveTypes'>
  readonly target: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly targetAction: Prisma.FieldRef<"daily_quest", 'String'>
  readonly quantity: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly questStatus: Prisma.FieldRef<"daily_quest", 'QuestProgressStatus'>
  readonly count: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly cashReward: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly xpReward: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly itemRewardQuantity: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly createdAt: Prisma.FieldRef<"daily_quest", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"daily_quest", 'DateTime'>
  readonly itemRewardId: Prisma.FieldRef<"daily_quest", 'Int'>
  readonly userId: Prisma.FieldRef<"daily_quest", 'Int'>
}
    

// Custom InputTypes
/**
 * daily_quest findUnique
 */
export type daily_questFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter, which daily_quest to fetch.
   */
  where: Prisma.daily_questWhereUniqueInput
}

/**
 * daily_quest findUniqueOrThrow
 */
export type daily_questFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter, which daily_quest to fetch.
   */
  where: Prisma.daily_questWhereUniqueInput
}

/**
 * daily_quest findFirst
 */
export type daily_questFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter, which daily_quest to fetch.
   */
  where?: Prisma.daily_questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_quests to fetch.
   */
  orderBy?: Prisma.daily_questOrderByWithRelationInput | Prisma.daily_questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for daily_quests.
   */
  cursor?: Prisma.daily_questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of daily_quests.
   */
  distinct?: Prisma.Daily_questScalarFieldEnum | Prisma.Daily_questScalarFieldEnum[]
}

/**
 * daily_quest findFirstOrThrow
 */
export type daily_questFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter, which daily_quest to fetch.
   */
  where?: Prisma.daily_questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_quests to fetch.
   */
  orderBy?: Prisma.daily_questOrderByWithRelationInput | Prisma.daily_questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for daily_quests.
   */
  cursor?: Prisma.daily_questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_quests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of daily_quests.
   */
  distinct?: Prisma.Daily_questScalarFieldEnum | Prisma.Daily_questScalarFieldEnum[]
}

/**
 * daily_quest findMany
 */
export type daily_questFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter, which daily_quests to fetch.
   */
  where?: Prisma.daily_questWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of daily_quests to fetch.
   */
  orderBy?: Prisma.daily_questOrderByWithRelationInput | Prisma.daily_questOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing daily_quests.
   */
  cursor?: Prisma.daily_questWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` daily_quests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` daily_quests.
   */
  skip?: number
  distinct?: Prisma.Daily_questScalarFieldEnum | Prisma.Daily_questScalarFieldEnum[]
}

/**
 * daily_quest create
 */
export type daily_questCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * The data needed to create a daily_quest.
   */
  data: Prisma.XOR<Prisma.daily_questCreateInput, Prisma.daily_questUncheckedCreateInput>
}

/**
 * daily_quest createMany
 */
export type daily_questCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many daily_quests.
   */
  data: Prisma.daily_questCreateManyInput | Prisma.daily_questCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * daily_quest update
 */
export type daily_questUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * The data needed to update a daily_quest.
   */
  data: Prisma.XOR<Prisma.daily_questUpdateInput, Prisma.daily_questUncheckedUpdateInput>
  /**
   * Choose, which daily_quest to update.
   */
  where: Prisma.daily_questWhereUniqueInput
}

/**
 * daily_quest updateMany
 */
export type daily_questUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update daily_quests.
   */
  data: Prisma.XOR<Prisma.daily_questUpdateManyMutationInput, Prisma.daily_questUncheckedUpdateManyInput>
  /**
   * Filter which daily_quests to update
   */
  where?: Prisma.daily_questWhereInput
  /**
   * Limit how many daily_quests to update.
   */
  limit?: number
}

/**
 * daily_quest upsert
 */
export type daily_questUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * The filter to search for the daily_quest to update in case it exists.
   */
  where: Prisma.daily_questWhereUniqueInput
  /**
   * In case the daily_quest found by the `where` argument doesn't exist, create a new daily_quest with this data.
   */
  create: Prisma.XOR<Prisma.daily_questCreateInput, Prisma.daily_questUncheckedCreateInput>
  /**
   * In case the daily_quest was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.daily_questUpdateInput, Prisma.daily_questUncheckedUpdateInput>
}

/**
 * daily_quest delete
 */
export type daily_questDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  /**
   * Filter which daily_quest to delete.
   */
  where: Prisma.daily_questWhereUniqueInput
}

/**
 * daily_quest deleteMany
 */
export type daily_questDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which daily_quests to delete
   */
  where?: Prisma.daily_questWhereInput
  /**
   * Limit how many daily_quests to delete.
   */
  limit?: number
}

/**
 * daily_quest.item
 */
export type daily_quest$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * daily_quest.user
 */
export type daily_quest$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * daily_quest without action
 */
export type daily_questDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
}
