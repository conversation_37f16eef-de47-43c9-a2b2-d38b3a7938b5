
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_completed_course` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_completed_course
 * 
 */
export type user_completed_courseModel = runtime.Types.Result.DefaultSelection<Prisma.$user_completed_coursePayload>

export type AggregateUser_completed_course = {
  _count: User_completed_courseCountAggregateOutputType | null
  _avg: User_completed_courseAvgAggregateOutputType | null
  _sum: User_completed_courseSumAggregateOutputType | null
  _min: User_completed_courseMinAggregateOutputType | null
  _max: User_completed_courseMaxAggregateOutputType | null
}

export type User_completed_courseAvgAggregateOutputType = {
  id: number | null
  courseId: number | null
  userId: number | null
}

export type User_completed_courseSumAggregateOutputType = {
  id: number | null
  courseId: number | null
  userId: number | null
}

export type User_completed_courseMinAggregateOutputType = {
  id: number | null
  courseId: number | null
  completedAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type User_completed_courseMaxAggregateOutputType = {
  id: number | null
  courseId: number | null
  completedAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type User_completed_courseCountAggregateOutputType = {
  id: number
  courseId: number
  completedAt: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type User_completed_courseAvgAggregateInputType = {
  id?: true
  courseId?: true
  userId?: true
}

export type User_completed_courseSumAggregateInputType = {
  id?: true
  courseId?: true
  userId?: true
}

export type User_completed_courseMinAggregateInputType = {
  id?: true
  courseId?: true
  completedAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type User_completed_courseMaxAggregateInputType = {
  id?: true
  courseId?: true
  completedAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type User_completed_courseCountAggregateInputType = {
  id?: true
  courseId?: true
  completedAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type User_completed_courseAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_completed_course to aggregate.
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_completed_courses to fetch.
   */
  orderBy?: Prisma.user_completed_courseOrderByWithRelationInput | Prisma.user_completed_courseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_completed_courseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_completed_courses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_completed_courses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_completed_courses
  **/
  _count?: true | User_completed_courseCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_completed_courseAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_completed_courseSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_completed_courseMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_completed_courseMaxAggregateInputType
}

export type GetUser_completed_courseAggregateType<T extends User_completed_courseAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_completed_course]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_completed_course[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_completed_course[P]>
}




export type user_completed_courseGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_completed_courseWhereInput
  orderBy?: Prisma.user_completed_courseOrderByWithAggregationInput | Prisma.user_completed_courseOrderByWithAggregationInput[]
  by: Prisma.User_completed_courseScalarFieldEnum[] | Prisma.User_completed_courseScalarFieldEnum
  having?: Prisma.user_completed_courseScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_completed_courseCountAggregateInputType | true
  _avg?: User_completed_courseAvgAggregateInputType
  _sum?: User_completed_courseSumAggregateInputType
  _min?: User_completed_courseMinAggregateInputType
  _max?: User_completed_courseMaxAggregateInputType
}

export type User_completed_courseGroupByOutputType = {
  id: number
  courseId: number
  completedAt: Date
  createdAt: Date
  updatedAt: Date
  userId: number | null
  _count: User_completed_courseCountAggregateOutputType | null
  _avg: User_completed_courseAvgAggregateOutputType | null
  _sum: User_completed_courseSumAggregateOutputType | null
  _min: User_completed_courseMinAggregateOutputType | null
  _max: User_completed_courseMaxAggregateOutputType | null
}

type GetUser_completed_courseGroupByPayload<T extends user_completed_courseGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_completed_courseGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_completed_courseGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_completed_courseGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_completed_courseGroupByOutputType[P]>
      }
    >
  >



export type user_completed_courseWhereInput = {
  AND?: Prisma.user_completed_courseWhereInput | Prisma.user_completed_courseWhereInput[]
  OR?: Prisma.user_completed_courseWhereInput[]
  NOT?: Prisma.user_completed_courseWhereInput | Prisma.user_completed_courseWhereInput[]
  id?: Prisma.IntFilter<"user_completed_course"> | number
  courseId?: Prisma.IntFilter<"user_completed_course"> | number
  completedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_completed_course"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type user_completed_courseOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  completedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
}

export type user_completed_courseWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.user_completed_courseWhereInput | Prisma.user_completed_courseWhereInput[]
  OR?: Prisma.user_completed_courseWhereInput[]
  NOT?: Prisma.user_completed_courseWhereInput | Prisma.user_completed_courseWhereInput[]
  courseId?: Prisma.IntFilter<"user_completed_course"> | number
  completedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_completed_course"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type user_completed_courseOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  completedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_completed_courseCountOrderByAggregateInput
  _avg?: Prisma.user_completed_courseAvgOrderByAggregateInput
  _max?: Prisma.user_completed_courseMaxOrderByAggregateInput
  _min?: Prisma.user_completed_courseMinOrderByAggregateInput
  _sum?: Prisma.user_completed_courseSumOrderByAggregateInput
}

export type user_completed_courseScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_completed_courseScalarWhereWithAggregatesInput | Prisma.user_completed_courseScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_completed_courseScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_completed_courseScalarWhereWithAggregatesInput | Prisma.user_completed_courseScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_completed_course"> | number
  courseId?: Prisma.IntWithAggregatesFilter<"user_completed_course"> | number
  completedAt?: Prisma.DateTimeWithAggregatesFilter<"user_completed_course"> | Date | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_completed_course"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_completed_course"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"user_completed_course"> | number | null
}

export type user_completed_courseCreateInput = {
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutUser_completed_courseInput
}

export type user_completed_courseUncheckedCreateInput = {
  id?: number
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_completed_courseUpdateInput = {
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutUser_completed_courseNestedInput
}

export type user_completed_courseUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_completed_courseCreateManyInput = {
  id?: number
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_completed_courseUpdateManyMutationInput = {
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_completed_courseUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_completed_courseListRelationFilter = {
  every?: Prisma.user_completed_courseWhereInput
  some?: Prisma.user_completed_courseWhereInput
  none?: Prisma.user_completed_courseWhereInput
}

export type user_completed_courseOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_completed_courseCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  completedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_completed_courseAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_completed_courseMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  completedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_completed_courseMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  completedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_completed_courseSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  courseId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_completed_courseCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput> | Prisma.user_completed_courseCreateWithoutUserInput[] | Prisma.user_completed_courseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_completed_courseCreateOrConnectWithoutUserInput | Prisma.user_completed_courseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_completed_courseCreateManyUserInputEnvelope
  connect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
}

export type user_completed_courseUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput> | Prisma.user_completed_courseCreateWithoutUserInput[] | Prisma.user_completed_courseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_completed_courseCreateOrConnectWithoutUserInput | Prisma.user_completed_courseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_completed_courseCreateManyUserInputEnvelope
  connect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
}

export type user_completed_courseUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput> | Prisma.user_completed_courseCreateWithoutUserInput[] | Prisma.user_completed_courseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_completed_courseCreateOrConnectWithoutUserInput | Prisma.user_completed_courseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_completed_courseUpsertWithWhereUniqueWithoutUserInput | Prisma.user_completed_courseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_completed_courseCreateManyUserInputEnvelope
  set?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  disconnect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  delete?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  connect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  update?: Prisma.user_completed_courseUpdateWithWhereUniqueWithoutUserInput | Prisma.user_completed_courseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_completed_courseUpdateManyWithWhereWithoutUserInput | Prisma.user_completed_courseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_completed_courseScalarWhereInput | Prisma.user_completed_courseScalarWhereInput[]
}

export type user_completed_courseUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput> | Prisma.user_completed_courseCreateWithoutUserInput[] | Prisma.user_completed_courseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_completed_courseCreateOrConnectWithoutUserInput | Prisma.user_completed_courseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_completed_courseUpsertWithWhereUniqueWithoutUserInput | Prisma.user_completed_courseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_completed_courseCreateManyUserInputEnvelope
  set?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  disconnect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  delete?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  connect?: Prisma.user_completed_courseWhereUniqueInput | Prisma.user_completed_courseWhereUniqueInput[]
  update?: Prisma.user_completed_courseUpdateWithWhereUniqueWithoutUserInput | Prisma.user_completed_courseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_completed_courseUpdateManyWithWhereWithoutUserInput | Prisma.user_completed_courseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_completed_courseScalarWhereInput | Prisma.user_completed_courseScalarWhereInput[]
}

export type user_completed_courseCreateWithoutUserInput = {
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_completed_courseUncheckedCreateWithoutUserInput = {
  id?: number
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_completed_courseCreateOrConnectWithoutUserInput = {
  where: Prisma.user_completed_courseWhereUniqueInput
  create: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput>
}

export type user_completed_courseCreateManyUserInputEnvelope = {
  data: Prisma.user_completed_courseCreateManyUserInput | Prisma.user_completed_courseCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_completed_courseUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_completed_courseWhereUniqueInput
  update: Prisma.XOR<Prisma.user_completed_courseUpdateWithoutUserInput, Prisma.user_completed_courseUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_completed_courseCreateWithoutUserInput, Prisma.user_completed_courseUncheckedCreateWithoutUserInput>
}

export type user_completed_courseUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_completed_courseWhereUniqueInput
  data: Prisma.XOR<Prisma.user_completed_courseUpdateWithoutUserInput, Prisma.user_completed_courseUncheckedUpdateWithoutUserInput>
}

export type user_completed_courseUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_completed_courseScalarWhereInput
  data: Prisma.XOR<Prisma.user_completed_courseUpdateManyMutationInput, Prisma.user_completed_courseUncheckedUpdateManyWithoutUserInput>
}

export type user_completed_courseScalarWhereInput = {
  AND?: Prisma.user_completed_courseScalarWhereInput | Prisma.user_completed_courseScalarWhereInput[]
  OR?: Prisma.user_completed_courseScalarWhereInput[]
  NOT?: Prisma.user_completed_courseScalarWhereInput | Prisma.user_completed_courseScalarWhereInput[]
  id?: Prisma.IntFilter<"user_completed_course"> | number
  courseId?: Prisma.IntFilter<"user_completed_course"> | number
  completedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_completed_course"> | Date | string
  userId?: Prisma.IntNullableFilter<"user_completed_course"> | number | null
}

export type user_completed_courseCreateManyUserInput = {
  id?: number
  courseId: number
  completedAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type user_completed_courseUpdateWithoutUserInput = {
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_completed_courseUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_completed_courseUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  courseId?: Prisma.IntFieldUpdateOperationsInput | number
  completedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type user_completed_courseSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  courseId?: boolean
  completedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.user_completed_course$userArgs<ExtArgs>
}, ExtArgs["result"]["user_completed_course"]>



export type user_completed_courseSelectScalar = {
  id?: boolean
  courseId?: boolean
  completedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type user_completed_courseOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "courseId" | "completedAt" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["user_completed_course"]>
export type user_completed_courseInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.user_completed_course$userArgs<ExtArgs>
}

export type $user_completed_coursePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_completed_course"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    courseId: number
    completedAt: Date
    createdAt: Date
    updatedAt: Date
    userId: number | null
  }, ExtArgs["result"]["user_completed_course"]>
  composites: {}
}

export type user_completed_courseGetPayload<S extends boolean | null | undefined | user_completed_courseDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload, S>

export type user_completed_courseCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_completed_courseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_completed_courseCountAggregateInputType | true
  }

export interface user_completed_courseDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_completed_course'], meta: { name: 'user_completed_course' } }
  /**
   * Find zero or one User_completed_course that matches the filter.
   * @param {user_completed_courseFindUniqueArgs} args - Arguments to find a User_completed_course
   * @example
   * // Get one User_completed_course
   * const user_completed_course = await prisma.user_completed_course.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_completed_courseFindUniqueArgs>(args: Prisma.SelectSubset<T, user_completed_courseFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_completed_course that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_completed_courseFindUniqueOrThrowArgs} args - Arguments to find a User_completed_course
   * @example
   * // Get one User_completed_course
   * const user_completed_course = await prisma.user_completed_course.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_completed_courseFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_completed_courseFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_completed_course that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseFindFirstArgs} args - Arguments to find a User_completed_course
   * @example
   * // Get one User_completed_course
   * const user_completed_course = await prisma.user_completed_course.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_completed_courseFindFirstArgs>(args?: Prisma.SelectSubset<T, user_completed_courseFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_completed_course that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseFindFirstOrThrowArgs} args - Arguments to find a User_completed_course
   * @example
   * // Get one User_completed_course
   * const user_completed_course = await prisma.user_completed_course.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_completed_courseFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_completed_courseFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_completed_courses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_completed_courses
   * const user_completed_courses = await prisma.user_completed_course.findMany()
   * 
   * // Get first 10 User_completed_courses
   * const user_completed_courses = await prisma.user_completed_course.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_completed_courseWithIdOnly = await prisma.user_completed_course.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_completed_courseFindManyArgs>(args?: Prisma.SelectSubset<T, user_completed_courseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_completed_course.
   * @param {user_completed_courseCreateArgs} args - Arguments to create a User_completed_course.
   * @example
   * // Create one User_completed_course
   * const User_completed_course = await prisma.user_completed_course.create({
   *   data: {
   *     // ... data to create a User_completed_course
   *   }
   * })
   * 
   */
  create<T extends user_completed_courseCreateArgs>(args: Prisma.SelectSubset<T, user_completed_courseCreateArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_completed_courses.
   * @param {user_completed_courseCreateManyArgs} args - Arguments to create many User_completed_courses.
   * @example
   * // Create many User_completed_courses
   * const user_completed_course = await prisma.user_completed_course.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_completed_courseCreateManyArgs>(args?: Prisma.SelectSubset<T, user_completed_courseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_completed_course.
   * @param {user_completed_courseDeleteArgs} args - Arguments to delete one User_completed_course.
   * @example
   * // Delete one User_completed_course
   * const User_completed_course = await prisma.user_completed_course.delete({
   *   where: {
   *     // ... filter to delete one User_completed_course
   *   }
   * })
   * 
   */
  delete<T extends user_completed_courseDeleteArgs>(args: Prisma.SelectSubset<T, user_completed_courseDeleteArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_completed_course.
   * @param {user_completed_courseUpdateArgs} args - Arguments to update one User_completed_course.
   * @example
   * // Update one User_completed_course
   * const user_completed_course = await prisma.user_completed_course.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_completed_courseUpdateArgs>(args: Prisma.SelectSubset<T, user_completed_courseUpdateArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_completed_courses.
   * @param {user_completed_courseDeleteManyArgs} args - Arguments to filter User_completed_courses to delete.
   * @example
   * // Delete a few User_completed_courses
   * const { count } = await prisma.user_completed_course.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_completed_courseDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_completed_courseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_completed_courses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_completed_courses
   * const user_completed_course = await prisma.user_completed_course.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_completed_courseUpdateManyArgs>(args: Prisma.SelectSubset<T, user_completed_courseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_completed_course.
   * @param {user_completed_courseUpsertArgs} args - Arguments to update or create a User_completed_course.
   * @example
   * // Update or create a User_completed_course
   * const user_completed_course = await prisma.user_completed_course.upsert({
   *   create: {
   *     // ... data to create a User_completed_course
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_completed_course we want to update
   *   }
   * })
   */
  upsert<T extends user_completed_courseUpsertArgs>(args: Prisma.SelectSubset<T, user_completed_courseUpsertArgs<ExtArgs>>): Prisma.Prisma__user_completed_courseClient<runtime.Types.Result.GetResult<Prisma.$user_completed_coursePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_completed_courses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseCountArgs} args - Arguments to filter User_completed_courses to count.
   * @example
   * // Count the number of User_completed_courses
   * const count = await prisma.user_completed_course.count({
   *   where: {
   *     // ... the filter for the User_completed_courses we want to count
   *   }
   * })
  **/
  count<T extends user_completed_courseCountArgs>(
    args?: Prisma.Subset<T, user_completed_courseCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_completed_courseCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_completed_course.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_completed_courseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_completed_courseAggregateArgs>(args: Prisma.Subset<T, User_completed_courseAggregateArgs>): Prisma.PrismaPromise<GetUser_completed_courseAggregateType<T>>

  /**
   * Group by User_completed_course.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_completed_courseGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_completed_courseGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_completed_courseGroupByArgs['orderBy'] }
      : { orderBy?: user_completed_courseGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_completed_courseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_completed_courseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_completed_course model
 */
readonly fields: user_completed_courseFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_completed_course.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_completed_courseClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.user_completed_course$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_completed_course$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_completed_course model
 */
export interface user_completed_courseFieldRefs {
  readonly id: Prisma.FieldRef<"user_completed_course", 'Int'>
  readonly courseId: Prisma.FieldRef<"user_completed_course", 'Int'>
  readonly completedAt: Prisma.FieldRef<"user_completed_course", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"user_completed_course", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_completed_course", 'DateTime'>
  readonly userId: Prisma.FieldRef<"user_completed_course", 'Int'>
}
    

// Custom InputTypes
/**
 * user_completed_course findUnique
 */
export type user_completed_courseFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter, which user_completed_course to fetch.
   */
  where: Prisma.user_completed_courseWhereUniqueInput
}

/**
 * user_completed_course findUniqueOrThrow
 */
export type user_completed_courseFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter, which user_completed_course to fetch.
   */
  where: Prisma.user_completed_courseWhereUniqueInput
}

/**
 * user_completed_course findFirst
 */
export type user_completed_courseFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter, which user_completed_course to fetch.
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_completed_courses to fetch.
   */
  orderBy?: Prisma.user_completed_courseOrderByWithRelationInput | Prisma.user_completed_courseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_completed_courses.
   */
  cursor?: Prisma.user_completed_courseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_completed_courses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_completed_courses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_completed_courses.
   */
  distinct?: Prisma.User_completed_courseScalarFieldEnum | Prisma.User_completed_courseScalarFieldEnum[]
}

/**
 * user_completed_course findFirstOrThrow
 */
export type user_completed_courseFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter, which user_completed_course to fetch.
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_completed_courses to fetch.
   */
  orderBy?: Prisma.user_completed_courseOrderByWithRelationInput | Prisma.user_completed_courseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_completed_courses.
   */
  cursor?: Prisma.user_completed_courseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_completed_courses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_completed_courses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_completed_courses.
   */
  distinct?: Prisma.User_completed_courseScalarFieldEnum | Prisma.User_completed_courseScalarFieldEnum[]
}

/**
 * user_completed_course findMany
 */
export type user_completed_courseFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter, which user_completed_courses to fetch.
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_completed_courses to fetch.
   */
  orderBy?: Prisma.user_completed_courseOrderByWithRelationInput | Prisma.user_completed_courseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_completed_courses.
   */
  cursor?: Prisma.user_completed_courseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_completed_courses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_completed_courses.
   */
  skip?: number
  distinct?: Prisma.User_completed_courseScalarFieldEnum | Prisma.User_completed_courseScalarFieldEnum[]
}

/**
 * user_completed_course create
 */
export type user_completed_courseCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * The data needed to create a user_completed_course.
   */
  data: Prisma.XOR<Prisma.user_completed_courseCreateInput, Prisma.user_completed_courseUncheckedCreateInput>
}

/**
 * user_completed_course createMany
 */
export type user_completed_courseCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_completed_courses.
   */
  data: Prisma.user_completed_courseCreateManyInput | Prisma.user_completed_courseCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_completed_course update
 */
export type user_completed_courseUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * The data needed to update a user_completed_course.
   */
  data: Prisma.XOR<Prisma.user_completed_courseUpdateInput, Prisma.user_completed_courseUncheckedUpdateInput>
  /**
   * Choose, which user_completed_course to update.
   */
  where: Prisma.user_completed_courseWhereUniqueInput
}

/**
 * user_completed_course updateMany
 */
export type user_completed_courseUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_completed_courses.
   */
  data: Prisma.XOR<Prisma.user_completed_courseUpdateManyMutationInput, Prisma.user_completed_courseUncheckedUpdateManyInput>
  /**
   * Filter which user_completed_courses to update
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * Limit how many user_completed_courses to update.
   */
  limit?: number
}

/**
 * user_completed_course upsert
 */
export type user_completed_courseUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * The filter to search for the user_completed_course to update in case it exists.
   */
  where: Prisma.user_completed_courseWhereUniqueInput
  /**
   * In case the user_completed_course found by the `where` argument doesn't exist, create a new user_completed_course with this data.
   */
  create: Prisma.XOR<Prisma.user_completed_courseCreateInput, Prisma.user_completed_courseUncheckedCreateInput>
  /**
   * In case the user_completed_course was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_completed_courseUpdateInput, Prisma.user_completed_courseUncheckedUpdateInput>
}

/**
 * user_completed_course delete
 */
export type user_completed_courseDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
  /**
   * Filter which user_completed_course to delete.
   */
  where: Prisma.user_completed_courseWhereUniqueInput
}

/**
 * user_completed_course deleteMany
 */
export type user_completed_courseDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_completed_courses to delete
   */
  where?: Prisma.user_completed_courseWhereInput
  /**
   * Limit how many user_completed_courses to delete.
   */
  limit?: number
}

/**
 * user_completed_course.user
 */
export type user_completed_course$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * user_completed_course without action
 */
export type user_completed_courseDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_completed_course
   */
  select?: Prisma.user_completed_courseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_completed_course
   */
  omit?: Prisma.user_completed_courseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_completed_courseInclude<ExtArgs> | null
}
