
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `shrine_donation` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model shrine_donation
 * 
 */
export type shrine_donationModel = runtime.Types.Result.DefaultSelection<Prisma.$shrine_donationPayload>

export type AggregateShrine_donation = {
  _count: Shrine_donationCountAggregateOutputType | null
  _avg: Shrine_donationAvgAggregateOutputType | null
  _sum: Shrine_donationSumAggregateOutputType | null
  _min: Shrine_donationMinAggregateOutputType | null
  _max: Shrine_donationMaxAggregateOutputType | null
}

export type Shrine_donationAvgAggregateOutputType = {
  id: number | null
  amount: number | null
  shrineGoalId: number | null
  userId: number | null
}

export type Shrine_donationSumAggregateOutputType = {
  id: number | null
  amount: number | null
  shrineGoalId: number | null
  userId: number | null
}

export type Shrine_donationMinAggregateOutputType = {
  id: number | null
  amount: number | null
  date: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  shrineGoalId: number | null
  userId: number | null
}

export type Shrine_donationMaxAggregateOutputType = {
  id: number | null
  amount: number | null
  date: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  shrineGoalId: number | null
  userId: number | null
}

export type Shrine_donationCountAggregateOutputType = {
  id: number
  amount: number
  date: number
  createdAt: number
  updatedAt: number
  shrineGoalId: number
  userId: number
  _all: number
}


export type Shrine_donationAvgAggregateInputType = {
  id?: true
  amount?: true
  shrineGoalId?: true
  userId?: true
}

export type Shrine_donationSumAggregateInputType = {
  id?: true
  amount?: true
  shrineGoalId?: true
  userId?: true
}

export type Shrine_donationMinAggregateInputType = {
  id?: true
  amount?: true
  date?: true
  createdAt?: true
  updatedAt?: true
  shrineGoalId?: true
  userId?: true
}

export type Shrine_donationMaxAggregateInputType = {
  id?: true
  amount?: true
  date?: true
  createdAt?: true
  updatedAt?: true
  shrineGoalId?: true
  userId?: true
}

export type Shrine_donationCountAggregateInputType = {
  id?: true
  amount?: true
  date?: true
  createdAt?: true
  updatedAt?: true
  shrineGoalId?: true
  userId?: true
  _all?: true
}

export type Shrine_donationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shrine_donation to aggregate.
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_donations to fetch.
   */
  orderBy?: Prisma.shrine_donationOrderByWithRelationInput | Prisma.shrine_donationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.shrine_donationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned shrine_donations
  **/
  _count?: true | Shrine_donationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Shrine_donationAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Shrine_donationSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Shrine_donationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Shrine_donationMaxAggregateInputType
}

export type GetShrine_donationAggregateType<T extends Shrine_donationAggregateArgs> = {
      [P in keyof T & keyof AggregateShrine_donation]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateShrine_donation[P]>
    : Prisma.GetScalarType<T[P], AggregateShrine_donation[P]>
}




export type shrine_donationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shrine_donationWhereInput
  orderBy?: Prisma.shrine_donationOrderByWithAggregationInput | Prisma.shrine_donationOrderByWithAggregationInput[]
  by: Prisma.Shrine_donationScalarFieldEnum[] | Prisma.Shrine_donationScalarFieldEnum
  having?: Prisma.shrine_donationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Shrine_donationCountAggregateInputType | true
  _avg?: Shrine_donationAvgAggregateInputType
  _sum?: Shrine_donationSumAggregateInputType
  _min?: Shrine_donationMinAggregateInputType
  _max?: Shrine_donationMaxAggregateInputType
}

export type Shrine_donationGroupByOutputType = {
  id: number
  amount: number
  date: Date
  createdAt: Date
  updatedAt: Date
  shrineGoalId: number | null
  userId: number | null
  _count: Shrine_donationCountAggregateOutputType | null
  _avg: Shrine_donationAvgAggregateOutputType | null
  _sum: Shrine_donationSumAggregateOutputType | null
  _min: Shrine_donationMinAggregateOutputType | null
  _max: Shrine_donationMaxAggregateOutputType | null
}

type GetShrine_donationGroupByPayload<T extends shrine_donationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Shrine_donationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Shrine_donationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Shrine_donationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Shrine_donationGroupByOutputType[P]>
      }
    >
  >



export type shrine_donationWhereInput = {
  AND?: Prisma.shrine_donationWhereInput | Prisma.shrine_donationWhereInput[]
  OR?: Prisma.shrine_donationWhereInput[]
  NOT?: Prisma.shrine_donationWhereInput | Prisma.shrine_donationWhereInput[]
  id?: Prisma.IntFilter<"shrine_donation"> | number
  amount?: Prisma.IntFilter<"shrine_donation"> | number
  date?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  shrineGoalId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
  userId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
  shrine_goal?: Prisma.XOR<Prisma.Shrine_goalNullableScalarRelationFilter, Prisma.shrine_goalWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type shrine_donationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  date?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  shrine_goal?: Prisma.shrine_goalOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type shrine_donationWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.shrine_donationWhereInput | Prisma.shrine_donationWhereInput[]
  OR?: Prisma.shrine_donationWhereInput[]
  NOT?: Prisma.shrine_donationWhereInput | Prisma.shrine_donationWhereInput[]
  amount?: Prisma.IntFilter<"shrine_donation"> | number
  date?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  shrineGoalId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
  userId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
  shrine_goal?: Prisma.XOR<Prisma.Shrine_goalNullableScalarRelationFilter, Prisma.shrine_goalWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type shrine_donationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  date?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.shrine_donationCountOrderByAggregateInput
  _avg?: Prisma.shrine_donationAvgOrderByAggregateInput
  _max?: Prisma.shrine_donationMaxOrderByAggregateInput
  _min?: Prisma.shrine_donationMinOrderByAggregateInput
  _sum?: Prisma.shrine_donationSumOrderByAggregateInput
}

export type shrine_donationScalarWhereWithAggregatesInput = {
  AND?: Prisma.shrine_donationScalarWhereWithAggregatesInput | Prisma.shrine_donationScalarWhereWithAggregatesInput[]
  OR?: Prisma.shrine_donationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.shrine_donationScalarWhereWithAggregatesInput | Prisma.shrine_donationScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"shrine_donation"> | number
  amount?: Prisma.IntWithAggregatesFilter<"shrine_donation"> | number
  date?: Prisma.DateTimeWithAggregatesFilter<"shrine_donation"> | Date | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"shrine_donation"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"shrine_donation"> | Date | string
  shrineGoalId?: Prisma.IntNullableWithAggregatesFilter<"shrine_donation"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"shrine_donation"> | number | null
}

export type shrine_donationCreateInput = {
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrine_goal?: Prisma.shrine_goalCreateNestedOneWithoutShrine_donationInput
  user?: Prisma.userCreateNestedOneWithoutShrine_donationInput
}

export type shrine_donationUncheckedCreateInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrineGoalId?: number | null
  userId?: number | null
}

export type shrine_donationUpdateInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrine_goal?: Prisma.shrine_goalUpdateOneWithoutShrine_donationNestedInput
  user?: Prisma.userUpdateOneWithoutShrine_donationNestedInput
}

export type shrine_donationUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrineGoalId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shrine_donationCreateManyInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrineGoalId?: number | null
  userId?: number | null
}

export type shrine_donationUpdateManyMutationInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type shrine_donationUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrineGoalId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shrine_donationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  date?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type shrine_donationAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type shrine_donationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  date?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type shrine_donationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  date?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type shrine_donationSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  shrineGoalId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type Shrine_donationListRelationFilter = {
  every?: Prisma.shrine_donationWhereInput
  some?: Prisma.shrine_donationWhereInput
  none?: Prisma.shrine_donationWhereInput
}

export type shrine_donationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type shrine_donationCreateNestedManyWithoutShrine_goalInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput> | Prisma.shrine_donationCreateWithoutShrine_goalInput[] | Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput | Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput[]
  createMany?: Prisma.shrine_donationCreateManyShrine_goalInputEnvelope
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
}

export type shrine_donationUncheckedCreateNestedManyWithoutShrine_goalInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput> | Prisma.shrine_donationCreateWithoutShrine_goalInput[] | Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput | Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput[]
  createMany?: Prisma.shrine_donationCreateManyShrine_goalInputEnvelope
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
}

export type shrine_donationUpdateManyWithoutShrine_goalNestedInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput> | Prisma.shrine_donationCreateWithoutShrine_goalInput[] | Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput | Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput[]
  upsert?: Prisma.shrine_donationUpsertWithWhereUniqueWithoutShrine_goalInput | Prisma.shrine_donationUpsertWithWhereUniqueWithoutShrine_goalInput[]
  createMany?: Prisma.shrine_donationCreateManyShrine_goalInputEnvelope
  set?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  disconnect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  delete?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  update?: Prisma.shrine_donationUpdateWithWhereUniqueWithoutShrine_goalInput | Prisma.shrine_donationUpdateWithWhereUniqueWithoutShrine_goalInput[]
  updateMany?: Prisma.shrine_donationUpdateManyWithWhereWithoutShrine_goalInput | Prisma.shrine_donationUpdateManyWithWhereWithoutShrine_goalInput[]
  deleteMany?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
}

export type shrine_donationUncheckedUpdateManyWithoutShrine_goalNestedInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput> | Prisma.shrine_donationCreateWithoutShrine_goalInput[] | Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput | Prisma.shrine_donationCreateOrConnectWithoutShrine_goalInput[]
  upsert?: Prisma.shrine_donationUpsertWithWhereUniqueWithoutShrine_goalInput | Prisma.shrine_donationUpsertWithWhereUniqueWithoutShrine_goalInput[]
  createMany?: Prisma.shrine_donationCreateManyShrine_goalInputEnvelope
  set?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  disconnect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  delete?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  update?: Prisma.shrine_donationUpdateWithWhereUniqueWithoutShrine_goalInput | Prisma.shrine_donationUpdateWithWhereUniqueWithoutShrine_goalInput[]
  updateMany?: Prisma.shrine_donationUpdateManyWithWhereWithoutShrine_goalInput | Prisma.shrine_donationUpdateManyWithWhereWithoutShrine_goalInput[]
  deleteMany?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
}

export type shrine_donationCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput> | Prisma.shrine_donationCreateWithoutUserInput[] | Prisma.shrine_donationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutUserInput | Prisma.shrine_donationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.shrine_donationCreateManyUserInputEnvelope
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
}

export type shrine_donationUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput> | Prisma.shrine_donationCreateWithoutUserInput[] | Prisma.shrine_donationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutUserInput | Prisma.shrine_donationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.shrine_donationCreateManyUserInputEnvelope
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
}

export type shrine_donationUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput> | Prisma.shrine_donationCreateWithoutUserInput[] | Prisma.shrine_donationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutUserInput | Prisma.shrine_donationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.shrine_donationUpsertWithWhereUniqueWithoutUserInput | Prisma.shrine_donationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.shrine_donationCreateManyUserInputEnvelope
  set?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  disconnect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  delete?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  update?: Prisma.shrine_donationUpdateWithWhereUniqueWithoutUserInput | Prisma.shrine_donationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.shrine_donationUpdateManyWithWhereWithoutUserInput | Prisma.shrine_donationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
}

export type shrine_donationUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput> | Prisma.shrine_donationCreateWithoutUserInput[] | Prisma.shrine_donationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.shrine_donationCreateOrConnectWithoutUserInput | Prisma.shrine_donationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.shrine_donationUpsertWithWhereUniqueWithoutUserInput | Prisma.shrine_donationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.shrine_donationCreateManyUserInputEnvelope
  set?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  disconnect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  delete?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  connect?: Prisma.shrine_donationWhereUniqueInput | Prisma.shrine_donationWhereUniqueInput[]
  update?: Prisma.shrine_donationUpdateWithWhereUniqueWithoutUserInput | Prisma.shrine_donationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.shrine_donationUpdateManyWithWhereWithoutUserInput | Prisma.shrine_donationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
}

export type shrine_donationCreateWithoutShrine_goalInput = {
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutShrine_donationInput
}

export type shrine_donationUncheckedCreateWithoutShrine_goalInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type shrine_donationCreateOrConnectWithoutShrine_goalInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  create: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput>
}

export type shrine_donationCreateManyShrine_goalInputEnvelope = {
  data: Prisma.shrine_donationCreateManyShrine_goalInput | Prisma.shrine_donationCreateManyShrine_goalInput[]
  skipDuplicates?: boolean
}

export type shrine_donationUpsertWithWhereUniqueWithoutShrine_goalInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  update: Prisma.XOR<Prisma.shrine_donationUpdateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedUpdateWithoutShrine_goalInput>
  create: Prisma.XOR<Prisma.shrine_donationCreateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedCreateWithoutShrine_goalInput>
}

export type shrine_donationUpdateWithWhereUniqueWithoutShrine_goalInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  data: Prisma.XOR<Prisma.shrine_donationUpdateWithoutShrine_goalInput, Prisma.shrine_donationUncheckedUpdateWithoutShrine_goalInput>
}

export type shrine_donationUpdateManyWithWhereWithoutShrine_goalInput = {
  where: Prisma.shrine_donationScalarWhereInput
  data: Prisma.XOR<Prisma.shrine_donationUpdateManyMutationInput, Prisma.shrine_donationUncheckedUpdateManyWithoutShrine_goalInput>
}

export type shrine_donationScalarWhereInput = {
  AND?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
  OR?: Prisma.shrine_donationScalarWhereInput[]
  NOT?: Prisma.shrine_donationScalarWhereInput | Prisma.shrine_donationScalarWhereInput[]
  id?: Prisma.IntFilter<"shrine_donation"> | number
  amount?: Prisma.IntFilter<"shrine_donation"> | number
  date?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"shrine_donation"> | Date | string
  shrineGoalId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
  userId?: Prisma.IntNullableFilter<"shrine_donation"> | number | null
}

export type shrine_donationCreateWithoutUserInput = {
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrine_goal?: Prisma.shrine_goalCreateNestedOneWithoutShrine_donationInput
}

export type shrine_donationUncheckedCreateWithoutUserInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrineGoalId?: number | null
}

export type shrine_donationCreateOrConnectWithoutUserInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  create: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput>
}

export type shrine_donationCreateManyUserInputEnvelope = {
  data: Prisma.shrine_donationCreateManyUserInput | Prisma.shrine_donationCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type shrine_donationUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  update: Prisma.XOR<Prisma.shrine_donationUpdateWithoutUserInput, Prisma.shrine_donationUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.shrine_donationCreateWithoutUserInput, Prisma.shrine_donationUncheckedCreateWithoutUserInput>
}

export type shrine_donationUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.shrine_donationWhereUniqueInput
  data: Prisma.XOR<Prisma.shrine_donationUpdateWithoutUserInput, Prisma.shrine_donationUncheckedUpdateWithoutUserInput>
}

export type shrine_donationUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.shrine_donationScalarWhereInput
  data: Prisma.XOR<Prisma.shrine_donationUpdateManyMutationInput, Prisma.shrine_donationUncheckedUpdateManyWithoutUserInput>
}

export type shrine_donationCreateManyShrine_goalInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type shrine_donationUpdateWithoutShrine_goalInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutShrine_donationNestedInput
}

export type shrine_donationUncheckedUpdateWithoutShrine_goalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shrine_donationUncheckedUpdateManyWithoutShrine_goalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shrine_donationCreateManyUserInput = {
  id?: number
  amount?: number
  date: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  shrineGoalId?: number | null
}

export type shrine_donationUpdateWithoutUserInput = {
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrine_goal?: Prisma.shrine_goalUpdateOneWithoutShrine_donationNestedInput
}

export type shrine_donationUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrineGoalId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type shrine_donationUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  date?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  shrineGoalId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type shrine_donationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  amount?: boolean
  date?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shrineGoalId?: boolean
  userId?: boolean
  shrine_goal?: boolean | Prisma.shrine_donation$shrine_goalArgs<ExtArgs>
  user?: boolean | Prisma.shrine_donation$userArgs<ExtArgs>
}, ExtArgs["result"]["shrine_donation"]>



export type shrine_donationSelectScalar = {
  id?: boolean
  amount?: boolean
  date?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  shrineGoalId?: boolean
  userId?: boolean
}

export type shrine_donationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "amount" | "date" | "createdAt" | "updatedAt" | "shrineGoalId" | "userId", ExtArgs["result"]["shrine_donation"]>
export type shrine_donationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  shrine_goal?: boolean | Prisma.shrine_donation$shrine_goalArgs<ExtArgs>
  user?: boolean | Prisma.shrine_donation$userArgs<ExtArgs>
}

export type $shrine_donationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "shrine_donation"
  objects: {
    shrine_goal: Prisma.$shrine_goalPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    amount: number
    date: Date
    createdAt: Date
    updatedAt: Date
    shrineGoalId: number | null
    userId: number | null
  }, ExtArgs["result"]["shrine_donation"]>
  composites: {}
}

export type shrine_donationGetPayload<S extends boolean | null | undefined | shrine_donationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload, S>

export type shrine_donationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<shrine_donationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Shrine_donationCountAggregateInputType | true
  }

export interface shrine_donationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['shrine_donation'], meta: { name: 'shrine_donation' } }
  /**
   * Find zero or one Shrine_donation that matches the filter.
   * @param {shrine_donationFindUniqueArgs} args - Arguments to find a Shrine_donation
   * @example
   * // Get one Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends shrine_donationFindUniqueArgs>(args: Prisma.SelectSubset<T, shrine_donationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Shrine_donation that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {shrine_donationFindUniqueOrThrowArgs} args - Arguments to find a Shrine_donation
   * @example
   * // Get one Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends shrine_donationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, shrine_donationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shrine_donation that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationFindFirstArgs} args - Arguments to find a Shrine_donation
   * @example
   * // Get one Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends shrine_donationFindFirstArgs>(args?: Prisma.SelectSubset<T, shrine_donationFindFirstArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shrine_donation that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationFindFirstOrThrowArgs} args - Arguments to find a Shrine_donation
   * @example
   * // Get one Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends shrine_donationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, shrine_donationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Shrine_donations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Shrine_donations
   * const shrine_donations = await prisma.shrine_donation.findMany()
   * 
   * // Get first 10 Shrine_donations
   * const shrine_donations = await prisma.shrine_donation.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const shrine_donationWithIdOnly = await prisma.shrine_donation.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends shrine_donationFindManyArgs>(args?: Prisma.SelectSubset<T, shrine_donationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Shrine_donation.
   * @param {shrine_donationCreateArgs} args - Arguments to create a Shrine_donation.
   * @example
   * // Create one Shrine_donation
   * const Shrine_donation = await prisma.shrine_donation.create({
   *   data: {
   *     // ... data to create a Shrine_donation
   *   }
   * })
   * 
   */
  create<T extends shrine_donationCreateArgs>(args: Prisma.SelectSubset<T, shrine_donationCreateArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Shrine_donations.
   * @param {shrine_donationCreateManyArgs} args - Arguments to create many Shrine_donations.
   * @example
   * // Create many Shrine_donations
   * const shrine_donation = await prisma.shrine_donation.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends shrine_donationCreateManyArgs>(args?: Prisma.SelectSubset<T, shrine_donationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Shrine_donation.
   * @param {shrine_donationDeleteArgs} args - Arguments to delete one Shrine_donation.
   * @example
   * // Delete one Shrine_donation
   * const Shrine_donation = await prisma.shrine_donation.delete({
   *   where: {
   *     // ... filter to delete one Shrine_donation
   *   }
   * })
   * 
   */
  delete<T extends shrine_donationDeleteArgs>(args: Prisma.SelectSubset<T, shrine_donationDeleteArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Shrine_donation.
   * @param {shrine_donationUpdateArgs} args - Arguments to update one Shrine_donation.
   * @example
   * // Update one Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends shrine_donationUpdateArgs>(args: Prisma.SelectSubset<T, shrine_donationUpdateArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Shrine_donations.
   * @param {shrine_donationDeleteManyArgs} args - Arguments to filter Shrine_donations to delete.
   * @example
   * // Delete a few Shrine_donations
   * const { count } = await prisma.shrine_donation.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends shrine_donationDeleteManyArgs>(args?: Prisma.SelectSubset<T, shrine_donationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Shrine_donations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Shrine_donations
   * const shrine_donation = await prisma.shrine_donation.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends shrine_donationUpdateManyArgs>(args: Prisma.SelectSubset<T, shrine_donationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Shrine_donation.
   * @param {shrine_donationUpsertArgs} args - Arguments to update or create a Shrine_donation.
   * @example
   * // Update or create a Shrine_donation
   * const shrine_donation = await prisma.shrine_donation.upsert({
   *   create: {
   *     // ... data to create a Shrine_donation
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Shrine_donation we want to update
   *   }
   * })
   */
  upsert<T extends shrine_donationUpsertArgs>(args: Prisma.SelectSubset<T, shrine_donationUpsertArgs<ExtArgs>>): Prisma.Prisma__shrine_donationClient<runtime.Types.Result.GetResult<Prisma.$shrine_donationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Shrine_donations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationCountArgs} args - Arguments to filter Shrine_donations to count.
   * @example
   * // Count the number of Shrine_donations
   * const count = await prisma.shrine_donation.count({
   *   where: {
   *     // ... the filter for the Shrine_donations we want to count
   *   }
   * })
  **/
  count<T extends shrine_donationCountArgs>(
    args?: Prisma.Subset<T, shrine_donationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Shrine_donationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Shrine_donation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Shrine_donationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Shrine_donationAggregateArgs>(args: Prisma.Subset<T, Shrine_donationAggregateArgs>): Prisma.PrismaPromise<GetShrine_donationAggregateType<T>>

  /**
   * Group by Shrine_donation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shrine_donationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends shrine_donationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: shrine_donationGroupByArgs['orderBy'] }
      : { orderBy?: shrine_donationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, shrine_donationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetShrine_donationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the shrine_donation model
 */
readonly fields: shrine_donationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for shrine_donation.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__shrine_donationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  shrine_goal<T extends Prisma.shrine_donation$shrine_goalArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shrine_donation$shrine_goalArgs<ExtArgs>>): Prisma.Prisma__shrine_goalClient<runtime.Types.Result.GetResult<Prisma.$shrine_goalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.shrine_donation$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shrine_donation$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the shrine_donation model
 */
export interface shrine_donationFieldRefs {
  readonly id: Prisma.FieldRef<"shrine_donation", 'Int'>
  readonly amount: Prisma.FieldRef<"shrine_donation", 'Int'>
  readonly date: Prisma.FieldRef<"shrine_donation", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"shrine_donation", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"shrine_donation", 'DateTime'>
  readonly shrineGoalId: Prisma.FieldRef<"shrine_donation", 'Int'>
  readonly userId: Prisma.FieldRef<"shrine_donation", 'Int'>
}
    

// Custom InputTypes
/**
 * shrine_donation findUnique
 */
export type shrine_donationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter, which shrine_donation to fetch.
   */
  where: Prisma.shrine_donationWhereUniqueInput
}

/**
 * shrine_donation findUniqueOrThrow
 */
export type shrine_donationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter, which shrine_donation to fetch.
   */
  where: Prisma.shrine_donationWhereUniqueInput
}

/**
 * shrine_donation findFirst
 */
export type shrine_donationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter, which shrine_donation to fetch.
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_donations to fetch.
   */
  orderBy?: Prisma.shrine_donationOrderByWithRelationInput | Prisma.shrine_donationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shrine_donations.
   */
  cursor?: Prisma.shrine_donationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shrine_donations.
   */
  distinct?: Prisma.Shrine_donationScalarFieldEnum | Prisma.Shrine_donationScalarFieldEnum[]
}

/**
 * shrine_donation findFirstOrThrow
 */
export type shrine_donationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter, which shrine_donation to fetch.
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_donations to fetch.
   */
  orderBy?: Prisma.shrine_donationOrderByWithRelationInput | Prisma.shrine_donationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shrine_donations.
   */
  cursor?: Prisma.shrine_donationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shrine_donations.
   */
  distinct?: Prisma.Shrine_donationScalarFieldEnum | Prisma.Shrine_donationScalarFieldEnum[]
}

/**
 * shrine_donation findMany
 */
export type shrine_donationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter, which shrine_donations to fetch.
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shrine_donations to fetch.
   */
  orderBy?: Prisma.shrine_donationOrderByWithRelationInput | Prisma.shrine_donationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing shrine_donations.
   */
  cursor?: Prisma.shrine_donationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shrine_donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shrine_donations.
   */
  skip?: number
  distinct?: Prisma.Shrine_donationScalarFieldEnum | Prisma.Shrine_donationScalarFieldEnum[]
}

/**
 * shrine_donation create
 */
export type shrine_donationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * The data needed to create a shrine_donation.
   */
  data: Prisma.XOR<Prisma.shrine_donationCreateInput, Prisma.shrine_donationUncheckedCreateInput>
}

/**
 * shrine_donation createMany
 */
export type shrine_donationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many shrine_donations.
   */
  data: Prisma.shrine_donationCreateManyInput | Prisma.shrine_donationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * shrine_donation update
 */
export type shrine_donationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * The data needed to update a shrine_donation.
   */
  data: Prisma.XOR<Prisma.shrine_donationUpdateInput, Prisma.shrine_donationUncheckedUpdateInput>
  /**
   * Choose, which shrine_donation to update.
   */
  where: Prisma.shrine_donationWhereUniqueInput
}

/**
 * shrine_donation updateMany
 */
export type shrine_donationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update shrine_donations.
   */
  data: Prisma.XOR<Prisma.shrine_donationUpdateManyMutationInput, Prisma.shrine_donationUncheckedUpdateManyInput>
  /**
   * Filter which shrine_donations to update
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * Limit how many shrine_donations to update.
   */
  limit?: number
}

/**
 * shrine_donation upsert
 */
export type shrine_donationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * The filter to search for the shrine_donation to update in case it exists.
   */
  where: Prisma.shrine_donationWhereUniqueInput
  /**
   * In case the shrine_donation found by the `where` argument doesn't exist, create a new shrine_donation with this data.
   */
  create: Prisma.XOR<Prisma.shrine_donationCreateInput, Prisma.shrine_donationUncheckedCreateInput>
  /**
   * In case the shrine_donation was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.shrine_donationUpdateInput, Prisma.shrine_donationUncheckedUpdateInput>
}

/**
 * shrine_donation delete
 */
export type shrine_donationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
  /**
   * Filter which shrine_donation to delete.
   */
  where: Prisma.shrine_donationWhereUniqueInput
}

/**
 * shrine_donation deleteMany
 */
export type shrine_donationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shrine_donations to delete
   */
  where?: Prisma.shrine_donationWhereInput
  /**
   * Limit how many shrine_donations to delete.
   */
  limit?: number
}

/**
 * shrine_donation.shrine_goal
 */
export type shrine_donation$shrine_goalArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_goal
   */
  select?: Prisma.shrine_goalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_goal
   */
  omit?: Prisma.shrine_goalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_goalInclude<ExtArgs> | null
  where?: Prisma.shrine_goalWhereInput
}

/**
 * shrine_donation.user
 */
export type shrine_donation$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * shrine_donation without action
 */
export type shrine_donationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shrine_donation
   */
  select?: Prisma.shrine_donationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shrine_donation
   */
  omit?: Prisma.shrine_donationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shrine_donationInclude<ExtArgs> | null
}
