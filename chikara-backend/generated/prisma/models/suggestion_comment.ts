
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `suggestion_comment` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model suggestion_comment
 * 
 */
export type suggestion_commentModel = runtime.Types.Result.DefaultSelection<Prisma.$suggestion_commentPayload>

export type AggregateSuggestion_comment = {
  _count: Suggestion_commentCountAggregateOutputType | null
  _avg: Suggestion_commentAvgAggregateOutputType | null
  _sum: Suggestion_commentSumAggregateOutputType | null
  _min: Suggestion_commentMinAggregateOutputType | null
  _max: Suggestion_commentMaxAggregateOutputType | null
}

export type Suggestion_commentAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_commentSumAggregateOutputType = {
  id: number | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_commentMinAggregateOutputType = {
  id: number | null
  message: string | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_commentMaxAggregateOutputType = {
  id: number | null
  message: string | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  suggestionId: number | null
}

export type Suggestion_commentCountAggregateOutputType = {
  id: number
  message: number
  createdAt: number
  updatedAt: number
  userId: number
  suggestionId: number
  _all: number
}


export type Suggestion_commentAvgAggregateInputType = {
  id?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_commentSumAggregateInputType = {
  id?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_commentMinAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_commentMaxAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
}

export type Suggestion_commentCountAggregateInputType = {
  id?: true
  message?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  suggestionId?: true
  _all?: true
}

export type Suggestion_commentAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestion_comment to aggregate.
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_comments to fetch.
   */
  orderBy?: Prisma.suggestion_commentOrderByWithRelationInput | Prisma.suggestion_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.suggestion_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned suggestion_comments
  **/
  _count?: true | Suggestion_commentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Suggestion_commentAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Suggestion_commentSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Suggestion_commentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Suggestion_commentMaxAggregateInputType
}

export type GetSuggestion_commentAggregateType<T extends Suggestion_commentAggregateArgs> = {
      [P in keyof T & keyof AggregateSuggestion_comment]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSuggestion_comment[P]>
    : Prisma.GetScalarType<T[P], AggregateSuggestion_comment[P]>
}




export type suggestion_commentGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.suggestion_commentWhereInput
  orderBy?: Prisma.suggestion_commentOrderByWithAggregationInput | Prisma.suggestion_commentOrderByWithAggregationInput[]
  by: Prisma.Suggestion_commentScalarFieldEnum[] | Prisma.Suggestion_commentScalarFieldEnum
  having?: Prisma.suggestion_commentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Suggestion_commentCountAggregateInputType | true
  _avg?: Suggestion_commentAvgAggregateInputType
  _sum?: Suggestion_commentSumAggregateInputType
  _min?: Suggestion_commentMinAggregateInputType
  _max?: Suggestion_commentMaxAggregateInputType
}

export type Suggestion_commentGroupByOutputType = {
  id: number
  message: string
  createdAt: Date
  updatedAt: Date
  userId: number | null
  suggestionId: number | null
  _count: Suggestion_commentCountAggregateOutputType | null
  _avg: Suggestion_commentAvgAggregateOutputType | null
  _sum: Suggestion_commentSumAggregateOutputType | null
  _min: Suggestion_commentMinAggregateOutputType | null
  _max: Suggestion_commentMaxAggregateOutputType | null
}

type GetSuggestion_commentGroupByPayload<T extends suggestion_commentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Suggestion_commentGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Suggestion_commentGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Suggestion_commentGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Suggestion_commentGroupByOutputType[P]>
      }
    >
  >



export type suggestion_commentWhereInput = {
  AND?: Prisma.suggestion_commentWhereInput | Prisma.suggestion_commentWhereInput[]
  OR?: Prisma.suggestion_commentWhereInput[]
  NOT?: Prisma.suggestion_commentWhereInput | Prisma.suggestion_commentWhereInput[]
  id?: Prisma.IntFilter<"suggestion_comment"> | number
  message?: Prisma.StringFilter<"suggestion_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion?: Prisma.XOR<Prisma.SuggestionNullableScalarRelationFilter, Prisma.suggestionWhereInput> | null
}

export type suggestion_commentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  suggestion?: Prisma.suggestionOrderByWithRelationInput
  _relevance?: Prisma.suggestion_commentOrderByRelevanceInput
}

export type suggestion_commentWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.suggestion_commentWhereInput | Prisma.suggestion_commentWhereInput[]
  OR?: Prisma.suggestion_commentWhereInput[]
  NOT?: Prisma.suggestion_commentWhereInput | Prisma.suggestion_commentWhereInput[]
  message?: Prisma.StringFilter<"suggestion_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  suggestion?: Prisma.XOR<Prisma.SuggestionNullableScalarRelationFilter, Prisma.suggestionWhereInput> | null
}, "id">

export type suggestion_commentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  suggestionId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.suggestion_commentCountOrderByAggregateInput
  _avg?: Prisma.suggestion_commentAvgOrderByAggregateInput
  _max?: Prisma.suggestion_commentMaxOrderByAggregateInput
  _min?: Prisma.suggestion_commentMinOrderByAggregateInput
  _sum?: Prisma.suggestion_commentSumOrderByAggregateInput
}

export type suggestion_commentScalarWhereWithAggregatesInput = {
  AND?: Prisma.suggestion_commentScalarWhereWithAggregatesInput | Prisma.suggestion_commentScalarWhereWithAggregatesInput[]
  OR?: Prisma.suggestion_commentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.suggestion_commentScalarWhereWithAggregatesInput | Prisma.suggestion_commentScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"suggestion_comment"> | number
  message?: Prisma.StringWithAggregatesFilter<"suggestion_comment"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion_comment"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"suggestion_comment"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"suggestion_comment"> | number | null
  suggestionId?: Prisma.IntNullableWithAggregatesFilter<"suggestion_comment"> | number | null
}

export type suggestion_commentCreateInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestion_commentInput
  suggestion?: Prisma.suggestionCreateNestedOneWithoutSuggestion_commentInput
}

export type suggestion_commentUncheckedCreateInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestionId?: number | null
}

export type suggestion_commentUpdateInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestion_commentNestedInput
  suggestion?: Prisma.suggestionUpdateOneWithoutSuggestion_commentNestedInput
}

export type suggestion_commentUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_commentCreateManyInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  suggestionId?: number | null
}

export type suggestion_commentUpdateManyMutationInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type suggestion_commentUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Suggestion_commentListRelationFilter = {
  every?: Prisma.suggestion_commentWhereInput
  some?: Prisma.suggestion_commentWhereInput
  none?: Prisma.suggestion_commentWhereInput
}

export type suggestion_commentOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type suggestion_commentOrderByRelevanceInput = {
  fields: Prisma.suggestion_commentOrderByRelevanceFieldEnum | Prisma.suggestion_commentOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type suggestion_commentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_commentAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_commentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_commentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_commentSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  suggestionId?: Prisma.SortOrder
}

export type suggestion_commentCreateNestedManyWithoutSuggestionInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_commentCreateWithoutSuggestionInput[] | Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_commentCreateManySuggestionInputEnvelope
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
}

export type suggestion_commentUncheckedCreateNestedManyWithoutSuggestionInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_commentCreateWithoutSuggestionInput[] | Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_commentCreateManySuggestionInputEnvelope
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
}

export type suggestion_commentUpdateManyWithoutSuggestionNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_commentCreateWithoutSuggestionInput[] | Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput[]
  upsert?: Prisma.suggestion_commentUpsertWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_commentUpsertWithWhereUniqueWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_commentCreateManySuggestionInputEnvelope
  set?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  disconnect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  delete?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  update?: Prisma.suggestion_commentUpdateWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_commentUpdateWithWhereUniqueWithoutSuggestionInput[]
  updateMany?: Prisma.suggestion_commentUpdateManyWithWhereWithoutSuggestionInput | Prisma.suggestion_commentUpdateManyWithWhereWithoutSuggestionInput[]
  deleteMany?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
}

export type suggestion_commentUncheckedUpdateManyWithoutSuggestionNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput> | Prisma.suggestion_commentCreateWithoutSuggestionInput[] | Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput | Prisma.suggestion_commentCreateOrConnectWithoutSuggestionInput[]
  upsert?: Prisma.suggestion_commentUpsertWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_commentUpsertWithWhereUniqueWithoutSuggestionInput[]
  createMany?: Prisma.suggestion_commentCreateManySuggestionInputEnvelope
  set?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  disconnect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  delete?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  update?: Prisma.suggestion_commentUpdateWithWhereUniqueWithoutSuggestionInput | Prisma.suggestion_commentUpdateWithWhereUniqueWithoutSuggestionInput[]
  updateMany?: Prisma.suggestion_commentUpdateManyWithWhereWithoutSuggestionInput | Prisma.suggestion_commentUpdateManyWithWhereWithoutSuggestionInput[]
  deleteMany?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
}

export type suggestion_commentCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput> | Prisma.suggestion_commentCreateWithoutUserInput[] | Prisma.suggestion_commentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutUserInput | Prisma.suggestion_commentCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestion_commentCreateManyUserInputEnvelope
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
}

export type suggestion_commentUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput> | Prisma.suggestion_commentCreateWithoutUserInput[] | Prisma.suggestion_commentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutUserInput | Prisma.suggestion_commentCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.suggestion_commentCreateManyUserInputEnvelope
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
}

export type suggestion_commentUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput> | Prisma.suggestion_commentCreateWithoutUserInput[] | Prisma.suggestion_commentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutUserInput | Prisma.suggestion_commentCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestion_commentUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestion_commentUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestion_commentCreateManyUserInputEnvelope
  set?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  disconnect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  delete?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  update?: Prisma.suggestion_commentUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestion_commentUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestion_commentUpdateManyWithWhereWithoutUserInput | Prisma.suggestion_commentUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
}

export type suggestion_commentUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput> | Prisma.suggestion_commentCreateWithoutUserInput[] | Prisma.suggestion_commentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.suggestion_commentCreateOrConnectWithoutUserInput | Prisma.suggestion_commentCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.suggestion_commentUpsertWithWhereUniqueWithoutUserInput | Prisma.suggestion_commentUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.suggestion_commentCreateManyUserInputEnvelope
  set?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  disconnect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  delete?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  connect?: Prisma.suggestion_commentWhereUniqueInput | Prisma.suggestion_commentWhereUniqueInput[]
  update?: Prisma.suggestion_commentUpdateWithWhereUniqueWithoutUserInput | Prisma.suggestion_commentUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.suggestion_commentUpdateManyWithWhereWithoutUserInput | Prisma.suggestion_commentUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
}

export type suggestion_commentCreateWithoutSuggestionInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutSuggestion_commentInput
}

export type suggestion_commentUncheckedCreateWithoutSuggestionInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type suggestion_commentCreateOrConnectWithoutSuggestionInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput>
}

export type suggestion_commentCreateManySuggestionInputEnvelope = {
  data: Prisma.suggestion_commentCreateManySuggestionInput | Prisma.suggestion_commentCreateManySuggestionInput[]
  skipDuplicates?: boolean
}

export type suggestion_commentUpsertWithWhereUniqueWithoutSuggestionInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  update: Prisma.XOR<Prisma.suggestion_commentUpdateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedUpdateWithoutSuggestionInput>
  create: Prisma.XOR<Prisma.suggestion_commentCreateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedCreateWithoutSuggestionInput>
}

export type suggestion_commentUpdateWithWhereUniqueWithoutSuggestionInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  data: Prisma.XOR<Prisma.suggestion_commentUpdateWithoutSuggestionInput, Prisma.suggestion_commentUncheckedUpdateWithoutSuggestionInput>
}

export type suggestion_commentUpdateManyWithWhereWithoutSuggestionInput = {
  where: Prisma.suggestion_commentScalarWhereInput
  data: Prisma.XOR<Prisma.suggestion_commentUpdateManyMutationInput, Prisma.suggestion_commentUncheckedUpdateManyWithoutSuggestionInput>
}

export type suggestion_commentScalarWhereInput = {
  AND?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
  OR?: Prisma.suggestion_commentScalarWhereInput[]
  NOT?: Prisma.suggestion_commentScalarWhereInput | Prisma.suggestion_commentScalarWhereInput[]
  id?: Prisma.IntFilter<"suggestion_comment"> | number
  message?: Prisma.StringFilter<"suggestion_comment"> | string
  createdAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"suggestion_comment"> | Date | string
  userId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
  suggestionId?: Prisma.IntNullableFilter<"suggestion_comment"> | number | null
}

export type suggestion_commentCreateWithoutUserInput = {
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestion?: Prisma.suggestionCreateNestedOneWithoutSuggestion_commentInput
}

export type suggestion_commentUncheckedCreateWithoutUserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestionId?: number | null
}

export type suggestion_commentCreateOrConnectWithoutUserInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  create: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput>
}

export type suggestion_commentCreateManyUserInputEnvelope = {
  data: Prisma.suggestion_commentCreateManyUserInput | Prisma.suggestion_commentCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type suggestion_commentUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  update: Prisma.XOR<Prisma.suggestion_commentUpdateWithoutUserInput, Prisma.suggestion_commentUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.suggestion_commentCreateWithoutUserInput, Prisma.suggestion_commentUncheckedCreateWithoutUserInput>
}

export type suggestion_commentUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.suggestion_commentWhereUniqueInput
  data: Prisma.XOR<Prisma.suggestion_commentUpdateWithoutUserInput, Prisma.suggestion_commentUncheckedUpdateWithoutUserInput>
}

export type suggestion_commentUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.suggestion_commentScalarWhereInput
  data: Prisma.XOR<Prisma.suggestion_commentUpdateManyMutationInput, Prisma.suggestion_commentUncheckedUpdateManyWithoutUserInput>
}

export type suggestion_commentCreateManySuggestionInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type suggestion_commentUpdateWithoutSuggestionInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutSuggestion_commentNestedInput
}

export type suggestion_commentUncheckedUpdateWithoutSuggestionInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_commentUncheckedUpdateManyWithoutSuggestionInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_commentCreateManyUserInput = {
  id?: number
  message: string
  createdAt?: Date | string
  updatedAt?: Date | string
  suggestionId?: number | null
}

export type suggestion_commentUpdateWithoutUserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestion?: Prisma.suggestionUpdateOneWithoutSuggestion_commentNestedInput
}

export type suggestion_commentUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type suggestion_commentUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  suggestionId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type suggestion_commentSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  message?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  suggestionId?: boolean
  user?: boolean | Prisma.suggestion_comment$userArgs<ExtArgs>
  suggestion?: boolean | Prisma.suggestion_comment$suggestionArgs<ExtArgs>
}, ExtArgs["result"]["suggestion_comment"]>



export type suggestion_commentSelectScalar = {
  id?: boolean
  message?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  suggestionId?: boolean
}

export type suggestion_commentOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "message" | "createdAt" | "updatedAt" | "userId" | "suggestionId", ExtArgs["result"]["suggestion_comment"]>
export type suggestion_commentInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.suggestion_comment$userArgs<ExtArgs>
  suggestion?: boolean | Prisma.suggestion_comment$suggestionArgs<ExtArgs>
}

export type $suggestion_commentPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "suggestion_comment"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    suggestion: Prisma.$suggestionPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    message: string
    createdAt: Date
    updatedAt: Date
    userId: number | null
    suggestionId: number | null
  }, ExtArgs["result"]["suggestion_comment"]>
  composites: {}
}

export type suggestion_commentGetPayload<S extends boolean | null | undefined | suggestion_commentDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload, S>

export type suggestion_commentCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<suggestion_commentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Suggestion_commentCountAggregateInputType | true
  }

export interface suggestion_commentDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['suggestion_comment'], meta: { name: 'suggestion_comment' } }
  /**
   * Find zero or one Suggestion_comment that matches the filter.
   * @param {suggestion_commentFindUniqueArgs} args - Arguments to find a Suggestion_comment
   * @example
   * // Get one Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends suggestion_commentFindUniqueArgs>(args: Prisma.SelectSubset<T, suggestion_commentFindUniqueArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Suggestion_comment that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {suggestion_commentFindUniqueOrThrowArgs} args - Arguments to find a Suggestion_comment
   * @example
   * // Get one Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends suggestion_commentFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, suggestion_commentFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion_comment that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentFindFirstArgs} args - Arguments to find a Suggestion_comment
   * @example
   * // Get one Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends suggestion_commentFindFirstArgs>(args?: Prisma.SelectSubset<T, suggestion_commentFindFirstArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Suggestion_comment that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentFindFirstOrThrowArgs} args - Arguments to find a Suggestion_comment
   * @example
   * // Get one Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends suggestion_commentFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, suggestion_commentFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Suggestion_comments that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Suggestion_comments
   * const suggestion_comments = await prisma.suggestion_comment.findMany()
   * 
   * // Get first 10 Suggestion_comments
   * const suggestion_comments = await prisma.suggestion_comment.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const suggestion_commentWithIdOnly = await prisma.suggestion_comment.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends suggestion_commentFindManyArgs>(args?: Prisma.SelectSubset<T, suggestion_commentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Suggestion_comment.
   * @param {suggestion_commentCreateArgs} args - Arguments to create a Suggestion_comment.
   * @example
   * // Create one Suggestion_comment
   * const Suggestion_comment = await prisma.suggestion_comment.create({
   *   data: {
   *     // ... data to create a Suggestion_comment
   *   }
   * })
   * 
   */
  create<T extends suggestion_commentCreateArgs>(args: Prisma.SelectSubset<T, suggestion_commentCreateArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Suggestion_comments.
   * @param {suggestion_commentCreateManyArgs} args - Arguments to create many Suggestion_comments.
   * @example
   * // Create many Suggestion_comments
   * const suggestion_comment = await prisma.suggestion_comment.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends suggestion_commentCreateManyArgs>(args?: Prisma.SelectSubset<T, suggestion_commentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Suggestion_comment.
   * @param {suggestion_commentDeleteArgs} args - Arguments to delete one Suggestion_comment.
   * @example
   * // Delete one Suggestion_comment
   * const Suggestion_comment = await prisma.suggestion_comment.delete({
   *   where: {
   *     // ... filter to delete one Suggestion_comment
   *   }
   * })
   * 
   */
  delete<T extends suggestion_commentDeleteArgs>(args: Prisma.SelectSubset<T, suggestion_commentDeleteArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Suggestion_comment.
   * @param {suggestion_commentUpdateArgs} args - Arguments to update one Suggestion_comment.
   * @example
   * // Update one Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends suggestion_commentUpdateArgs>(args: Prisma.SelectSubset<T, suggestion_commentUpdateArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Suggestion_comments.
   * @param {suggestion_commentDeleteManyArgs} args - Arguments to filter Suggestion_comments to delete.
   * @example
   * // Delete a few Suggestion_comments
   * const { count } = await prisma.suggestion_comment.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends suggestion_commentDeleteManyArgs>(args?: Prisma.SelectSubset<T, suggestion_commentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Suggestion_comments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Suggestion_comments
   * const suggestion_comment = await prisma.suggestion_comment.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends suggestion_commentUpdateManyArgs>(args: Prisma.SelectSubset<T, suggestion_commentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Suggestion_comment.
   * @param {suggestion_commentUpsertArgs} args - Arguments to update or create a Suggestion_comment.
   * @example
   * // Update or create a Suggestion_comment
   * const suggestion_comment = await prisma.suggestion_comment.upsert({
   *   create: {
   *     // ... data to create a Suggestion_comment
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Suggestion_comment we want to update
   *   }
   * })
   */
  upsert<T extends suggestion_commentUpsertArgs>(args: Prisma.SelectSubset<T, suggestion_commentUpsertArgs<ExtArgs>>): Prisma.Prisma__suggestion_commentClient<runtime.Types.Result.GetResult<Prisma.$suggestion_commentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Suggestion_comments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentCountArgs} args - Arguments to filter Suggestion_comments to count.
   * @example
   * // Count the number of Suggestion_comments
   * const count = await prisma.suggestion_comment.count({
   *   where: {
   *     // ... the filter for the Suggestion_comments we want to count
   *   }
   * })
  **/
  count<T extends suggestion_commentCountArgs>(
    args?: Prisma.Subset<T, suggestion_commentCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Suggestion_commentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Suggestion_comment.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Suggestion_commentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Suggestion_commentAggregateArgs>(args: Prisma.Subset<T, Suggestion_commentAggregateArgs>): Prisma.PrismaPromise<GetSuggestion_commentAggregateType<T>>

  /**
   * Group by Suggestion_comment.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {suggestion_commentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends suggestion_commentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: suggestion_commentGroupByArgs['orderBy'] }
      : { orderBy?: suggestion_commentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, suggestion_commentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSuggestion_commentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the suggestion_comment model
 */
readonly fields: suggestion_commentFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for suggestion_comment.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__suggestion_commentClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.suggestion_comment$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion_comment$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  suggestion<T extends Prisma.suggestion_comment$suggestionArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.suggestion_comment$suggestionArgs<ExtArgs>>): Prisma.Prisma__suggestionClient<runtime.Types.Result.GetResult<Prisma.$suggestionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the suggestion_comment model
 */
export interface suggestion_commentFieldRefs {
  readonly id: Prisma.FieldRef<"suggestion_comment", 'Int'>
  readonly message: Prisma.FieldRef<"suggestion_comment", 'String'>
  readonly createdAt: Prisma.FieldRef<"suggestion_comment", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"suggestion_comment", 'DateTime'>
  readonly userId: Prisma.FieldRef<"suggestion_comment", 'Int'>
  readonly suggestionId: Prisma.FieldRef<"suggestion_comment", 'Int'>
}
    

// Custom InputTypes
/**
 * suggestion_comment findUnique
 */
export type suggestion_commentFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_comment to fetch.
   */
  where: Prisma.suggestion_commentWhereUniqueInput
}

/**
 * suggestion_comment findUniqueOrThrow
 */
export type suggestion_commentFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_comment to fetch.
   */
  where: Prisma.suggestion_commentWhereUniqueInput
}

/**
 * suggestion_comment findFirst
 */
export type suggestion_commentFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_comment to fetch.
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_comments to fetch.
   */
  orderBy?: Prisma.suggestion_commentOrderByWithRelationInput | Prisma.suggestion_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestion_comments.
   */
  cursor?: Prisma.suggestion_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestion_comments.
   */
  distinct?: Prisma.Suggestion_commentScalarFieldEnum | Prisma.Suggestion_commentScalarFieldEnum[]
}

/**
 * suggestion_comment findFirstOrThrow
 */
export type suggestion_commentFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_comment to fetch.
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_comments to fetch.
   */
  orderBy?: Prisma.suggestion_commentOrderByWithRelationInput | Prisma.suggestion_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for suggestion_comments.
   */
  cursor?: Prisma.suggestion_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_comments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of suggestion_comments.
   */
  distinct?: Prisma.Suggestion_commentScalarFieldEnum | Prisma.Suggestion_commentScalarFieldEnum[]
}

/**
 * suggestion_comment findMany
 */
export type suggestion_commentFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter, which suggestion_comments to fetch.
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of suggestion_comments to fetch.
   */
  orderBy?: Prisma.suggestion_commentOrderByWithRelationInput | Prisma.suggestion_commentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing suggestion_comments.
   */
  cursor?: Prisma.suggestion_commentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` suggestion_comments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` suggestion_comments.
   */
  skip?: number
  distinct?: Prisma.Suggestion_commentScalarFieldEnum | Prisma.Suggestion_commentScalarFieldEnum[]
}

/**
 * suggestion_comment create
 */
export type suggestion_commentCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * The data needed to create a suggestion_comment.
   */
  data: Prisma.XOR<Prisma.suggestion_commentCreateInput, Prisma.suggestion_commentUncheckedCreateInput>
}

/**
 * suggestion_comment createMany
 */
export type suggestion_commentCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many suggestion_comments.
   */
  data: Prisma.suggestion_commentCreateManyInput | Prisma.suggestion_commentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * suggestion_comment update
 */
export type suggestion_commentUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * The data needed to update a suggestion_comment.
   */
  data: Prisma.XOR<Prisma.suggestion_commentUpdateInput, Prisma.suggestion_commentUncheckedUpdateInput>
  /**
   * Choose, which suggestion_comment to update.
   */
  where: Prisma.suggestion_commentWhereUniqueInput
}

/**
 * suggestion_comment updateMany
 */
export type suggestion_commentUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update suggestion_comments.
   */
  data: Prisma.XOR<Prisma.suggestion_commentUpdateManyMutationInput, Prisma.suggestion_commentUncheckedUpdateManyInput>
  /**
   * Filter which suggestion_comments to update
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * Limit how many suggestion_comments to update.
   */
  limit?: number
}

/**
 * suggestion_comment upsert
 */
export type suggestion_commentUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * The filter to search for the suggestion_comment to update in case it exists.
   */
  where: Prisma.suggestion_commentWhereUniqueInput
  /**
   * In case the suggestion_comment found by the `where` argument doesn't exist, create a new suggestion_comment with this data.
   */
  create: Prisma.XOR<Prisma.suggestion_commentCreateInput, Prisma.suggestion_commentUncheckedCreateInput>
  /**
   * In case the suggestion_comment was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.suggestion_commentUpdateInput, Prisma.suggestion_commentUncheckedUpdateInput>
}

/**
 * suggestion_comment delete
 */
export type suggestion_commentDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
  /**
   * Filter which suggestion_comment to delete.
   */
  where: Prisma.suggestion_commentWhereUniqueInput
}

/**
 * suggestion_comment deleteMany
 */
export type suggestion_commentDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which suggestion_comments to delete
   */
  where?: Prisma.suggestion_commentWhereInput
  /**
   * Limit how many suggestion_comments to delete.
   */
  limit?: number
}

/**
 * suggestion_comment.user
 */
export type suggestion_comment$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * suggestion_comment.suggestion
 */
export type suggestion_comment$suggestionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion
   */
  select?: Prisma.suggestionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion
   */
  omit?: Prisma.suggestionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestionInclude<ExtArgs> | null
  where?: Prisma.suggestionWhereInput
}

/**
 * suggestion_comment without action
 */
export type suggestion_commentDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the suggestion_comment
   */
  select?: Prisma.suggestion_commentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the suggestion_comment
   */
  omit?: Prisma.suggestion_commentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.suggestion_commentInclude<ExtArgs> | null
}
