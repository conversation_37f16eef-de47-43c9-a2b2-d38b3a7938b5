
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `battle_log` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model battle_log
 * 
 */
export type battle_logModel = runtime.Types.Result.DefaultSelection<Prisma.$battle_logPayload>

export type AggregateBattle_log = {
  _count: Battle_logCountAggregateOutputType | null
  _avg: Battle_logAvgAggregateOutputType | null
  _sum: Battle_logSumAggregateOutputType | null
  _min: Battle_logMinAggregateOutputType | null
  _max: Battle_logMaxAggregateOutputType | null
}

export type Battle_logAvgAggregateOutputType = {
  id: number | null
  attackerId: number | null
  defenderId: number | null
}

export type Battle_logSumAggregateOutputType = {
  id: number | null
  attackerId: number | null
  defenderId: number | null
}

export type Battle_logMinAggregateOutputType = {
  id: number | null
  victory: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  attackerId: number | null
  defenderId: number | null
}

export type Battle_logMaxAggregateOutputType = {
  id: number | null
  victory: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  attackerId: number | null
  defenderId: number | null
}

export type Battle_logCountAggregateOutputType = {
  id: number
  victory: number
  createdAt: number
  updatedAt: number
  attackerId: number
  defenderId: number
  _all: number
}


export type Battle_logAvgAggregateInputType = {
  id?: true
  attackerId?: true
  defenderId?: true
}

export type Battle_logSumAggregateInputType = {
  id?: true
  attackerId?: true
  defenderId?: true
}

export type Battle_logMinAggregateInputType = {
  id?: true
  victory?: true
  createdAt?: true
  updatedAt?: true
  attackerId?: true
  defenderId?: true
}

export type Battle_logMaxAggregateInputType = {
  id?: true
  victory?: true
  createdAt?: true
  updatedAt?: true
  attackerId?: true
  defenderId?: true
}

export type Battle_logCountAggregateInputType = {
  id?: true
  victory?: true
  createdAt?: true
  updatedAt?: true
  attackerId?: true
  defenderId?: true
  _all?: true
}

export type Battle_logAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which battle_log to aggregate.
   */
  where?: Prisma.battle_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of battle_logs to fetch.
   */
  orderBy?: Prisma.battle_logOrderByWithRelationInput | Prisma.battle_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.battle_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` battle_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` battle_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned battle_logs
  **/
  _count?: true | Battle_logCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Battle_logAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Battle_logSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Battle_logMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Battle_logMaxAggregateInputType
}

export type GetBattle_logAggregateType<T extends Battle_logAggregateArgs> = {
      [P in keyof T & keyof AggregateBattle_log]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateBattle_log[P]>
    : Prisma.GetScalarType<T[P], AggregateBattle_log[P]>
}




export type battle_logGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.battle_logWhereInput
  orderBy?: Prisma.battle_logOrderByWithAggregationInput | Prisma.battle_logOrderByWithAggregationInput[]
  by: Prisma.Battle_logScalarFieldEnum[] | Prisma.Battle_logScalarFieldEnum
  having?: Prisma.battle_logScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Battle_logCountAggregateInputType | true
  _avg?: Battle_logAvgAggregateInputType
  _sum?: Battle_logSumAggregateInputType
  _min?: Battle_logMinAggregateInputType
  _max?: Battle_logMaxAggregateInputType
}

export type Battle_logGroupByOutputType = {
  id: number
  victory: boolean
  createdAt: Date
  updatedAt: Date
  attackerId: number | null
  defenderId: number | null
  _count: Battle_logCountAggregateOutputType | null
  _avg: Battle_logAvgAggregateOutputType | null
  _sum: Battle_logSumAggregateOutputType | null
  _min: Battle_logMinAggregateOutputType | null
  _max: Battle_logMaxAggregateOutputType | null
}

type GetBattle_logGroupByPayload<T extends battle_logGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Battle_logGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Battle_logGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Battle_logGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Battle_logGroupByOutputType[P]>
      }
    >
  >



export type battle_logWhereInput = {
  AND?: Prisma.battle_logWhereInput | Prisma.battle_logWhereInput[]
  OR?: Prisma.battle_logWhereInput[]
  NOT?: Prisma.battle_logWhereInput | Prisma.battle_logWhereInput[]
  id?: Prisma.IntFilter<"battle_log"> | number
  victory?: Prisma.BoolFilter<"battle_log"> | boolean
  createdAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  attackerId?: Prisma.IntNullableFilter<"battle_log"> | number | null
  defenderId?: Prisma.IntNullableFilter<"battle_log"> | number | null
  attacker?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  defender?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type battle_logOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  victory?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  attackerId?: Prisma.SortOrderInput | Prisma.SortOrder
  defenderId?: Prisma.SortOrderInput | Prisma.SortOrder
  attacker?: Prisma.userOrderByWithRelationInput
  defender?: Prisma.userOrderByWithRelationInput
}

export type battle_logWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.battle_logWhereInput | Prisma.battle_logWhereInput[]
  OR?: Prisma.battle_logWhereInput[]
  NOT?: Prisma.battle_logWhereInput | Prisma.battle_logWhereInput[]
  victory?: Prisma.BoolFilter<"battle_log"> | boolean
  createdAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  attackerId?: Prisma.IntNullableFilter<"battle_log"> | number | null
  defenderId?: Prisma.IntNullableFilter<"battle_log"> | number | null
  attacker?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  defender?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type battle_logOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  victory?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  attackerId?: Prisma.SortOrderInput | Prisma.SortOrder
  defenderId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.battle_logCountOrderByAggregateInput
  _avg?: Prisma.battle_logAvgOrderByAggregateInput
  _max?: Prisma.battle_logMaxOrderByAggregateInput
  _min?: Prisma.battle_logMinOrderByAggregateInput
  _sum?: Prisma.battle_logSumOrderByAggregateInput
}

export type battle_logScalarWhereWithAggregatesInput = {
  AND?: Prisma.battle_logScalarWhereWithAggregatesInput | Prisma.battle_logScalarWhereWithAggregatesInput[]
  OR?: Prisma.battle_logScalarWhereWithAggregatesInput[]
  NOT?: Prisma.battle_logScalarWhereWithAggregatesInput | Prisma.battle_logScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"battle_log"> | number
  victory?: Prisma.BoolWithAggregatesFilter<"battle_log"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"battle_log"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"battle_log"> | Date | string
  attackerId?: Prisma.IntNullableWithAggregatesFilter<"battle_log"> | number | null
  defenderId?: Prisma.IntNullableWithAggregatesFilter<"battle_log"> | number | null
}

export type battle_logCreateInput = {
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attacker?: Prisma.userCreateNestedOneWithoutBattlesAsAttackerInput
  defender?: Prisma.userCreateNestedOneWithoutBattlesAsDefenderInput
}

export type battle_logUncheckedCreateInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attackerId?: number | null
  defenderId?: number | null
}

export type battle_logUpdateInput = {
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attacker?: Prisma.userUpdateOneWithoutBattlesAsAttackerNestedInput
  defender?: Prisma.userUpdateOneWithoutBattlesAsDefenderNestedInput
}

export type battle_logUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attackerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  defenderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type battle_logCreateManyInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attackerId?: number | null
  defenderId?: number | null
}

export type battle_logUpdateManyMutationInput = {
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type battle_logUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attackerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  defenderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type battle_logCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  victory?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  attackerId?: Prisma.SortOrder
  defenderId?: Prisma.SortOrder
}

export type battle_logAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  attackerId?: Prisma.SortOrder
  defenderId?: Prisma.SortOrder
}

export type battle_logMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  victory?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  attackerId?: Prisma.SortOrder
  defenderId?: Prisma.SortOrder
}

export type battle_logMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  victory?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  attackerId?: Prisma.SortOrder
  defenderId?: Prisma.SortOrder
}

export type battle_logSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  attackerId?: Prisma.SortOrder
  defenderId?: Prisma.SortOrder
}

export type Battle_logListRelationFilter = {
  every?: Prisma.battle_logWhereInput
  some?: Prisma.battle_logWhereInput
  none?: Prisma.battle_logWhereInput
}

export type battle_logOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type battle_logCreateNestedManyWithoutAttackerInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput> | Prisma.battle_logCreateWithoutAttackerInput[] | Prisma.battle_logUncheckedCreateWithoutAttackerInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutAttackerInput | Prisma.battle_logCreateOrConnectWithoutAttackerInput[]
  createMany?: Prisma.battle_logCreateManyAttackerInputEnvelope
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
}

export type battle_logCreateNestedManyWithoutDefenderInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput> | Prisma.battle_logCreateWithoutDefenderInput[] | Prisma.battle_logUncheckedCreateWithoutDefenderInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutDefenderInput | Prisma.battle_logCreateOrConnectWithoutDefenderInput[]
  createMany?: Prisma.battle_logCreateManyDefenderInputEnvelope
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
}

export type battle_logUncheckedCreateNestedManyWithoutAttackerInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput> | Prisma.battle_logCreateWithoutAttackerInput[] | Prisma.battle_logUncheckedCreateWithoutAttackerInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutAttackerInput | Prisma.battle_logCreateOrConnectWithoutAttackerInput[]
  createMany?: Prisma.battle_logCreateManyAttackerInputEnvelope
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
}

export type battle_logUncheckedCreateNestedManyWithoutDefenderInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput> | Prisma.battle_logCreateWithoutDefenderInput[] | Prisma.battle_logUncheckedCreateWithoutDefenderInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutDefenderInput | Prisma.battle_logCreateOrConnectWithoutDefenderInput[]
  createMany?: Prisma.battle_logCreateManyDefenderInputEnvelope
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
}

export type battle_logUpdateManyWithoutAttackerNestedInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput> | Prisma.battle_logCreateWithoutAttackerInput[] | Prisma.battle_logUncheckedCreateWithoutAttackerInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutAttackerInput | Prisma.battle_logCreateOrConnectWithoutAttackerInput[]
  upsert?: Prisma.battle_logUpsertWithWhereUniqueWithoutAttackerInput | Prisma.battle_logUpsertWithWhereUniqueWithoutAttackerInput[]
  createMany?: Prisma.battle_logCreateManyAttackerInputEnvelope
  set?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  disconnect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  delete?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  update?: Prisma.battle_logUpdateWithWhereUniqueWithoutAttackerInput | Prisma.battle_logUpdateWithWhereUniqueWithoutAttackerInput[]
  updateMany?: Prisma.battle_logUpdateManyWithWhereWithoutAttackerInput | Prisma.battle_logUpdateManyWithWhereWithoutAttackerInput[]
  deleteMany?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
}

export type battle_logUpdateManyWithoutDefenderNestedInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput> | Prisma.battle_logCreateWithoutDefenderInput[] | Prisma.battle_logUncheckedCreateWithoutDefenderInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutDefenderInput | Prisma.battle_logCreateOrConnectWithoutDefenderInput[]
  upsert?: Prisma.battle_logUpsertWithWhereUniqueWithoutDefenderInput | Prisma.battle_logUpsertWithWhereUniqueWithoutDefenderInput[]
  createMany?: Prisma.battle_logCreateManyDefenderInputEnvelope
  set?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  disconnect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  delete?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  update?: Prisma.battle_logUpdateWithWhereUniqueWithoutDefenderInput | Prisma.battle_logUpdateWithWhereUniqueWithoutDefenderInput[]
  updateMany?: Prisma.battle_logUpdateManyWithWhereWithoutDefenderInput | Prisma.battle_logUpdateManyWithWhereWithoutDefenderInput[]
  deleteMany?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
}

export type battle_logUncheckedUpdateManyWithoutAttackerNestedInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput> | Prisma.battle_logCreateWithoutAttackerInput[] | Prisma.battle_logUncheckedCreateWithoutAttackerInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutAttackerInput | Prisma.battle_logCreateOrConnectWithoutAttackerInput[]
  upsert?: Prisma.battle_logUpsertWithWhereUniqueWithoutAttackerInput | Prisma.battle_logUpsertWithWhereUniqueWithoutAttackerInput[]
  createMany?: Prisma.battle_logCreateManyAttackerInputEnvelope
  set?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  disconnect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  delete?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  update?: Prisma.battle_logUpdateWithWhereUniqueWithoutAttackerInput | Prisma.battle_logUpdateWithWhereUniqueWithoutAttackerInput[]
  updateMany?: Prisma.battle_logUpdateManyWithWhereWithoutAttackerInput | Prisma.battle_logUpdateManyWithWhereWithoutAttackerInput[]
  deleteMany?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
}

export type battle_logUncheckedUpdateManyWithoutDefenderNestedInput = {
  create?: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput> | Prisma.battle_logCreateWithoutDefenderInput[] | Prisma.battle_logUncheckedCreateWithoutDefenderInput[]
  connectOrCreate?: Prisma.battle_logCreateOrConnectWithoutDefenderInput | Prisma.battle_logCreateOrConnectWithoutDefenderInput[]
  upsert?: Prisma.battle_logUpsertWithWhereUniqueWithoutDefenderInput | Prisma.battle_logUpsertWithWhereUniqueWithoutDefenderInput[]
  createMany?: Prisma.battle_logCreateManyDefenderInputEnvelope
  set?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  disconnect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  delete?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  connect?: Prisma.battle_logWhereUniqueInput | Prisma.battle_logWhereUniqueInput[]
  update?: Prisma.battle_logUpdateWithWhereUniqueWithoutDefenderInput | Prisma.battle_logUpdateWithWhereUniqueWithoutDefenderInput[]
  updateMany?: Prisma.battle_logUpdateManyWithWhereWithoutDefenderInput | Prisma.battle_logUpdateManyWithWhereWithoutDefenderInput[]
  deleteMany?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
}

export type battle_logCreateWithoutAttackerInput = {
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  defender?: Prisma.userCreateNestedOneWithoutBattlesAsDefenderInput
}

export type battle_logUncheckedCreateWithoutAttackerInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  defenderId?: number | null
}

export type battle_logCreateOrConnectWithoutAttackerInput = {
  where: Prisma.battle_logWhereUniqueInput
  create: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput>
}

export type battle_logCreateManyAttackerInputEnvelope = {
  data: Prisma.battle_logCreateManyAttackerInput | Prisma.battle_logCreateManyAttackerInput[]
  skipDuplicates?: boolean
}

export type battle_logCreateWithoutDefenderInput = {
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attacker?: Prisma.userCreateNestedOneWithoutBattlesAsAttackerInput
}

export type battle_logUncheckedCreateWithoutDefenderInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attackerId?: number | null
}

export type battle_logCreateOrConnectWithoutDefenderInput = {
  where: Prisma.battle_logWhereUniqueInput
  create: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput>
}

export type battle_logCreateManyDefenderInputEnvelope = {
  data: Prisma.battle_logCreateManyDefenderInput | Prisma.battle_logCreateManyDefenderInput[]
  skipDuplicates?: boolean
}

export type battle_logUpsertWithWhereUniqueWithoutAttackerInput = {
  where: Prisma.battle_logWhereUniqueInput
  update: Prisma.XOR<Prisma.battle_logUpdateWithoutAttackerInput, Prisma.battle_logUncheckedUpdateWithoutAttackerInput>
  create: Prisma.XOR<Prisma.battle_logCreateWithoutAttackerInput, Prisma.battle_logUncheckedCreateWithoutAttackerInput>
}

export type battle_logUpdateWithWhereUniqueWithoutAttackerInput = {
  where: Prisma.battle_logWhereUniqueInput
  data: Prisma.XOR<Prisma.battle_logUpdateWithoutAttackerInput, Prisma.battle_logUncheckedUpdateWithoutAttackerInput>
}

export type battle_logUpdateManyWithWhereWithoutAttackerInput = {
  where: Prisma.battle_logScalarWhereInput
  data: Prisma.XOR<Prisma.battle_logUpdateManyMutationInput, Prisma.battle_logUncheckedUpdateManyWithoutAttackerInput>
}

export type battle_logScalarWhereInput = {
  AND?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
  OR?: Prisma.battle_logScalarWhereInput[]
  NOT?: Prisma.battle_logScalarWhereInput | Prisma.battle_logScalarWhereInput[]
  id?: Prisma.IntFilter<"battle_log"> | number
  victory?: Prisma.BoolFilter<"battle_log"> | boolean
  createdAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"battle_log"> | Date | string
  attackerId?: Prisma.IntNullableFilter<"battle_log"> | number | null
  defenderId?: Prisma.IntNullableFilter<"battle_log"> | number | null
}

export type battle_logUpsertWithWhereUniqueWithoutDefenderInput = {
  where: Prisma.battle_logWhereUniqueInput
  update: Prisma.XOR<Prisma.battle_logUpdateWithoutDefenderInput, Prisma.battle_logUncheckedUpdateWithoutDefenderInput>
  create: Prisma.XOR<Prisma.battle_logCreateWithoutDefenderInput, Prisma.battle_logUncheckedCreateWithoutDefenderInput>
}

export type battle_logUpdateWithWhereUniqueWithoutDefenderInput = {
  where: Prisma.battle_logWhereUniqueInput
  data: Prisma.XOR<Prisma.battle_logUpdateWithoutDefenderInput, Prisma.battle_logUncheckedUpdateWithoutDefenderInput>
}

export type battle_logUpdateManyWithWhereWithoutDefenderInput = {
  where: Prisma.battle_logScalarWhereInput
  data: Prisma.XOR<Prisma.battle_logUpdateManyMutationInput, Prisma.battle_logUncheckedUpdateManyWithoutDefenderInput>
}

export type battle_logCreateManyAttackerInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  defenderId?: number | null
}

export type battle_logCreateManyDefenderInput = {
  id?: number
  victory: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  attackerId?: number | null
}

export type battle_logUpdateWithoutAttackerInput = {
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  defender?: Prisma.userUpdateOneWithoutBattlesAsDefenderNestedInput
}

export type battle_logUncheckedUpdateWithoutAttackerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  defenderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type battle_logUncheckedUpdateManyWithoutAttackerInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  defenderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type battle_logUpdateWithoutDefenderInput = {
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attacker?: Prisma.userUpdateOneWithoutBattlesAsAttackerNestedInput
}

export type battle_logUncheckedUpdateWithoutDefenderInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attackerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type battle_logUncheckedUpdateManyWithoutDefenderInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  victory?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attackerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type battle_logSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  victory?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  attackerId?: boolean
  defenderId?: boolean
  attacker?: boolean | Prisma.battle_log$attackerArgs<ExtArgs>
  defender?: boolean | Prisma.battle_log$defenderArgs<ExtArgs>
}, ExtArgs["result"]["battle_log"]>



export type battle_logSelectScalar = {
  id?: boolean
  victory?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  attackerId?: boolean
  defenderId?: boolean
}

export type battle_logOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "victory" | "createdAt" | "updatedAt" | "attackerId" | "defenderId", ExtArgs["result"]["battle_log"]>
export type battle_logInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  attacker?: boolean | Prisma.battle_log$attackerArgs<ExtArgs>
  defender?: boolean | Prisma.battle_log$defenderArgs<ExtArgs>
}

export type $battle_logPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "battle_log"
  objects: {
    attacker: Prisma.$userPayload<ExtArgs> | null
    defender: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    victory: boolean
    createdAt: Date
    updatedAt: Date
    attackerId: number | null
    defenderId: number | null
  }, ExtArgs["result"]["battle_log"]>
  composites: {}
}

export type battle_logGetPayload<S extends boolean | null | undefined | battle_logDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$battle_logPayload, S>

export type battle_logCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<battle_logFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Battle_logCountAggregateInputType | true
  }

export interface battle_logDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['battle_log'], meta: { name: 'battle_log' } }
  /**
   * Find zero or one Battle_log that matches the filter.
   * @param {battle_logFindUniqueArgs} args - Arguments to find a Battle_log
   * @example
   * // Get one Battle_log
   * const battle_log = await prisma.battle_log.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends battle_logFindUniqueArgs>(args: Prisma.SelectSubset<T, battle_logFindUniqueArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Battle_log that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {battle_logFindUniqueOrThrowArgs} args - Arguments to find a Battle_log
   * @example
   * // Get one Battle_log
   * const battle_log = await prisma.battle_log.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends battle_logFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, battle_logFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Battle_log that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logFindFirstArgs} args - Arguments to find a Battle_log
   * @example
   * // Get one Battle_log
   * const battle_log = await prisma.battle_log.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends battle_logFindFirstArgs>(args?: Prisma.SelectSubset<T, battle_logFindFirstArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Battle_log that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logFindFirstOrThrowArgs} args - Arguments to find a Battle_log
   * @example
   * // Get one Battle_log
   * const battle_log = await prisma.battle_log.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends battle_logFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, battle_logFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Battle_logs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Battle_logs
   * const battle_logs = await prisma.battle_log.findMany()
   * 
   * // Get first 10 Battle_logs
   * const battle_logs = await prisma.battle_log.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const battle_logWithIdOnly = await prisma.battle_log.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends battle_logFindManyArgs>(args?: Prisma.SelectSubset<T, battle_logFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Battle_log.
   * @param {battle_logCreateArgs} args - Arguments to create a Battle_log.
   * @example
   * // Create one Battle_log
   * const Battle_log = await prisma.battle_log.create({
   *   data: {
   *     // ... data to create a Battle_log
   *   }
   * })
   * 
   */
  create<T extends battle_logCreateArgs>(args: Prisma.SelectSubset<T, battle_logCreateArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Battle_logs.
   * @param {battle_logCreateManyArgs} args - Arguments to create many Battle_logs.
   * @example
   * // Create many Battle_logs
   * const battle_log = await prisma.battle_log.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends battle_logCreateManyArgs>(args?: Prisma.SelectSubset<T, battle_logCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Battle_log.
   * @param {battle_logDeleteArgs} args - Arguments to delete one Battle_log.
   * @example
   * // Delete one Battle_log
   * const Battle_log = await prisma.battle_log.delete({
   *   where: {
   *     // ... filter to delete one Battle_log
   *   }
   * })
   * 
   */
  delete<T extends battle_logDeleteArgs>(args: Prisma.SelectSubset<T, battle_logDeleteArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Battle_log.
   * @param {battle_logUpdateArgs} args - Arguments to update one Battle_log.
   * @example
   * // Update one Battle_log
   * const battle_log = await prisma.battle_log.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends battle_logUpdateArgs>(args: Prisma.SelectSubset<T, battle_logUpdateArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Battle_logs.
   * @param {battle_logDeleteManyArgs} args - Arguments to filter Battle_logs to delete.
   * @example
   * // Delete a few Battle_logs
   * const { count } = await prisma.battle_log.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends battle_logDeleteManyArgs>(args?: Prisma.SelectSubset<T, battle_logDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Battle_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Battle_logs
   * const battle_log = await prisma.battle_log.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends battle_logUpdateManyArgs>(args: Prisma.SelectSubset<T, battle_logUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Battle_log.
   * @param {battle_logUpsertArgs} args - Arguments to update or create a Battle_log.
   * @example
   * // Update or create a Battle_log
   * const battle_log = await prisma.battle_log.upsert({
   *   create: {
   *     // ... data to create a Battle_log
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Battle_log we want to update
   *   }
   * })
   */
  upsert<T extends battle_logUpsertArgs>(args: Prisma.SelectSubset<T, battle_logUpsertArgs<ExtArgs>>): Prisma.Prisma__battle_logClient<runtime.Types.Result.GetResult<Prisma.$battle_logPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Battle_logs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logCountArgs} args - Arguments to filter Battle_logs to count.
   * @example
   * // Count the number of Battle_logs
   * const count = await prisma.battle_log.count({
   *   where: {
   *     // ... the filter for the Battle_logs we want to count
   *   }
   * })
  **/
  count<T extends battle_logCountArgs>(
    args?: Prisma.Subset<T, battle_logCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Battle_logCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Battle_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Battle_logAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Battle_logAggregateArgs>(args: Prisma.Subset<T, Battle_logAggregateArgs>): Prisma.PrismaPromise<GetBattle_logAggregateType<T>>

  /**
   * Group by Battle_log.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {battle_logGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends battle_logGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: battle_logGroupByArgs['orderBy'] }
      : { orderBy?: battle_logGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, battle_logGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetBattle_logGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the battle_log model
 */
readonly fields: battle_logFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for battle_log.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__battle_logClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  attacker<T extends Prisma.battle_log$attackerArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.battle_log$attackerArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  defender<T extends Prisma.battle_log$defenderArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.battle_log$defenderArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the battle_log model
 */
export interface battle_logFieldRefs {
  readonly id: Prisma.FieldRef<"battle_log", 'Int'>
  readonly victory: Prisma.FieldRef<"battle_log", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"battle_log", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"battle_log", 'DateTime'>
  readonly attackerId: Prisma.FieldRef<"battle_log", 'Int'>
  readonly defenderId: Prisma.FieldRef<"battle_log", 'Int'>
}
    

// Custom InputTypes
/**
 * battle_log findUnique
 */
export type battle_logFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter, which battle_log to fetch.
   */
  where: Prisma.battle_logWhereUniqueInput
}

/**
 * battle_log findUniqueOrThrow
 */
export type battle_logFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter, which battle_log to fetch.
   */
  where: Prisma.battle_logWhereUniqueInput
}

/**
 * battle_log findFirst
 */
export type battle_logFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter, which battle_log to fetch.
   */
  where?: Prisma.battle_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of battle_logs to fetch.
   */
  orderBy?: Prisma.battle_logOrderByWithRelationInput | Prisma.battle_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for battle_logs.
   */
  cursor?: Prisma.battle_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` battle_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` battle_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of battle_logs.
   */
  distinct?: Prisma.Battle_logScalarFieldEnum | Prisma.Battle_logScalarFieldEnum[]
}

/**
 * battle_log findFirstOrThrow
 */
export type battle_logFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter, which battle_log to fetch.
   */
  where?: Prisma.battle_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of battle_logs to fetch.
   */
  orderBy?: Prisma.battle_logOrderByWithRelationInput | Prisma.battle_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for battle_logs.
   */
  cursor?: Prisma.battle_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` battle_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` battle_logs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of battle_logs.
   */
  distinct?: Prisma.Battle_logScalarFieldEnum | Prisma.Battle_logScalarFieldEnum[]
}

/**
 * battle_log findMany
 */
export type battle_logFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter, which battle_logs to fetch.
   */
  where?: Prisma.battle_logWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of battle_logs to fetch.
   */
  orderBy?: Prisma.battle_logOrderByWithRelationInput | Prisma.battle_logOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing battle_logs.
   */
  cursor?: Prisma.battle_logWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` battle_logs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` battle_logs.
   */
  skip?: number
  distinct?: Prisma.Battle_logScalarFieldEnum | Prisma.Battle_logScalarFieldEnum[]
}

/**
 * battle_log create
 */
export type battle_logCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * The data needed to create a battle_log.
   */
  data: Prisma.XOR<Prisma.battle_logCreateInput, Prisma.battle_logUncheckedCreateInput>
}

/**
 * battle_log createMany
 */
export type battle_logCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many battle_logs.
   */
  data: Prisma.battle_logCreateManyInput | Prisma.battle_logCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * battle_log update
 */
export type battle_logUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * The data needed to update a battle_log.
   */
  data: Prisma.XOR<Prisma.battle_logUpdateInput, Prisma.battle_logUncheckedUpdateInput>
  /**
   * Choose, which battle_log to update.
   */
  where: Prisma.battle_logWhereUniqueInput
}

/**
 * battle_log updateMany
 */
export type battle_logUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update battle_logs.
   */
  data: Prisma.XOR<Prisma.battle_logUpdateManyMutationInput, Prisma.battle_logUncheckedUpdateManyInput>
  /**
   * Filter which battle_logs to update
   */
  where?: Prisma.battle_logWhereInput
  /**
   * Limit how many battle_logs to update.
   */
  limit?: number
}

/**
 * battle_log upsert
 */
export type battle_logUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * The filter to search for the battle_log to update in case it exists.
   */
  where: Prisma.battle_logWhereUniqueInput
  /**
   * In case the battle_log found by the `where` argument doesn't exist, create a new battle_log with this data.
   */
  create: Prisma.XOR<Prisma.battle_logCreateInput, Prisma.battle_logUncheckedCreateInput>
  /**
   * In case the battle_log was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.battle_logUpdateInput, Prisma.battle_logUncheckedUpdateInput>
}

/**
 * battle_log delete
 */
export type battle_logDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
  /**
   * Filter which battle_log to delete.
   */
  where: Prisma.battle_logWhereUniqueInput
}

/**
 * battle_log deleteMany
 */
export type battle_logDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which battle_logs to delete
   */
  where?: Prisma.battle_logWhereInput
  /**
   * Limit how many battle_logs to delete.
   */
  limit?: number
}

/**
 * battle_log.attacker
 */
export type battle_log$attackerArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * battle_log.defender
 */
export type battle_log$defenderArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * battle_log without action
 */
export type battle_logDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the battle_log
   */
  select?: Prisma.battle_logSelect<ExtArgs> | null
  /**
   * Omit specific fields from the battle_log
   */
  omit?: Prisma.battle_logOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.battle_logInclude<ExtArgs> | null
}
