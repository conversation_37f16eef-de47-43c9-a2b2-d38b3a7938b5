
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_status_effect` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_status_effect
 * 
 */
export type user_status_effectModel = runtime.Types.Result.DefaultSelection<Prisma.$user_status_effectPayload>

export type AggregateUser_status_effect = {
  _count: User_status_effectCountAggregateOutputType | null
  _avg: User_status_effectAvgAggregateOutputType | null
  _sum: User_status_effectSumAggregateOutputType | null
  _min: User_status_effectMinAggregateOutputType | null
  _max: User_status_effectMaxAggregateOutputType | null
}

export type User_status_effectAvgAggregateOutputType = {
  id: number | null
  stacks: number | null
  endsAt: number | null
  effectId: number | null
  userId: number | null
}

export type User_status_effectSumAggregateOutputType = {
  id: number | null
  stacks: number | null
  endsAt: bigint | null
  effectId: number | null
  userId: number | null
}

export type User_status_effectMinAggregateOutputType = {
  id: number | null
  stacks: number | null
  endsAt: bigint | null
  customName: string | null
  createdAt: Date | null
  updatedAt: Date | null
  effectId: number | null
  userId: number | null
}

export type User_status_effectMaxAggregateOutputType = {
  id: number | null
  stacks: number | null
  endsAt: bigint | null
  customName: string | null
  createdAt: Date | null
  updatedAt: Date | null
  effectId: number | null
  userId: number | null
}

export type User_status_effectCountAggregateOutputType = {
  id: number
  stacks: number
  endsAt: number
  customName: number
  createdAt: number
  updatedAt: number
  effectId: number
  userId: number
  _all: number
}


export type User_status_effectAvgAggregateInputType = {
  id?: true
  stacks?: true
  endsAt?: true
  effectId?: true
  userId?: true
}

export type User_status_effectSumAggregateInputType = {
  id?: true
  stacks?: true
  endsAt?: true
  effectId?: true
  userId?: true
}

export type User_status_effectMinAggregateInputType = {
  id?: true
  stacks?: true
  endsAt?: true
  customName?: true
  createdAt?: true
  updatedAt?: true
  effectId?: true
  userId?: true
}

export type User_status_effectMaxAggregateInputType = {
  id?: true
  stacks?: true
  endsAt?: true
  customName?: true
  createdAt?: true
  updatedAt?: true
  effectId?: true
  userId?: true
}

export type User_status_effectCountAggregateInputType = {
  id?: true
  stacks?: true
  endsAt?: true
  customName?: true
  createdAt?: true
  updatedAt?: true
  effectId?: true
  userId?: true
  _all?: true
}

export type User_status_effectAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_status_effect to aggregate.
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_status_effects to fetch.
   */
  orderBy?: Prisma.user_status_effectOrderByWithRelationInput | Prisma.user_status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_status_effects
  **/
  _count?: true | User_status_effectCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_status_effectAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_status_effectSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_status_effectMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_status_effectMaxAggregateInputType
}

export type GetUser_status_effectAggregateType<T extends User_status_effectAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_status_effect]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_status_effect[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_status_effect[P]>
}




export type user_status_effectGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_status_effectWhereInput
  orderBy?: Prisma.user_status_effectOrderByWithAggregationInput | Prisma.user_status_effectOrderByWithAggregationInput[]
  by: Prisma.User_status_effectScalarFieldEnum[] | Prisma.User_status_effectScalarFieldEnum
  having?: Prisma.user_status_effectScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_status_effectCountAggregateInputType | true
  _avg?: User_status_effectAvgAggregateInputType
  _sum?: User_status_effectSumAggregateInputType
  _min?: User_status_effectMinAggregateInputType
  _max?: User_status_effectMaxAggregateInputType
}

export type User_status_effectGroupByOutputType = {
  id: number
  stacks: number
  endsAt: bigint
  customName: string | null
  createdAt: Date
  updatedAt: Date
  effectId: number | null
  userId: number | null
  _count: User_status_effectCountAggregateOutputType | null
  _avg: User_status_effectAvgAggregateOutputType | null
  _sum: User_status_effectSumAggregateOutputType | null
  _min: User_status_effectMinAggregateOutputType | null
  _max: User_status_effectMaxAggregateOutputType | null
}

type GetUser_status_effectGroupByPayload<T extends user_status_effectGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_status_effectGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_status_effectGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_status_effectGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_status_effectGroupByOutputType[P]>
      }
    >
  >



export type user_status_effectWhereInput = {
  AND?: Prisma.user_status_effectWhereInput | Prisma.user_status_effectWhereInput[]
  OR?: Prisma.user_status_effectWhereInput[]
  NOT?: Prisma.user_status_effectWhereInput | Prisma.user_status_effectWhereInput[]
  id?: Prisma.IntFilter<"user_status_effect"> | number
  stacks?: Prisma.IntFilter<"user_status_effect"> | number
  endsAt?: Prisma.BigIntFilter<"user_status_effect"> | bigint | number
  customName?: Prisma.StringNullableFilter<"user_status_effect"> | string | null
  createdAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  effectId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
  userId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
  effect?: Prisma.XOR<Prisma.Status_effectNullableScalarRelationFilter, Prisma.status_effectWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type user_status_effectOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  customName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  effect?: Prisma.status_effectOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.user_status_effectOrderByRelevanceInput
}

export type user_status_effectWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  effectId_userId_customName?: Prisma.user_status_effectEffectIdUserIdCustomNameCompoundUniqueInput
  AND?: Prisma.user_status_effectWhereInput | Prisma.user_status_effectWhereInput[]
  OR?: Prisma.user_status_effectWhereInput[]
  NOT?: Prisma.user_status_effectWhereInput | Prisma.user_status_effectWhereInput[]
  stacks?: Prisma.IntFilter<"user_status_effect"> | number
  endsAt?: Prisma.BigIntFilter<"user_status_effect"> | bigint | number
  customName?: Prisma.StringNullableFilter<"user_status_effect"> | string | null
  createdAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  effectId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
  userId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
  effect?: Prisma.XOR<Prisma.Status_effectNullableScalarRelationFilter, Prisma.status_effectWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id" | "effectId_userId_customName">

export type user_status_effectOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  customName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.user_status_effectCountOrderByAggregateInput
  _avg?: Prisma.user_status_effectAvgOrderByAggregateInput
  _max?: Prisma.user_status_effectMaxOrderByAggregateInput
  _min?: Prisma.user_status_effectMinOrderByAggregateInput
  _sum?: Prisma.user_status_effectSumOrderByAggregateInput
}

export type user_status_effectScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_status_effectScalarWhereWithAggregatesInput | Prisma.user_status_effectScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_status_effectScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_status_effectScalarWhereWithAggregatesInput | Prisma.user_status_effectScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"user_status_effect"> | number
  stacks?: Prisma.IntWithAggregatesFilter<"user_status_effect"> | number
  endsAt?: Prisma.BigIntWithAggregatesFilter<"user_status_effect"> | bigint | number
  customName?: Prisma.StringNullableWithAggregatesFilter<"user_status_effect"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_status_effect"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_status_effect"> | Date | string
  effectId?: Prisma.IntNullableWithAggregatesFilter<"user_status_effect"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"user_status_effect"> | number | null
}

export type user_status_effectCreateInput = {
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effect?: Prisma.status_effectCreateNestedOneWithoutUser_status_effectInput
  user?: Prisma.userCreateNestedOneWithoutUser_status_effectInput
}

export type user_status_effectUncheckedCreateInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effectId?: number | null
  userId?: number | null
}

export type user_status_effectUpdateInput = {
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effect?: Prisma.status_effectUpdateOneWithoutUser_status_effectNestedInput
  user?: Prisma.userUpdateOneWithoutUser_status_effectNestedInput
}

export type user_status_effectUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effectId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_status_effectCreateManyInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effectId?: number | null
  userId?: number | null
}

export type user_status_effectUpdateManyMutationInput = {
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_status_effectUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effectId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type User_status_effectListRelationFilter = {
  every?: Prisma.user_status_effectWhereInput
  some?: Prisma.user_status_effectWhereInput
  none?: Prisma.user_status_effectWhereInput
}

export type user_status_effectOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_status_effectOrderByRelevanceInput = {
  fields: Prisma.user_status_effectOrderByRelevanceFieldEnum | Prisma.user_status_effectOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type user_status_effectEffectIdUserIdCustomNameCompoundUniqueInput = {
  effectId: number
  userId: number
  customName: string
}

export type user_status_effectCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  customName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_status_effectAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_status_effectMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  customName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_status_effectMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  customName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_status_effectSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stacks?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  effectId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_status_effectCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput> | Prisma.user_status_effectCreateWithoutUserInput[] | Prisma.user_status_effectUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutUserInput | Prisma.user_status_effectCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_status_effectCreateManyUserInputEnvelope
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
}

export type user_status_effectUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput> | Prisma.user_status_effectCreateWithoutUserInput[] | Prisma.user_status_effectUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutUserInput | Prisma.user_status_effectCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_status_effectCreateManyUserInputEnvelope
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
}

export type user_status_effectUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput> | Prisma.user_status_effectCreateWithoutUserInput[] | Prisma.user_status_effectUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutUserInput | Prisma.user_status_effectCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_status_effectUpsertWithWhereUniqueWithoutUserInput | Prisma.user_status_effectUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_status_effectCreateManyUserInputEnvelope
  set?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  disconnect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  delete?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  update?: Prisma.user_status_effectUpdateWithWhereUniqueWithoutUserInput | Prisma.user_status_effectUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_status_effectUpdateManyWithWhereWithoutUserInput | Prisma.user_status_effectUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
}

export type user_status_effectUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput> | Prisma.user_status_effectCreateWithoutUserInput[] | Prisma.user_status_effectUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutUserInput | Prisma.user_status_effectCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_status_effectUpsertWithWhereUniqueWithoutUserInput | Prisma.user_status_effectUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_status_effectCreateManyUserInputEnvelope
  set?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  disconnect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  delete?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  update?: Prisma.user_status_effectUpdateWithWhereUniqueWithoutUserInput | Prisma.user_status_effectUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_status_effectUpdateManyWithWhereWithoutUserInput | Prisma.user_status_effectUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
}

export type user_status_effectCreateNestedManyWithoutEffectInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput> | Prisma.user_status_effectCreateWithoutEffectInput[] | Prisma.user_status_effectUncheckedCreateWithoutEffectInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutEffectInput | Prisma.user_status_effectCreateOrConnectWithoutEffectInput[]
  createMany?: Prisma.user_status_effectCreateManyEffectInputEnvelope
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
}

export type user_status_effectUncheckedCreateNestedManyWithoutEffectInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput> | Prisma.user_status_effectCreateWithoutEffectInput[] | Prisma.user_status_effectUncheckedCreateWithoutEffectInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutEffectInput | Prisma.user_status_effectCreateOrConnectWithoutEffectInput[]
  createMany?: Prisma.user_status_effectCreateManyEffectInputEnvelope
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
}

export type user_status_effectUpdateManyWithoutEffectNestedInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput> | Prisma.user_status_effectCreateWithoutEffectInput[] | Prisma.user_status_effectUncheckedCreateWithoutEffectInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutEffectInput | Prisma.user_status_effectCreateOrConnectWithoutEffectInput[]
  upsert?: Prisma.user_status_effectUpsertWithWhereUniqueWithoutEffectInput | Prisma.user_status_effectUpsertWithWhereUniqueWithoutEffectInput[]
  createMany?: Prisma.user_status_effectCreateManyEffectInputEnvelope
  set?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  disconnect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  delete?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  update?: Prisma.user_status_effectUpdateWithWhereUniqueWithoutEffectInput | Prisma.user_status_effectUpdateWithWhereUniqueWithoutEffectInput[]
  updateMany?: Prisma.user_status_effectUpdateManyWithWhereWithoutEffectInput | Prisma.user_status_effectUpdateManyWithWhereWithoutEffectInput[]
  deleteMany?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
}

export type user_status_effectUncheckedUpdateManyWithoutEffectNestedInput = {
  create?: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput> | Prisma.user_status_effectCreateWithoutEffectInput[] | Prisma.user_status_effectUncheckedCreateWithoutEffectInput[]
  connectOrCreate?: Prisma.user_status_effectCreateOrConnectWithoutEffectInput | Prisma.user_status_effectCreateOrConnectWithoutEffectInput[]
  upsert?: Prisma.user_status_effectUpsertWithWhereUniqueWithoutEffectInput | Prisma.user_status_effectUpsertWithWhereUniqueWithoutEffectInput[]
  createMany?: Prisma.user_status_effectCreateManyEffectInputEnvelope
  set?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  disconnect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  delete?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  connect?: Prisma.user_status_effectWhereUniqueInput | Prisma.user_status_effectWhereUniqueInput[]
  update?: Prisma.user_status_effectUpdateWithWhereUniqueWithoutEffectInput | Prisma.user_status_effectUpdateWithWhereUniqueWithoutEffectInput[]
  updateMany?: Prisma.user_status_effectUpdateManyWithWhereWithoutEffectInput | Prisma.user_status_effectUpdateManyWithWhereWithoutEffectInput[]
  deleteMany?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
}

export type user_status_effectCreateWithoutUserInput = {
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effect?: Prisma.status_effectCreateNestedOneWithoutUser_status_effectInput
}

export type user_status_effectUncheckedCreateWithoutUserInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effectId?: number | null
}

export type user_status_effectCreateOrConnectWithoutUserInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  create: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput>
}

export type user_status_effectCreateManyUserInputEnvelope = {
  data: Prisma.user_status_effectCreateManyUserInput | Prisma.user_status_effectCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_status_effectUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  update: Prisma.XOR<Prisma.user_status_effectUpdateWithoutUserInput, Prisma.user_status_effectUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_status_effectCreateWithoutUserInput, Prisma.user_status_effectUncheckedCreateWithoutUserInput>
}

export type user_status_effectUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  data: Prisma.XOR<Prisma.user_status_effectUpdateWithoutUserInput, Prisma.user_status_effectUncheckedUpdateWithoutUserInput>
}

export type user_status_effectUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_status_effectScalarWhereInput
  data: Prisma.XOR<Prisma.user_status_effectUpdateManyMutationInput, Prisma.user_status_effectUncheckedUpdateManyWithoutUserInput>
}

export type user_status_effectScalarWhereInput = {
  AND?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
  OR?: Prisma.user_status_effectScalarWhereInput[]
  NOT?: Prisma.user_status_effectScalarWhereInput | Prisma.user_status_effectScalarWhereInput[]
  id?: Prisma.IntFilter<"user_status_effect"> | number
  stacks?: Prisma.IntFilter<"user_status_effect"> | number
  endsAt?: Prisma.BigIntFilter<"user_status_effect"> | bigint | number
  customName?: Prisma.StringNullableFilter<"user_status_effect"> | string | null
  createdAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_status_effect"> | Date | string
  effectId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
  userId?: Prisma.IntNullableFilter<"user_status_effect"> | number | null
}

export type user_status_effectCreateWithoutEffectInput = {
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutUser_status_effectInput
}

export type user_status_effectUncheckedCreateWithoutEffectInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_status_effectCreateOrConnectWithoutEffectInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  create: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput>
}

export type user_status_effectCreateManyEffectInputEnvelope = {
  data: Prisma.user_status_effectCreateManyEffectInput | Prisma.user_status_effectCreateManyEffectInput[]
  skipDuplicates?: boolean
}

export type user_status_effectUpsertWithWhereUniqueWithoutEffectInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  update: Prisma.XOR<Prisma.user_status_effectUpdateWithoutEffectInput, Prisma.user_status_effectUncheckedUpdateWithoutEffectInput>
  create: Prisma.XOR<Prisma.user_status_effectCreateWithoutEffectInput, Prisma.user_status_effectUncheckedCreateWithoutEffectInput>
}

export type user_status_effectUpdateWithWhereUniqueWithoutEffectInput = {
  where: Prisma.user_status_effectWhereUniqueInput
  data: Prisma.XOR<Prisma.user_status_effectUpdateWithoutEffectInput, Prisma.user_status_effectUncheckedUpdateWithoutEffectInput>
}

export type user_status_effectUpdateManyWithWhereWithoutEffectInput = {
  where: Prisma.user_status_effectScalarWhereInput
  data: Prisma.XOR<Prisma.user_status_effectUpdateManyMutationInput, Prisma.user_status_effectUncheckedUpdateManyWithoutEffectInput>
}

export type user_status_effectCreateManyUserInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  effectId?: number | null
}

export type user_status_effectUpdateWithoutUserInput = {
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effect?: Prisma.status_effectUpdateOneWithoutUser_status_effectNestedInput
}

export type user_status_effectUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effectId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_status_effectUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  effectId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_status_effectCreateManyEffectInput = {
  id?: number
  stacks?: number
  endsAt: bigint | number
  customName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type user_status_effectUpdateWithoutEffectInput = {
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutUser_status_effectNestedInput
}

export type user_status_effectUncheckedUpdateWithoutEffectInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type user_status_effectUncheckedUpdateManyWithoutEffectInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  stacks?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.BigIntFieldUpdateOperationsInput | bigint | number
  customName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type user_status_effectSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stacks?: boolean
  endsAt?: boolean
  customName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  effectId?: boolean
  userId?: boolean
  effect?: boolean | Prisma.user_status_effect$effectArgs<ExtArgs>
  user?: boolean | Prisma.user_status_effect$userArgs<ExtArgs>
}, ExtArgs["result"]["user_status_effect"]>



export type user_status_effectSelectScalar = {
  id?: boolean
  stacks?: boolean
  endsAt?: boolean
  customName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  effectId?: boolean
  userId?: boolean
}

export type user_status_effectOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "stacks" | "endsAt" | "customName" | "createdAt" | "updatedAt" | "effectId" | "userId", ExtArgs["result"]["user_status_effect"]>
export type user_status_effectInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  effect?: boolean | Prisma.user_status_effect$effectArgs<ExtArgs>
  user?: boolean | Prisma.user_status_effect$userArgs<ExtArgs>
}

export type $user_status_effectPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_status_effect"
  objects: {
    effect: Prisma.$status_effectPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    stacks: number
    endsAt: bigint
    customName: string | null
    createdAt: Date
    updatedAt: Date
    effectId: number | null
    userId: number | null
  }, ExtArgs["result"]["user_status_effect"]>
  composites: {}
}

export type user_status_effectGetPayload<S extends boolean | null | undefined | user_status_effectDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload, S>

export type user_status_effectCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_status_effectFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_status_effectCountAggregateInputType | true
  }

export interface user_status_effectDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_status_effect'], meta: { name: 'user_status_effect' } }
  /**
   * Find zero or one User_status_effect that matches the filter.
   * @param {user_status_effectFindUniqueArgs} args - Arguments to find a User_status_effect
   * @example
   * // Get one User_status_effect
   * const user_status_effect = await prisma.user_status_effect.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_status_effectFindUniqueArgs>(args: Prisma.SelectSubset<T, user_status_effectFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_status_effect that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_status_effectFindUniqueOrThrowArgs} args - Arguments to find a User_status_effect
   * @example
   * // Get one User_status_effect
   * const user_status_effect = await prisma.user_status_effect.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_status_effectFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_status_effectFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_status_effect that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectFindFirstArgs} args - Arguments to find a User_status_effect
   * @example
   * // Get one User_status_effect
   * const user_status_effect = await prisma.user_status_effect.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_status_effectFindFirstArgs>(args?: Prisma.SelectSubset<T, user_status_effectFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_status_effect that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectFindFirstOrThrowArgs} args - Arguments to find a User_status_effect
   * @example
   * // Get one User_status_effect
   * const user_status_effect = await prisma.user_status_effect.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_status_effectFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_status_effectFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_status_effects that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_status_effects
   * const user_status_effects = await prisma.user_status_effect.findMany()
   * 
   * // Get first 10 User_status_effects
   * const user_status_effects = await prisma.user_status_effect.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const user_status_effectWithIdOnly = await prisma.user_status_effect.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends user_status_effectFindManyArgs>(args?: Prisma.SelectSubset<T, user_status_effectFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_status_effect.
   * @param {user_status_effectCreateArgs} args - Arguments to create a User_status_effect.
   * @example
   * // Create one User_status_effect
   * const User_status_effect = await prisma.user_status_effect.create({
   *   data: {
   *     // ... data to create a User_status_effect
   *   }
   * })
   * 
   */
  create<T extends user_status_effectCreateArgs>(args: Prisma.SelectSubset<T, user_status_effectCreateArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_status_effects.
   * @param {user_status_effectCreateManyArgs} args - Arguments to create many User_status_effects.
   * @example
   * // Create many User_status_effects
   * const user_status_effect = await prisma.user_status_effect.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_status_effectCreateManyArgs>(args?: Prisma.SelectSubset<T, user_status_effectCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_status_effect.
   * @param {user_status_effectDeleteArgs} args - Arguments to delete one User_status_effect.
   * @example
   * // Delete one User_status_effect
   * const User_status_effect = await prisma.user_status_effect.delete({
   *   where: {
   *     // ... filter to delete one User_status_effect
   *   }
   * })
   * 
   */
  delete<T extends user_status_effectDeleteArgs>(args: Prisma.SelectSubset<T, user_status_effectDeleteArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_status_effect.
   * @param {user_status_effectUpdateArgs} args - Arguments to update one User_status_effect.
   * @example
   * // Update one User_status_effect
   * const user_status_effect = await prisma.user_status_effect.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_status_effectUpdateArgs>(args: Prisma.SelectSubset<T, user_status_effectUpdateArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_status_effects.
   * @param {user_status_effectDeleteManyArgs} args - Arguments to filter User_status_effects to delete.
   * @example
   * // Delete a few User_status_effects
   * const { count } = await prisma.user_status_effect.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_status_effectDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_status_effectDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_status_effects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_status_effects
   * const user_status_effect = await prisma.user_status_effect.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_status_effectUpdateManyArgs>(args: Prisma.SelectSubset<T, user_status_effectUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_status_effect.
   * @param {user_status_effectUpsertArgs} args - Arguments to update or create a User_status_effect.
   * @example
   * // Update or create a User_status_effect
   * const user_status_effect = await prisma.user_status_effect.upsert({
   *   create: {
   *     // ... data to create a User_status_effect
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_status_effect we want to update
   *   }
   * })
   */
  upsert<T extends user_status_effectUpsertArgs>(args: Prisma.SelectSubset<T, user_status_effectUpsertArgs<ExtArgs>>): Prisma.Prisma__user_status_effectClient<runtime.Types.Result.GetResult<Prisma.$user_status_effectPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_status_effects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectCountArgs} args - Arguments to filter User_status_effects to count.
   * @example
   * // Count the number of User_status_effects
   * const count = await prisma.user_status_effect.count({
   *   where: {
   *     // ... the filter for the User_status_effects we want to count
   *   }
   * })
  **/
  count<T extends user_status_effectCountArgs>(
    args?: Prisma.Subset<T, user_status_effectCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_status_effectCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_status_effect.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_status_effectAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_status_effectAggregateArgs>(args: Prisma.Subset<T, User_status_effectAggregateArgs>): Prisma.PrismaPromise<GetUser_status_effectAggregateType<T>>

  /**
   * Group by User_status_effect.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_status_effectGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_status_effectGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_status_effectGroupByArgs['orderBy'] }
      : { orderBy?: user_status_effectGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_status_effectGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_status_effectGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_status_effect model
 */
readonly fields: user_status_effectFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_status_effect.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_status_effectClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  effect<T extends Prisma.user_status_effect$effectArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_status_effect$effectArgs<ExtArgs>>): Prisma.Prisma__status_effectClient<runtime.Types.Result.GetResult<Prisma.$status_effectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.user_status_effect$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.user_status_effect$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_status_effect model
 */
export interface user_status_effectFieldRefs {
  readonly id: Prisma.FieldRef<"user_status_effect", 'Int'>
  readonly stacks: Prisma.FieldRef<"user_status_effect", 'Int'>
  readonly endsAt: Prisma.FieldRef<"user_status_effect", 'BigInt'>
  readonly customName: Prisma.FieldRef<"user_status_effect", 'String'>
  readonly createdAt: Prisma.FieldRef<"user_status_effect", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_status_effect", 'DateTime'>
  readonly effectId: Prisma.FieldRef<"user_status_effect", 'Int'>
  readonly userId: Prisma.FieldRef<"user_status_effect", 'Int'>
}
    

// Custom InputTypes
/**
 * user_status_effect findUnique
 */
export type user_status_effectFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter, which user_status_effect to fetch.
   */
  where: Prisma.user_status_effectWhereUniqueInput
}

/**
 * user_status_effect findUniqueOrThrow
 */
export type user_status_effectFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter, which user_status_effect to fetch.
   */
  where: Prisma.user_status_effectWhereUniqueInput
}

/**
 * user_status_effect findFirst
 */
export type user_status_effectFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter, which user_status_effect to fetch.
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_status_effects to fetch.
   */
  orderBy?: Prisma.user_status_effectOrderByWithRelationInput | Prisma.user_status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_status_effects.
   */
  cursor?: Prisma.user_status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_status_effects.
   */
  distinct?: Prisma.User_status_effectScalarFieldEnum | Prisma.User_status_effectScalarFieldEnum[]
}

/**
 * user_status_effect findFirstOrThrow
 */
export type user_status_effectFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter, which user_status_effect to fetch.
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_status_effects to fetch.
   */
  orderBy?: Prisma.user_status_effectOrderByWithRelationInput | Prisma.user_status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_status_effects.
   */
  cursor?: Prisma.user_status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_status_effects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_status_effects.
   */
  distinct?: Prisma.User_status_effectScalarFieldEnum | Prisma.User_status_effectScalarFieldEnum[]
}

/**
 * user_status_effect findMany
 */
export type user_status_effectFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter, which user_status_effects to fetch.
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_status_effects to fetch.
   */
  orderBy?: Prisma.user_status_effectOrderByWithRelationInput | Prisma.user_status_effectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_status_effects.
   */
  cursor?: Prisma.user_status_effectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_status_effects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_status_effects.
   */
  skip?: number
  distinct?: Prisma.User_status_effectScalarFieldEnum | Prisma.User_status_effectScalarFieldEnum[]
}

/**
 * user_status_effect create
 */
export type user_status_effectCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * The data needed to create a user_status_effect.
   */
  data: Prisma.XOR<Prisma.user_status_effectCreateInput, Prisma.user_status_effectUncheckedCreateInput>
}

/**
 * user_status_effect createMany
 */
export type user_status_effectCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_status_effects.
   */
  data: Prisma.user_status_effectCreateManyInput | Prisma.user_status_effectCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_status_effect update
 */
export type user_status_effectUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * The data needed to update a user_status_effect.
   */
  data: Prisma.XOR<Prisma.user_status_effectUpdateInput, Prisma.user_status_effectUncheckedUpdateInput>
  /**
   * Choose, which user_status_effect to update.
   */
  where: Prisma.user_status_effectWhereUniqueInput
}

/**
 * user_status_effect updateMany
 */
export type user_status_effectUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_status_effects.
   */
  data: Prisma.XOR<Prisma.user_status_effectUpdateManyMutationInput, Prisma.user_status_effectUncheckedUpdateManyInput>
  /**
   * Filter which user_status_effects to update
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * Limit how many user_status_effects to update.
   */
  limit?: number
}

/**
 * user_status_effect upsert
 */
export type user_status_effectUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * The filter to search for the user_status_effect to update in case it exists.
   */
  where: Prisma.user_status_effectWhereUniqueInput
  /**
   * In case the user_status_effect found by the `where` argument doesn't exist, create a new user_status_effect with this data.
   */
  create: Prisma.XOR<Prisma.user_status_effectCreateInput, Prisma.user_status_effectUncheckedCreateInput>
  /**
   * In case the user_status_effect was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_status_effectUpdateInput, Prisma.user_status_effectUncheckedUpdateInput>
}

/**
 * user_status_effect delete
 */
export type user_status_effectDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
  /**
   * Filter which user_status_effect to delete.
   */
  where: Prisma.user_status_effectWhereUniqueInput
}

/**
 * user_status_effect deleteMany
 */
export type user_status_effectDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_status_effects to delete
   */
  where?: Prisma.user_status_effectWhereInput
  /**
   * Limit how many user_status_effects to delete.
   */
  limit?: number
}

/**
 * user_status_effect.effect
 */
export type user_status_effect$effectArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the status_effect
   */
  select?: Prisma.status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the status_effect
   */
  omit?: Prisma.status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.status_effectInclude<ExtArgs> | null
  where?: Prisma.status_effectWhereInput
}

/**
 * user_status_effect.user
 */
export type user_status_effect$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * user_status_effect without action
 */
export type user_status_effectDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_status_effect
   */
  select?: Prisma.user_status_effectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_status_effect
   */
  omit?: Prisma.user_status_effectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_status_effectInclude<ExtArgs> | null
}
