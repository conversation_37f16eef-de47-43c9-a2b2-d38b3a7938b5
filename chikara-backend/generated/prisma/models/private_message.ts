
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `private_message` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model private_message
 * 
 */
export type private_messageModel = runtime.Types.Result.DefaultSelection<Prisma.$private_messagePayload>

export type AggregatePrivate_message = {
  _count: Private_messageCountAggregateOutputType | null
  _avg: Private_messageAvgAggregateOutputType | null
  _sum: Private_messageSumAggregateOutputType | null
  _min: Private_messageMinAggregateOutputType | null
  _max: Private_messageMaxAggregateOutputType | null
}

export type Private_messageAvgAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Private_messageSumAggregateOutputType = {
  id: number | null
  senderId: number | null
  receiverId: number | null
}

export type Private_messageMinAggregateOutputType = {
  id: number | null
  message: string | null
  read: boolean | null
  isGlobal: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Private_messageMaxAggregateOutputType = {
  id: number | null
  message: string | null
  read: boolean | null
  isGlobal: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  senderId: number | null
  receiverId: number | null
}

export type Private_messageCountAggregateOutputType = {
  id: number
  message: number
  read: number
  isGlobal: number
  createdAt: number
  updatedAt: number
  senderId: number
  receiverId: number
  _all: number
}


export type Private_messageAvgAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Private_messageSumAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
}

export type Private_messageMinAggregateInputType = {
  id?: true
  message?: true
  read?: true
  isGlobal?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Private_messageMaxAggregateInputType = {
  id?: true
  message?: true
  read?: true
  isGlobal?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
}

export type Private_messageCountAggregateInputType = {
  id?: true
  message?: true
  read?: true
  isGlobal?: true
  createdAt?: true
  updatedAt?: true
  senderId?: true
  receiverId?: true
  _all?: true
}

export type Private_messageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which private_message to aggregate.
   */
  where?: Prisma.private_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of private_messages to fetch.
   */
  orderBy?: Prisma.private_messageOrderByWithRelationInput | Prisma.private_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.private_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` private_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` private_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned private_messages
  **/
  _count?: true | Private_messageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Private_messageAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Private_messageSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Private_messageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Private_messageMaxAggregateInputType
}

export type GetPrivate_messageAggregateType<T extends Private_messageAggregateArgs> = {
      [P in keyof T & keyof AggregatePrivate_message]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePrivate_message[P]>
    : Prisma.GetScalarType<T[P], AggregatePrivate_message[P]>
}




export type private_messageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.private_messageWhereInput
  orderBy?: Prisma.private_messageOrderByWithAggregationInput | Prisma.private_messageOrderByWithAggregationInput[]
  by: Prisma.Private_messageScalarFieldEnum[] | Prisma.Private_messageScalarFieldEnum
  having?: Prisma.private_messageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Private_messageCountAggregateInputType | true
  _avg?: Private_messageAvgAggregateInputType
  _sum?: Private_messageSumAggregateInputType
  _min?: Private_messageMinAggregateInputType
  _max?: Private_messageMaxAggregateInputType
}

export type Private_messageGroupByOutputType = {
  id: number
  message: string
  read: boolean | null
  isGlobal: boolean | null
  createdAt: Date
  updatedAt: Date
  senderId: number | null
  receiverId: number | null
  _count: Private_messageCountAggregateOutputType | null
  _avg: Private_messageAvgAggregateOutputType | null
  _sum: Private_messageSumAggregateOutputType | null
  _min: Private_messageMinAggregateOutputType | null
  _max: Private_messageMaxAggregateOutputType | null
}

type GetPrivate_messageGroupByPayload<T extends private_messageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Private_messageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Private_messageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Private_messageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Private_messageGroupByOutputType[P]>
      }
    >
  >



export type private_messageWhereInput = {
  AND?: Prisma.private_messageWhereInput | Prisma.private_messageWhereInput[]
  OR?: Prisma.private_messageWhereInput[]
  NOT?: Prisma.private_messageWhereInput | Prisma.private_messageWhereInput[]
  id?: Prisma.IntFilter<"private_message"> | number
  message?: Prisma.StringFilter<"private_message"> | string
  read?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  isGlobal?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  senderId?: Prisma.IntNullableFilter<"private_message"> | number | null
  receiverId?: Prisma.IntNullableFilter<"private_message"> | number | null
  user_private_message_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_private_message_receiverIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type private_messageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  read?: Prisma.SortOrderInput | Prisma.SortOrder
  isGlobal?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  receiverId?: Prisma.SortOrderInput | Prisma.SortOrder
  user_private_message_senderIdTouser?: Prisma.userOrderByWithRelationInput
  user_private_message_receiverIdTouser?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.private_messageOrderByRelevanceInput
}

export type private_messageWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.private_messageWhereInput | Prisma.private_messageWhereInput[]
  OR?: Prisma.private_messageWhereInput[]
  NOT?: Prisma.private_messageWhereInput | Prisma.private_messageWhereInput[]
  message?: Prisma.StringFilter<"private_message"> | string
  read?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  isGlobal?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  senderId?: Prisma.IntNullableFilter<"private_message"> | number | null
  receiverId?: Prisma.IntNullableFilter<"private_message"> | number | null
  user_private_message_senderIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user_private_message_receiverIdTouser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type private_messageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  read?: Prisma.SortOrderInput | Prisma.SortOrder
  isGlobal?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrderInput | Prisma.SortOrder
  receiverId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.private_messageCountOrderByAggregateInput
  _avg?: Prisma.private_messageAvgOrderByAggregateInput
  _max?: Prisma.private_messageMaxOrderByAggregateInput
  _min?: Prisma.private_messageMinOrderByAggregateInput
  _sum?: Prisma.private_messageSumOrderByAggregateInput
}

export type private_messageScalarWhereWithAggregatesInput = {
  AND?: Prisma.private_messageScalarWhereWithAggregatesInput | Prisma.private_messageScalarWhereWithAggregatesInput[]
  OR?: Prisma.private_messageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.private_messageScalarWhereWithAggregatesInput | Prisma.private_messageScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"private_message"> | number
  message?: Prisma.StringWithAggregatesFilter<"private_message"> | string
  read?: Prisma.BoolNullableWithAggregatesFilter<"private_message"> | boolean | null
  isGlobal?: Prisma.BoolNullableWithAggregatesFilter<"private_message"> | boolean | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"private_message"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"private_message"> | Date | string
  senderId?: Prisma.IntNullableWithAggregatesFilter<"private_message"> | number | null
  receiverId?: Prisma.IntNullableWithAggregatesFilter<"private_message"> | number | null
}

export type private_messageCreateInput = {
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_private_message_senderIdTouser?: Prisma.userCreateNestedOneWithoutPrivate_message_private_message_senderIdTouserInput
  user_private_message_receiverIdTouser?: Prisma.userCreateNestedOneWithoutPrivate_message_private_message_receiverIdTouserInput
}

export type private_messageUncheckedCreateInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  receiverId?: number | null
}

export type private_messageUpdateInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_private_message_senderIdTouser?: Prisma.userUpdateOneWithoutPrivate_message_private_message_senderIdTouserNestedInput
  user_private_message_receiverIdTouser?: Prisma.userUpdateOneWithoutPrivate_message_private_message_receiverIdTouserNestedInput
}

export type private_messageUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type private_messageCreateManyInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
  receiverId?: number | null
}

export type private_messageUpdateManyMutationInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type private_messageUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type private_messageOrderByRelevanceInput = {
  fields: Prisma.private_messageOrderByRelevanceFieldEnum | Prisma.private_messageOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type private_messageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  read?: Prisma.SortOrder
  isGlobal?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type private_messageAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type private_messageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  read?: Prisma.SortOrder
  isGlobal?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type private_messageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  read?: Prisma.SortOrder
  isGlobal?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type private_messageSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
}

export type Private_messageListRelationFilter = {
  every?: Prisma.private_messageWhereInput
  some?: Prisma.private_messageWhereInput
  none?: Prisma.private_messageWhereInput
}

export type private_messageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type private_messageCreateNestedManyWithoutUser_private_message_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_senderIdTouserInputEnvelope
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
}

export type private_messageCreateNestedManyWithoutUser_private_message_receiverIdTouserInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInputEnvelope
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
}

export type private_messageUncheckedCreateNestedManyWithoutUser_private_message_senderIdTouserInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_senderIdTouserInputEnvelope
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
}

export type private_messageUncheckedCreateNestedManyWithoutUser_private_message_receiverIdTouserInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInputEnvelope
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
}

export type private_messageUpdateManyWithoutUser_private_message_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput[]
  upsert?: Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_senderIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_senderIdTouserInputEnvelope
  set?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  disconnect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  delete?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  update?: Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_senderIdTouserInput[]
  updateMany?: Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_senderIdTouserInput[]
  deleteMany?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
}

export type private_messageUpdateManyWithoutUser_private_message_receiverIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput[]
  upsert?: Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInputEnvelope
  set?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  disconnect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  delete?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  update?: Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput[]
  updateMany?: Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_receiverIdTouserInput[]
  deleteMany?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
}

export type private_messageUncheckedUpdateManyWithoutUser_private_message_senderIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput[]
  upsert?: Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_senderIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_senderIdTouserInputEnvelope
  set?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  disconnect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  delete?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  update?: Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_senderIdTouserInput[]
  updateMany?: Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_senderIdTouserInput | Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_senderIdTouserInput[]
  deleteMany?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
}

export type private_messageUncheckedUpdateManyWithoutUser_private_message_receiverIdTouserNestedInput = {
  create?: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput> | Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput[] | Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput[]
  connectOrCreate?: Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput[]
  upsert?: Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpsertWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput[]
  createMany?: Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInputEnvelope
  set?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  disconnect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  delete?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  connect?: Prisma.private_messageWhereUniqueInput | Prisma.private_messageWhereUniqueInput[]
  update?: Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpdateWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput[]
  updateMany?: Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_receiverIdTouserInput | Prisma.private_messageUpdateManyWithWhereWithoutUser_private_message_receiverIdTouserInput[]
  deleteMany?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
}

export type private_messageCreateWithoutUser_private_message_senderIdTouserInput = {
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_private_message_receiverIdTouser?: Prisma.userCreateNestedOneWithoutPrivate_message_private_message_receiverIdTouserInput
}

export type private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId?: number | null
}

export type private_messageCreateOrConnectWithoutUser_private_message_senderIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput>
}

export type private_messageCreateManyUser_private_message_senderIdTouserInputEnvelope = {
  data: Prisma.private_messageCreateManyUser_private_message_senderIdTouserInput | Prisma.private_messageCreateManyUser_private_message_senderIdTouserInput[]
  skipDuplicates?: boolean
}

export type private_messageCreateWithoutUser_private_message_receiverIdTouserInput = {
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user_private_message_senderIdTouser?: Prisma.userCreateNestedOneWithoutPrivate_message_private_message_senderIdTouserInput
}

export type private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
}

export type private_messageCreateOrConnectWithoutUser_private_message_receiverIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput>
}

export type private_messageCreateManyUser_private_message_receiverIdTouserInputEnvelope = {
  data: Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInput | Prisma.private_messageCreateManyUser_private_message_receiverIdTouserInput[]
  skipDuplicates?: boolean
}

export type private_messageUpsertWithWhereUniqueWithoutUser_private_message_senderIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  update: Prisma.XOR<Prisma.private_messageUpdateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedUpdateWithoutUser_private_message_senderIdTouserInput>
  create: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_senderIdTouserInput>
}

export type private_messageUpdateWithWhereUniqueWithoutUser_private_message_senderIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  data: Prisma.XOR<Prisma.private_messageUpdateWithoutUser_private_message_senderIdTouserInput, Prisma.private_messageUncheckedUpdateWithoutUser_private_message_senderIdTouserInput>
}

export type private_messageUpdateManyWithWhereWithoutUser_private_message_senderIdTouserInput = {
  where: Prisma.private_messageScalarWhereInput
  data: Prisma.XOR<Prisma.private_messageUpdateManyMutationInput, Prisma.private_messageUncheckedUpdateManyWithoutUser_private_message_senderIdTouserInput>
}

export type private_messageScalarWhereInput = {
  AND?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
  OR?: Prisma.private_messageScalarWhereInput[]
  NOT?: Prisma.private_messageScalarWhereInput | Prisma.private_messageScalarWhereInput[]
  id?: Prisma.IntFilter<"private_message"> | number
  message?: Prisma.StringFilter<"private_message"> | string
  read?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  isGlobal?: Prisma.BoolNullableFilter<"private_message"> | boolean | null
  createdAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"private_message"> | Date | string
  senderId?: Prisma.IntNullableFilter<"private_message"> | number | null
  receiverId?: Prisma.IntNullableFilter<"private_message"> | number | null
}

export type private_messageUpsertWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  update: Prisma.XOR<Prisma.private_messageUpdateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedUpdateWithoutUser_private_message_receiverIdTouserInput>
  create: Prisma.XOR<Prisma.private_messageCreateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedCreateWithoutUser_private_message_receiverIdTouserInput>
}

export type private_messageUpdateWithWhereUniqueWithoutUser_private_message_receiverIdTouserInput = {
  where: Prisma.private_messageWhereUniqueInput
  data: Prisma.XOR<Prisma.private_messageUpdateWithoutUser_private_message_receiverIdTouserInput, Prisma.private_messageUncheckedUpdateWithoutUser_private_message_receiverIdTouserInput>
}

export type private_messageUpdateManyWithWhereWithoutUser_private_message_receiverIdTouserInput = {
  where: Prisma.private_messageScalarWhereInput
  data: Prisma.XOR<Prisma.private_messageUpdateManyMutationInput, Prisma.private_messageUncheckedUpdateManyWithoutUser_private_message_receiverIdTouserInput>
}

export type private_messageCreateManyUser_private_message_senderIdTouserInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  receiverId?: number | null
}

export type private_messageCreateManyUser_private_message_receiverIdTouserInput = {
  id?: number
  message: string
  read?: boolean | null
  isGlobal?: boolean | null
  createdAt?: Date | string
  updatedAt?: Date | string
  senderId?: number | null
}

export type private_messageUpdateWithoutUser_private_message_senderIdTouserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_private_message_receiverIdTouser?: Prisma.userUpdateOneWithoutPrivate_message_private_message_receiverIdTouserNestedInput
}

export type private_messageUncheckedUpdateWithoutUser_private_message_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type private_messageUncheckedUpdateManyWithoutUser_private_message_senderIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiverId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type private_messageUpdateWithoutUser_private_message_receiverIdTouserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user_private_message_senderIdTouser?: Prisma.userUpdateOneWithoutPrivate_message_private_message_senderIdTouserNestedInput
}

export type private_messageUncheckedUpdateWithoutUser_private_message_receiverIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type private_messageUncheckedUpdateManyWithoutUser_private_message_receiverIdTouserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  read?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  isGlobal?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  senderId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type private_messageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  message?: boolean
  read?: boolean
  isGlobal?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
  user_private_message_senderIdTouser?: boolean | Prisma.private_message$user_private_message_senderIdTouserArgs<ExtArgs>
  user_private_message_receiverIdTouser?: boolean | Prisma.private_message$user_private_message_receiverIdTouserArgs<ExtArgs>
}, ExtArgs["result"]["private_message"]>



export type private_messageSelectScalar = {
  id?: boolean
  message?: boolean
  read?: boolean
  isGlobal?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  senderId?: boolean
  receiverId?: boolean
}

export type private_messageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "message" | "read" | "isGlobal" | "createdAt" | "updatedAt" | "senderId" | "receiverId", ExtArgs["result"]["private_message"]>
export type private_messageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user_private_message_senderIdTouser?: boolean | Prisma.private_message$user_private_message_senderIdTouserArgs<ExtArgs>
  user_private_message_receiverIdTouser?: boolean | Prisma.private_message$user_private_message_receiverIdTouserArgs<ExtArgs>
}

export type $private_messagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "private_message"
  objects: {
    user_private_message_senderIdTouser: Prisma.$userPayload<ExtArgs> | null
    user_private_message_receiverIdTouser: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    message: string
    read: boolean | null
    isGlobal: boolean | null
    createdAt: Date
    updatedAt: Date
    senderId: number | null
    receiverId: number | null
  }, ExtArgs["result"]["private_message"]>
  composites: {}
}

export type private_messageGetPayload<S extends boolean | null | undefined | private_messageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$private_messagePayload, S>

export type private_messageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<private_messageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Private_messageCountAggregateInputType | true
  }

export interface private_messageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['private_message'], meta: { name: 'private_message' } }
  /**
   * Find zero or one Private_message that matches the filter.
   * @param {private_messageFindUniqueArgs} args - Arguments to find a Private_message
   * @example
   * // Get one Private_message
   * const private_message = await prisma.private_message.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends private_messageFindUniqueArgs>(args: Prisma.SelectSubset<T, private_messageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Private_message that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {private_messageFindUniqueOrThrowArgs} args - Arguments to find a Private_message
   * @example
   * // Get one Private_message
   * const private_message = await prisma.private_message.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends private_messageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, private_messageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Private_message that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageFindFirstArgs} args - Arguments to find a Private_message
   * @example
   * // Get one Private_message
   * const private_message = await prisma.private_message.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends private_messageFindFirstArgs>(args?: Prisma.SelectSubset<T, private_messageFindFirstArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Private_message that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageFindFirstOrThrowArgs} args - Arguments to find a Private_message
   * @example
   * // Get one Private_message
   * const private_message = await prisma.private_message.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends private_messageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, private_messageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Private_messages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Private_messages
   * const private_messages = await prisma.private_message.findMany()
   * 
   * // Get first 10 Private_messages
   * const private_messages = await prisma.private_message.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const private_messageWithIdOnly = await prisma.private_message.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends private_messageFindManyArgs>(args?: Prisma.SelectSubset<T, private_messageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Private_message.
   * @param {private_messageCreateArgs} args - Arguments to create a Private_message.
   * @example
   * // Create one Private_message
   * const Private_message = await prisma.private_message.create({
   *   data: {
   *     // ... data to create a Private_message
   *   }
   * })
   * 
   */
  create<T extends private_messageCreateArgs>(args: Prisma.SelectSubset<T, private_messageCreateArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Private_messages.
   * @param {private_messageCreateManyArgs} args - Arguments to create many Private_messages.
   * @example
   * // Create many Private_messages
   * const private_message = await prisma.private_message.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends private_messageCreateManyArgs>(args?: Prisma.SelectSubset<T, private_messageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Private_message.
   * @param {private_messageDeleteArgs} args - Arguments to delete one Private_message.
   * @example
   * // Delete one Private_message
   * const Private_message = await prisma.private_message.delete({
   *   where: {
   *     // ... filter to delete one Private_message
   *   }
   * })
   * 
   */
  delete<T extends private_messageDeleteArgs>(args: Prisma.SelectSubset<T, private_messageDeleteArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Private_message.
   * @param {private_messageUpdateArgs} args - Arguments to update one Private_message.
   * @example
   * // Update one Private_message
   * const private_message = await prisma.private_message.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends private_messageUpdateArgs>(args: Prisma.SelectSubset<T, private_messageUpdateArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Private_messages.
   * @param {private_messageDeleteManyArgs} args - Arguments to filter Private_messages to delete.
   * @example
   * // Delete a few Private_messages
   * const { count } = await prisma.private_message.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends private_messageDeleteManyArgs>(args?: Prisma.SelectSubset<T, private_messageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Private_messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Private_messages
   * const private_message = await prisma.private_message.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends private_messageUpdateManyArgs>(args: Prisma.SelectSubset<T, private_messageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Private_message.
   * @param {private_messageUpsertArgs} args - Arguments to update or create a Private_message.
   * @example
   * // Update or create a Private_message
   * const private_message = await prisma.private_message.upsert({
   *   create: {
   *     // ... data to create a Private_message
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Private_message we want to update
   *   }
   * })
   */
  upsert<T extends private_messageUpsertArgs>(args: Prisma.SelectSubset<T, private_messageUpsertArgs<ExtArgs>>): Prisma.Prisma__private_messageClient<runtime.Types.Result.GetResult<Prisma.$private_messagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Private_messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageCountArgs} args - Arguments to filter Private_messages to count.
   * @example
   * // Count the number of Private_messages
   * const count = await prisma.private_message.count({
   *   where: {
   *     // ... the filter for the Private_messages we want to count
   *   }
   * })
  **/
  count<T extends private_messageCountArgs>(
    args?: Prisma.Subset<T, private_messageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Private_messageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Private_message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Private_messageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Private_messageAggregateArgs>(args: Prisma.Subset<T, Private_messageAggregateArgs>): Prisma.PrismaPromise<GetPrivate_messageAggregateType<T>>

  /**
   * Group by Private_message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {private_messageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends private_messageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: private_messageGroupByArgs['orderBy'] }
      : { orderBy?: private_messageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, private_messageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPrivate_messageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the private_message model
 */
readonly fields: private_messageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for private_message.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__private_messageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user_private_message_senderIdTouser<T extends Prisma.private_message$user_private_message_senderIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.private_message$user_private_message_senderIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user_private_message_receiverIdTouser<T extends Prisma.private_message$user_private_message_receiverIdTouserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.private_message$user_private_message_receiverIdTouserArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the private_message model
 */
export interface private_messageFieldRefs {
  readonly id: Prisma.FieldRef<"private_message", 'Int'>
  readonly message: Prisma.FieldRef<"private_message", 'String'>
  readonly read: Prisma.FieldRef<"private_message", 'Boolean'>
  readonly isGlobal: Prisma.FieldRef<"private_message", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"private_message", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"private_message", 'DateTime'>
  readonly senderId: Prisma.FieldRef<"private_message", 'Int'>
  readonly receiverId: Prisma.FieldRef<"private_message", 'Int'>
}
    

// Custom InputTypes
/**
 * private_message findUnique
 */
export type private_messageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter, which private_message to fetch.
   */
  where: Prisma.private_messageWhereUniqueInput
}

/**
 * private_message findUniqueOrThrow
 */
export type private_messageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter, which private_message to fetch.
   */
  where: Prisma.private_messageWhereUniqueInput
}

/**
 * private_message findFirst
 */
export type private_messageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter, which private_message to fetch.
   */
  where?: Prisma.private_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of private_messages to fetch.
   */
  orderBy?: Prisma.private_messageOrderByWithRelationInput | Prisma.private_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for private_messages.
   */
  cursor?: Prisma.private_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` private_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` private_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of private_messages.
   */
  distinct?: Prisma.Private_messageScalarFieldEnum | Prisma.Private_messageScalarFieldEnum[]
}

/**
 * private_message findFirstOrThrow
 */
export type private_messageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter, which private_message to fetch.
   */
  where?: Prisma.private_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of private_messages to fetch.
   */
  orderBy?: Prisma.private_messageOrderByWithRelationInput | Prisma.private_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for private_messages.
   */
  cursor?: Prisma.private_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` private_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` private_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of private_messages.
   */
  distinct?: Prisma.Private_messageScalarFieldEnum | Prisma.Private_messageScalarFieldEnum[]
}

/**
 * private_message findMany
 */
export type private_messageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter, which private_messages to fetch.
   */
  where?: Prisma.private_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of private_messages to fetch.
   */
  orderBy?: Prisma.private_messageOrderByWithRelationInput | Prisma.private_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing private_messages.
   */
  cursor?: Prisma.private_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` private_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` private_messages.
   */
  skip?: number
  distinct?: Prisma.Private_messageScalarFieldEnum | Prisma.Private_messageScalarFieldEnum[]
}

/**
 * private_message create
 */
export type private_messageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * The data needed to create a private_message.
   */
  data: Prisma.XOR<Prisma.private_messageCreateInput, Prisma.private_messageUncheckedCreateInput>
}

/**
 * private_message createMany
 */
export type private_messageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many private_messages.
   */
  data: Prisma.private_messageCreateManyInput | Prisma.private_messageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * private_message update
 */
export type private_messageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * The data needed to update a private_message.
   */
  data: Prisma.XOR<Prisma.private_messageUpdateInput, Prisma.private_messageUncheckedUpdateInput>
  /**
   * Choose, which private_message to update.
   */
  where: Prisma.private_messageWhereUniqueInput
}

/**
 * private_message updateMany
 */
export type private_messageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update private_messages.
   */
  data: Prisma.XOR<Prisma.private_messageUpdateManyMutationInput, Prisma.private_messageUncheckedUpdateManyInput>
  /**
   * Filter which private_messages to update
   */
  where?: Prisma.private_messageWhereInput
  /**
   * Limit how many private_messages to update.
   */
  limit?: number
}

/**
 * private_message upsert
 */
export type private_messageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * The filter to search for the private_message to update in case it exists.
   */
  where: Prisma.private_messageWhereUniqueInput
  /**
   * In case the private_message found by the `where` argument doesn't exist, create a new private_message with this data.
   */
  create: Prisma.XOR<Prisma.private_messageCreateInput, Prisma.private_messageUncheckedCreateInput>
  /**
   * In case the private_message was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.private_messageUpdateInput, Prisma.private_messageUncheckedUpdateInput>
}

/**
 * private_message delete
 */
export type private_messageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
  /**
   * Filter which private_message to delete.
   */
  where: Prisma.private_messageWhereUniqueInput
}

/**
 * private_message deleteMany
 */
export type private_messageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which private_messages to delete
   */
  where?: Prisma.private_messageWhereInput
  /**
   * Limit how many private_messages to delete.
   */
  limit?: number
}

/**
 * private_message.user_private_message_senderIdTouser
 */
export type private_message$user_private_message_senderIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * private_message.user_private_message_receiverIdTouser
 */
export type private_message$user_private_message_receiverIdTouserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * private_message without action
 */
export type private_messageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the private_message
   */
  select?: Prisma.private_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the private_message
   */
  omit?: Prisma.private_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.private_messageInclude<ExtArgs> | null
}
