
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `chat_message` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model chat_message
 * 
 */
export type chat_messageModel = runtime.Types.Result.DefaultSelection<Prisma.$chat_messagePayload>

export type AggregateChat_message = {
  _count: Chat_messageCountAggregateOutputType | null
  _avg: Chat_messageAvgAggregateOutputType | null
  _sum: Chat_messageSumAggregateOutputType | null
  _min: Chat_messageMinAggregateOutputType | null
  _max: Chat_messageMaxAggregateOutputType | null
}

export type Chat_messageAvgAggregateOutputType = {
  id: number | null
  chatRoomId: number | null
  userId: number | null
  parentMessageId: number | null
}

export type Chat_messageSumAggregateOutputType = {
  id: number | null
  chatRoomId: number | null
  userId: number | null
  parentMessageId: number | null
}

export type Chat_messageMinAggregateOutputType = {
  id: number | null
  message: string | null
  hidden: boolean | null
  announcementType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  chatRoomId: number | null
  userId: number | null
  parentMessageId: number | null
}

export type Chat_messageMaxAggregateOutputType = {
  id: number | null
  message: string | null
  hidden: boolean | null
  announcementType: string | null
  createdAt: Date | null
  updatedAt: Date | null
  chatRoomId: number | null
  userId: number | null
  parentMessageId: number | null
}

export type Chat_messageCountAggregateOutputType = {
  id: number
  message: number
  hidden: number
  announcementType: number
  createdAt: number
  updatedAt: number
  chatRoomId: number
  userId: number
  parentMessageId: number
  _all: number
}


export type Chat_messageAvgAggregateInputType = {
  id?: true
  chatRoomId?: true
  userId?: true
  parentMessageId?: true
}

export type Chat_messageSumAggregateInputType = {
  id?: true
  chatRoomId?: true
  userId?: true
  parentMessageId?: true
}

export type Chat_messageMinAggregateInputType = {
  id?: true
  message?: true
  hidden?: true
  announcementType?: true
  createdAt?: true
  updatedAt?: true
  chatRoomId?: true
  userId?: true
  parentMessageId?: true
}

export type Chat_messageMaxAggregateInputType = {
  id?: true
  message?: true
  hidden?: true
  announcementType?: true
  createdAt?: true
  updatedAt?: true
  chatRoomId?: true
  userId?: true
  parentMessageId?: true
}

export type Chat_messageCountAggregateInputType = {
  id?: true
  message?: true
  hidden?: true
  announcementType?: true
  createdAt?: true
  updatedAt?: true
  chatRoomId?: true
  userId?: true
  parentMessageId?: true
  _all?: true
}

export type Chat_messageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which chat_message to aggregate.
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_messages to fetch.
   */
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.chat_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned chat_messages
  **/
  _count?: true | Chat_messageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Chat_messageAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Chat_messageSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Chat_messageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Chat_messageMaxAggregateInputType
}

export type GetChat_messageAggregateType<T extends Chat_messageAggregateArgs> = {
      [P in keyof T & keyof AggregateChat_message]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateChat_message[P]>
    : Prisma.GetScalarType<T[P], AggregateChat_message[P]>
}




export type chat_messageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.chat_messageWhereInput
  orderBy?: Prisma.chat_messageOrderByWithAggregationInput | Prisma.chat_messageOrderByWithAggregationInput[]
  by: Prisma.Chat_messageScalarFieldEnum[] | Prisma.Chat_messageScalarFieldEnum
  having?: Prisma.chat_messageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Chat_messageCountAggregateInputType | true
  _avg?: Chat_messageAvgAggregateInputType
  _sum?: Chat_messageSumAggregateInputType
  _min?: Chat_messageMinAggregateInputType
  _max?: Chat_messageMaxAggregateInputType
}

export type Chat_messageGroupByOutputType = {
  id: number
  message: string
  hidden: boolean | null
  announcementType: string | null
  createdAt: Date
  updatedAt: Date
  chatRoomId: number | null
  userId: number | null
  parentMessageId: number | null
  _count: Chat_messageCountAggregateOutputType | null
  _avg: Chat_messageAvgAggregateOutputType | null
  _sum: Chat_messageSumAggregateOutputType | null
  _min: Chat_messageMinAggregateOutputType | null
  _max: Chat_messageMaxAggregateOutputType | null
}

type GetChat_messageGroupByPayload<T extends chat_messageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Chat_messageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Chat_messageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Chat_messageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Chat_messageGroupByOutputType[P]>
      }
    >
  >



export type chat_messageWhereInput = {
  AND?: Prisma.chat_messageWhereInput | Prisma.chat_messageWhereInput[]
  OR?: Prisma.chat_messageWhereInput[]
  NOT?: Prisma.chat_messageWhereInput | Prisma.chat_messageWhereInput[]
  id?: Prisma.IntFilter<"chat_message"> | number
  message?: Prisma.StringFilter<"chat_message"> | string
  hidden?: Prisma.BoolNullableFilter<"chat_message"> | boolean | null
  announcementType?: Prisma.StringNullableFilter<"chat_message"> | string | null
  createdAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  chatRoomId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  userId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  parentMessageId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  chat_room?: Prisma.XOR<Prisma.Chat_roomNullableScalarRelationFilter, Prisma.chat_roomWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  chat_message?: Prisma.XOR<Prisma.Chat_messageNullableScalarRelationFilter, Prisma.chat_messageWhereInput> | null
  parent_message?: Prisma.Chat_messageListRelationFilter
}

export type chat_messageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  hidden?: Prisma.SortOrderInput | Prisma.SortOrder
  announcementType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  parentMessageId?: Prisma.SortOrderInput | Prisma.SortOrder
  chat_room?: Prisma.chat_roomOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
  chat_message?: Prisma.chat_messageOrderByWithRelationInput
  parent_message?: Prisma.chat_messageOrderByRelationAggregateInput
  _relevance?: Prisma.chat_messageOrderByRelevanceInput
}

export type chat_messageWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.chat_messageWhereInput | Prisma.chat_messageWhereInput[]
  OR?: Prisma.chat_messageWhereInput[]
  NOT?: Prisma.chat_messageWhereInput | Prisma.chat_messageWhereInput[]
  message?: Prisma.StringFilter<"chat_message"> | string
  hidden?: Prisma.BoolNullableFilter<"chat_message"> | boolean | null
  announcementType?: Prisma.StringNullableFilter<"chat_message"> | string | null
  createdAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  chatRoomId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  userId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  parentMessageId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  chat_room?: Prisma.XOR<Prisma.Chat_roomNullableScalarRelationFilter, Prisma.chat_roomWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  chat_message?: Prisma.XOR<Prisma.Chat_messageNullableScalarRelationFilter, Prisma.chat_messageWhereInput> | null
  parent_message?: Prisma.Chat_messageListRelationFilter
}, "id">

export type chat_messageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  hidden?: Prisma.SortOrderInput | Prisma.SortOrder
  announcementType?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  parentMessageId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.chat_messageCountOrderByAggregateInput
  _avg?: Prisma.chat_messageAvgOrderByAggregateInput
  _max?: Prisma.chat_messageMaxOrderByAggregateInput
  _min?: Prisma.chat_messageMinOrderByAggregateInput
  _sum?: Prisma.chat_messageSumOrderByAggregateInput
}

export type chat_messageScalarWhereWithAggregatesInput = {
  AND?: Prisma.chat_messageScalarWhereWithAggregatesInput | Prisma.chat_messageScalarWhereWithAggregatesInput[]
  OR?: Prisma.chat_messageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.chat_messageScalarWhereWithAggregatesInput | Prisma.chat_messageScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"chat_message"> | number
  message?: Prisma.StringWithAggregatesFilter<"chat_message"> | string
  hidden?: Prisma.BoolNullableWithAggregatesFilter<"chat_message"> | boolean | null
  announcementType?: Prisma.StringNullableWithAggregatesFilter<"chat_message"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"chat_message"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"chat_message"> | Date | string
  chatRoomId?: Prisma.IntNullableWithAggregatesFilter<"chat_message"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"chat_message"> | number | null
  parentMessageId?: Prisma.IntNullableWithAggregatesFilter<"chat_message"> | number | null
}

export type chat_messageCreateInput = {
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedOneWithoutChat_messageInput
  user?: Prisma.userCreateNestedOneWithoutChat_messageInput
  chat_message?: Prisma.chat_messageCreateNestedOneWithoutParent_messageInput
  parent_message?: Prisma.chat_messageCreateNestedManyWithoutChat_messageInput
}

export type chat_messageUncheckedCreateInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  userId?: number | null
  parentMessageId?: number | null
  parent_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_messageInput
}

export type chat_messageUpdateInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateOneWithoutChat_messageNestedInput
  user?: Prisma.userUpdateOneWithoutChat_messageNestedInput
  chat_message?: Prisma.chat_messageUpdateOneWithoutParent_messageNestedInput
  parent_message?: Prisma.chat_messageUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parent_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageCreateManyInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  userId?: number | null
  parentMessageId?: number | null
}

export type chat_messageUpdateManyMutationInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type chat_messageUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Chat_messageNullableScalarRelationFilter = {
  is?: Prisma.chat_messageWhereInput | null
  isNot?: Prisma.chat_messageWhereInput | null
}

export type Chat_messageListRelationFilter = {
  every?: Prisma.chat_messageWhereInput
  some?: Prisma.chat_messageWhereInput
  none?: Prisma.chat_messageWhereInput
}

export type chat_messageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type chat_messageOrderByRelevanceInput = {
  fields: Prisma.chat_messageOrderByRelevanceFieldEnum | Prisma.chat_messageOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type chat_messageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  hidden?: Prisma.SortOrder
  announcementType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  parentMessageId?: Prisma.SortOrder
}

export type chat_messageAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  parentMessageId?: Prisma.SortOrder
}

export type chat_messageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  hidden?: Prisma.SortOrder
  announcementType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  parentMessageId?: Prisma.SortOrder
}

export type chat_messageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  message?: Prisma.SortOrder
  hidden?: Prisma.SortOrder
  announcementType?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  parentMessageId?: Prisma.SortOrder
}

export type chat_messageSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  chatRoomId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  parentMessageId?: Prisma.SortOrder
}

export type chat_messageCreateNestedOneWithoutParent_messageInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutParent_messageInput, Prisma.chat_messageUncheckedCreateWithoutParent_messageInput>
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutParent_messageInput
  connect?: Prisma.chat_messageWhereUniqueInput
}

export type chat_messageCreateNestedManyWithoutChat_messageInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput> | Prisma.chat_messageCreateWithoutChat_messageInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_messageInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_messageInput | Prisma.chat_messageCreateOrConnectWithoutChat_messageInput[]
  createMany?: Prisma.chat_messageCreateManyChat_messageInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUncheckedCreateNestedManyWithoutChat_messageInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput> | Prisma.chat_messageCreateWithoutChat_messageInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_messageInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_messageInput | Prisma.chat_messageCreateOrConnectWithoutChat_messageInput[]
  createMany?: Prisma.chat_messageCreateManyChat_messageInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUpdateOneWithoutParent_messageNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutParent_messageInput, Prisma.chat_messageUncheckedCreateWithoutParent_messageInput>
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutParent_messageInput
  upsert?: Prisma.chat_messageUpsertWithoutParent_messageInput
  disconnect?: Prisma.chat_messageWhereInput | boolean
  delete?: Prisma.chat_messageWhereInput | boolean
  connect?: Prisma.chat_messageWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.chat_messageUpdateToOneWithWhereWithoutParent_messageInput, Prisma.chat_messageUpdateWithoutParent_messageInput>, Prisma.chat_messageUncheckedUpdateWithoutParent_messageInput>
}

export type chat_messageUpdateManyWithoutChat_messageNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput> | Prisma.chat_messageCreateWithoutChat_messageInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_messageInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_messageInput | Prisma.chat_messageCreateOrConnectWithoutChat_messageInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_messageInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_messageInput[]
  createMany?: Prisma.chat_messageCreateManyChat_messageInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_messageInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_messageInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutChat_messageInput | Prisma.chat_messageUpdateManyWithWhereWithoutChat_messageInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageUncheckedUpdateManyWithoutChat_messageNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput> | Prisma.chat_messageCreateWithoutChat_messageInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_messageInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_messageInput | Prisma.chat_messageCreateOrConnectWithoutChat_messageInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_messageInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_messageInput[]
  createMany?: Prisma.chat_messageCreateManyChat_messageInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_messageInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_messageInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutChat_messageInput | Prisma.chat_messageUpdateManyWithWhereWithoutChat_messageInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageCreateNestedManyWithoutChat_roomInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput> | Prisma.chat_messageCreateWithoutChat_roomInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_roomInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_roomInput | Prisma.chat_messageCreateOrConnectWithoutChat_roomInput[]
  createMany?: Prisma.chat_messageCreateManyChat_roomInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUncheckedCreateNestedManyWithoutChat_roomInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput> | Prisma.chat_messageCreateWithoutChat_roomInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_roomInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_roomInput | Prisma.chat_messageCreateOrConnectWithoutChat_roomInput[]
  createMany?: Prisma.chat_messageCreateManyChat_roomInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUpdateManyWithoutChat_roomNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput> | Prisma.chat_messageCreateWithoutChat_roomInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_roomInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_roomInput | Prisma.chat_messageCreateOrConnectWithoutChat_roomInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_roomInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_roomInput[]
  createMany?: Prisma.chat_messageCreateManyChat_roomInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_roomInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_roomInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutChat_roomInput | Prisma.chat_messageUpdateManyWithWhereWithoutChat_roomInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageUncheckedUpdateManyWithoutChat_roomNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput> | Prisma.chat_messageCreateWithoutChat_roomInput[] | Prisma.chat_messageUncheckedCreateWithoutChat_roomInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutChat_roomInput | Prisma.chat_messageCreateOrConnectWithoutChat_roomInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_roomInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutChat_roomInput[]
  createMany?: Prisma.chat_messageCreateManyChat_roomInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_roomInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutChat_roomInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutChat_roomInput | Prisma.chat_messageUpdateManyWithWhereWithoutChat_roomInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput> | Prisma.chat_messageCreateWithoutUserInput[] | Prisma.chat_messageUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutUserInput | Prisma.chat_messageCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.chat_messageCreateManyUserInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput> | Prisma.chat_messageCreateWithoutUserInput[] | Prisma.chat_messageUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutUserInput | Prisma.chat_messageCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.chat_messageCreateManyUserInputEnvelope
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
}

export type chat_messageUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput> | Prisma.chat_messageCreateWithoutUserInput[] | Prisma.chat_messageUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutUserInput | Prisma.chat_messageCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutUserInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.chat_messageCreateManyUserInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutUserInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutUserInput | Prisma.chat_messageUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput> | Prisma.chat_messageCreateWithoutUserInput[] | Prisma.chat_messageUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.chat_messageCreateOrConnectWithoutUserInput | Prisma.chat_messageCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.chat_messageUpsertWithWhereUniqueWithoutUserInput | Prisma.chat_messageUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.chat_messageCreateManyUserInputEnvelope
  set?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  disconnect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  delete?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  connect?: Prisma.chat_messageWhereUniqueInput | Prisma.chat_messageWhereUniqueInput[]
  update?: Prisma.chat_messageUpdateWithWhereUniqueWithoutUserInput | Prisma.chat_messageUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.chat_messageUpdateManyWithWhereWithoutUserInput | Prisma.chat_messageUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
}

export type chat_messageCreateWithoutParent_messageInput = {
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedOneWithoutChat_messageInput
  user?: Prisma.userCreateNestedOneWithoutChat_messageInput
  chat_message?: Prisma.chat_messageCreateNestedOneWithoutParent_messageInput
}

export type chat_messageUncheckedCreateWithoutParent_messageInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  userId?: number | null
  parentMessageId?: number | null
}

export type chat_messageCreateOrConnectWithoutParent_messageInput = {
  where: Prisma.chat_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutParent_messageInput, Prisma.chat_messageUncheckedCreateWithoutParent_messageInput>
}

export type chat_messageCreateWithoutChat_messageInput = {
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedOneWithoutChat_messageInput
  user?: Prisma.userCreateNestedOneWithoutChat_messageInput
  parent_message?: Prisma.chat_messageCreateNestedManyWithoutChat_messageInput
}

export type chat_messageUncheckedCreateWithoutChat_messageInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  userId?: number | null
  parent_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_messageInput
}

export type chat_messageCreateOrConnectWithoutChat_messageInput = {
  where: Prisma.chat_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput>
}

export type chat_messageCreateManyChat_messageInputEnvelope = {
  data: Prisma.chat_messageCreateManyChat_messageInput | Prisma.chat_messageCreateManyChat_messageInput[]
  skipDuplicates?: boolean
}

export type chat_messageUpsertWithoutParent_messageInput = {
  update: Prisma.XOR<Prisma.chat_messageUpdateWithoutParent_messageInput, Prisma.chat_messageUncheckedUpdateWithoutParent_messageInput>
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutParent_messageInput, Prisma.chat_messageUncheckedCreateWithoutParent_messageInput>
  where?: Prisma.chat_messageWhereInput
}

export type chat_messageUpdateToOneWithWhereWithoutParent_messageInput = {
  where?: Prisma.chat_messageWhereInput
  data: Prisma.XOR<Prisma.chat_messageUpdateWithoutParent_messageInput, Prisma.chat_messageUncheckedUpdateWithoutParent_messageInput>
}

export type chat_messageUpdateWithoutParent_messageInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateOneWithoutChat_messageNestedInput
  user?: Prisma.userUpdateOneWithoutChat_messageNestedInput
  chat_message?: Prisma.chat_messageUpdateOneWithoutParent_messageNestedInput
}

export type chat_messageUncheckedUpdateWithoutParent_messageInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type chat_messageUpsertWithWhereUniqueWithoutChat_messageInput = {
  where: Prisma.chat_messageWhereUniqueInput
  update: Prisma.XOR<Prisma.chat_messageUpdateWithoutChat_messageInput, Prisma.chat_messageUncheckedUpdateWithoutChat_messageInput>
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_messageInput, Prisma.chat_messageUncheckedCreateWithoutChat_messageInput>
}

export type chat_messageUpdateWithWhereUniqueWithoutChat_messageInput = {
  where: Prisma.chat_messageWhereUniqueInput
  data: Prisma.XOR<Prisma.chat_messageUpdateWithoutChat_messageInput, Prisma.chat_messageUncheckedUpdateWithoutChat_messageInput>
}

export type chat_messageUpdateManyWithWhereWithoutChat_messageInput = {
  where: Prisma.chat_messageScalarWhereInput
  data: Prisma.XOR<Prisma.chat_messageUpdateManyMutationInput, Prisma.chat_messageUncheckedUpdateManyWithoutChat_messageInput>
}

export type chat_messageScalarWhereInput = {
  AND?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
  OR?: Prisma.chat_messageScalarWhereInput[]
  NOT?: Prisma.chat_messageScalarWhereInput | Prisma.chat_messageScalarWhereInput[]
  id?: Prisma.IntFilter<"chat_message"> | number
  message?: Prisma.StringFilter<"chat_message"> | string
  hidden?: Prisma.BoolNullableFilter<"chat_message"> | boolean | null
  announcementType?: Prisma.StringNullableFilter<"chat_message"> | string | null
  createdAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"chat_message"> | Date | string
  chatRoomId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  userId?: Prisma.IntNullableFilter<"chat_message"> | number | null
  parentMessageId?: Prisma.IntNullableFilter<"chat_message"> | number | null
}

export type chat_messageCreateWithoutChat_roomInput = {
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutChat_messageInput
  chat_message?: Prisma.chat_messageCreateNestedOneWithoutParent_messageInput
  parent_message?: Prisma.chat_messageCreateNestedManyWithoutChat_messageInput
}

export type chat_messageUncheckedCreateWithoutChat_roomInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  parentMessageId?: number | null
  parent_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_messageInput
}

export type chat_messageCreateOrConnectWithoutChat_roomInput = {
  where: Prisma.chat_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput>
}

export type chat_messageCreateManyChat_roomInputEnvelope = {
  data: Prisma.chat_messageCreateManyChat_roomInput | Prisma.chat_messageCreateManyChat_roomInput[]
  skipDuplicates?: boolean
}

export type chat_messageUpsertWithWhereUniqueWithoutChat_roomInput = {
  where: Prisma.chat_messageWhereUniqueInput
  update: Prisma.XOR<Prisma.chat_messageUpdateWithoutChat_roomInput, Prisma.chat_messageUncheckedUpdateWithoutChat_roomInput>
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutChat_roomInput, Prisma.chat_messageUncheckedCreateWithoutChat_roomInput>
}

export type chat_messageUpdateWithWhereUniqueWithoutChat_roomInput = {
  where: Prisma.chat_messageWhereUniqueInput
  data: Prisma.XOR<Prisma.chat_messageUpdateWithoutChat_roomInput, Prisma.chat_messageUncheckedUpdateWithoutChat_roomInput>
}

export type chat_messageUpdateManyWithWhereWithoutChat_roomInput = {
  where: Prisma.chat_messageScalarWhereInput
  data: Prisma.XOR<Prisma.chat_messageUpdateManyMutationInput, Prisma.chat_messageUncheckedUpdateManyWithoutChat_roomInput>
}

export type chat_messageCreateWithoutUserInput = {
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chat_room?: Prisma.chat_roomCreateNestedOneWithoutChat_messageInput
  chat_message?: Prisma.chat_messageCreateNestedOneWithoutParent_messageInput
  parent_message?: Prisma.chat_messageCreateNestedManyWithoutChat_messageInput
}

export type chat_messageUncheckedCreateWithoutUserInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  parentMessageId?: number | null
  parent_message?: Prisma.chat_messageUncheckedCreateNestedManyWithoutChat_messageInput
}

export type chat_messageCreateOrConnectWithoutUserInput = {
  where: Prisma.chat_messageWhereUniqueInput
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput>
}

export type chat_messageCreateManyUserInputEnvelope = {
  data: Prisma.chat_messageCreateManyUserInput | Prisma.chat_messageCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type chat_messageUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.chat_messageWhereUniqueInput
  update: Prisma.XOR<Prisma.chat_messageUpdateWithoutUserInput, Prisma.chat_messageUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.chat_messageCreateWithoutUserInput, Prisma.chat_messageUncheckedCreateWithoutUserInput>
}

export type chat_messageUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.chat_messageWhereUniqueInput
  data: Prisma.XOR<Prisma.chat_messageUpdateWithoutUserInput, Prisma.chat_messageUncheckedUpdateWithoutUserInput>
}

export type chat_messageUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.chat_messageScalarWhereInput
  data: Prisma.XOR<Prisma.chat_messageUpdateManyMutationInput, Prisma.chat_messageUncheckedUpdateManyWithoutUserInput>
}

export type chat_messageCreateManyChat_messageInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  userId?: number | null
}

export type chat_messageUpdateWithoutChat_messageInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateOneWithoutChat_messageNestedInput
  user?: Prisma.userUpdateOneWithoutChat_messageNestedInput
  parent_message?: Prisma.chat_messageUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateWithoutChat_messageInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parent_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateManyWithoutChat_messageInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type chat_messageCreateManyChat_roomInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  parentMessageId?: number | null
}

export type chat_messageUpdateWithoutChat_roomInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutChat_messageNestedInput
  chat_message?: Prisma.chat_messageUpdateOneWithoutParent_messageNestedInput
  parent_message?: Prisma.chat_messageUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateWithoutChat_roomInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parent_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateManyWithoutChat_roomInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type chat_messageCreateManyUserInput = {
  id?: number
  message: string
  hidden?: boolean | null
  announcementType?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  chatRoomId?: number | null
  parentMessageId?: number | null
}

export type chat_messageUpdateWithoutUserInput = {
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chat_room?: Prisma.chat_roomUpdateOneWithoutChat_messageNestedInput
  chat_message?: Prisma.chat_messageUpdateOneWithoutParent_messageNestedInput
  parent_message?: Prisma.chat_messageUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parent_message?: Prisma.chat_messageUncheckedUpdateManyWithoutChat_messageNestedInput
}

export type chat_messageUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  message?: Prisma.StringFieldUpdateOperationsInput | string
  hidden?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  announcementType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  chatRoomId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  parentMessageId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}


/**
 * Count Type Chat_messageCountOutputType
 */

export type Chat_messageCountOutputType = {
  parent_message: number
}

export type Chat_messageCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  parent_message?: boolean | Chat_messageCountOutputTypeCountParent_messageArgs
}

/**
 * Chat_messageCountOutputType without action
 */
export type Chat_messageCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Chat_messageCountOutputType
   */
  select?: Prisma.Chat_messageCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Chat_messageCountOutputType without action
 */
export type Chat_messageCountOutputTypeCountParent_messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.chat_messageWhereInput
}


export type chat_messageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  message?: boolean
  hidden?: boolean
  announcementType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  chatRoomId?: boolean
  userId?: boolean
  parentMessageId?: boolean
  chat_room?: boolean | Prisma.chat_message$chat_roomArgs<ExtArgs>
  user?: boolean | Prisma.chat_message$userArgs<ExtArgs>
  chat_message?: boolean | Prisma.chat_message$chat_messageArgs<ExtArgs>
  parent_message?: boolean | Prisma.chat_message$parent_messageArgs<ExtArgs>
  _count?: boolean | Prisma.Chat_messageCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["chat_message"]>



export type chat_messageSelectScalar = {
  id?: boolean
  message?: boolean
  hidden?: boolean
  announcementType?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  chatRoomId?: boolean
  userId?: boolean
  parentMessageId?: boolean
}

export type chat_messageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "message" | "hidden" | "announcementType" | "createdAt" | "updatedAt" | "chatRoomId" | "userId" | "parentMessageId", ExtArgs["result"]["chat_message"]>
export type chat_messageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  chat_room?: boolean | Prisma.chat_message$chat_roomArgs<ExtArgs>
  user?: boolean | Prisma.chat_message$userArgs<ExtArgs>
  chat_message?: boolean | Prisma.chat_message$chat_messageArgs<ExtArgs>
  parent_message?: boolean | Prisma.chat_message$parent_messageArgs<ExtArgs>
  _count?: boolean | Prisma.Chat_messageCountOutputTypeDefaultArgs<ExtArgs>
}

export type $chat_messagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "chat_message"
  objects: {
    chat_room: Prisma.$chat_roomPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
    chat_message: Prisma.$chat_messagePayload<ExtArgs> | null
    parent_message: Prisma.$chat_messagePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    message: string
    hidden: boolean | null
    announcementType: string | null
    createdAt: Date
    updatedAt: Date
    chatRoomId: number | null
    userId: number | null
    parentMessageId: number | null
  }, ExtArgs["result"]["chat_message"]>
  composites: {}
}

export type chat_messageGetPayload<S extends boolean | null | undefined | chat_messageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$chat_messagePayload, S>

export type chat_messageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<chat_messageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Chat_messageCountAggregateInputType | true
  }

export interface chat_messageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['chat_message'], meta: { name: 'chat_message' } }
  /**
   * Find zero or one Chat_message that matches the filter.
   * @param {chat_messageFindUniqueArgs} args - Arguments to find a Chat_message
   * @example
   * // Get one Chat_message
   * const chat_message = await prisma.chat_message.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends chat_messageFindUniqueArgs>(args: Prisma.SelectSubset<T, chat_messageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Chat_message that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {chat_messageFindUniqueOrThrowArgs} args - Arguments to find a Chat_message
   * @example
   * // Get one Chat_message
   * const chat_message = await prisma.chat_message.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends chat_messageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, chat_messageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Chat_message that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageFindFirstArgs} args - Arguments to find a Chat_message
   * @example
   * // Get one Chat_message
   * const chat_message = await prisma.chat_message.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends chat_messageFindFirstArgs>(args?: Prisma.SelectSubset<T, chat_messageFindFirstArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Chat_message that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageFindFirstOrThrowArgs} args - Arguments to find a Chat_message
   * @example
   * // Get one Chat_message
   * const chat_message = await prisma.chat_message.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends chat_messageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, chat_messageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Chat_messages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Chat_messages
   * const chat_messages = await prisma.chat_message.findMany()
   * 
   * // Get first 10 Chat_messages
   * const chat_messages = await prisma.chat_message.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const chat_messageWithIdOnly = await prisma.chat_message.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends chat_messageFindManyArgs>(args?: Prisma.SelectSubset<T, chat_messageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Chat_message.
   * @param {chat_messageCreateArgs} args - Arguments to create a Chat_message.
   * @example
   * // Create one Chat_message
   * const Chat_message = await prisma.chat_message.create({
   *   data: {
   *     // ... data to create a Chat_message
   *   }
   * })
   * 
   */
  create<T extends chat_messageCreateArgs>(args: Prisma.SelectSubset<T, chat_messageCreateArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Chat_messages.
   * @param {chat_messageCreateManyArgs} args - Arguments to create many Chat_messages.
   * @example
   * // Create many Chat_messages
   * const chat_message = await prisma.chat_message.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends chat_messageCreateManyArgs>(args?: Prisma.SelectSubset<T, chat_messageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Chat_message.
   * @param {chat_messageDeleteArgs} args - Arguments to delete one Chat_message.
   * @example
   * // Delete one Chat_message
   * const Chat_message = await prisma.chat_message.delete({
   *   where: {
   *     // ... filter to delete one Chat_message
   *   }
   * })
   * 
   */
  delete<T extends chat_messageDeleteArgs>(args: Prisma.SelectSubset<T, chat_messageDeleteArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Chat_message.
   * @param {chat_messageUpdateArgs} args - Arguments to update one Chat_message.
   * @example
   * // Update one Chat_message
   * const chat_message = await prisma.chat_message.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends chat_messageUpdateArgs>(args: Prisma.SelectSubset<T, chat_messageUpdateArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Chat_messages.
   * @param {chat_messageDeleteManyArgs} args - Arguments to filter Chat_messages to delete.
   * @example
   * // Delete a few Chat_messages
   * const { count } = await prisma.chat_message.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends chat_messageDeleteManyArgs>(args?: Prisma.SelectSubset<T, chat_messageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Chat_messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Chat_messages
   * const chat_message = await prisma.chat_message.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends chat_messageUpdateManyArgs>(args: Prisma.SelectSubset<T, chat_messageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Chat_message.
   * @param {chat_messageUpsertArgs} args - Arguments to update or create a Chat_message.
   * @example
   * // Update or create a Chat_message
   * const chat_message = await prisma.chat_message.upsert({
   *   create: {
   *     // ... data to create a Chat_message
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Chat_message we want to update
   *   }
   * })
   */
  upsert<T extends chat_messageUpsertArgs>(args: Prisma.SelectSubset<T, chat_messageUpsertArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Chat_messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageCountArgs} args - Arguments to filter Chat_messages to count.
   * @example
   * // Count the number of Chat_messages
   * const count = await prisma.chat_message.count({
   *   where: {
   *     // ... the filter for the Chat_messages we want to count
   *   }
   * })
  **/
  count<T extends chat_messageCountArgs>(
    args?: Prisma.Subset<T, chat_messageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Chat_messageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Chat_message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Chat_messageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Chat_messageAggregateArgs>(args: Prisma.Subset<T, Chat_messageAggregateArgs>): Prisma.PrismaPromise<GetChat_messageAggregateType<T>>

  /**
   * Group by Chat_message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {chat_messageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends chat_messageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: chat_messageGroupByArgs['orderBy'] }
      : { orderBy?: chat_messageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, chat_messageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetChat_messageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the chat_message model
 */
readonly fields: chat_messageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for chat_message.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__chat_messageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  chat_room<T extends Prisma.chat_message$chat_roomArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_message$chat_roomArgs<ExtArgs>>): Prisma.Prisma__chat_roomClient<runtime.Types.Result.GetResult<Prisma.$chat_roomPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.chat_message$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_message$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  chat_message<T extends Prisma.chat_message$chat_messageArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_message$chat_messageArgs<ExtArgs>>): Prisma.Prisma__chat_messageClient<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  parent_message<T extends Prisma.chat_message$parent_messageArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.chat_message$parent_messageArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$chat_messagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the chat_message model
 */
export interface chat_messageFieldRefs {
  readonly id: Prisma.FieldRef<"chat_message", 'Int'>
  readonly message: Prisma.FieldRef<"chat_message", 'String'>
  readonly hidden: Prisma.FieldRef<"chat_message", 'Boolean'>
  readonly announcementType: Prisma.FieldRef<"chat_message", 'String'>
  readonly createdAt: Prisma.FieldRef<"chat_message", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"chat_message", 'DateTime'>
  readonly chatRoomId: Prisma.FieldRef<"chat_message", 'Int'>
  readonly userId: Prisma.FieldRef<"chat_message", 'Int'>
  readonly parentMessageId: Prisma.FieldRef<"chat_message", 'Int'>
}
    

// Custom InputTypes
/**
 * chat_message findUnique
 */
export type chat_messageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter, which chat_message to fetch.
   */
  where: Prisma.chat_messageWhereUniqueInput
}

/**
 * chat_message findUniqueOrThrow
 */
export type chat_messageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter, which chat_message to fetch.
   */
  where: Prisma.chat_messageWhereUniqueInput
}

/**
 * chat_message findFirst
 */
export type chat_messageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter, which chat_message to fetch.
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_messages to fetch.
   */
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for chat_messages.
   */
  cursor?: Prisma.chat_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of chat_messages.
   */
  distinct?: Prisma.Chat_messageScalarFieldEnum | Prisma.Chat_messageScalarFieldEnum[]
}

/**
 * chat_message findFirstOrThrow
 */
export type chat_messageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter, which chat_message to fetch.
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_messages to fetch.
   */
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for chat_messages.
   */
  cursor?: Prisma.chat_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of chat_messages.
   */
  distinct?: Prisma.Chat_messageScalarFieldEnum | Prisma.Chat_messageScalarFieldEnum[]
}

/**
 * chat_message findMany
 */
export type chat_messageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter, which chat_messages to fetch.
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of chat_messages to fetch.
   */
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing chat_messages.
   */
  cursor?: Prisma.chat_messageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` chat_messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` chat_messages.
   */
  skip?: number
  distinct?: Prisma.Chat_messageScalarFieldEnum | Prisma.Chat_messageScalarFieldEnum[]
}

/**
 * chat_message create
 */
export type chat_messageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * The data needed to create a chat_message.
   */
  data: Prisma.XOR<Prisma.chat_messageCreateInput, Prisma.chat_messageUncheckedCreateInput>
}

/**
 * chat_message createMany
 */
export type chat_messageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many chat_messages.
   */
  data: Prisma.chat_messageCreateManyInput | Prisma.chat_messageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * chat_message update
 */
export type chat_messageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * The data needed to update a chat_message.
   */
  data: Prisma.XOR<Prisma.chat_messageUpdateInput, Prisma.chat_messageUncheckedUpdateInput>
  /**
   * Choose, which chat_message to update.
   */
  where: Prisma.chat_messageWhereUniqueInput
}

/**
 * chat_message updateMany
 */
export type chat_messageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update chat_messages.
   */
  data: Prisma.XOR<Prisma.chat_messageUpdateManyMutationInput, Prisma.chat_messageUncheckedUpdateManyInput>
  /**
   * Filter which chat_messages to update
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * Limit how many chat_messages to update.
   */
  limit?: number
}

/**
 * chat_message upsert
 */
export type chat_messageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * The filter to search for the chat_message to update in case it exists.
   */
  where: Prisma.chat_messageWhereUniqueInput
  /**
   * In case the chat_message found by the `where` argument doesn't exist, create a new chat_message with this data.
   */
  create: Prisma.XOR<Prisma.chat_messageCreateInput, Prisma.chat_messageUncheckedCreateInput>
  /**
   * In case the chat_message was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.chat_messageUpdateInput, Prisma.chat_messageUncheckedUpdateInput>
}

/**
 * chat_message delete
 */
export type chat_messageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  /**
   * Filter which chat_message to delete.
   */
  where: Prisma.chat_messageWhereUniqueInput
}

/**
 * chat_message deleteMany
 */
export type chat_messageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which chat_messages to delete
   */
  where?: Prisma.chat_messageWhereInput
  /**
   * Limit how many chat_messages to delete.
   */
  limit?: number
}

/**
 * chat_message.chat_room
 */
export type chat_message$chat_roomArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_room
   */
  select?: Prisma.chat_roomSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_room
   */
  omit?: Prisma.chat_roomOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_roomInclude<ExtArgs> | null
  where?: Prisma.chat_roomWhereInput
}

/**
 * chat_message.user
 */
export type chat_message$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * chat_message.chat_message
 */
export type chat_message$chat_messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  where?: Prisma.chat_messageWhereInput
}

/**
 * chat_message.parent_message
 */
export type chat_message$parent_messageArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
  where?: Prisma.chat_messageWhereInput
  orderBy?: Prisma.chat_messageOrderByWithRelationInput | Prisma.chat_messageOrderByWithRelationInput[]
  cursor?: Prisma.chat_messageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Chat_messageScalarFieldEnum | Prisma.Chat_messageScalarFieldEnum[]
}

/**
 * chat_message without action
 */
export type chat_messageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the chat_message
   */
  select?: Prisma.chat_messageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the chat_message
   */
  omit?: Prisma.chat_messageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.chat_messageInclude<ExtArgs> | null
}
