
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `verification` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model verification
 * 
 */
export type verificationModel = runtime.Types.Result.DefaultSelection<Prisma.$verificationPayload>

export type AggregateVerification = {
  _count: VerificationCountAggregateOutputType | null
  _avg: VerificationAvgAggregateOutputType | null
  _sum: VerificationSumAggregateOutputType | null
  _min: VerificationMinAggregateOutputType | null
  _max: VerificationMaxAggregateOutputType | null
}

export type VerificationAvgAggregateOutputType = {
  id: number | null
}

export type VerificationSumAggregateOutputType = {
  id: number | null
}

export type VerificationMinAggregateOutputType = {
  id: number | null
  identifier: string | null
  value: string | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VerificationMaxAggregateOutputType = {
  id: number | null
  identifier: string | null
  value: string | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VerificationCountAggregateOutputType = {
  id: number
  identifier: number
  value: number
  expiresAt: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type VerificationAvgAggregateInputType = {
  id?: true
}

export type VerificationSumAggregateInputType = {
  id?: true
}

export type VerificationMinAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
}

export type VerificationMaxAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
}

export type VerificationCountAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type VerificationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which verification to aggregate.
   */
  where?: Prisma.verificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of verifications to fetch.
   */
  orderBy?: Prisma.verificationOrderByWithRelationInput | Prisma.verificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.verificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned verifications
  **/
  _count?: true | VerificationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: VerificationAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: VerificationSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: VerificationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: VerificationMaxAggregateInputType
}

export type GetVerificationAggregateType<T extends VerificationAggregateArgs> = {
      [P in keyof T & keyof AggregateVerification]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateVerification[P]>
    : Prisma.GetScalarType<T[P], AggregateVerification[P]>
}




export type verificationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.verificationWhereInput
  orderBy?: Prisma.verificationOrderByWithAggregationInput | Prisma.verificationOrderByWithAggregationInput[]
  by: Prisma.VerificationScalarFieldEnum[] | Prisma.VerificationScalarFieldEnum
  having?: Prisma.verificationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: VerificationCountAggregateInputType | true
  _avg?: VerificationAvgAggregateInputType
  _sum?: VerificationSumAggregateInputType
  _min?: VerificationMinAggregateInputType
  _max?: VerificationMaxAggregateInputType
}

export type VerificationGroupByOutputType = {
  id: number
  identifier: string
  value: string
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
  _count: VerificationCountAggregateOutputType | null
  _avg: VerificationAvgAggregateOutputType | null
  _sum: VerificationSumAggregateOutputType | null
  _min: VerificationMinAggregateOutputType | null
  _max: VerificationMaxAggregateOutputType | null
}

type GetVerificationGroupByPayload<T extends verificationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<VerificationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof VerificationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], VerificationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], VerificationGroupByOutputType[P]>
      }
    >
  >



export type verificationWhereInput = {
  AND?: Prisma.verificationWhereInput | Prisma.verificationWhereInput[]
  OR?: Prisma.verificationWhereInput[]
  NOT?: Prisma.verificationWhereInput | Prisma.verificationWhereInput[]
  id?: Prisma.IntFilter<"verification"> | number
  identifier?: Prisma.StringFilter<"verification"> | string
  value?: Prisma.StringFilter<"verification"> | string
  expiresAt?: Prisma.DateTimeFilter<"verification"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"verification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"verification"> | Date | string
}

export type verificationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _relevance?: Prisma.verificationOrderByRelevanceInput
}

export type verificationWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.verificationWhereInput | Prisma.verificationWhereInput[]
  OR?: Prisma.verificationWhereInput[]
  NOT?: Prisma.verificationWhereInput | Prisma.verificationWhereInput[]
  identifier?: Prisma.StringFilter<"verification"> | string
  value?: Prisma.StringFilter<"verification"> | string
  expiresAt?: Prisma.DateTimeFilter<"verification"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"verification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"verification"> | Date | string
}, "id">

export type verificationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.verificationCountOrderByAggregateInput
  _avg?: Prisma.verificationAvgOrderByAggregateInput
  _max?: Prisma.verificationMaxOrderByAggregateInput
  _min?: Prisma.verificationMinOrderByAggregateInput
  _sum?: Prisma.verificationSumOrderByAggregateInput
}

export type verificationScalarWhereWithAggregatesInput = {
  AND?: Prisma.verificationScalarWhereWithAggregatesInput | Prisma.verificationScalarWhereWithAggregatesInput[]
  OR?: Prisma.verificationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.verificationScalarWhereWithAggregatesInput | Prisma.verificationScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"verification"> | number
  identifier?: Prisma.StringWithAggregatesFilter<"verification"> | string
  value?: Prisma.StringWithAggregatesFilter<"verification"> | string
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"verification"> | Date | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"verification"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"verification"> | Date | string
}

export type verificationCreateInput = {
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt: Date | string
  updatedAt: Date | string
}

export type verificationUncheckedCreateInput = {
  id?: number
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt: Date | string
  updatedAt: Date | string
}

export type verificationUpdateInput = {
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type verificationUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type verificationCreateManyInput = {
  id?: number
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt: Date | string
  updatedAt: Date | string
}

export type verificationUpdateManyMutationInput = {
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type verificationUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type verificationOrderByRelevanceInput = {
  fields: Prisma.verificationOrderByRelevanceFieldEnum | Prisma.verificationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type verificationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type verificationAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type verificationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type verificationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type verificationSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}



export type verificationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["verification"]>



export type verificationSelectScalar = {
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type verificationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "identifier" | "value" | "expiresAt" | "createdAt" | "updatedAt", ExtArgs["result"]["verification"]>

export type $verificationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "verification"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    identifier: string
    value: string
    expiresAt: Date
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["verification"]>
  composites: {}
}

export type verificationGetPayload<S extends boolean | null | undefined | verificationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$verificationPayload, S>

export type verificationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<verificationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: VerificationCountAggregateInputType | true
  }

export interface verificationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['verification'], meta: { name: 'verification' } }
  /**
   * Find zero or one Verification that matches the filter.
   * @param {verificationFindUniqueArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends verificationFindUniqueArgs>(args: Prisma.SelectSubset<T, verificationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Verification that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {verificationFindUniqueOrThrowArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends verificationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, verificationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Verification that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationFindFirstArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends verificationFindFirstArgs>(args?: Prisma.SelectSubset<T, verificationFindFirstArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Verification that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationFindFirstOrThrowArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends verificationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, verificationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Verifications that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Verifications
   * const verifications = await prisma.verification.findMany()
   * 
   * // Get first 10 Verifications
   * const verifications = await prisma.verification.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const verificationWithIdOnly = await prisma.verification.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends verificationFindManyArgs>(args?: Prisma.SelectSubset<T, verificationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Verification.
   * @param {verificationCreateArgs} args - Arguments to create a Verification.
   * @example
   * // Create one Verification
   * const Verification = await prisma.verification.create({
   *   data: {
   *     // ... data to create a Verification
   *   }
   * })
   * 
   */
  create<T extends verificationCreateArgs>(args: Prisma.SelectSubset<T, verificationCreateArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Verifications.
   * @param {verificationCreateManyArgs} args - Arguments to create many Verifications.
   * @example
   * // Create many Verifications
   * const verification = await prisma.verification.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends verificationCreateManyArgs>(args?: Prisma.SelectSubset<T, verificationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Verification.
   * @param {verificationDeleteArgs} args - Arguments to delete one Verification.
   * @example
   * // Delete one Verification
   * const Verification = await prisma.verification.delete({
   *   where: {
   *     // ... filter to delete one Verification
   *   }
   * })
   * 
   */
  delete<T extends verificationDeleteArgs>(args: Prisma.SelectSubset<T, verificationDeleteArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Verification.
   * @param {verificationUpdateArgs} args - Arguments to update one Verification.
   * @example
   * // Update one Verification
   * const verification = await prisma.verification.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends verificationUpdateArgs>(args: Prisma.SelectSubset<T, verificationUpdateArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Verifications.
   * @param {verificationDeleteManyArgs} args - Arguments to filter Verifications to delete.
   * @example
   * // Delete a few Verifications
   * const { count } = await prisma.verification.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends verificationDeleteManyArgs>(args?: Prisma.SelectSubset<T, verificationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Verifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Verifications
   * const verification = await prisma.verification.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends verificationUpdateManyArgs>(args: Prisma.SelectSubset<T, verificationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Verification.
   * @param {verificationUpsertArgs} args - Arguments to update or create a Verification.
   * @example
   * // Update or create a Verification
   * const verification = await prisma.verification.upsert({
   *   create: {
   *     // ... data to create a Verification
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Verification we want to update
   *   }
   * })
   */
  upsert<T extends verificationUpsertArgs>(args: Prisma.SelectSubset<T, verificationUpsertArgs<ExtArgs>>): Prisma.Prisma__verificationClient<runtime.Types.Result.GetResult<Prisma.$verificationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Verifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationCountArgs} args - Arguments to filter Verifications to count.
   * @example
   * // Count the number of Verifications
   * const count = await prisma.verification.count({
   *   where: {
   *     // ... the filter for the Verifications we want to count
   *   }
   * })
  **/
  count<T extends verificationCountArgs>(
    args?: Prisma.Subset<T, verificationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], VerificationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Verification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends VerificationAggregateArgs>(args: Prisma.Subset<T, VerificationAggregateArgs>): Prisma.PrismaPromise<GetVerificationAggregateType<T>>

  /**
   * Group by Verification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {verificationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends verificationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: verificationGroupByArgs['orderBy'] }
      : { orderBy?: verificationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, verificationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetVerificationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the verification model
 */
readonly fields: verificationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for verification.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__verificationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the verification model
 */
export interface verificationFieldRefs {
  readonly id: Prisma.FieldRef<"verification", 'Int'>
  readonly identifier: Prisma.FieldRef<"verification", 'String'>
  readonly value: Prisma.FieldRef<"verification", 'String'>
  readonly expiresAt: Prisma.FieldRef<"verification", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"verification", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"verification", 'DateTime'>
}
    

// Custom InputTypes
/**
 * verification findUnique
 */
export type verificationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter, which verification to fetch.
   */
  where: Prisma.verificationWhereUniqueInput
}

/**
 * verification findUniqueOrThrow
 */
export type verificationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter, which verification to fetch.
   */
  where: Prisma.verificationWhereUniqueInput
}

/**
 * verification findFirst
 */
export type verificationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter, which verification to fetch.
   */
  where?: Prisma.verificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of verifications to fetch.
   */
  orderBy?: Prisma.verificationOrderByWithRelationInput | Prisma.verificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for verifications.
   */
  cursor?: Prisma.verificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of verifications.
   */
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * verification findFirstOrThrow
 */
export type verificationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter, which verification to fetch.
   */
  where?: Prisma.verificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of verifications to fetch.
   */
  orderBy?: Prisma.verificationOrderByWithRelationInput | Prisma.verificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for verifications.
   */
  cursor?: Prisma.verificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of verifications.
   */
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * verification findMany
 */
export type verificationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter, which verifications to fetch.
   */
  where?: Prisma.verificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of verifications to fetch.
   */
  orderBy?: Prisma.verificationOrderByWithRelationInput | Prisma.verificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing verifications.
   */
  cursor?: Prisma.verificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` verifications.
   */
  skip?: number
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * verification create
 */
export type verificationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * The data needed to create a verification.
   */
  data: Prisma.XOR<Prisma.verificationCreateInput, Prisma.verificationUncheckedCreateInput>
}

/**
 * verification createMany
 */
export type verificationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many verifications.
   */
  data: Prisma.verificationCreateManyInput | Prisma.verificationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * verification update
 */
export type verificationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * The data needed to update a verification.
   */
  data: Prisma.XOR<Prisma.verificationUpdateInput, Prisma.verificationUncheckedUpdateInput>
  /**
   * Choose, which verification to update.
   */
  where: Prisma.verificationWhereUniqueInput
}

/**
 * verification updateMany
 */
export type verificationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update verifications.
   */
  data: Prisma.XOR<Prisma.verificationUpdateManyMutationInput, Prisma.verificationUncheckedUpdateManyInput>
  /**
   * Filter which verifications to update
   */
  where?: Prisma.verificationWhereInput
  /**
   * Limit how many verifications to update.
   */
  limit?: number
}

/**
 * verification upsert
 */
export type verificationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * The filter to search for the verification to update in case it exists.
   */
  where: Prisma.verificationWhereUniqueInput
  /**
   * In case the verification found by the `where` argument doesn't exist, create a new verification with this data.
   */
  create: Prisma.XOR<Prisma.verificationCreateInput, Prisma.verificationUncheckedCreateInput>
  /**
   * In case the verification was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.verificationUpdateInput, Prisma.verificationUncheckedUpdateInput>
}

/**
 * verification delete
 */
export type verificationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
  /**
   * Filter which verification to delete.
   */
  where: Prisma.verificationWhereUniqueInput
}

/**
 * verification deleteMany
 */
export type verificationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which verifications to delete
   */
  where?: Prisma.verificationWhereInput
  /**
   * Limit how many verifications to delete.
   */
  limit?: number
}

/**
 * verification without action
 */
export type verificationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the verification
   */
  select?: Prisma.verificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the verification
   */
  omit?: Prisma.verificationOmit<ExtArgs> | null
}
