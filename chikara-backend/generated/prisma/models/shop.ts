
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `shop` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model shop
 * 
 */
export type shopModel = runtime.Types.Result.DefaultSelection<Prisma.$shopPayload>

export type AggregateShop = {
  _count: ShopCountAggregateOutputType | null
  _avg: ShopAvgAggregateOutputType | null
  _sum: ShopSumAggregateOutputType | null
  _min: ShopMinAggregateOutputType | null
  _max: ShopMaxAggregateOutputType | null
}

export type ShopAvgAggregateOutputType = {
  id: number | null
}

export type ShopSumAggregateOutputType = {
  id: number | null
}

export type ShopMinAggregateOutputType = {
  id: number | null
  name: string | null
  shopType: $Enums.ShopTypes | null
  avatar: string | null
  description: string | null
  disabled: boolean | null
}

export type ShopMaxAggregateOutputType = {
  id: number | null
  name: string | null
  shopType: $Enums.ShopTypes | null
  avatar: string | null
  description: string | null
  disabled: boolean | null
}

export type ShopCountAggregateOutputType = {
  id: number
  name: number
  shopType: number
  avatar: number
  description: number
  disabled: number
  _all: number
}


export type ShopAvgAggregateInputType = {
  id?: true
}

export type ShopSumAggregateInputType = {
  id?: true
}

export type ShopMinAggregateInputType = {
  id?: true
  name?: true
  shopType?: true
  avatar?: true
  description?: true
  disabled?: true
}

export type ShopMaxAggregateInputType = {
  id?: true
  name?: true
  shopType?: true
  avatar?: true
  description?: true
  disabled?: true
}

export type ShopCountAggregateInputType = {
  id?: true
  name?: true
  shopType?: true
  avatar?: true
  description?: true
  disabled?: true
  _all?: true
}

export type ShopAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shop to aggregate.
   */
  where?: Prisma.shopWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shops to fetch.
   */
  orderBy?: Prisma.shopOrderByWithRelationInput | Prisma.shopOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.shopWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shops from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shops.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned shops
  **/
  _count?: true | ShopCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ShopAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ShopSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ShopMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ShopMaxAggregateInputType
}

export type GetShopAggregateType<T extends ShopAggregateArgs> = {
      [P in keyof T & keyof AggregateShop]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateShop[P]>
    : Prisma.GetScalarType<T[P], AggregateShop[P]>
}




export type shopGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shopWhereInput
  orderBy?: Prisma.shopOrderByWithAggregationInput | Prisma.shopOrderByWithAggregationInput[]
  by: Prisma.ShopScalarFieldEnum[] | Prisma.ShopScalarFieldEnum
  having?: Prisma.shopScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ShopCountAggregateInputType | true
  _avg?: ShopAvgAggregateInputType
  _sum?: ShopSumAggregateInputType
  _min?: ShopMinAggregateInputType
  _max?: ShopMaxAggregateInputType
}

export type ShopGroupByOutputType = {
  id: number
  name: string
  shopType: $Enums.ShopTypes
  avatar: string | null
  description: string
  disabled: boolean | null
  _count: ShopCountAggregateOutputType | null
  _avg: ShopAvgAggregateOutputType | null
  _sum: ShopSumAggregateOutputType | null
  _min: ShopMinAggregateOutputType | null
  _max: ShopMaxAggregateOutputType | null
}

type GetShopGroupByPayload<T extends shopGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ShopGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ShopGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ShopGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ShopGroupByOutputType[P]>
      }
    >
  >



export type shopWhereInput = {
  AND?: Prisma.shopWhereInput | Prisma.shopWhereInput[]
  OR?: Prisma.shopWhereInput[]
  NOT?: Prisma.shopWhereInput | Prisma.shopWhereInput[]
  id?: Prisma.IntFilter<"shop"> | number
  name?: Prisma.StringFilter<"shop"> | string
  shopType?: Prisma.EnumShopTypesFilter<"shop"> | $Enums.ShopTypes
  avatar?: Prisma.StringNullableFilter<"shop"> | string | null
  description?: Prisma.StringFilter<"shop"> | string
  disabled?: Prisma.BoolNullableFilter<"shop"> | boolean | null
  quest?: Prisma.QuestListRelationFilter
  shop_listing?: Prisma.Shop_listingListRelationFilter
  trader_rep?: Prisma.Trader_repListRelationFilter
  explore_static_node?: Prisma.Explore_static_nodeListRelationFilter
}

export type shopOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  shopType?: Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  description?: Prisma.SortOrder
  disabled?: Prisma.SortOrderInput | Prisma.SortOrder
  quest?: Prisma.questOrderByRelationAggregateInput
  shop_listing?: Prisma.shop_listingOrderByRelationAggregateInput
  trader_rep?: Prisma.trader_repOrderByRelationAggregateInput
  explore_static_node?: Prisma.explore_static_nodeOrderByRelationAggregateInput
  _relevance?: Prisma.shopOrderByRelevanceInput
}

export type shopWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.shopWhereInput | Prisma.shopWhereInput[]
  OR?: Prisma.shopWhereInput[]
  NOT?: Prisma.shopWhereInput | Prisma.shopWhereInput[]
  name?: Prisma.StringFilter<"shop"> | string
  shopType?: Prisma.EnumShopTypesFilter<"shop"> | $Enums.ShopTypes
  avatar?: Prisma.StringNullableFilter<"shop"> | string | null
  description?: Prisma.StringFilter<"shop"> | string
  disabled?: Prisma.BoolNullableFilter<"shop"> | boolean | null
  quest?: Prisma.QuestListRelationFilter
  shop_listing?: Prisma.Shop_listingListRelationFilter
  trader_rep?: Prisma.Trader_repListRelationFilter
  explore_static_node?: Prisma.Explore_static_nodeListRelationFilter
}, "id">

export type shopOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  shopType?: Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  description?: Prisma.SortOrder
  disabled?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.shopCountOrderByAggregateInput
  _avg?: Prisma.shopAvgOrderByAggregateInput
  _max?: Prisma.shopMaxOrderByAggregateInput
  _min?: Prisma.shopMinOrderByAggregateInput
  _sum?: Prisma.shopSumOrderByAggregateInput
}

export type shopScalarWhereWithAggregatesInput = {
  AND?: Prisma.shopScalarWhereWithAggregatesInput | Prisma.shopScalarWhereWithAggregatesInput[]
  OR?: Prisma.shopScalarWhereWithAggregatesInput[]
  NOT?: Prisma.shopScalarWhereWithAggregatesInput | Prisma.shopScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"shop"> | number
  name?: Prisma.StringWithAggregatesFilter<"shop"> | string
  shopType?: Prisma.EnumShopTypesWithAggregatesFilter<"shop"> | $Enums.ShopTypes
  avatar?: Prisma.StringNullableWithAggregatesFilter<"shop"> | string | null
  description?: Prisma.StringWithAggregatesFilter<"shop"> | string
  disabled?: Prisma.BoolNullableWithAggregatesFilter<"shop"> | boolean | null
}

export type shopCreateInput = {
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeCreateNestedManyWithoutShopInput
}

export type shopUncheckedCreateInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questUncheckedCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repUncheckedCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedCreateNestedManyWithoutShopInput
}

export type shopUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUpdateManyWithoutShopNestedInput
}

export type shopUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUncheckedUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUncheckedUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedUpdateManyWithoutShopNestedInput
}

export type shopCreateManyInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
}

export type shopUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
}

export type shopUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
}

export type ShopNullableScalarRelationFilter = {
  is?: Prisma.shopWhereInput | null
  isNot?: Prisma.shopWhereInput | null
}

export type shopOrderByRelevanceInput = {
  fields: Prisma.shopOrderByRelevanceFieldEnum | Prisma.shopOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type shopCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  shopType?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  description?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
}

export type shopAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type shopMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  shopType?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  description?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
}

export type shopMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  shopType?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  description?: Prisma.SortOrder
  disabled?: Prisma.SortOrder
}

export type shopSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type shopCreateNestedOneWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutQuestInput, Prisma.shopUncheckedCreateWithoutQuestInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutQuestInput
  connect?: Prisma.shopWhereUniqueInput
}

export type shopUpdateOneWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutQuestInput, Prisma.shopUncheckedCreateWithoutQuestInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutQuestInput
  upsert?: Prisma.shopUpsertWithoutQuestInput
  disconnect?: Prisma.shopWhereInput | boolean
  delete?: Prisma.shopWhereInput | boolean
  connect?: Prisma.shopWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.shopUpdateToOneWithWhereWithoutQuestInput, Prisma.shopUpdateWithoutQuestInput>, Prisma.shopUncheckedUpdateWithoutQuestInput>
}

export type EnumShopTypesFieldUpdateOperationsInput = {
  set?: $Enums.ShopTypes
}

export type shopCreateNestedOneWithoutShop_listingInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutShop_listingInput, Prisma.shopUncheckedCreateWithoutShop_listingInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutShop_listingInput
  connect?: Prisma.shopWhereUniqueInput
}

export type shopUpdateOneWithoutShop_listingNestedInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutShop_listingInput, Prisma.shopUncheckedCreateWithoutShop_listingInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutShop_listingInput
  upsert?: Prisma.shopUpsertWithoutShop_listingInput
  disconnect?: Prisma.shopWhereInput | boolean
  delete?: Prisma.shopWhereInput | boolean
  connect?: Prisma.shopWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.shopUpdateToOneWithWhereWithoutShop_listingInput, Prisma.shopUpdateWithoutShop_listingInput>, Prisma.shopUncheckedUpdateWithoutShop_listingInput>
}

export type shopCreateNestedOneWithoutTrader_repInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutTrader_repInput, Prisma.shopUncheckedCreateWithoutTrader_repInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutTrader_repInput
  connect?: Prisma.shopWhereUniqueInput
}

export type shopUpdateOneWithoutTrader_repNestedInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutTrader_repInput, Prisma.shopUncheckedCreateWithoutTrader_repInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutTrader_repInput
  upsert?: Prisma.shopUpsertWithoutTrader_repInput
  disconnect?: Prisma.shopWhereInput | boolean
  delete?: Prisma.shopWhereInput | boolean
  connect?: Prisma.shopWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.shopUpdateToOneWithWhereWithoutTrader_repInput, Prisma.shopUpdateWithoutTrader_repInput>, Prisma.shopUncheckedUpdateWithoutTrader_repInput>
}

export type shopCreateNestedOneWithoutExplore_static_nodeInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutExplore_static_nodeInput, Prisma.shopUncheckedCreateWithoutExplore_static_nodeInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutExplore_static_nodeInput
  connect?: Prisma.shopWhereUniqueInput
}

export type shopUpdateOneWithoutExplore_static_nodeNestedInput = {
  create?: Prisma.XOR<Prisma.shopCreateWithoutExplore_static_nodeInput, Prisma.shopUncheckedCreateWithoutExplore_static_nodeInput>
  connectOrCreate?: Prisma.shopCreateOrConnectWithoutExplore_static_nodeInput
  upsert?: Prisma.shopUpsertWithoutExplore_static_nodeInput
  disconnect?: Prisma.shopWhereInput | boolean
  delete?: Prisma.shopWhereInput | boolean
  connect?: Prisma.shopWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.shopUpdateToOneWithWhereWithoutExplore_static_nodeInput, Prisma.shopUpdateWithoutExplore_static_nodeInput>, Prisma.shopUncheckedUpdateWithoutExplore_static_nodeInput>
}

export type shopCreateWithoutQuestInput = {
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeCreateNestedManyWithoutShopInput
}

export type shopUncheckedCreateWithoutQuestInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repUncheckedCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedCreateNestedManyWithoutShopInput
}

export type shopCreateOrConnectWithoutQuestInput = {
  where: Prisma.shopWhereUniqueInput
  create: Prisma.XOR<Prisma.shopCreateWithoutQuestInput, Prisma.shopUncheckedCreateWithoutQuestInput>
}

export type shopUpsertWithoutQuestInput = {
  update: Prisma.XOR<Prisma.shopUpdateWithoutQuestInput, Prisma.shopUncheckedUpdateWithoutQuestInput>
  create: Prisma.XOR<Prisma.shopCreateWithoutQuestInput, Prisma.shopUncheckedCreateWithoutQuestInput>
  where?: Prisma.shopWhereInput
}

export type shopUpdateToOneWithWhereWithoutQuestInput = {
  where?: Prisma.shopWhereInput
  data: Prisma.XOR<Prisma.shopUpdateWithoutQuestInput, Prisma.shopUncheckedUpdateWithoutQuestInput>
}

export type shopUpdateWithoutQuestInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  shop_listing?: Prisma.shop_listingUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUpdateManyWithoutShopNestedInput
}

export type shopUncheckedUpdateWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUncheckedUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedUpdateManyWithoutShopNestedInput
}

export type shopCreateWithoutShop_listingInput = {
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeCreateNestedManyWithoutShopInput
}

export type shopUncheckedCreateWithoutShop_listingInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questUncheckedCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repUncheckedCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedCreateNestedManyWithoutShopInput
}

export type shopCreateOrConnectWithoutShop_listingInput = {
  where: Prisma.shopWhereUniqueInput
  create: Prisma.XOR<Prisma.shopCreateWithoutShop_listingInput, Prisma.shopUncheckedCreateWithoutShop_listingInput>
}

export type shopUpsertWithoutShop_listingInput = {
  update: Prisma.XOR<Prisma.shopUpdateWithoutShop_listingInput, Prisma.shopUncheckedUpdateWithoutShop_listingInput>
  create: Prisma.XOR<Prisma.shopCreateWithoutShop_listingInput, Prisma.shopUncheckedCreateWithoutShop_listingInput>
  where?: Prisma.shopWhereInput
}

export type shopUpdateToOneWithWhereWithoutShop_listingInput = {
  where?: Prisma.shopWhereInput
  data: Prisma.XOR<Prisma.shopUpdateWithoutShop_listingInput, Prisma.shopUncheckedUpdateWithoutShop_listingInput>
}

export type shopUpdateWithoutShop_listingInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUpdateManyWithoutShopNestedInput
}

export type shopUncheckedUpdateWithoutShop_listingInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUncheckedUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUncheckedUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedUpdateManyWithoutShopNestedInput
}

export type shopCreateWithoutTrader_repInput = {
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeCreateNestedManyWithoutShopInput
}

export type shopUncheckedCreateWithoutTrader_repInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questUncheckedCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutShopInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedCreateNestedManyWithoutShopInput
}

export type shopCreateOrConnectWithoutTrader_repInput = {
  where: Prisma.shopWhereUniqueInput
  create: Prisma.XOR<Prisma.shopCreateWithoutTrader_repInput, Prisma.shopUncheckedCreateWithoutTrader_repInput>
}

export type shopUpsertWithoutTrader_repInput = {
  update: Prisma.XOR<Prisma.shopUpdateWithoutTrader_repInput, Prisma.shopUncheckedUpdateWithoutTrader_repInput>
  create: Prisma.XOR<Prisma.shopCreateWithoutTrader_repInput, Prisma.shopUncheckedCreateWithoutTrader_repInput>
  where?: Prisma.shopWhereInput
}

export type shopUpdateToOneWithWhereWithoutTrader_repInput = {
  where?: Prisma.shopWhereInput
  data: Prisma.XOR<Prisma.shopUpdateWithoutTrader_repInput, Prisma.shopUncheckedUpdateWithoutTrader_repInput>
}

export type shopUpdateWithoutTrader_repInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUpdateManyWithoutShopNestedInput
}

export type shopUncheckedUpdateWithoutTrader_repInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUncheckedUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutShopNestedInput
  explore_static_node?: Prisma.explore_static_nodeUncheckedUpdateManyWithoutShopNestedInput
}

export type shopCreateWithoutExplore_static_nodeInput = {
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repCreateNestedManyWithoutShopInput
}

export type shopUncheckedCreateWithoutExplore_static_nodeInput = {
  id?: number
  name: string
  shopType: $Enums.ShopTypes
  avatar?: string | null
  description: string
  disabled?: boolean | null
  quest?: Prisma.questUncheckedCreateNestedManyWithoutShopInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutShopInput
  trader_rep?: Prisma.trader_repUncheckedCreateNestedManyWithoutShopInput
}

export type shopCreateOrConnectWithoutExplore_static_nodeInput = {
  where: Prisma.shopWhereUniqueInput
  create: Prisma.XOR<Prisma.shopCreateWithoutExplore_static_nodeInput, Prisma.shopUncheckedCreateWithoutExplore_static_nodeInput>
}

export type shopUpsertWithoutExplore_static_nodeInput = {
  update: Prisma.XOR<Prisma.shopUpdateWithoutExplore_static_nodeInput, Prisma.shopUncheckedUpdateWithoutExplore_static_nodeInput>
  create: Prisma.XOR<Prisma.shopCreateWithoutExplore_static_nodeInput, Prisma.shopUncheckedCreateWithoutExplore_static_nodeInput>
  where?: Prisma.shopWhereInput
}

export type shopUpdateToOneWithWhereWithoutExplore_static_nodeInput = {
  where?: Prisma.shopWhereInput
  data: Prisma.XOR<Prisma.shopUpdateWithoutExplore_static_nodeInput, Prisma.shopUncheckedUpdateWithoutExplore_static_nodeInput>
}

export type shopUpdateWithoutExplore_static_nodeInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUpdateManyWithoutShopNestedInput
}

export type shopUncheckedUpdateWithoutExplore_static_nodeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  shopType?: Prisma.EnumShopTypesFieldUpdateOperationsInput | $Enums.ShopTypes
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.StringFieldUpdateOperationsInput | string
  disabled?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  quest?: Prisma.questUncheckedUpdateManyWithoutShopNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutShopNestedInput
  trader_rep?: Prisma.trader_repUncheckedUpdateManyWithoutShopNestedInput
}


/**
 * Count Type ShopCountOutputType
 */

export type ShopCountOutputType = {
  quest: number
  shop_listing: number
  trader_rep: number
  explore_static_node: number
}

export type ShopCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest?: boolean | ShopCountOutputTypeCountQuestArgs
  shop_listing?: boolean | ShopCountOutputTypeCountShop_listingArgs
  trader_rep?: boolean | ShopCountOutputTypeCountTrader_repArgs
  explore_static_node?: boolean | ShopCountOutputTypeCountExplore_static_nodeArgs
}

/**
 * ShopCountOutputType without action
 */
export type ShopCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ShopCountOutputType
   */
  select?: Prisma.ShopCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ShopCountOutputType without action
 */
export type ShopCountOutputTypeCountQuestArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.questWhereInput
}

/**
 * ShopCountOutputType without action
 */
export type ShopCountOutputTypeCountShop_listingArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shop_listingWhereInput
}

/**
 * ShopCountOutputType without action
 */
export type ShopCountOutputTypeCountTrader_repArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.trader_repWhereInput
}

/**
 * ShopCountOutputType without action
 */
export type ShopCountOutputTypeCountExplore_static_nodeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.explore_static_nodeWhereInput
}


export type shopSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  shopType?: boolean
  avatar?: boolean
  description?: boolean
  disabled?: boolean
  quest?: boolean | Prisma.shop$questArgs<ExtArgs>
  shop_listing?: boolean | Prisma.shop$shop_listingArgs<ExtArgs>
  trader_rep?: boolean | Prisma.shop$trader_repArgs<ExtArgs>
  explore_static_node?: boolean | Prisma.shop$explore_static_nodeArgs<ExtArgs>
  _count?: boolean | Prisma.ShopCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["shop"]>



export type shopSelectScalar = {
  id?: boolean
  name?: boolean
  shopType?: boolean
  avatar?: boolean
  description?: boolean
  disabled?: boolean
}

export type shopOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "shopType" | "avatar" | "description" | "disabled", ExtArgs["result"]["shop"]>
export type shopInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest?: boolean | Prisma.shop$questArgs<ExtArgs>
  shop_listing?: boolean | Prisma.shop$shop_listingArgs<ExtArgs>
  trader_rep?: boolean | Prisma.shop$trader_repArgs<ExtArgs>
  explore_static_node?: boolean | Prisma.shop$explore_static_nodeArgs<ExtArgs>
  _count?: boolean | Prisma.ShopCountOutputTypeDefaultArgs<ExtArgs>
}

export type $shopPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "shop"
  objects: {
    quest: Prisma.$questPayload<ExtArgs>[]
    shop_listing: Prisma.$shop_listingPayload<ExtArgs>[]
    trader_rep: Prisma.$trader_repPayload<ExtArgs>[]
    explore_static_node: Prisma.$explore_static_nodePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    shopType: $Enums.ShopTypes
    avatar: string | null
    description: string
    disabled: boolean | null
  }, ExtArgs["result"]["shop"]>
  composites: {}
}

export type shopGetPayload<S extends boolean | null | undefined | shopDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$shopPayload, S>

export type shopCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<shopFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ShopCountAggregateInputType | true
  }

export interface shopDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['shop'], meta: { name: 'shop' } }
  /**
   * Find zero or one Shop that matches the filter.
   * @param {shopFindUniqueArgs} args - Arguments to find a Shop
   * @example
   * // Get one Shop
   * const shop = await prisma.shop.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends shopFindUniqueArgs>(args: Prisma.SelectSubset<T, shopFindUniqueArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Shop that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {shopFindUniqueOrThrowArgs} args - Arguments to find a Shop
   * @example
   * // Get one Shop
   * const shop = await prisma.shop.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends shopFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, shopFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shop that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopFindFirstArgs} args - Arguments to find a Shop
   * @example
   * // Get one Shop
   * const shop = await prisma.shop.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends shopFindFirstArgs>(args?: Prisma.SelectSubset<T, shopFindFirstArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Shop that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopFindFirstOrThrowArgs} args - Arguments to find a Shop
   * @example
   * // Get one Shop
   * const shop = await prisma.shop.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends shopFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, shopFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Shops that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Shops
   * const shops = await prisma.shop.findMany()
   * 
   * // Get first 10 Shops
   * const shops = await prisma.shop.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const shopWithIdOnly = await prisma.shop.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends shopFindManyArgs>(args?: Prisma.SelectSubset<T, shopFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Shop.
   * @param {shopCreateArgs} args - Arguments to create a Shop.
   * @example
   * // Create one Shop
   * const Shop = await prisma.shop.create({
   *   data: {
   *     // ... data to create a Shop
   *   }
   * })
   * 
   */
  create<T extends shopCreateArgs>(args: Prisma.SelectSubset<T, shopCreateArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Shops.
   * @param {shopCreateManyArgs} args - Arguments to create many Shops.
   * @example
   * // Create many Shops
   * const shop = await prisma.shop.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends shopCreateManyArgs>(args?: Prisma.SelectSubset<T, shopCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Shop.
   * @param {shopDeleteArgs} args - Arguments to delete one Shop.
   * @example
   * // Delete one Shop
   * const Shop = await prisma.shop.delete({
   *   where: {
   *     // ... filter to delete one Shop
   *   }
   * })
   * 
   */
  delete<T extends shopDeleteArgs>(args: Prisma.SelectSubset<T, shopDeleteArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Shop.
   * @param {shopUpdateArgs} args - Arguments to update one Shop.
   * @example
   * // Update one Shop
   * const shop = await prisma.shop.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends shopUpdateArgs>(args: Prisma.SelectSubset<T, shopUpdateArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Shops.
   * @param {shopDeleteManyArgs} args - Arguments to filter Shops to delete.
   * @example
   * // Delete a few Shops
   * const { count } = await prisma.shop.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends shopDeleteManyArgs>(args?: Prisma.SelectSubset<T, shopDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Shops.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Shops
   * const shop = await prisma.shop.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends shopUpdateManyArgs>(args: Prisma.SelectSubset<T, shopUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Shop.
   * @param {shopUpsertArgs} args - Arguments to update or create a Shop.
   * @example
   * // Update or create a Shop
   * const shop = await prisma.shop.upsert({
   *   create: {
   *     // ... data to create a Shop
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Shop we want to update
   *   }
   * })
   */
  upsert<T extends shopUpsertArgs>(args: Prisma.SelectSubset<T, shopUpsertArgs<ExtArgs>>): Prisma.Prisma__shopClient<runtime.Types.Result.GetResult<Prisma.$shopPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Shops.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopCountArgs} args - Arguments to filter Shops to count.
   * @example
   * // Count the number of Shops
   * const count = await prisma.shop.count({
   *   where: {
   *     // ... the filter for the Shops we want to count
   *   }
   * })
  **/
  count<T extends shopCountArgs>(
    args?: Prisma.Subset<T, shopCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ShopCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Shop.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ShopAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ShopAggregateArgs>(args: Prisma.Subset<T, ShopAggregateArgs>): Prisma.PrismaPromise<GetShopAggregateType<T>>

  /**
   * Group by Shop.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {shopGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends shopGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: shopGroupByArgs['orderBy'] }
      : { orderBy?: shopGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, shopGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetShopGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the shop model
 */
readonly fields: shopFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for shop.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__shopClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest<T extends Prisma.shop$questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop$questArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  shop_listing<T extends Prisma.shop$shop_listingArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop$shop_listingArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  trader_rep<T extends Prisma.shop$trader_repArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop$trader_repArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$trader_repPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  explore_static_node<T extends Prisma.shop$explore_static_nodeArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.shop$explore_static_nodeArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$explore_static_nodePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the shop model
 */
export interface shopFieldRefs {
  readonly id: Prisma.FieldRef<"shop", 'Int'>
  readonly name: Prisma.FieldRef<"shop", 'String'>
  readonly shopType: Prisma.FieldRef<"shop", 'ShopTypes'>
  readonly avatar: Prisma.FieldRef<"shop", 'String'>
  readonly description: Prisma.FieldRef<"shop", 'String'>
  readonly disabled: Prisma.FieldRef<"shop", 'Boolean'>
}
    

// Custom InputTypes
/**
 * shop findUnique
 */
export type shopFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter, which shop to fetch.
   */
  where: Prisma.shopWhereUniqueInput
}

/**
 * shop findUniqueOrThrow
 */
export type shopFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter, which shop to fetch.
   */
  where: Prisma.shopWhereUniqueInput
}

/**
 * shop findFirst
 */
export type shopFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter, which shop to fetch.
   */
  where?: Prisma.shopWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shops to fetch.
   */
  orderBy?: Prisma.shopOrderByWithRelationInput | Prisma.shopOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shops.
   */
  cursor?: Prisma.shopWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shops from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shops.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shops.
   */
  distinct?: Prisma.ShopScalarFieldEnum | Prisma.ShopScalarFieldEnum[]
}

/**
 * shop findFirstOrThrow
 */
export type shopFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter, which shop to fetch.
   */
  where?: Prisma.shopWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shops to fetch.
   */
  orderBy?: Prisma.shopOrderByWithRelationInput | Prisma.shopOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for shops.
   */
  cursor?: Prisma.shopWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shops from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shops.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of shops.
   */
  distinct?: Prisma.ShopScalarFieldEnum | Prisma.ShopScalarFieldEnum[]
}

/**
 * shop findMany
 */
export type shopFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter, which shops to fetch.
   */
  where?: Prisma.shopWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of shops to fetch.
   */
  orderBy?: Prisma.shopOrderByWithRelationInput | Prisma.shopOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing shops.
   */
  cursor?: Prisma.shopWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` shops from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` shops.
   */
  skip?: number
  distinct?: Prisma.ShopScalarFieldEnum | Prisma.ShopScalarFieldEnum[]
}

/**
 * shop create
 */
export type shopCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * The data needed to create a shop.
   */
  data: Prisma.XOR<Prisma.shopCreateInput, Prisma.shopUncheckedCreateInput>
}

/**
 * shop createMany
 */
export type shopCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many shops.
   */
  data: Prisma.shopCreateManyInput | Prisma.shopCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * shop update
 */
export type shopUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * The data needed to update a shop.
   */
  data: Prisma.XOR<Prisma.shopUpdateInput, Prisma.shopUncheckedUpdateInput>
  /**
   * Choose, which shop to update.
   */
  where: Prisma.shopWhereUniqueInput
}

/**
 * shop updateMany
 */
export type shopUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update shops.
   */
  data: Prisma.XOR<Prisma.shopUpdateManyMutationInput, Prisma.shopUncheckedUpdateManyInput>
  /**
   * Filter which shops to update
   */
  where?: Prisma.shopWhereInput
  /**
   * Limit how many shops to update.
   */
  limit?: number
}

/**
 * shop upsert
 */
export type shopUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * The filter to search for the shop to update in case it exists.
   */
  where: Prisma.shopWhereUniqueInput
  /**
   * In case the shop found by the `where` argument doesn't exist, create a new shop with this data.
   */
  create: Prisma.XOR<Prisma.shopCreateInput, Prisma.shopUncheckedCreateInput>
  /**
   * In case the shop was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.shopUpdateInput, Prisma.shopUncheckedUpdateInput>
}

/**
 * shop delete
 */
export type shopDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
  /**
   * Filter which shop to delete.
   */
  where: Prisma.shopWhereUniqueInput
}

/**
 * shop deleteMany
 */
export type shopDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which shops to delete
   */
  where?: Prisma.shopWhereInput
  /**
   * Limit how many shops to delete.
   */
  limit?: number
}

/**
 * shop.quest
 */
export type shop$questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
  orderBy?: Prisma.questOrderByWithRelationInput | Prisma.questOrderByWithRelationInput[]
  cursor?: Prisma.questWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.QuestScalarFieldEnum | Prisma.QuestScalarFieldEnum[]
}

/**
 * shop.shop_listing
 */
export type shop$shop_listingArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  where?: Prisma.shop_listingWhereInput
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  cursor?: Prisma.shop_listingWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Shop_listingScalarFieldEnum | Prisma.Shop_listingScalarFieldEnum[]
}

/**
 * shop.trader_rep
 */
export type shop$trader_repArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the trader_rep
   */
  select?: Prisma.trader_repSelect<ExtArgs> | null
  /**
   * Omit specific fields from the trader_rep
   */
  omit?: Prisma.trader_repOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.trader_repInclude<ExtArgs> | null
  where?: Prisma.trader_repWhereInput
  orderBy?: Prisma.trader_repOrderByWithRelationInput | Prisma.trader_repOrderByWithRelationInput[]
  cursor?: Prisma.trader_repWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Trader_repScalarFieldEnum | Prisma.Trader_repScalarFieldEnum[]
}

/**
 * shop.explore_static_node
 */
export type shop$explore_static_nodeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_static_node
   */
  select?: Prisma.explore_static_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_static_node
   */
  omit?: Prisma.explore_static_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_static_nodeInclude<ExtArgs> | null
  where?: Prisma.explore_static_nodeWhereInput
  orderBy?: Prisma.explore_static_nodeOrderByWithRelationInput | Prisma.explore_static_nodeOrderByWithRelationInput[]
  cursor?: Prisma.explore_static_nodeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Explore_static_nodeScalarFieldEnum | Prisma.Explore_static_nodeScalarFieldEnum[]
}

/**
 * shop without action
 */
export type shopDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop
   */
  select?: Prisma.shopSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop
   */
  omit?: Prisma.shopOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shopInclude<ExtArgs> | null
}
