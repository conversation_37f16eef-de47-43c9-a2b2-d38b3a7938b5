
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `explore_player_node` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model explore_player_node
 * 
 */
export type explore_player_nodeModel = runtime.Types.Result.DefaultSelection<Prisma.$explore_player_nodePayload>

export type AggregateExplore_player_node = {
  _count: Explore_player_nodeCountAggregateOutputType | null
  _avg: Explore_player_nodeAvgAggregateOutputType | null
  _sum: Explore_player_nodeSumAggregateOutputType | null
  _min: Explore_player_nodeMinAggregateOutputType | null
  _max: Explore_player_nodeMaxAggregateOutputType | null
}

export type Explore_player_nodeAvgAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type Explore_player_nodeSumAggregateOutputType = {
  id: number | null
  userId: number | null
}

export type Explore_player_nodeMinAggregateOutputType = {
  id: number | null
  nodeType: $Enums.ExploreNodeType | null
  title: string | null
  description: string | null
  location: $Enums.ExploreNodeLocation | null
  status: $Enums.ExploreNodeStatus | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type Explore_player_nodeMaxAggregateOutputType = {
  id: number | null
  nodeType: $Enums.ExploreNodeType | null
  title: string | null
  description: string | null
  location: $Enums.ExploreNodeLocation | null
  status: $Enums.ExploreNodeStatus | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
}

export type Explore_player_nodeCountAggregateOutputType = {
  id: number
  nodeType: number
  title: number
  description: number
  position: number
  metadata: number
  location: number
  status: number
  expiresAt: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}


export type Explore_player_nodeAvgAggregateInputType = {
  id?: true
  userId?: true
}

export type Explore_player_nodeSumAggregateInputType = {
  id?: true
  userId?: true
}

export type Explore_player_nodeMinAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  location?: true
  status?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type Explore_player_nodeMaxAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  location?: true
  status?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type Explore_player_nodeCountAggregateInputType = {
  id?: true
  nodeType?: true
  title?: true
  description?: true
  position?: true
  metadata?: true
  location?: true
  status?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type Explore_player_nodeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which explore_player_node to aggregate.
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_player_nodes to fetch.
   */
  orderBy?: Prisma.explore_player_nodeOrderByWithRelationInput | Prisma.explore_player_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.explore_player_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_player_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_player_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned explore_player_nodes
  **/
  _count?: true | Explore_player_nodeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Explore_player_nodeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Explore_player_nodeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Explore_player_nodeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Explore_player_nodeMaxAggregateInputType
}

export type GetExplore_player_nodeAggregateType<T extends Explore_player_nodeAggregateArgs> = {
      [P in keyof T & keyof AggregateExplore_player_node]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateExplore_player_node[P]>
    : Prisma.GetScalarType<T[P], AggregateExplore_player_node[P]>
}




export type explore_player_nodeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.explore_player_nodeWhereInput
  orderBy?: Prisma.explore_player_nodeOrderByWithAggregationInput | Prisma.explore_player_nodeOrderByWithAggregationInput[]
  by: Prisma.Explore_player_nodeScalarFieldEnum[] | Prisma.Explore_player_nodeScalarFieldEnum
  having?: Prisma.explore_player_nodeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Explore_player_nodeCountAggregateInputType | true
  _avg?: Explore_player_nodeAvgAggregateInputType
  _sum?: Explore_player_nodeSumAggregateInputType
  _min?: Explore_player_nodeMinAggregateInputType
  _max?: Explore_player_nodeMaxAggregateInputType
}

export type Explore_player_nodeGroupByOutputType = {
  id: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position: runtime.JsonValue
  metadata: runtime.JsonValue | null
  location: $Enums.ExploreNodeLocation
  status: $Enums.ExploreNodeStatus
  expiresAt: Date | null
  createdAt: Date
  updatedAt: Date
  userId: number
  _count: Explore_player_nodeCountAggregateOutputType | null
  _avg: Explore_player_nodeAvgAggregateOutputType | null
  _sum: Explore_player_nodeSumAggregateOutputType | null
  _min: Explore_player_nodeMinAggregateOutputType | null
  _max: Explore_player_nodeMaxAggregateOutputType | null
}

type GetExplore_player_nodeGroupByPayload<T extends explore_player_nodeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Explore_player_nodeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Explore_player_nodeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Explore_player_nodeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Explore_player_nodeGroupByOutputType[P]>
      }
    >
  >



export type explore_player_nodeWhereInput = {
  AND?: Prisma.explore_player_nodeWhereInput | Prisma.explore_player_nodeWhereInput[]
  OR?: Prisma.explore_player_nodeWhereInput[]
  NOT?: Prisma.explore_player_nodeWhereInput | Prisma.explore_player_nodeWhereInput[]
  id?: Prisma.IntFilter<"explore_player_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_player_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_player_node"> | string
  description?: Prisma.StringFilter<"explore_player_node"> | string
  position?: Prisma.JsonFilter<"explore_player_node">
  metadata?: Prisma.JsonNullableFilter<"explore_player_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_player_node"> | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFilter<"explore_player_node"> | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.DateTimeNullableFilter<"explore_player_node"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  userId?: Prisma.IntFilter<"explore_player_node"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type explore_player_nodeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  _relevance?: Prisma.explore_player_nodeOrderByRelevanceInput
}

export type explore_player_nodeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.explore_player_nodeWhereInput | Prisma.explore_player_nodeWhereInput[]
  OR?: Prisma.explore_player_nodeWhereInput[]
  NOT?: Prisma.explore_player_nodeWhereInput | Prisma.explore_player_nodeWhereInput[]
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_player_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_player_node"> | string
  description?: Prisma.StringFilter<"explore_player_node"> | string
  position?: Prisma.JsonFilter<"explore_player_node">
  metadata?: Prisma.JsonNullableFilter<"explore_player_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_player_node"> | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFilter<"explore_player_node"> | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.DateTimeNullableFilter<"explore_player_node"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  userId?: Prisma.IntFilter<"explore_player_node"> | number
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "id">

export type explore_player_nodeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.explore_player_nodeCountOrderByAggregateInput
  _avg?: Prisma.explore_player_nodeAvgOrderByAggregateInput
  _max?: Prisma.explore_player_nodeMaxOrderByAggregateInput
  _min?: Prisma.explore_player_nodeMinOrderByAggregateInput
  _sum?: Prisma.explore_player_nodeSumOrderByAggregateInput
}

export type explore_player_nodeScalarWhereWithAggregatesInput = {
  AND?: Prisma.explore_player_nodeScalarWhereWithAggregatesInput | Prisma.explore_player_nodeScalarWhereWithAggregatesInput[]
  OR?: Prisma.explore_player_nodeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.explore_player_nodeScalarWhereWithAggregatesInput | Prisma.explore_player_nodeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"explore_player_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeWithAggregatesFilter<"explore_player_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringWithAggregatesFilter<"explore_player_node"> | string
  description?: Prisma.StringWithAggregatesFilter<"explore_player_node"> | string
  position?: Prisma.JsonWithAggregatesFilter<"explore_player_node">
  metadata?: Prisma.JsonNullableWithAggregatesFilter<"explore_player_node">
  location?: Prisma.EnumExploreNodeLocationWithAggregatesFilter<"explore_player_node"> | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusWithAggregatesFilter<"explore_player_node"> | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.DateTimeNullableWithAggregatesFilter<"explore_player_node"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"explore_player_node"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"explore_player_node"> | Date | string
  userId?: Prisma.IntWithAggregatesFilter<"explore_player_node"> | number
}

export type explore_player_nodeCreateInput = {
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutExploreNodesInput
}

export type explore_player_nodeUncheckedCreateInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type explore_player_nodeUpdateInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutExploreNodesNestedInput
}

export type explore_player_nodeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type explore_player_nodeCreateManyInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type explore_player_nodeUpdateManyMutationInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_player_nodeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type Explore_player_nodeListRelationFilter = {
  every?: Prisma.explore_player_nodeWhereInput
  some?: Prisma.explore_player_nodeWhereInput
  none?: Prisma.explore_player_nodeWhereInput
}

export type explore_player_nodeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type explore_player_nodeOrderByRelevanceInput = {
  fields: Prisma.explore_player_nodeOrderByRelevanceFieldEnum | Prisma.explore_player_nodeOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type explore_player_nodeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  position?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
  location?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type explore_player_nodeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type explore_player_nodeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  location?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type explore_player_nodeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nodeType?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  location?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type explore_player_nodeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type explore_player_nodeCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput> | Prisma.explore_player_nodeCreateWithoutUserInput[] | Prisma.explore_player_nodeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.explore_player_nodeCreateOrConnectWithoutUserInput | Prisma.explore_player_nodeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.explore_player_nodeCreateManyUserInputEnvelope
  connect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
}

export type explore_player_nodeUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput> | Prisma.explore_player_nodeCreateWithoutUserInput[] | Prisma.explore_player_nodeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.explore_player_nodeCreateOrConnectWithoutUserInput | Prisma.explore_player_nodeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.explore_player_nodeCreateManyUserInputEnvelope
  connect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
}

export type explore_player_nodeUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput> | Prisma.explore_player_nodeCreateWithoutUserInput[] | Prisma.explore_player_nodeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.explore_player_nodeCreateOrConnectWithoutUserInput | Prisma.explore_player_nodeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.explore_player_nodeUpsertWithWhereUniqueWithoutUserInput | Prisma.explore_player_nodeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.explore_player_nodeCreateManyUserInputEnvelope
  set?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  disconnect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  delete?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  connect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  update?: Prisma.explore_player_nodeUpdateWithWhereUniqueWithoutUserInput | Prisma.explore_player_nodeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.explore_player_nodeUpdateManyWithWhereWithoutUserInput | Prisma.explore_player_nodeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.explore_player_nodeScalarWhereInput | Prisma.explore_player_nodeScalarWhereInput[]
}

export type explore_player_nodeUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput> | Prisma.explore_player_nodeCreateWithoutUserInput[] | Prisma.explore_player_nodeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.explore_player_nodeCreateOrConnectWithoutUserInput | Prisma.explore_player_nodeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.explore_player_nodeUpsertWithWhereUniqueWithoutUserInput | Prisma.explore_player_nodeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.explore_player_nodeCreateManyUserInputEnvelope
  set?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  disconnect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  delete?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  connect?: Prisma.explore_player_nodeWhereUniqueInput | Prisma.explore_player_nodeWhereUniqueInput[]
  update?: Prisma.explore_player_nodeUpdateWithWhereUniqueWithoutUserInput | Prisma.explore_player_nodeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.explore_player_nodeUpdateManyWithWhereWithoutUserInput | Prisma.explore_player_nodeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.explore_player_nodeScalarWhereInput | Prisma.explore_player_nodeScalarWhereInput[]
}

export type EnumExploreNodeStatusFieldUpdateOperationsInput = {
  set?: $Enums.ExploreNodeStatus
}

export type explore_player_nodeCreateWithoutUserInput = {
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_player_nodeUncheckedCreateWithoutUserInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_player_nodeCreateOrConnectWithoutUserInput = {
  where: Prisma.explore_player_nodeWhereUniqueInput
  create: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput>
}

export type explore_player_nodeCreateManyUserInputEnvelope = {
  data: Prisma.explore_player_nodeCreateManyUserInput | Prisma.explore_player_nodeCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type explore_player_nodeUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.explore_player_nodeWhereUniqueInput
  update: Prisma.XOR<Prisma.explore_player_nodeUpdateWithoutUserInput, Prisma.explore_player_nodeUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.explore_player_nodeCreateWithoutUserInput, Prisma.explore_player_nodeUncheckedCreateWithoutUserInput>
}

export type explore_player_nodeUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.explore_player_nodeWhereUniqueInput
  data: Prisma.XOR<Prisma.explore_player_nodeUpdateWithoutUserInput, Prisma.explore_player_nodeUncheckedUpdateWithoutUserInput>
}

export type explore_player_nodeUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.explore_player_nodeScalarWhereInput
  data: Prisma.XOR<Prisma.explore_player_nodeUpdateManyMutationInput, Prisma.explore_player_nodeUncheckedUpdateManyWithoutUserInput>
}

export type explore_player_nodeScalarWhereInput = {
  AND?: Prisma.explore_player_nodeScalarWhereInput | Prisma.explore_player_nodeScalarWhereInput[]
  OR?: Prisma.explore_player_nodeScalarWhereInput[]
  NOT?: Prisma.explore_player_nodeScalarWhereInput | Prisma.explore_player_nodeScalarWhereInput[]
  id?: Prisma.IntFilter<"explore_player_node"> | number
  nodeType?: Prisma.EnumExploreNodeTypeFilter<"explore_player_node"> | $Enums.ExploreNodeType
  title?: Prisma.StringFilter<"explore_player_node"> | string
  description?: Prisma.StringFilter<"explore_player_node"> | string
  position?: Prisma.JsonFilter<"explore_player_node">
  metadata?: Prisma.JsonNullableFilter<"explore_player_node">
  location?: Prisma.EnumExploreNodeLocationFilter<"explore_player_node"> | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFilter<"explore_player_node"> | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.DateTimeNullableFilter<"explore_player_node"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"explore_player_node"> | Date | string
  userId?: Prisma.IntFilter<"explore_player_node"> | number
}

export type explore_player_nodeCreateManyUserInput = {
  id?: number
  nodeType: $Enums.ExploreNodeType
  title: string
  description: string
  position:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location: $Enums.ExploreNodeLocation
  status?: $Enums.ExploreNodeStatus
  expiresAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type explore_player_nodeUpdateWithoutUserInput = {
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_player_nodeUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type explore_player_nodeUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nodeType?: Prisma.EnumExploreNodeTypeFieldUpdateOperationsInput | $Enums.ExploreNodeType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  position?:unknown
  metadata?:unknown | Prisma.NullableJsonNullValueInput
  location?: Prisma.EnumExploreNodeLocationFieldUpdateOperationsInput | $Enums.ExploreNodeLocation
  status?: Prisma.EnumExploreNodeStatusFieldUpdateOperationsInput | $Enums.ExploreNodeStatus
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type explore_player_nodeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  nodeType?: boolean
  title?: boolean
  description?: boolean
  position?: boolean
  metadata?: boolean
  location?: boolean
  status?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["explore_player_node"]>



export type explore_player_nodeSelectScalar = {
  id?: boolean
  nodeType?: boolean
  title?: boolean
  description?: boolean
  position?: boolean
  metadata?: boolean
  location?: boolean
  status?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type explore_player_nodeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "nodeType" | "title" | "description" | "position" | "metadata" | "location" | "status" | "expiresAt" | "createdAt" | "updatedAt" | "userId", ExtArgs["result"]["explore_player_node"]>
export type explore_player_nodeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $explore_player_nodePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "explore_player_node"
  objects: {
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    nodeType: $Enums.ExploreNodeType
    title: string
    description: string
    position:unknown
    metadata:unknown | null
    location: $Enums.ExploreNodeLocation
    status: $Enums.ExploreNodeStatus
    expiresAt: Date | null
    createdAt: Date
    updatedAt: Date
    userId: number
  }, ExtArgs["result"]["explore_player_node"]>
  composites: {}
}

export type explore_player_nodeGetPayload<S extends boolean | null | undefined | explore_player_nodeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload, S>

export type explore_player_nodeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<explore_player_nodeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Explore_player_nodeCountAggregateInputType | true
  }

export interface explore_player_nodeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['explore_player_node'], meta: { name: 'explore_player_node' } }
  /**
   * Find zero or one Explore_player_node that matches the filter.
   * @param {explore_player_nodeFindUniqueArgs} args - Arguments to find a Explore_player_node
   * @example
   * // Get one Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends explore_player_nodeFindUniqueArgs>(args: Prisma.SelectSubset<T, explore_player_nodeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Explore_player_node that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {explore_player_nodeFindUniqueOrThrowArgs} args - Arguments to find a Explore_player_node
   * @example
   * // Get one Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends explore_player_nodeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, explore_player_nodeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Explore_player_node that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeFindFirstArgs} args - Arguments to find a Explore_player_node
   * @example
   * // Get one Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends explore_player_nodeFindFirstArgs>(args?: Prisma.SelectSubset<T, explore_player_nodeFindFirstArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Explore_player_node that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeFindFirstOrThrowArgs} args - Arguments to find a Explore_player_node
   * @example
   * // Get one Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends explore_player_nodeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, explore_player_nodeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Explore_player_nodes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Explore_player_nodes
   * const explore_player_nodes = await prisma.explore_player_node.findMany()
   * 
   * // Get first 10 Explore_player_nodes
   * const explore_player_nodes = await prisma.explore_player_node.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const explore_player_nodeWithIdOnly = await prisma.explore_player_node.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends explore_player_nodeFindManyArgs>(args?: Prisma.SelectSubset<T, explore_player_nodeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Explore_player_node.
   * @param {explore_player_nodeCreateArgs} args - Arguments to create a Explore_player_node.
   * @example
   * // Create one Explore_player_node
   * const Explore_player_node = await prisma.explore_player_node.create({
   *   data: {
   *     // ... data to create a Explore_player_node
   *   }
   * })
   * 
   */
  create<T extends explore_player_nodeCreateArgs>(args: Prisma.SelectSubset<T, explore_player_nodeCreateArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Explore_player_nodes.
   * @param {explore_player_nodeCreateManyArgs} args - Arguments to create many Explore_player_nodes.
   * @example
   * // Create many Explore_player_nodes
   * const explore_player_node = await prisma.explore_player_node.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends explore_player_nodeCreateManyArgs>(args?: Prisma.SelectSubset<T, explore_player_nodeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Explore_player_node.
   * @param {explore_player_nodeDeleteArgs} args - Arguments to delete one Explore_player_node.
   * @example
   * // Delete one Explore_player_node
   * const Explore_player_node = await prisma.explore_player_node.delete({
   *   where: {
   *     // ... filter to delete one Explore_player_node
   *   }
   * })
   * 
   */
  delete<T extends explore_player_nodeDeleteArgs>(args: Prisma.SelectSubset<T, explore_player_nodeDeleteArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Explore_player_node.
   * @param {explore_player_nodeUpdateArgs} args - Arguments to update one Explore_player_node.
   * @example
   * // Update one Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends explore_player_nodeUpdateArgs>(args: Prisma.SelectSubset<T, explore_player_nodeUpdateArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Explore_player_nodes.
   * @param {explore_player_nodeDeleteManyArgs} args - Arguments to filter Explore_player_nodes to delete.
   * @example
   * // Delete a few Explore_player_nodes
   * const { count } = await prisma.explore_player_node.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends explore_player_nodeDeleteManyArgs>(args?: Prisma.SelectSubset<T, explore_player_nodeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Explore_player_nodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Explore_player_nodes
   * const explore_player_node = await prisma.explore_player_node.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends explore_player_nodeUpdateManyArgs>(args: Prisma.SelectSubset<T, explore_player_nodeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Explore_player_node.
   * @param {explore_player_nodeUpsertArgs} args - Arguments to update or create a Explore_player_node.
   * @example
   * // Update or create a Explore_player_node
   * const explore_player_node = await prisma.explore_player_node.upsert({
   *   create: {
   *     // ... data to create a Explore_player_node
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Explore_player_node we want to update
   *   }
   * })
   */
  upsert<T extends explore_player_nodeUpsertArgs>(args: Prisma.SelectSubset<T, explore_player_nodeUpsertArgs<ExtArgs>>): Prisma.Prisma__explore_player_nodeClient<runtime.Types.Result.GetResult<Prisma.$explore_player_nodePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Explore_player_nodes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeCountArgs} args - Arguments to filter Explore_player_nodes to count.
   * @example
   * // Count the number of Explore_player_nodes
   * const count = await prisma.explore_player_node.count({
   *   where: {
   *     // ... the filter for the Explore_player_nodes we want to count
   *   }
   * })
  **/
  count<T extends explore_player_nodeCountArgs>(
    args?: Prisma.Subset<T, explore_player_nodeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Explore_player_nodeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Explore_player_node.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Explore_player_nodeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Explore_player_nodeAggregateArgs>(args: Prisma.Subset<T, Explore_player_nodeAggregateArgs>): Prisma.PrismaPromise<GetExplore_player_nodeAggregateType<T>>

  /**
   * Group by Explore_player_node.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {explore_player_nodeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends explore_player_nodeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: explore_player_nodeGroupByArgs['orderBy'] }
      : { orderBy?: explore_player_nodeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, explore_player_nodeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetExplore_player_nodeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the explore_player_node model
 */
readonly fields: explore_player_nodeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for explore_player_node.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__explore_player_nodeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the explore_player_node model
 */
export interface explore_player_nodeFieldRefs {
  readonly id: Prisma.FieldRef<"explore_player_node", 'Int'>
  readonly nodeType: Prisma.FieldRef<"explore_player_node", 'ExploreNodeType'>
  readonly title: Prisma.FieldRef<"explore_player_node", 'String'>
  readonly description: Prisma.FieldRef<"explore_player_node", 'String'>
  readonly position: Prisma.FieldRef<"explore_player_node", 'Json'>
  readonly metadata: Prisma.FieldRef<"explore_player_node", 'Json'>
  readonly location: Prisma.FieldRef<"explore_player_node", 'ExploreNodeLocation'>
  readonly status: Prisma.FieldRef<"explore_player_node", 'ExploreNodeStatus'>
  readonly expiresAt: Prisma.FieldRef<"explore_player_node", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"explore_player_node", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"explore_player_node", 'DateTime'>
  readonly userId: Prisma.FieldRef<"explore_player_node", 'Int'>
}
    

// Custom InputTypes
/**
 * explore_player_node findUnique
 */
export type explore_player_nodeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_player_node to fetch.
   */
  where: Prisma.explore_player_nodeWhereUniqueInput
}

/**
 * explore_player_node findUniqueOrThrow
 */
export type explore_player_nodeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_player_node to fetch.
   */
  where: Prisma.explore_player_nodeWhereUniqueInput
}

/**
 * explore_player_node findFirst
 */
export type explore_player_nodeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_player_node to fetch.
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_player_nodes to fetch.
   */
  orderBy?: Prisma.explore_player_nodeOrderByWithRelationInput | Prisma.explore_player_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for explore_player_nodes.
   */
  cursor?: Prisma.explore_player_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_player_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_player_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of explore_player_nodes.
   */
  distinct?: Prisma.Explore_player_nodeScalarFieldEnum | Prisma.Explore_player_nodeScalarFieldEnum[]
}

/**
 * explore_player_node findFirstOrThrow
 */
export type explore_player_nodeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_player_node to fetch.
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_player_nodes to fetch.
   */
  orderBy?: Prisma.explore_player_nodeOrderByWithRelationInput | Prisma.explore_player_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for explore_player_nodes.
   */
  cursor?: Prisma.explore_player_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_player_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_player_nodes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of explore_player_nodes.
   */
  distinct?: Prisma.Explore_player_nodeScalarFieldEnum | Prisma.Explore_player_nodeScalarFieldEnum[]
}

/**
 * explore_player_node findMany
 */
export type explore_player_nodeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter, which explore_player_nodes to fetch.
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of explore_player_nodes to fetch.
   */
  orderBy?: Prisma.explore_player_nodeOrderByWithRelationInput | Prisma.explore_player_nodeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing explore_player_nodes.
   */
  cursor?: Prisma.explore_player_nodeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` explore_player_nodes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` explore_player_nodes.
   */
  skip?: number
  distinct?: Prisma.Explore_player_nodeScalarFieldEnum | Prisma.Explore_player_nodeScalarFieldEnum[]
}

/**
 * explore_player_node create
 */
export type explore_player_nodeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * The data needed to create a explore_player_node.
   */
  data: Prisma.XOR<Prisma.explore_player_nodeCreateInput, Prisma.explore_player_nodeUncheckedCreateInput>
}

/**
 * explore_player_node createMany
 */
export type explore_player_nodeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many explore_player_nodes.
   */
  data: Prisma.explore_player_nodeCreateManyInput | Prisma.explore_player_nodeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * explore_player_node update
 */
export type explore_player_nodeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * The data needed to update a explore_player_node.
   */
  data: Prisma.XOR<Prisma.explore_player_nodeUpdateInput, Prisma.explore_player_nodeUncheckedUpdateInput>
  /**
   * Choose, which explore_player_node to update.
   */
  where: Prisma.explore_player_nodeWhereUniqueInput
}

/**
 * explore_player_node updateMany
 */
export type explore_player_nodeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update explore_player_nodes.
   */
  data: Prisma.XOR<Prisma.explore_player_nodeUpdateManyMutationInput, Prisma.explore_player_nodeUncheckedUpdateManyInput>
  /**
   * Filter which explore_player_nodes to update
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * Limit how many explore_player_nodes to update.
   */
  limit?: number
}

/**
 * explore_player_node upsert
 */
export type explore_player_nodeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * The filter to search for the explore_player_node to update in case it exists.
   */
  where: Prisma.explore_player_nodeWhereUniqueInput
  /**
   * In case the explore_player_node found by the `where` argument doesn't exist, create a new explore_player_node with this data.
   */
  create: Prisma.XOR<Prisma.explore_player_nodeCreateInput, Prisma.explore_player_nodeUncheckedCreateInput>
  /**
   * In case the explore_player_node was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.explore_player_nodeUpdateInput, Prisma.explore_player_nodeUncheckedUpdateInput>
}

/**
 * explore_player_node delete
 */
export type explore_player_nodeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
  /**
   * Filter which explore_player_node to delete.
   */
  where: Prisma.explore_player_nodeWhereUniqueInput
}

/**
 * explore_player_node deleteMany
 */
export type explore_player_nodeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which explore_player_nodes to delete
   */
  where?: Prisma.explore_player_nodeWhereInput
  /**
   * Limit how many explore_player_nodes to delete.
   */
  limit?: number
}

/**
 * explore_player_node without action
 */
export type explore_player_nodeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the explore_player_node
   */
  select?: Prisma.explore_player_nodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the explore_player_node
   */
  omit?: Prisma.explore_player_nodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.explore_player_nodeInclude<ExtArgs> | null
}
