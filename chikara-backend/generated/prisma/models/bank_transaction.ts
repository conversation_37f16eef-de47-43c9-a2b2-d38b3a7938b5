
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `bank_transaction` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model bank_transaction
 * 
 */
export type bank_transactionModel = runtime.Types.Result.DefaultSelection<Prisma.$bank_transactionPayload>

export type AggregateBank_transaction = {
  _count: Bank_transactionCountAggregateOutputType | null
  _avg: Bank_transactionAvgAggregateOutputType | null
  _sum: Bank_transactionSumAggregateOutputType | null
  _min: Bank_transactionMinAggregateOutputType | null
  _max: Bank_transactionMaxAggregateOutputType | null
}

export type Bank_transactionAvgAggregateOutputType = {
  id: number | null
  cash: number | null
  transactionFee: number | null
  initiatorCashBalance: number | null
  initiatorBankBalance: number | null
  secondPartyCashBalance: number | null
  secondPartyBankBalance: number | null
  initiatorId: number | null
  secondPartyId: number | null
  userId: number | null
}

export type Bank_transactionSumAggregateOutputType = {
  id: number | null
  cash: number | null
  transactionFee: number | null
  initiatorCashBalance: number | null
  initiatorBankBalance: number | null
  secondPartyCashBalance: number | null
  secondPartyBankBalance: number | null
  initiatorId: number | null
  secondPartyId: number | null
  userId: number | null
}

export type Bank_transactionMinAggregateOutputType = {
  id: number | null
  transaction_type: $Enums.BankTransactionTypes | null
  cash: number | null
  transactionFee: number | null
  initiatorCashBalance: number | null
  initiatorBankBalance: number | null
  secondPartyCashBalance: number | null
  secondPartyBankBalance: number | null
  createdAt: Date | null
  updatedAt: Date | null
  initiatorId: number | null
  secondPartyId: number | null
  userId: number | null
}

export type Bank_transactionMaxAggregateOutputType = {
  id: number | null
  transaction_type: $Enums.BankTransactionTypes | null
  cash: number | null
  transactionFee: number | null
  initiatorCashBalance: number | null
  initiatorBankBalance: number | null
  secondPartyCashBalance: number | null
  secondPartyBankBalance: number | null
  createdAt: Date | null
  updatedAt: Date | null
  initiatorId: number | null
  secondPartyId: number | null
  userId: number | null
}

export type Bank_transactionCountAggregateOutputType = {
  id: number
  transaction_type: number
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance: number
  secondPartyBankBalance: number
  createdAt: number
  updatedAt: number
  initiatorId: number
  secondPartyId: number
  userId: number
  _all: number
}


export type Bank_transactionAvgAggregateInputType = {
  id?: true
  cash?: true
  transactionFee?: true
  initiatorCashBalance?: true
  initiatorBankBalance?: true
  secondPartyCashBalance?: true
  secondPartyBankBalance?: true
  initiatorId?: true
  secondPartyId?: true
  userId?: true
}

export type Bank_transactionSumAggregateInputType = {
  id?: true
  cash?: true
  transactionFee?: true
  initiatorCashBalance?: true
  initiatorBankBalance?: true
  secondPartyCashBalance?: true
  secondPartyBankBalance?: true
  initiatorId?: true
  secondPartyId?: true
  userId?: true
}

export type Bank_transactionMinAggregateInputType = {
  id?: true
  transaction_type?: true
  cash?: true
  transactionFee?: true
  initiatorCashBalance?: true
  initiatorBankBalance?: true
  secondPartyCashBalance?: true
  secondPartyBankBalance?: true
  createdAt?: true
  updatedAt?: true
  initiatorId?: true
  secondPartyId?: true
  userId?: true
}

export type Bank_transactionMaxAggregateInputType = {
  id?: true
  transaction_type?: true
  cash?: true
  transactionFee?: true
  initiatorCashBalance?: true
  initiatorBankBalance?: true
  secondPartyCashBalance?: true
  secondPartyBankBalance?: true
  createdAt?: true
  updatedAt?: true
  initiatorId?: true
  secondPartyId?: true
  userId?: true
}

export type Bank_transactionCountAggregateInputType = {
  id?: true
  transaction_type?: true
  cash?: true
  transactionFee?: true
  initiatorCashBalance?: true
  initiatorBankBalance?: true
  secondPartyCashBalance?: true
  secondPartyBankBalance?: true
  createdAt?: true
  updatedAt?: true
  initiatorId?: true
  secondPartyId?: true
  userId?: true
  _all?: true
}

export type Bank_transactionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which bank_transaction to aggregate.
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bank_transactions to fetch.
   */
  orderBy?: Prisma.bank_transactionOrderByWithRelationInput | Prisma.bank_transactionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.bank_transactionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bank_transactions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bank_transactions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned bank_transactions
  **/
  _count?: true | Bank_transactionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Bank_transactionAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Bank_transactionSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Bank_transactionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Bank_transactionMaxAggregateInputType
}

export type GetBank_transactionAggregateType<T extends Bank_transactionAggregateArgs> = {
      [P in keyof T & keyof AggregateBank_transaction]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateBank_transaction[P]>
    : Prisma.GetScalarType<T[P], AggregateBank_transaction[P]>
}




export type bank_transactionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.bank_transactionWhereInput
  orderBy?: Prisma.bank_transactionOrderByWithAggregationInput | Prisma.bank_transactionOrderByWithAggregationInput[]
  by: Prisma.Bank_transactionScalarFieldEnum[] | Prisma.Bank_transactionScalarFieldEnum
  having?: Prisma.bank_transactionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Bank_transactionCountAggregateInputType | true
  _avg?: Bank_transactionAvgAggregateInputType
  _sum?: Bank_transactionSumAggregateInputType
  _min?: Bank_transactionMinAggregateInputType
  _max?: Bank_transactionMaxAggregateInputType
}

export type Bank_transactionGroupByOutputType = {
  id: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance: number | null
  secondPartyBankBalance: number | null
  createdAt: Date
  updatedAt: Date
  initiatorId: number | null
  secondPartyId: number | null
  userId: number | null
  _count: Bank_transactionCountAggregateOutputType | null
  _avg: Bank_transactionAvgAggregateOutputType | null
  _sum: Bank_transactionSumAggregateOutputType | null
  _min: Bank_transactionMinAggregateOutputType | null
  _max: Bank_transactionMaxAggregateOutputType | null
}

type GetBank_transactionGroupByPayload<T extends bank_transactionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Bank_transactionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Bank_transactionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Bank_transactionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Bank_transactionGroupByOutputType[P]>
      }
    >
  >



export type bank_transactionWhereInput = {
  AND?: Prisma.bank_transactionWhereInput | Prisma.bank_transactionWhereInput[]
  OR?: Prisma.bank_transactionWhereInput[]
  NOT?: Prisma.bank_transactionWhereInput | Prisma.bank_transactionWhereInput[]
  id?: Prisma.IntFilter<"bank_transaction"> | number
  transaction_type?: Prisma.EnumBankTransactionTypesFilter<"bank_transaction"> | $Enums.BankTransactionTypes
  cash?: Prisma.IntFilter<"bank_transaction"> | number
  transactionFee?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorCashBalance?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorBankBalance?: Prisma.IntFilter<"bank_transaction"> | number
  secondPartyCashBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyBankBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  createdAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  initiatorId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  userId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  initiator?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  secondParty?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type bank_transactionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  transaction_type?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  initiator?: Prisma.userOrderByWithRelationInput
  secondParty?: Prisma.userOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type bank_transactionWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.bank_transactionWhereInput | Prisma.bank_transactionWhereInput[]
  OR?: Prisma.bank_transactionWhereInput[]
  NOT?: Prisma.bank_transactionWhereInput | Prisma.bank_transactionWhereInput[]
  transaction_type?: Prisma.EnumBankTransactionTypesFilter<"bank_transaction"> | $Enums.BankTransactionTypes
  cash?: Prisma.IntFilter<"bank_transaction"> | number
  transactionFee?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorCashBalance?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorBankBalance?: Prisma.IntFilter<"bank_transaction"> | number
  secondPartyCashBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyBankBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  createdAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  initiatorId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  userId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  initiator?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  secondParty?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type bank_transactionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  transaction_type?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrderInput | Prisma.SortOrder
  secondPartyId?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.bank_transactionCountOrderByAggregateInput
  _avg?: Prisma.bank_transactionAvgOrderByAggregateInput
  _max?: Prisma.bank_transactionMaxOrderByAggregateInput
  _min?: Prisma.bank_transactionMinOrderByAggregateInput
  _sum?: Prisma.bank_transactionSumOrderByAggregateInput
}

export type bank_transactionScalarWhereWithAggregatesInput = {
  AND?: Prisma.bank_transactionScalarWhereWithAggregatesInput | Prisma.bank_transactionScalarWhereWithAggregatesInput[]
  OR?: Prisma.bank_transactionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.bank_transactionScalarWhereWithAggregatesInput | Prisma.bank_transactionScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"bank_transaction"> | number
  transaction_type?: Prisma.EnumBankTransactionTypesWithAggregatesFilter<"bank_transaction"> | $Enums.BankTransactionTypes
  cash?: Prisma.IntWithAggregatesFilter<"bank_transaction"> | number
  transactionFee?: Prisma.IntWithAggregatesFilter<"bank_transaction"> | number
  initiatorCashBalance?: Prisma.IntWithAggregatesFilter<"bank_transaction"> | number
  initiatorBankBalance?: Prisma.IntWithAggregatesFilter<"bank_transaction"> | number
  secondPartyCashBalance?: Prisma.IntNullableWithAggregatesFilter<"bank_transaction"> | number | null
  secondPartyBankBalance?: Prisma.IntNullableWithAggregatesFilter<"bank_transaction"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"bank_transaction"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"bank_transaction"> | Date | string
  initiatorId?: Prisma.IntNullableWithAggregatesFilter<"bank_transaction"> | number | null
  secondPartyId?: Prisma.IntNullableWithAggregatesFilter<"bank_transaction"> | number | null
  userId?: Prisma.IntNullableWithAggregatesFilter<"bank_transaction"> | number | null
}

export type bank_transactionCreateInput = {
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiator?: Prisma.userCreateNestedOneWithoutInitiatedBankTransactionsInput
  secondParty?: Prisma.userCreateNestedOneWithoutSecondPartyBankTransactionsInput
  user?: Prisma.userCreateNestedOneWithoutUserBankTransactionsInput
}

export type bank_transactionUncheckedCreateInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  secondPartyId?: number | null
  userId?: number | null
}

export type bank_transactionUpdateInput = {
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiator?: Prisma.userUpdateOneWithoutInitiatedBankTransactionsNestedInput
  secondParty?: Prisma.userUpdateOneWithoutSecondPartyBankTransactionsNestedInput
  user?: Prisma.userUpdateOneWithoutUserBankTransactionsNestedInput
}

export type bank_transactionUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionCreateManyInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  secondPartyId?: number | null
  userId?: number | null
}

export type bank_transactionUpdateManyMutationInput = {
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type bank_transactionUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  transaction_type?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type bank_transactionAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type bank_transactionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  transaction_type?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type bank_transactionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  transaction_type?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type bank_transactionSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  cash?: Prisma.SortOrder
  transactionFee?: Prisma.SortOrder
  initiatorCashBalance?: Prisma.SortOrder
  initiatorBankBalance?: Prisma.SortOrder
  secondPartyCashBalance?: Prisma.SortOrder
  secondPartyBankBalance?: Prisma.SortOrder
  initiatorId?: Prisma.SortOrder
  secondPartyId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type Bank_transactionListRelationFilter = {
  every?: Prisma.bank_transactionWhereInput
  some?: Prisma.bank_transactionWhereInput
  none?: Prisma.bank_transactionWhereInput
}

export type bank_transactionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EnumBankTransactionTypesFieldUpdateOperationsInput = {
  set?: $Enums.BankTransactionTypes
}

export type bank_transactionCreateNestedManyWithoutInitiatorInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput> | Prisma.bank_transactionCreateWithoutInitiatorInput[] | Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput | Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput[]
  createMany?: Prisma.bank_transactionCreateManyInitiatorInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionCreateNestedManyWithoutSecondPartyInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput> | Prisma.bank_transactionCreateWithoutSecondPartyInput[] | Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput | Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput[]
  createMany?: Prisma.bank_transactionCreateManySecondPartyInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput> | Prisma.bank_transactionCreateWithoutUserInput[] | Prisma.bank_transactionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutUserInput | Prisma.bank_transactionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.bank_transactionCreateManyUserInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionUncheckedCreateNestedManyWithoutInitiatorInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput> | Prisma.bank_transactionCreateWithoutInitiatorInput[] | Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput | Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput[]
  createMany?: Prisma.bank_transactionCreateManyInitiatorInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionUncheckedCreateNestedManyWithoutSecondPartyInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput> | Prisma.bank_transactionCreateWithoutSecondPartyInput[] | Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput | Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput[]
  createMany?: Prisma.bank_transactionCreateManySecondPartyInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput> | Prisma.bank_transactionCreateWithoutUserInput[] | Prisma.bank_transactionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutUserInput | Prisma.bank_transactionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.bank_transactionCreateManyUserInputEnvelope
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
}

export type bank_transactionUpdateManyWithoutInitiatorNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput> | Prisma.bank_transactionCreateWithoutInitiatorInput[] | Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput | Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutInitiatorInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutInitiatorInput[]
  createMany?: Prisma.bank_transactionCreateManyInitiatorInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutInitiatorInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutInitiatorInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutInitiatorInput | Prisma.bank_transactionUpdateManyWithWhereWithoutInitiatorInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionUpdateManyWithoutSecondPartyNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput> | Prisma.bank_transactionCreateWithoutSecondPartyInput[] | Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput | Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutSecondPartyInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutSecondPartyInput[]
  createMany?: Prisma.bank_transactionCreateManySecondPartyInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutSecondPartyInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutSecondPartyInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutSecondPartyInput | Prisma.bank_transactionUpdateManyWithWhereWithoutSecondPartyInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput> | Prisma.bank_transactionCreateWithoutUserInput[] | Prisma.bank_transactionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutUserInput | Prisma.bank_transactionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutUserInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.bank_transactionCreateManyUserInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutUserInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutUserInput | Prisma.bank_transactionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionUncheckedUpdateManyWithoutInitiatorNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput> | Prisma.bank_transactionCreateWithoutInitiatorInput[] | Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput | Prisma.bank_transactionCreateOrConnectWithoutInitiatorInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutInitiatorInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutInitiatorInput[]
  createMany?: Prisma.bank_transactionCreateManyInitiatorInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutInitiatorInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutInitiatorInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutInitiatorInput | Prisma.bank_transactionUpdateManyWithWhereWithoutInitiatorInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionUncheckedUpdateManyWithoutSecondPartyNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput> | Prisma.bank_transactionCreateWithoutSecondPartyInput[] | Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput | Prisma.bank_transactionCreateOrConnectWithoutSecondPartyInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutSecondPartyInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutSecondPartyInput[]
  createMany?: Prisma.bank_transactionCreateManySecondPartyInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutSecondPartyInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutSecondPartyInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutSecondPartyInput | Prisma.bank_transactionUpdateManyWithWhereWithoutSecondPartyInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput> | Prisma.bank_transactionCreateWithoutUserInput[] | Prisma.bank_transactionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.bank_transactionCreateOrConnectWithoutUserInput | Prisma.bank_transactionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.bank_transactionUpsertWithWhereUniqueWithoutUserInput | Prisma.bank_transactionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.bank_transactionCreateManyUserInputEnvelope
  set?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  disconnect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  delete?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  connect?: Prisma.bank_transactionWhereUniqueInput | Prisma.bank_transactionWhereUniqueInput[]
  update?: Prisma.bank_transactionUpdateWithWhereUniqueWithoutUserInput | Prisma.bank_transactionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.bank_transactionUpdateManyWithWhereWithoutUserInput | Prisma.bank_transactionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
}

export type bank_transactionCreateWithoutInitiatorInput = {
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  secondParty?: Prisma.userCreateNestedOneWithoutSecondPartyBankTransactionsInput
  user?: Prisma.userCreateNestedOneWithoutUserBankTransactionsInput
}

export type bank_transactionUncheckedCreateWithoutInitiatorInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  secondPartyId?: number | null
  userId?: number | null
}

export type bank_transactionCreateOrConnectWithoutInitiatorInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput>
}

export type bank_transactionCreateManyInitiatorInputEnvelope = {
  data: Prisma.bank_transactionCreateManyInitiatorInput | Prisma.bank_transactionCreateManyInitiatorInput[]
  skipDuplicates?: boolean
}

export type bank_transactionCreateWithoutSecondPartyInput = {
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiator?: Prisma.userCreateNestedOneWithoutInitiatedBankTransactionsInput
  user?: Prisma.userCreateNestedOneWithoutUserBankTransactionsInput
}

export type bank_transactionUncheckedCreateWithoutSecondPartyInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  userId?: number | null
}

export type bank_transactionCreateOrConnectWithoutSecondPartyInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput>
}

export type bank_transactionCreateManySecondPartyInputEnvelope = {
  data: Prisma.bank_transactionCreateManySecondPartyInput | Prisma.bank_transactionCreateManySecondPartyInput[]
  skipDuplicates?: boolean
}

export type bank_transactionCreateWithoutUserInput = {
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiator?: Prisma.userCreateNestedOneWithoutInitiatedBankTransactionsInput
  secondParty?: Prisma.userCreateNestedOneWithoutSecondPartyBankTransactionsInput
}

export type bank_transactionUncheckedCreateWithoutUserInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  secondPartyId?: number | null
}

export type bank_transactionCreateOrConnectWithoutUserInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput>
}

export type bank_transactionCreateManyUserInputEnvelope = {
  data: Prisma.bank_transactionCreateManyUserInput | Prisma.bank_transactionCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type bank_transactionUpsertWithWhereUniqueWithoutInitiatorInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  update: Prisma.XOR<Prisma.bank_transactionUpdateWithoutInitiatorInput, Prisma.bank_transactionUncheckedUpdateWithoutInitiatorInput>
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutInitiatorInput, Prisma.bank_transactionUncheckedCreateWithoutInitiatorInput>
}

export type bank_transactionUpdateWithWhereUniqueWithoutInitiatorInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateWithoutInitiatorInput, Prisma.bank_transactionUncheckedUpdateWithoutInitiatorInput>
}

export type bank_transactionUpdateManyWithWhereWithoutInitiatorInput = {
  where: Prisma.bank_transactionScalarWhereInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateManyMutationInput, Prisma.bank_transactionUncheckedUpdateManyWithoutInitiatorInput>
}

export type bank_transactionScalarWhereInput = {
  AND?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
  OR?: Prisma.bank_transactionScalarWhereInput[]
  NOT?: Prisma.bank_transactionScalarWhereInput | Prisma.bank_transactionScalarWhereInput[]
  id?: Prisma.IntFilter<"bank_transaction"> | number
  transaction_type?: Prisma.EnumBankTransactionTypesFilter<"bank_transaction"> | $Enums.BankTransactionTypes
  cash?: Prisma.IntFilter<"bank_transaction"> | number
  transactionFee?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorCashBalance?: Prisma.IntFilter<"bank_transaction"> | number
  initiatorBankBalance?: Prisma.IntFilter<"bank_transaction"> | number
  secondPartyCashBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyBankBalance?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  createdAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"bank_transaction"> | Date | string
  initiatorId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  secondPartyId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
  userId?: Prisma.IntNullableFilter<"bank_transaction"> | number | null
}

export type bank_transactionUpsertWithWhereUniqueWithoutSecondPartyInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  update: Prisma.XOR<Prisma.bank_transactionUpdateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedUpdateWithoutSecondPartyInput>
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedCreateWithoutSecondPartyInput>
}

export type bank_transactionUpdateWithWhereUniqueWithoutSecondPartyInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateWithoutSecondPartyInput, Prisma.bank_transactionUncheckedUpdateWithoutSecondPartyInput>
}

export type bank_transactionUpdateManyWithWhereWithoutSecondPartyInput = {
  where: Prisma.bank_transactionScalarWhereInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateManyMutationInput, Prisma.bank_transactionUncheckedUpdateManyWithoutSecondPartyInput>
}

export type bank_transactionUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  update: Prisma.XOR<Prisma.bank_transactionUpdateWithoutUserInput, Prisma.bank_transactionUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.bank_transactionCreateWithoutUserInput, Prisma.bank_transactionUncheckedCreateWithoutUserInput>
}

export type bank_transactionUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.bank_transactionWhereUniqueInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateWithoutUserInput, Prisma.bank_transactionUncheckedUpdateWithoutUserInput>
}

export type bank_transactionUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.bank_transactionScalarWhereInput
  data: Prisma.XOR<Prisma.bank_transactionUpdateManyMutationInput, Prisma.bank_transactionUncheckedUpdateManyWithoutUserInput>
}

export type bank_transactionCreateManyInitiatorInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  secondPartyId?: number | null
  userId?: number | null
}

export type bank_transactionCreateManySecondPartyInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  userId?: number | null
}

export type bank_transactionCreateManyUserInput = {
  id?: number
  transaction_type: $Enums.BankTransactionTypes
  cash: number
  transactionFee: number
  initiatorCashBalance: number
  initiatorBankBalance: number
  secondPartyCashBalance?: number | null
  secondPartyBankBalance?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  initiatorId?: number | null
  secondPartyId?: number | null
}

export type bank_transactionUpdateWithoutInitiatorInput = {
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondParty?: Prisma.userUpdateOneWithoutSecondPartyBankTransactionsNestedInput
  user?: Prisma.userUpdateOneWithoutUserBankTransactionsNestedInput
}

export type bank_transactionUncheckedUpdateWithoutInitiatorInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionUncheckedUpdateManyWithoutInitiatorInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionUpdateWithoutSecondPartyInput = {
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiator?: Prisma.userUpdateOneWithoutInitiatedBankTransactionsNestedInput
  user?: Prisma.userUpdateOneWithoutUserBankTransactionsNestedInput
}

export type bank_transactionUncheckedUpdateWithoutSecondPartyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionUncheckedUpdateManyWithoutSecondPartyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionUpdateWithoutUserInput = {
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiator?: Prisma.userUpdateOneWithoutInitiatedBankTransactionsNestedInput
  secondParty?: Prisma.userUpdateOneWithoutSecondPartyBankTransactionsNestedInput
}

export type bank_transactionUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type bank_transactionUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  transaction_type?: Prisma.EnumBankTransactionTypesFieldUpdateOperationsInput | $Enums.BankTransactionTypes
  cash?: Prisma.IntFieldUpdateOperationsInput | number
  transactionFee?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorCashBalance?: Prisma.IntFieldUpdateOperationsInput | number
  initiatorBankBalance?: Prisma.IntFieldUpdateOperationsInput | number
  secondPartyCashBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyBankBalance?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  initiatorId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  secondPartyId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type bank_transactionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  transaction_type?: boolean
  cash?: boolean
  transactionFee?: boolean
  initiatorCashBalance?: boolean
  initiatorBankBalance?: boolean
  secondPartyCashBalance?: boolean
  secondPartyBankBalance?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  initiatorId?: boolean
  secondPartyId?: boolean
  userId?: boolean
  initiator?: boolean | Prisma.bank_transaction$initiatorArgs<ExtArgs>
  secondParty?: boolean | Prisma.bank_transaction$secondPartyArgs<ExtArgs>
  user?: boolean | Prisma.bank_transaction$userArgs<ExtArgs>
}, ExtArgs["result"]["bank_transaction"]>



export type bank_transactionSelectScalar = {
  id?: boolean
  transaction_type?: boolean
  cash?: boolean
  transactionFee?: boolean
  initiatorCashBalance?: boolean
  initiatorBankBalance?: boolean
  secondPartyCashBalance?: boolean
  secondPartyBankBalance?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  initiatorId?: boolean
  secondPartyId?: boolean
  userId?: boolean
}

export type bank_transactionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "transaction_type" | "cash" | "transactionFee" | "initiatorCashBalance" | "initiatorBankBalance" | "secondPartyCashBalance" | "secondPartyBankBalance" | "createdAt" | "updatedAt" | "initiatorId" | "secondPartyId" | "userId", ExtArgs["result"]["bank_transaction"]>
export type bank_transactionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  initiator?: boolean | Prisma.bank_transaction$initiatorArgs<ExtArgs>
  secondParty?: boolean | Prisma.bank_transaction$secondPartyArgs<ExtArgs>
  user?: boolean | Prisma.bank_transaction$userArgs<ExtArgs>
}

export type $bank_transactionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "bank_transaction"
  objects: {
    initiator: Prisma.$userPayload<ExtArgs> | null
    secondParty: Prisma.$userPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    transaction_type: $Enums.BankTransactionTypes
    cash: number
    transactionFee: number
    initiatorCashBalance: number
    initiatorBankBalance: number
    secondPartyCashBalance: number | null
    secondPartyBankBalance: number | null
    createdAt: Date
    updatedAt: Date
    initiatorId: number | null
    secondPartyId: number | null
    userId: number | null
  }, ExtArgs["result"]["bank_transaction"]>
  composites: {}
}

export type bank_transactionGetPayload<S extends boolean | null | undefined | bank_transactionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload, S>

export type bank_transactionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<bank_transactionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Bank_transactionCountAggregateInputType | true
  }

export interface bank_transactionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['bank_transaction'], meta: { name: 'bank_transaction' } }
  /**
   * Find zero or one Bank_transaction that matches the filter.
   * @param {bank_transactionFindUniqueArgs} args - Arguments to find a Bank_transaction
   * @example
   * // Get one Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends bank_transactionFindUniqueArgs>(args: Prisma.SelectSubset<T, bank_transactionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Bank_transaction that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {bank_transactionFindUniqueOrThrowArgs} args - Arguments to find a Bank_transaction
   * @example
   * // Get one Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends bank_transactionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, bank_transactionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Bank_transaction that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionFindFirstArgs} args - Arguments to find a Bank_transaction
   * @example
   * // Get one Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends bank_transactionFindFirstArgs>(args?: Prisma.SelectSubset<T, bank_transactionFindFirstArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Bank_transaction that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionFindFirstOrThrowArgs} args - Arguments to find a Bank_transaction
   * @example
   * // Get one Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends bank_transactionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, bank_transactionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Bank_transactions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Bank_transactions
   * const bank_transactions = await prisma.bank_transaction.findMany()
   * 
   * // Get first 10 Bank_transactions
   * const bank_transactions = await prisma.bank_transaction.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const bank_transactionWithIdOnly = await prisma.bank_transaction.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends bank_transactionFindManyArgs>(args?: Prisma.SelectSubset<T, bank_transactionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Bank_transaction.
   * @param {bank_transactionCreateArgs} args - Arguments to create a Bank_transaction.
   * @example
   * // Create one Bank_transaction
   * const Bank_transaction = await prisma.bank_transaction.create({
   *   data: {
   *     // ... data to create a Bank_transaction
   *   }
   * })
   * 
   */
  create<T extends bank_transactionCreateArgs>(args: Prisma.SelectSubset<T, bank_transactionCreateArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Bank_transactions.
   * @param {bank_transactionCreateManyArgs} args - Arguments to create many Bank_transactions.
   * @example
   * // Create many Bank_transactions
   * const bank_transaction = await prisma.bank_transaction.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends bank_transactionCreateManyArgs>(args?: Prisma.SelectSubset<T, bank_transactionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Bank_transaction.
   * @param {bank_transactionDeleteArgs} args - Arguments to delete one Bank_transaction.
   * @example
   * // Delete one Bank_transaction
   * const Bank_transaction = await prisma.bank_transaction.delete({
   *   where: {
   *     // ... filter to delete one Bank_transaction
   *   }
   * })
   * 
   */
  delete<T extends bank_transactionDeleteArgs>(args: Prisma.SelectSubset<T, bank_transactionDeleteArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Bank_transaction.
   * @param {bank_transactionUpdateArgs} args - Arguments to update one Bank_transaction.
   * @example
   * // Update one Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends bank_transactionUpdateArgs>(args: Prisma.SelectSubset<T, bank_transactionUpdateArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Bank_transactions.
   * @param {bank_transactionDeleteManyArgs} args - Arguments to filter Bank_transactions to delete.
   * @example
   * // Delete a few Bank_transactions
   * const { count } = await prisma.bank_transaction.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends bank_transactionDeleteManyArgs>(args?: Prisma.SelectSubset<T, bank_transactionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Bank_transactions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Bank_transactions
   * const bank_transaction = await prisma.bank_transaction.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends bank_transactionUpdateManyArgs>(args: Prisma.SelectSubset<T, bank_transactionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Bank_transaction.
   * @param {bank_transactionUpsertArgs} args - Arguments to update or create a Bank_transaction.
   * @example
   * // Update or create a Bank_transaction
   * const bank_transaction = await prisma.bank_transaction.upsert({
   *   create: {
   *     // ... data to create a Bank_transaction
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Bank_transaction we want to update
   *   }
   * })
   */
  upsert<T extends bank_transactionUpsertArgs>(args: Prisma.SelectSubset<T, bank_transactionUpsertArgs<ExtArgs>>): Prisma.Prisma__bank_transactionClient<runtime.Types.Result.GetResult<Prisma.$bank_transactionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Bank_transactions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionCountArgs} args - Arguments to filter Bank_transactions to count.
   * @example
   * // Count the number of Bank_transactions
   * const count = await prisma.bank_transaction.count({
   *   where: {
   *     // ... the filter for the Bank_transactions we want to count
   *   }
   * })
  **/
  count<T extends bank_transactionCountArgs>(
    args?: Prisma.Subset<T, bank_transactionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Bank_transactionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Bank_transaction.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Bank_transactionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Bank_transactionAggregateArgs>(args: Prisma.Subset<T, Bank_transactionAggregateArgs>): Prisma.PrismaPromise<GetBank_transactionAggregateType<T>>

  /**
   * Group by Bank_transaction.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {bank_transactionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends bank_transactionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: bank_transactionGroupByArgs['orderBy'] }
      : { orderBy?: bank_transactionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, bank_transactionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetBank_transactionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the bank_transaction model
 */
readonly fields: bank_transactionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for bank_transaction.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__bank_transactionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  initiator<T extends Prisma.bank_transaction$initiatorArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bank_transaction$initiatorArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  secondParty<T extends Prisma.bank_transaction$secondPartyArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bank_transaction$secondPartyArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.bank_transaction$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.bank_transaction$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the bank_transaction model
 */
export interface bank_transactionFieldRefs {
  readonly id: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly transaction_type: Prisma.FieldRef<"bank_transaction", 'BankTransactionTypes'>
  readonly cash: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly transactionFee: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly initiatorCashBalance: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly initiatorBankBalance: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly secondPartyCashBalance: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly secondPartyBankBalance: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly createdAt: Prisma.FieldRef<"bank_transaction", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"bank_transaction", 'DateTime'>
  readonly initiatorId: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly secondPartyId: Prisma.FieldRef<"bank_transaction", 'Int'>
  readonly userId: Prisma.FieldRef<"bank_transaction", 'Int'>
}
    

// Custom InputTypes
/**
 * bank_transaction findUnique
 */
export type bank_transactionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter, which bank_transaction to fetch.
   */
  where: Prisma.bank_transactionWhereUniqueInput
}

/**
 * bank_transaction findUniqueOrThrow
 */
export type bank_transactionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter, which bank_transaction to fetch.
   */
  where: Prisma.bank_transactionWhereUniqueInput
}

/**
 * bank_transaction findFirst
 */
export type bank_transactionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter, which bank_transaction to fetch.
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bank_transactions to fetch.
   */
  orderBy?: Prisma.bank_transactionOrderByWithRelationInput | Prisma.bank_transactionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for bank_transactions.
   */
  cursor?: Prisma.bank_transactionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bank_transactions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bank_transactions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of bank_transactions.
   */
  distinct?: Prisma.Bank_transactionScalarFieldEnum | Prisma.Bank_transactionScalarFieldEnum[]
}

/**
 * bank_transaction findFirstOrThrow
 */
export type bank_transactionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter, which bank_transaction to fetch.
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bank_transactions to fetch.
   */
  orderBy?: Prisma.bank_transactionOrderByWithRelationInput | Prisma.bank_transactionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for bank_transactions.
   */
  cursor?: Prisma.bank_transactionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bank_transactions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bank_transactions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of bank_transactions.
   */
  distinct?: Prisma.Bank_transactionScalarFieldEnum | Prisma.Bank_transactionScalarFieldEnum[]
}

/**
 * bank_transaction findMany
 */
export type bank_transactionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter, which bank_transactions to fetch.
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of bank_transactions to fetch.
   */
  orderBy?: Prisma.bank_transactionOrderByWithRelationInput | Prisma.bank_transactionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing bank_transactions.
   */
  cursor?: Prisma.bank_transactionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` bank_transactions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` bank_transactions.
   */
  skip?: number
  distinct?: Prisma.Bank_transactionScalarFieldEnum | Prisma.Bank_transactionScalarFieldEnum[]
}

/**
 * bank_transaction create
 */
export type bank_transactionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * The data needed to create a bank_transaction.
   */
  data: Prisma.XOR<Prisma.bank_transactionCreateInput, Prisma.bank_transactionUncheckedCreateInput>
}

/**
 * bank_transaction createMany
 */
export type bank_transactionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many bank_transactions.
   */
  data: Prisma.bank_transactionCreateManyInput | Prisma.bank_transactionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * bank_transaction update
 */
export type bank_transactionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * The data needed to update a bank_transaction.
   */
  data: Prisma.XOR<Prisma.bank_transactionUpdateInput, Prisma.bank_transactionUncheckedUpdateInput>
  /**
   * Choose, which bank_transaction to update.
   */
  where: Prisma.bank_transactionWhereUniqueInput
}

/**
 * bank_transaction updateMany
 */
export type bank_transactionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update bank_transactions.
   */
  data: Prisma.XOR<Prisma.bank_transactionUpdateManyMutationInput, Prisma.bank_transactionUncheckedUpdateManyInput>
  /**
   * Filter which bank_transactions to update
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * Limit how many bank_transactions to update.
   */
  limit?: number
}

/**
 * bank_transaction upsert
 */
export type bank_transactionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * The filter to search for the bank_transaction to update in case it exists.
   */
  where: Prisma.bank_transactionWhereUniqueInput
  /**
   * In case the bank_transaction found by the `where` argument doesn't exist, create a new bank_transaction with this data.
   */
  create: Prisma.XOR<Prisma.bank_transactionCreateInput, Prisma.bank_transactionUncheckedCreateInput>
  /**
   * In case the bank_transaction was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.bank_transactionUpdateInput, Prisma.bank_transactionUncheckedUpdateInput>
}

/**
 * bank_transaction delete
 */
export type bank_transactionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
  /**
   * Filter which bank_transaction to delete.
   */
  where: Prisma.bank_transactionWhereUniqueInput
}

/**
 * bank_transaction deleteMany
 */
export type bank_transactionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which bank_transactions to delete
   */
  where?: Prisma.bank_transactionWhereInput
  /**
   * Limit how many bank_transactions to delete.
   */
  limit?: number
}

/**
 * bank_transaction.initiator
 */
export type bank_transaction$initiatorArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bank_transaction.secondParty
 */
export type bank_transaction$secondPartyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bank_transaction.user
 */
export type bank_transaction$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * bank_transaction without action
 */
export type bank_transactionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the bank_transaction
   */
  select?: Prisma.bank_transactionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the bank_transaction
   */
  omit?: Prisma.bank_transactionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.bank_transactionInclude<ExtArgs> | null
}
