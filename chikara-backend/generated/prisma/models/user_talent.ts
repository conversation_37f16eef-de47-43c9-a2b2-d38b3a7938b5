
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `user_talent` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model user_talent
 * 
 */
export type user_talentModel = runtime.Types.Result.DefaultSelection<Prisma.$user_talentPayload>

export type AggregateUser_talent = {
  _count: User_talentCountAggregateOutputType | null
  _avg: User_talentAvgAggregateOutputType | null
  _sum: User_talentSumAggregateOutputType | null
  _min: User_talentMinAggregateOutputType | null
  _max: User_talentMaxAggregateOutputType | null
}

export type User_talentAvgAggregateOutputType = {
  level: number | null
  talentId: number | null
  userId: number | null
}

export type User_talentSumAggregateOutputType = {
  level: number | null
  talentId: number | null
  userId: number | null
}

export type User_talentMinAggregateOutputType = {
  level: number | null
  createdAt: Date | null
  updatedAt: Date | null
  talentId: number | null
  userId: number | null
}

export type User_talentMaxAggregateOutputType = {
  level: number | null
  createdAt: Date | null
  updatedAt: Date | null
  talentId: number | null
  userId: number | null
}

export type User_talentCountAggregateOutputType = {
  level: number
  createdAt: number
  updatedAt: number
  talentId: number
  userId: number
  _all: number
}


export type User_talentAvgAggregateInputType = {
  level?: true
  talentId?: true
  userId?: true
}

export type User_talentSumAggregateInputType = {
  level?: true
  talentId?: true
  userId?: true
}

export type User_talentMinAggregateInputType = {
  level?: true
  createdAt?: true
  updatedAt?: true
  talentId?: true
  userId?: true
}

export type User_talentMaxAggregateInputType = {
  level?: true
  createdAt?: true
  updatedAt?: true
  talentId?: true
  userId?: true
}

export type User_talentCountAggregateInputType = {
  level?: true
  createdAt?: true
  updatedAt?: true
  talentId?: true
  userId?: true
  _all?: true
}

export type User_talentAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_talent to aggregate.
   */
  where?: Prisma.user_talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_talents to fetch.
   */
  orderBy?: Prisma.user_talentOrderByWithRelationInput | Prisma.user_talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.user_talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned user_talents
  **/
  _count?: true | User_talentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: User_talentAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: User_talentSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: User_talentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: User_talentMaxAggregateInputType
}

export type GetUser_talentAggregateType<T extends User_talentAggregateArgs> = {
      [P in keyof T & keyof AggregateUser_talent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser_talent[P]>
    : Prisma.GetScalarType<T[P], AggregateUser_talent[P]>
}




export type user_talentGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_talentWhereInput
  orderBy?: Prisma.user_talentOrderByWithAggregationInput | Prisma.user_talentOrderByWithAggregationInput[]
  by: Prisma.User_talentScalarFieldEnum[] | Prisma.User_talentScalarFieldEnum
  having?: Prisma.user_talentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: User_talentCountAggregateInputType | true
  _avg?: User_talentAvgAggregateInputType
  _sum?: User_talentSumAggregateInputType
  _min?: User_talentMinAggregateInputType
  _max?: User_talentMaxAggregateInputType
}

export type User_talentGroupByOutputType = {
  level: number | null
  createdAt: Date
  updatedAt: Date
  talentId: number
  userId: number
  _count: User_talentCountAggregateOutputType | null
  _avg: User_talentAvgAggregateOutputType | null
  _sum: User_talentSumAggregateOutputType | null
  _min: User_talentMinAggregateOutputType | null
  _max: User_talentMaxAggregateOutputType | null
}

type GetUser_talentGroupByPayload<T extends user_talentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<User_talentGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof User_talentGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], User_talentGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], User_talentGroupByOutputType[P]>
      }
    >
  >



export type user_talentWhereInput = {
  AND?: Prisma.user_talentWhereInput | Prisma.user_talentWhereInput[]
  OR?: Prisma.user_talentWhereInput[]
  NOT?: Prisma.user_talentWhereInput | Prisma.user_talentWhereInput[]
  level?: Prisma.IntNullableFilter<"user_talent"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  talentId?: Prisma.IntFilter<"user_talent"> | number
  userId?: Prisma.IntFilter<"user_talent"> | number
  talent?: Prisma.XOR<Prisma.TalentScalarRelationFilter, Prisma.talentWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}

export type user_talentOrderByWithRelationInput = {
  level?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  talent?: Prisma.talentOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type user_talentWhereUniqueInput = Prisma.AtLeast<{
  talentId_userId?: Prisma.user_talentTalentIdUserIdCompoundUniqueInput
  AND?: Prisma.user_talentWhereInput | Prisma.user_talentWhereInput[]
  OR?: Prisma.user_talentWhereInput[]
  NOT?: Prisma.user_talentWhereInput | Prisma.user_talentWhereInput[]
  level?: Prisma.IntNullableFilter<"user_talent"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  talentId?: Prisma.IntFilter<"user_talent"> | number
  userId?: Prisma.IntFilter<"user_talent"> | number
  talent?: Prisma.XOR<Prisma.TalentScalarRelationFilter, Prisma.talentWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.userWhereInput>
}, "talentId_userId">

export type user_talentOrderByWithAggregationInput = {
  level?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.user_talentCountOrderByAggregateInput
  _avg?: Prisma.user_talentAvgOrderByAggregateInput
  _max?: Prisma.user_talentMaxOrderByAggregateInput
  _min?: Prisma.user_talentMinOrderByAggregateInput
  _sum?: Prisma.user_talentSumOrderByAggregateInput
}

export type user_talentScalarWhereWithAggregatesInput = {
  AND?: Prisma.user_talentScalarWhereWithAggregatesInput | Prisma.user_talentScalarWhereWithAggregatesInput[]
  OR?: Prisma.user_talentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.user_talentScalarWhereWithAggregatesInput | Prisma.user_talentScalarWhereWithAggregatesInput[]
  level?: Prisma.IntNullableWithAggregatesFilter<"user_talent"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"user_talent"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"user_talent"> | Date | string
  talentId?: Prisma.IntWithAggregatesFilter<"user_talent"> | number
  userId?: Prisma.IntWithAggregatesFilter<"user_talent"> | number
}

export type user_talentCreateInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talent: Prisma.talentCreateNestedOneWithoutUser_talentInput
  user: Prisma.userCreateNestedOneWithoutUser_talentInput
}

export type user_talentUncheckedCreateInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talentId: number
  userId: number
}

export type user_talentUpdateInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talent?: Prisma.talentUpdateOneRequiredWithoutUser_talentNestedInput
  user?: Prisma.userUpdateOneRequiredWithoutUser_talentNestedInput
}

export type user_talentUncheckedUpdateInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talentId?: Prisma.IntFieldUpdateOperationsInput | number
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_talentCreateManyInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talentId: number
  userId: number
}

export type user_talentUpdateManyMutationInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type user_talentUncheckedUpdateManyInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talentId?: Prisma.IntFieldUpdateOperationsInput | number
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type User_talentListRelationFilter = {
  every?: Prisma.user_talentWhereInput
  some?: Prisma.user_talentWhereInput
  none?: Prisma.user_talentWhereInput
}

export type user_talentOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type user_talentTalentIdUserIdCompoundUniqueInput = {
  talentId: number
  userId: number
}

export type user_talentCountOrderByAggregateInput = {
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_talentAvgOrderByAggregateInput = {
  level?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_talentMaxOrderByAggregateInput = {
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_talentMinOrderByAggregateInput = {
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_talentSumOrderByAggregateInput = {
  level?: Prisma.SortOrder
  talentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type user_talentCreateNestedManyWithoutTalentInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput> | Prisma.user_talentCreateWithoutTalentInput[] | Prisma.user_talentUncheckedCreateWithoutTalentInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutTalentInput | Prisma.user_talentCreateOrConnectWithoutTalentInput[]
  createMany?: Prisma.user_talentCreateManyTalentInputEnvelope
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
}

export type user_talentUncheckedCreateNestedManyWithoutTalentInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput> | Prisma.user_talentCreateWithoutTalentInput[] | Prisma.user_talentUncheckedCreateWithoutTalentInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutTalentInput | Prisma.user_talentCreateOrConnectWithoutTalentInput[]
  createMany?: Prisma.user_talentCreateManyTalentInputEnvelope
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
}

export type user_talentUpdateManyWithoutTalentNestedInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput> | Prisma.user_talentCreateWithoutTalentInput[] | Prisma.user_talentUncheckedCreateWithoutTalentInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutTalentInput | Prisma.user_talentCreateOrConnectWithoutTalentInput[]
  upsert?: Prisma.user_talentUpsertWithWhereUniqueWithoutTalentInput | Prisma.user_talentUpsertWithWhereUniqueWithoutTalentInput[]
  createMany?: Prisma.user_talentCreateManyTalentInputEnvelope
  set?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  disconnect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  delete?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  update?: Prisma.user_talentUpdateWithWhereUniqueWithoutTalentInput | Prisma.user_talentUpdateWithWhereUniqueWithoutTalentInput[]
  updateMany?: Prisma.user_talentUpdateManyWithWhereWithoutTalentInput | Prisma.user_talentUpdateManyWithWhereWithoutTalentInput[]
  deleteMany?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
}

export type user_talentUncheckedUpdateManyWithoutTalentNestedInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput> | Prisma.user_talentCreateWithoutTalentInput[] | Prisma.user_talentUncheckedCreateWithoutTalentInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutTalentInput | Prisma.user_talentCreateOrConnectWithoutTalentInput[]
  upsert?: Prisma.user_talentUpsertWithWhereUniqueWithoutTalentInput | Prisma.user_talentUpsertWithWhereUniqueWithoutTalentInput[]
  createMany?: Prisma.user_talentCreateManyTalentInputEnvelope
  set?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  disconnect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  delete?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  update?: Prisma.user_talentUpdateWithWhereUniqueWithoutTalentInput | Prisma.user_talentUpdateWithWhereUniqueWithoutTalentInput[]
  updateMany?: Prisma.user_talentUpdateManyWithWhereWithoutTalentInput | Prisma.user_talentUpdateManyWithWhereWithoutTalentInput[]
  deleteMany?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
}

export type user_talentCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput> | Prisma.user_talentCreateWithoutUserInput[] | Prisma.user_talentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutUserInput | Prisma.user_talentCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_talentCreateManyUserInputEnvelope
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
}

export type user_talentUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput> | Prisma.user_talentCreateWithoutUserInput[] | Prisma.user_talentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutUserInput | Prisma.user_talentCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.user_talentCreateManyUserInputEnvelope
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
}

export type user_talentUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput> | Prisma.user_talentCreateWithoutUserInput[] | Prisma.user_talentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutUserInput | Prisma.user_talentCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_talentUpsertWithWhereUniqueWithoutUserInput | Prisma.user_talentUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_talentCreateManyUserInputEnvelope
  set?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  disconnect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  delete?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  update?: Prisma.user_talentUpdateWithWhereUniqueWithoutUserInput | Prisma.user_talentUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_talentUpdateManyWithWhereWithoutUserInput | Prisma.user_talentUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
}

export type user_talentUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput> | Prisma.user_talentCreateWithoutUserInput[] | Prisma.user_talentUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.user_talentCreateOrConnectWithoutUserInput | Prisma.user_talentCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.user_talentUpsertWithWhereUniqueWithoutUserInput | Prisma.user_talentUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.user_talentCreateManyUserInputEnvelope
  set?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  disconnect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  delete?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  connect?: Prisma.user_talentWhereUniqueInput | Prisma.user_talentWhereUniqueInput[]
  update?: Prisma.user_talentUpdateWithWhereUniqueWithoutUserInput | Prisma.user_talentUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.user_talentUpdateManyWithWhereWithoutUserInput | Prisma.user_talentUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
}

export type user_talentCreateWithoutTalentInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.userCreateNestedOneWithoutUser_talentInput
}

export type user_talentUncheckedCreateWithoutTalentInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_talentCreateOrConnectWithoutTalentInput = {
  where: Prisma.user_talentWhereUniqueInput
  create: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput>
}

export type user_talentCreateManyTalentInputEnvelope = {
  data: Prisma.user_talentCreateManyTalentInput | Prisma.user_talentCreateManyTalentInput[]
  skipDuplicates?: boolean
}

export type user_talentUpsertWithWhereUniqueWithoutTalentInput = {
  where: Prisma.user_talentWhereUniqueInput
  update: Prisma.XOR<Prisma.user_talentUpdateWithoutTalentInput, Prisma.user_talentUncheckedUpdateWithoutTalentInput>
  create: Prisma.XOR<Prisma.user_talentCreateWithoutTalentInput, Prisma.user_talentUncheckedCreateWithoutTalentInput>
}

export type user_talentUpdateWithWhereUniqueWithoutTalentInput = {
  where: Prisma.user_talentWhereUniqueInput
  data: Prisma.XOR<Prisma.user_talentUpdateWithoutTalentInput, Prisma.user_talentUncheckedUpdateWithoutTalentInput>
}

export type user_talentUpdateManyWithWhereWithoutTalentInput = {
  where: Prisma.user_talentScalarWhereInput
  data: Prisma.XOR<Prisma.user_talentUpdateManyMutationInput, Prisma.user_talentUncheckedUpdateManyWithoutTalentInput>
}

export type user_talentScalarWhereInput = {
  AND?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
  OR?: Prisma.user_talentScalarWhereInput[]
  NOT?: Prisma.user_talentScalarWhereInput | Prisma.user_talentScalarWhereInput[]
  level?: Prisma.IntNullableFilter<"user_talent"> | number | null
  createdAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"user_talent"> | Date | string
  talentId?: Prisma.IntFilter<"user_talent"> | number
  userId?: Prisma.IntFilter<"user_talent"> | number
}

export type user_talentCreateWithoutUserInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talent: Prisma.talentCreateNestedOneWithoutUser_talentInput
}

export type user_talentUncheckedCreateWithoutUserInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talentId: number
}

export type user_talentCreateOrConnectWithoutUserInput = {
  where: Prisma.user_talentWhereUniqueInput
  create: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput>
}

export type user_talentCreateManyUserInputEnvelope = {
  data: Prisma.user_talentCreateManyUserInput | Prisma.user_talentCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type user_talentUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_talentWhereUniqueInput
  update: Prisma.XOR<Prisma.user_talentUpdateWithoutUserInput, Prisma.user_talentUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.user_talentCreateWithoutUserInput, Prisma.user_talentUncheckedCreateWithoutUserInput>
}

export type user_talentUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.user_talentWhereUniqueInput
  data: Prisma.XOR<Prisma.user_talentUpdateWithoutUserInput, Prisma.user_talentUncheckedUpdateWithoutUserInput>
}

export type user_talentUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.user_talentScalarWhereInput
  data: Prisma.XOR<Prisma.user_talentUpdateManyMutationInput, Prisma.user_talentUncheckedUpdateManyWithoutUserInput>
}

export type user_talentCreateManyTalentInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: number
}

export type user_talentUpdateWithoutTalentInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneRequiredWithoutUser_talentNestedInput
}

export type user_talentUncheckedUpdateWithoutTalentInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_talentUncheckedUpdateManyWithoutTalentInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_talentCreateManyUserInput = {
  level?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  talentId: number
}

export type user_talentUpdateWithoutUserInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talent?: Prisma.talentUpdateOneRequiredWithoutUser_talentNestedInput
}

export type user_talentUncheckedUpdateWithoutUserInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talentId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type user_talentUncheckedUpdateManyWithoutUserInput = {
  level?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  talentId?: Prisma.IntFieldUpdateOperationsInput | number
}



export type user_talentSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  level?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  talentId?: boolean
  userId?: boolean
  talent?: boolean | Prisma.talentDefaultArgs<ExtArgs>
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user_talent"]>



export type user_talentSelectScalar = {
  level?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  talentId?: boolean
  userId?: boolean
}

export type user_talentOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"level" | "createdAt" | "updatedAt" | "talentId" | "userId", ExtArgs["result"]["user_talent"]>
export type user_talentInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  talent?: boolean | Prisma.talentDefaultArgs<ExtArgs>
  user?: boolean | Prisma.userDefaultArgs<ExtArgs>
}

export type $user_talentPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "user_talent"
  objects: {
    talent: Prisma.$talentPayload<ExtArgs>
    user: Prisma.$userPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    level: number | null
    createdAt: Date
    updatedAt: Date
    talentId: number
    userId: number
  }, ExtArgs["result"]["user_talent"]>
  composites: {}
}

export type user_talentGetPayload<S extends boolean | null | undefined | user_talentDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$user_talentPayload, S>

export type user_talentCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<user_talentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: User_talentCountAggregateInputType | true
  }

export interface user_talentDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_talent'], meta: { name: 'user_talent' } }
  /**
   * Find zero or one User_talent that matches the filter.
   * @param {user_talentFindUniqueArgs} args - Arguments to find a User_talent
   * @example
   * // Get one User_talent
   * const user_talent = await prisma.user_talent.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends user_talentFindUniqueArgs>(args: Prisma.SelectSubset<T, user_talentFindUniqueArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User_talent that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {user_talentFindUniqueOrThrowArgs} args - Arguments to find a User_talent
   * @example
   * // Get one User_talent
   * const user_talent = await prisma.user_talent.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends user_talentFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, user_talentFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_talent that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentFindFirstArgs} args - Arguments to find a User_talent
   * @example
   * // Get one User_talent
   * const user_talent = await prisma.user_talent.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends user_talentFindFirstArgs>(args?: Prisma.SelectSubset<T, user_talentFindFirstArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User_talent that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentFindFirstOrThrowArgs} args - Arguments to find a User_talent
   * @example
   * // Get one User_talent
   * const user_talent = await prisma.user_talent.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends user_talentFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, user_talentFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more User_talents that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all User_talents
   * const user_talents = await prisma.user_talent.findMany()
   * 
   * // Get first 10 User_talents
   * const user_talents = await prisma.user_talent.findMany({ take: 10 })
   * 
   * // Only select the `level`
   * const user_talentWithLevelOnly = await prisma.user_talent.findMany({ select: { level: true } })
   * 
   */
  findMany<T extends user_talentFindManyArgs>(args?: Prisma.SelectSubset<T, user_talentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User_talent.
   * @param {user_talentCreateArgs} args - Arguments to create a User_talent.
   * @example
   * // Create one User_talent
   * const User_talent = await prisma.user_talent.create({
   *   data: {
   *     // ... data to create a User_talent
   *   }
   * })
   * 
   */
  create<T extends user_talentCreateArgs>(args: Prisma.SelectSubset<T, user_talentCreateArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many User_talents.
   * @param {user_talentCreateManyArgs} args - Arguments to create many User_talents.
   * @example
   * // Create many User_talents
   * const user_talent = await prisma.user_talent.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends user_talentCreateManyArgs>(args?: Prisma.SelectSubset<T, user_talentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User_talent.
   * @param {user_talentDeleteArgs} args - Arguments to delete one User_talent.
   * @example
   * // Delete one User_talent
   * const User_talent = await prisma.user_talent.delete({
   *   where: {
   *     // ... filter to delete one User_talent
   *   }
   * })
   * 
   */
  delete<T extends user_talentDeleteArgs>(args: Prisma.SelectSubset<T, user_talentDeleteArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User_talent.
   * @param {user_talentUpdateArgs} args - Arguments to update one User_talent.
   * @example
   * // Update one User_talent
   * const user_talent = await prisma.user_talent.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends user_talentUpdateArgs>(args: Prisma.SelectSubset<T, user_talentUpdateArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more User_talents.
   * @param {user_talentDeleteManyArgs} args - Arguments to filter User_talents to delete.
   * @example
   * // Delete a few User_talents
   * const { count } = await prisma.user_talent.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends user_talentDeleteManyArgs>(args?: Prisma.SelectSubset<T, user_talentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more User_talents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many User_talents
   * const user_talent = await prisma.user_talent.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends user_talentUpdateManyArgs>(args: Prisma.SelectSubset<T, user_talentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User_talent.
   * @param {user_talentUpsertArgs} args - Arguments to update or create a User_talent.
   * @example
   * // Update or create a User_talent
   * const user_talent = await prisma.user_talent.upsert({
   *   create: {
   *     // ... data to create a User_talent
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User_talent we want to update
   *   }
   * })
   */
  upsert<T extends user_talentUpsertArgs>(args: Prisma.SelectSubset<T, user_talentUpsertArgs<ExtArgs>>): Prisma.Prisma__user_talentClient<runtime.Types.Result.GetResult<Prisma.$user_talentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of User_talents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentCountArgs} args - Arguments to filter User_talents to count.
   * @example
   * // Count the number of User_talents
   * const count = await prisma.user_talent.count({
   *   where: {
   *     // ... the filter for the User_talents we want to count
   *   }
   * })
  **/
  count<T extends user_talentCountArgs>(
    args?: Prisma.Subset<T, user_talentCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], User_talentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User_talent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {User_talentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends User_talentAggregateArgs>(args: Prisma.Subset<T, User_talentAggregateArgs>): Prisma.PrismaPromise<GetUser_talentAggregateType<T>>

  /**
   * Group by User_talent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {user_talentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends user_talentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: user_talentGroupByArgs['orderBy'] }
      : { orderBy?: user_talentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, user_talentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_talentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the user_talent model
 */
readonly fields: user_talentFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for user_talent.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__user_talentClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  talent<T extends Prisma.talentDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.talentDefaultArgs<ExtArgs>>): Prisma.Prisma__talentClient<runtime.Types.Result.GetResult<Prisma.$talentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.userDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.userDefaultArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the user_talent model
 */
export interface user_talentFieldRefs {
  readonly level: Prisma.FieldRef<"user_talent", 'Int'>
  readonly createdAt: Prisma.FieldRef<"user_talent", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"user_talent", 'DateTime'>
  readonly talentId: Prisma.FieldRef<"user_talent", 'Int'>
  readonly userId: Prisma.FieldRef<"user_talent", 'Int'>
}
    

// Custom InputTypes
/**
 * user_talent findUnique
 */
export type user_talentFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter, which user_talent to fetch.
   */
  where: Prisma.user_talentWhereUniqueInput
}

/**
 * user_talent findUniqueOrThrow
 */
export type user_talentFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter, which user_talent to fetch.
   */
  where: Prisma.user_talentWhereUniqueInput
}

/**
 * user_talent findFirst
 */
export type user_talentFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter, which user_talent to fetch.
   */
  where?: Prisma.user_talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_talents to fetch.
   */
  orderBy?: Prisma.user_talentOrderByWithRelationInput | Prisma.user_talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_talents.
   */
  cursor?: Prisma.user_talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_talents.
   */
  distinct?: Prisma.User_talentScalarFieldEnum | Prisma.User_talentScalarFieldEnum[]
}

/**
 * user_talent findFirstOrThrow
 */
export type user_talentFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter, which user_talent to fetch.
   */
  where?: Prisma.user_talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_talents to fetch.
   */
  orderBy?: Prisma.user_talentOrderByWithRelationInput | Prisma.user_talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for user_talents.
   */
  cursor?: Prisma.user_talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_talents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of user_talents.
   */
  distinct?: Prisma.User_talentScalarFieldEnum | Prisma.User_talentScalarFieldEnum[]
}

/**
 * user_talent findMany
 */
export type user_talentFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter, which user_talents to fetch.
   */
  where?: Prisma.user_talentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of user_talents to fetch.
   */
  orderBy?: Prisma.user_talentOrderByWithRelationInput | Prisma.user_talentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing user_talents.
   */
  cursor?: Prisma.user_talentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` user_talents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` user_talents.
   */
  skip?: number
  distinct?: Prisma.User_talentScalarFieldEnum | Prisma.User_talentScalarFieldEnum[]
}

/**
 * user_talent create
 */
export type user_talentCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * The data needed to create a user_talent.
   */
  data: Prisma.XOR<Prisma.user_talentCreateInput, Prisma.user_talentUncheckedCreateInput>
}

/**
 * user_talent createMany
 */
export type user_talentCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many user_talents.
   */
  data: Prisma.user_talentCreateManyInput | Prisma.user_talentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * user_talent update
 */
export type user_talentUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * The data needed to update a user_talent.
   */
  data: Prisma.XOR<Prisma.user_talentUpdateInput, Prisma.user_talentUncheckedUpdateInput>
  /**
   * Choose, which user_talent to update.
   */
  where: Prisma.user_talentWhereUniqueInput
}

/**
 * user_talent updateMany
 */
export type user_talentUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update user_talents.
   */
  data: Prisma.XOR<Prisma.user_talentUpdateManyMutationInput, Prisma.user_talentUncheckedUpdateManyInput>
  /**
   * Filter which user_talents to update
   */
  where?: Prisma.user_talentWhereInput
  /**
   * Limit how many user_talents to update.
   */
  limit?: number
}

/**
 * user_talent upsert
 */
export type user_talentUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * The filter to search for the user_talent to update in case it exists.
   */
  where: Prisma.user_talentWhereUniqueInput
  /**
   * In case the user_talent found by the `where` argument doesn't exist, create a new user_talent with this data.
   */
  create: Prisma.XOR<Prisma.user_talentCreateInput, Prisma.user_talentUncheckedCreateInput>
  /**
   * In case the user_talent was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.user_talentUpdateInput, Prisma.user_talentUncheckedUpdateInput>
}

/**
 * user_talent delete
 */
export type user_talentDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
  /**
   * Filter which user_talent to delete.
   */
  where: Prisma.user_talentWhereUniqueInput
}

/**
 * user_talent deleteMany
 */
export type user_talentDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which user_talents to delete
   */
  where?: Prisma.user_talentWhereInput
  /**
   * Limit how many user_talents to delete.
   */
  limit?: number
}

/**
 * user_talent without action
 */
export type user_talentDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_talent
   */
  select?: Prisma.user_talentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_talent
   */
  omit?: Prisma.user_talentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_talentInclude<ExtArgs> | null
}
