
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `quest_objective` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model quest_objective
 * 
 */
export type quest_objectiveModel = runtime.Types.Result.DefaultSelection<Prisma.$quest_objectivePayload>

export type AggregateQuest_objective = {
  _count: Quest_objectiveCountAggregateOutputType | null
  _avg: Quest_objectiveAvgAggregateOutputType | null
  _sum: Quest_objectiveSumAggregateOutputType | null
  _min: Quest_objectiveMinAggregateOutputType | null
  _max: Quest_objectiveMaxAggregateOutputType | null
}

export type Quest_objectiveAvgAggregateOutputType = {
  id: number | null
  target: number | null
  quantity: number | null
  questId: number | null
  creatureId: number | null
  itemId: number | null
}

export type Quest_objectiveSumAggregateOutputType = {
  id: number | null
  target: number | null
  quantity: number | null
  questId: number | null
  creatureId: number | null
  itemId: number | null
}

export type Quest_objectiveMinAggregateOutputType = {
  id: number | null
  description: string | null
  objectiveType: $Enums.QuestObjectiveTypes | null
  target: number | null
  targetAction: string | null
  quantity: number | null
  location: $Enums.LocationTypes | null
  isRequired: boolean | null
  questId: number | null
  creatureId: number | null
  itemId: number | null
}

export type Quest_objectiveMaxAggregateOutputType = {
  id: number | null
  description: string | null
  objectiveType: $Enums.QuestObjectiveTypes | null
  target: number | null
  targetAction: string | null
  quantity: number | null
  location: $Enums.LocationTypes | null
  isRequired: boolean | null
  questId: number | null
  creatureId: number | null
  itemId: number | null
}

export type Quest_objectiveCountAggregateOutputType = {
  id: number
  description: number
  objectiveType: number
  target: number
  targetAction: number
  quantity: number
  location: number
  isRequired: number
  questId: number
  creatureId: number
  itemId: number
  _all: number
}


export type Quest_objectiveAvgAggregateInputType = {
  id?: true
  target?: true
  quantity?: true
  questId?: true
  creatureId?: true
  itemId?: true
}

export type Quest_objectiveSumAggregateInputType = {
  id?: true
  target?: true
  quantity?: true
  questId?: true
  creatureId?: true
  itemId?: true
}

export type Quest_objectiveMinAggregateInputType = {
  id?: true
  description?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  location?: true
  isRequired?: true
  questId?: true
  creatureId?: true
  itemId?: true
}

export type Quest_objectiveMaxAggregateInputType = {
  id?: true
  description?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  location?: true
  isRequired?: true
  questId?: true
  creatureId?: true
  itemId?: true
}

export type Quest_objectiveCountAggregateInputType = {
  id?: true
  description?: true
  objectiveType?: true
  target?: true
  targetAction?: true
  quantity?: true
  location?: true
  isRequired?: true
  questId?: true
  creatureId?: true
  itemId?: true
  _all?: true
}

export type Quest_objectiveAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_objective to aggregate.
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objectives to fetch.
   */
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objectives from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objectives.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned quest_objectives
  **/
  _count?: true | Quest_objectiveCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Quest_objectiveAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Quest_objectiveSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Quest_objectiveMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Quest_objectiveMaxAggregateInputType
}

export type GetQuest_objectiveAggregateType<T extends Quest_objectiveAggregateArgs> = {
      [P in keyof T & keyof AggregateQuest_objective]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateQuest_objective[P]>
    : Prisma.GetScalarType<T[P], AggregateQuest_objective[P]>
}




export type quest_objectiveGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objectiveWhereInput
  orderBy?: Prisma.quest_objectiveOrderByWithAggregationInput | Prisma.quest_objectiveOrderByWithAggregationInput[]
  by: Prisma.Quest_objectiveScalarFieldEnum[] | Prisma.Quest_objectiveScalarFieldEnum
  having?: Prisma.quest_objectiveScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Quest_objectiveCountAggregateInputType | true
  _avg?: Quest_objectiveAvgAggregateInputType
  _sum?: Quest_objectiveSumAggregateInputType
  _min?: Quest_objectiveMinAggregateInputType
  _max?: Quest_objectiveMaxAggregateInputType
}

export type Quest_objectiveGroupByOutputType = {
  id: number
  description: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target: number | null
  targetAction: string | null
  quantity: number | null
  location: $Enums.LocationTypes | null
  isRequired: boolean
  questId: number | null
  creatureId: number | null
  itemId: number | null
  _count: Quest_objectiveCountAggregateOutputType | null
  _avg: Quest_objectiveAvgAggregateOutputType | null
  _sum: Quest_objectiveSumAggregateOutputType | null
  _min: Quest_objectiveMinAggregateOutputType | null
  _max: Quest_objectiveMaxAggregateOutputType | null
}

type GetQuest_objectiveGroupByPayload<T extends quest_objectiveGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Quest_objectiveGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Quest_objectiveGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Quest_objectiveGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Quest_objectiveGroupByOutputType[P]>
      }
    >
  >



export type quest_objectiveWhereInput = {
  AND?: Prisma.quest_objectiveWhereInput | Prisma.quest_objectiveWhereInput[]
  OR?: Prisma.quest_objectiveWhereInput[]
  NOT?: Prisma.quest_objectiveWhereInput | Prisma.quest_objectiveWhereInput[]
  id?: Prisma.IntFilter<"quest_objective"> | number
  description?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"quest_objective"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  targetAction?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  quantity?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  location?: Prisma.EnumLocationTypesNullableFilter<"quest_objective"> | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFilter<"quest_objective"> | boolean
  questId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  creatureId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  creature?: Prisma.XOR<Prisma.CreatureNullableScalarRelationFilter, Prisma.creatureWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  quest_objective_progress?: Prisma.Quest_objective_progressListRelationFilter
  story_episode?: Prisma.XOR<Prisma.Story_episodeNullableScalarRelationFilter, Prisma.story_episodeWhereInput> | null
}

export type quest_objectiveOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrderInput | Prisma.SortOrder
  targetAction?: Prisma.SortOrderInput | Prisma.SortOrder
  quantity?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  isRequired?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  creatureId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  quest?: Prisma.questOrderByWithRelationInput
  creature?: Prisma.creatureOrderByWithRelationInput
  item?: Prisma.itemOrderByWithRelationInput
  quest_objective_progress?: Prisma.quest_objective_progressOrderByRelationAggregateInput
  story_episode?: Prisma.story_episodeOrderByWithRelationInput
  _relevance?: Prisma.quest_objectiveOrderByRelevanceInput
}

export type quest_objectiveWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.quest_objectiveWhereInput | Prisma.quest_objectiveWhereInput[]
  OR?: Prisma.quest_objectiveWhereInput[]
  NOT?: Prisma.quest_objectiveWhereInput | Prisma.quest_objectiveWhereInput[]
  description?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"quest_objective"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  targetAction?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  quantity?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  location?: Prisma.EnumLocationTypesNullableFilter<"quest_objective"> | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFilter<"quest_objective"> | boolean
  questId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  creatureId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  quest?: Prisma.XOR<Prisma.QuestNullableScalarRelationFilter, Prisma.questWhereInput> | null
  creature?: Prisma.XOR<Prisma.CreatureNullableScalarRelationFilter, Prisma.creatureWhereInput> | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  quest_objective_progress?: Prisma.Quest_objective_progressListRelationFilter
  story_episode?: Prisma.XOR<Prisma.Story_episodeNullableScalarRelationFilter, Prisma.story_episodeWhereInput> | null
}, "id">

export type quest_objectiveOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrderInput | Prisma.SortOrder
  targetAction?: Prisma.SortOrderInput | Prisma.SortOrder
  quantity?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  isRequired?: Prisma.SortOrder
  questId?: Prisma.SortOrderInput | Prisma.SortOrder
  creatureId?: Prisma.SortOrderInput | Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.quest_objectiveCountOrderByAggregateInput
  _avg?: Prisma.quest_objectiveAvgOrderByAggregateInput
  _max?: Prisma.quest_objectiveMaxOrderByAggregateInput
  _min?: Prisma.quest_objectiveMinOrderByAggregateInput
  _sum?: Prisma.quest_objectiveSumOrderByAggregateInput
}

export type quest_objectiveScalarWhereWithAggregatesInput = {
  AND?: Prisma.quest_objectiveScalarWhereWithAggregatesInput | Prisma.quest_objectiveScalarWhereWithAggregatesInput[]
  OR?: Prisma.quest_objectiveScalarWhereWithAggregatesInput[]
  NOT?: Prisma.quest_objectiveScalarWhereWithAggregatesInput | Prisma.quest_objectiveScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"quest_objective"> | number
  description?: Prisma.StringNullableWithAggregatesFilter<"quest_objective"> | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesWithAggregatesFilter<"quest_objective"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableWithAggregatesFilter<"quest_objective"> | number | null
  targetAction?: Prisma.StringNullableWithAggregatesFilter<"quest_objective"> | string | null
  quantity?: Prisma.IntNullableWithAggregatesFilter<"quest_objective"> | number | null
  location?: Prisma.EnumLocationTypesNullableWithAggregatesFilter<"quest_objective"> | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolWithAggregatesFilter<"quest_objective"> | boolean
  questId?: Prisma.IntNullableWithAggregatesFilter<"quest_objective"> | number | null
  creatureId?: Prisma.IntNullableWithAggregatesFilter<"quest_objective"> | number | null
  itemId?: Prisma.IntNullableWithAggregatesFilter<"quest_objective"> | number | null
}

export type quest_objectiveCreateInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  quest?: Prisma.questCreateNestedOneWithoutQuest_objectiveInput
  creature?: Prisma.creatureCreateNestedOneWithoutQuest_objectiveInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_objectiveInput
  quest_objective_progress?: Prisma.quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
  itemId?: number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUpdateInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  quest?: Prisma.questUpdateOneWithoutQuest_objectiveNestedInput
  creature?: Prisma.creatureUpdateOneWithoutQuest_objectiveNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_objectiveNestedInput
  quest_objective_progress?: Prisma.quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveCreateManyInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
  itemId?: number | null
}

export type quest_objectiveUpdateManyMutationInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type quest_objectiveUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Quest_objectiveListRelationFilter = {
  every?: Prisma.quest_objectiveWhereInput
  some?: Prisma.quest_objectiveWhereInput
  none?: Prisma.quest_objectiveWhereInput
}

export type quest_objectiveOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type quest_objectiveOrderByRelevanceInput = {
  fields: Prisma.quest_objectiveOrderByRelevanceFieldEnum | Prisma.quest_objectiveOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type quest_objectiveCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  description?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isRequired?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  creatureId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_objectiveAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  target?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  creatureId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_objectiveMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  description?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isRequired?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  creatureId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_objectiveMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  description?: Prisma.SortOrder
  objectiveType?: Prisma.SortOrder
  target?: Prisma.SortOrder
  targetAction?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isRequired?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  creatureId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type quest_objectiveSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  target?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  questId?: Prisma.SortOrder
  creatureId?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
}

export type Quest_objectiveScalarRelationFilter = {
  is?: Prisma.quest_objectiveWhereInput
  isNot?: Prisma.quest_objectiveWhereInput
}

export type quest_objectiveCreateNestedManyWithoutCreatureInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput> | Prisma.quest_objectiveCreateWithoutCreatureInput[] | Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput | Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput[]
  createMany?: Prisma.quest_objectiveCreateManyCreatureInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUncheckedCreateNestedManyWithoutCreatureInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput> | Prisma.quest_objectiveCreateWithoutCreatureInput[] | Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput | Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput[]
  createMany?: Prisma.quest_objectiveCreateManyCreatureInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUpdateManyWithoutCreatureNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput> | Prisma.quest_objectiveCreateWithoutCreatureInput[] | Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput | Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutCreatureInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutCreatureInput[]
  createMany?: Prisma.quest_objectiveCreateManyCreatureInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutCreatureInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutCreatureInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutCreatureInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutCreatureInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveUncheckedUpdateManyWithoutCreatureNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput> | Prisma.quest_objectiveCreateWithoutCreatureInput[] | Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput | Prisma.quest_objectiveCreateOrConnectWithoutCreatureInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutCreatureInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutCreatureInput[]
  createMany?: Prisma.quest_objectiveCreateManyCreatureInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutCreatureInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutCreatureInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutCreatureInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutCreatureInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput> | Prisma.quest_objectiveCreateWithoutItemInput[] | Prisma.quest_objectiveUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutItemInput | Prisma.quest_objectiveCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.quest_objectiveCreateManyItemInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput> | Prisma.quest_objectiveCreateWithoutItemInput[] | Prisma.quest_objectiveUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutItemInput | Prisma.quest_objectiveCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.quest_objectiveCreateManyItemInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput> | Prisma.quest_objectiveCreateWithoutItemInput[] | Prisma.quest_objectiveUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutItemInput | Prisma.quest_objectiveCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutItemInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.quest_objectiveCreateManyItemInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutItemInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutItemInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput> | Prisma.quest_objectiveCreateWithoutItemInput[] | Prisma.quest_objectiveUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutItemInput | Prisma.quest_objectiveCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutItemInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.quest_objectiveCreateManyItemInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutItemInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutItemInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput> | Prisma.quest_objectiveCreateWithoutQuestInput[] | Prisma.quest_objectiveUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuestInput | Prisma.quest_objectiveCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_objectiveCreateManyQuestInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUncheckedCreateNestedManyWithoutQuestInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput> | Prisma.quest_objectiveCreateWithoutQuestInput[] | Prisma.quest_objectiveUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuestInput | Prisma.quest_objectiveCreateOrConnectWithoutQuestInput[]
  createMany?: Prisma.quest_objectiveCreateManyQuestInputEnvelope
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
}

export type quest_objectiveUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput> | Prisma.quest_objectiveCreateWithoutQuestInput[] | Prisma.quest_objectiveUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuestInput | Prisma.quest_objectiveCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_objectiveCreateManyQuestInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutQuestInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveUncheckedUpdateManyWithoutQuestNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput> | Prisma.quest_objectiveCreateWithoutQuestInput[] | Prisma.quest_objectiveUncheckedCreateWithoutQuestInput[]
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuestInput | Prisma.quest_objectiveCreateOrConnectWithoutQuestInput[]
  upsert?: Prisma.quest_objectiveUpsertWithWhereUniqueWithoutQuestInput | Prisma.quest_objectiveUpsertWithWhereUniqueWithoutQuestInput[]
  createMany?: Prisma.quest_objectiveCreateManyQuestInputEnvelope
  set?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  disconnect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  delete?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  connect?: Prisma.quest_objectiveWhereUniqueInput | Prisma.quest_objectiveWhereUniqueInput[]
  update?: Prisma.quest_objectiveUpdateWithWhereUniqueWithoutQuestInput | Prisma.quest_objectiveUpdateWithWhereUniqueWithoutQuestInput[]
  updateMany?: Prisma.quest_objectiveUpdateManyWithWhereWithoutQuestInput | Prisma.quest_objectiveUpdateManyWithWhereWithoutQuestInput[]
  deleteMany?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
}

export type quest_objectiveCreateNestedOneWithoutQuest_objective_progressInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedCreateWithoutQuest_objective_progressInput>
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuest_objective_progressInput
  connect?: Prisma.quest_objectiveWhereUniqueInput
}

export type quest_objectiveUpdateOneRequiredWithoutQuest_objective_progressNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedCreateWithoutQuest_objective_progressInput>
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutQuest_objective_progressInput
  upsert?: Prisma.quest_objectiveUpsertWithoutQuest_objective_progressInput
  connect?: Prisma.quest_objectiveWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.quest_objectiveUpdateToOneWithWhereWithoutQuest_objective_progressInput, Prisma.quest_objectiveUpdateWithoutQuest_objective_progressInput>, Prisma.quest_objectiveUncheckedUpdateWithoutQuest_objective_progressInput>
}

export type quest_objectiveCreateNestedOneWithoutStory_episodeInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedCreateWithoutStory_episodeInput>
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutStory_episodeInput
  connect?: Prisma.quest_objectiveWhereUniqueInput
}

export type quest_objectiveUpdateOneRequiredWithoutStory_episodeNestedInput = {
  create?: Prisma.XOR<Prisma.quest_objectiveCreateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedCreateWithoutStory_episodeInput>
  connectOrCreate?: Prisma.quest_objectiveCreateOrConnectWithoutStory_episodeInput
  upsert?: Prisma.quest_objectiveUpsertWithoutStory_episodeInput
  connect?: Prisma.quest_objectiveWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.quest_objectiveUpdateToOneWithWhereWithoutStory_episodeInput, Prisma.quest_objectiveUpdateWithoutStory_episodeInput>, Prisma.quest_objectiveUncheckedUpdateWithoutStory_episodeInput>
}

export type quest_objectiveCreateWithoutCreatureInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  quest?: Prisma.questCreateNestedOneWithoutQuest_objectiveInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_objectiveInput
  quest_objective_progress?: Prisma.quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateWithoutCreatureInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  itemId?: number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveCreateOrConnectWithoutCreatureInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput>
}

export type quest_objectiveCreateManyCreatureInputEnvelope = {
  data: Prisma.quest_objectiveCreateManyCreatureInput | Prisma.quest_objectiveCreateManyCreatureInput[]
  skipDuplicates?: boolean
}

export type quest_objectiveUpsertWithWhereUniqueWithoutCreatureInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutCreatureInput, Prisma.quest_objectiveUncheckedUpdateWithoutCreatureInput>
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutCreatureInput, Prisma.quest_objectiveUncheckedCreateWithoutCreatureInput>
}

export type quest_objectiveUpdateWithWhereUniqueWithoutCreatureInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutCreatureInput, Prisma.quest_objectiveUncheckedUpdateWithoutCreatureInput>
}

export type quest_objectiveUpdateManyWithWhereWithoutCreatureInput = {
  where: Prisma.quest_objectiveScalarWhereInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateManyMutationInput, Prisma.quest_objectiveUncheckedUpdateManyWithoutCreatureInput>
}

export type quest_objectiveScalarWhereInput = {
  AND?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
  OR?: Prisma.quest_objectiveScalarWhereInput[]
  NOT?: Prisma.quest_objectiveScalarWhereInput | Prisma.quest_objectiveScalarWhereInput[]
  id?: Prisma.IntFilter<"quest_objective"> | number
  description?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFilter<"quest_objective"> | $Enums.QuestObjectiveTypes
  target?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  targetAction?: Prisma.StringNullableFilter<"quest_objective"> | string | null
  quantity?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  location?: Prisma.EnumLocationTypesNullableFilter<"quest_objective"> | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFilter<"quest_objective"> | boolean
  questId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  creatureId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
  itemId?: Prisma.IntNullableFilter<"quest_objective"> | number | null
}

export type quest_objectiveCreateWithoutItemInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  quest?: Prisma.questCreateNestedOneWithoutQuest_objectiveInput
  creature?: Prisma.creatureCreateNestedOneWithoutQuest_objectiveInput
  quest_objective_progress?: Prisma.quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateWithoutItemInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveCreateOrConnectWithoutItemInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput>
}

export type quest_objectiveCreateManyItemInputEnvelope = {
  data: Prisma.quest_objectiveCreateManyItemInput | Prisma.quest_objectiveCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type quest_objectiveUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutItemInput, Prisma.quest_objectiveUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutItemInput, Prisma.quest_objectiveUncheckedCreateWithoutItemInput>
}

export type quest_objectiveUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutItemInput, Prisma.quest_objectiveUncheckedUpdateWithoutItemInput>
}

export type quest_objectiveUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.quest_objectiveScalarWhereInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateManyMutationInput, Prisma.quest_objectiveUncheckedUpdateManyWithoutItemInput>
}

export type quest_objectiveCreateWithoutQuestInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  creature?: Prisma.creatureCreateNestedOneWithoutQuest_objectiveInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_objectiveInput
  quest_objective_progress?: Prisma.quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateWithoutQuestInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  creatureId?: number | null
  itemId?: number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveCreateOrConnectWithoutQuestInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput>
}

export type quest_objectiveCreateManyQuestInputEnvelope = {
  data: Prisma.quest_objectiveCreateManyQuestInput | Prisma.quest_objectiveCreateManyQuestInput[]
  skipDuplicates?: boolean
}

export type quest_objectiveUpsertWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  update: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutQuestInput, Prisma.quest_objectiveUncheckedUpdateWithoutQuestInput>
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuestInput, Prisma.quest_objectiveUncheckedCreateWithoutQuestInput>
}

export type quest_objectiveUpdateWithWhereUniqueWithoutQuestInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutQuestInput, Prisma.quest_objectiveUncheckedUpdateWithoutQuestInput>
}

export type quest_objectiveUpdateManyWithWhereWithoutQuestInput = {
  where: Prisma.quest_objectiveScalarWhereInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateManyMutationInput, Prisma.quest_objectiveUncheckedUpdateManyWithoutQuestInput>
}

export type quest_objectiveCreateWithoutQuest_objective_progressInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  quest?: Prisma.questCreateNestedOneWithoutQuest_objectiveInput
  creature?: Prisma.creatureCreateNestedOneWithoutQuest_objectiveInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_objectiveInput
  story_episode?: Prisma.story_episodeCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateWithoutQuest_objective_progressInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
  itemId?: number | null
  story_episode?: Prisma.story_episodeUncheckedCreateNestedOneWithoutQuest_objectiveInput
}

export type quest_objectiveCreateOrConnectWithoutQuest_objective_progressInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedCreateWithoutQuest_objective_progressInput>
}

export type quest_objectiveUpsertWithoutQuest_objective_progressInput = {
  update: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedUpdateWithoutQuest_objective_progressInput>
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedCreateWithoutQuest_objective_progressInput>
  where?: Prisma.quest_objectiveWhereInput
}

export type quest_objectiveUpdateToOneWithWhereWithoutQuest_objective_progressInput = {
  where?: Prisma.quest_objectiveWhereInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutQuest_objective_progressInput, Prisma.quest_objectiveUncheckedUpdateWithoutQuest_objective_progressInput>
}

export type quest_objectiveUpdateWithoutQuest_objective_progressInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  quest?: Prisma.questUpdateOneWithoutQuest_objectiveNestedInput
  creature?: Prisma.creatureUpdateOneWithoutQuest_objectiveNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateWithoutQuest_objective_progressInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  story_episode?: Prisma.story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveCreateWithoutStory_episodeInput = {
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  quest?: Prisma.questCreateNestedOneWithoutQuest_objectiveInput
  creature?: Prisma.creatureCreateNestedOneWithoutQuest_objectiveInput
  item?: Prisma.itemCreateNestedOneWithoutQuest_objectiveInput
  quest_objective_progress?: Prisma.quest_objective_progressCreateNestedManyWithoutQuest_objectiveInput
}

export type quest_objectiveUncheckedCreateWithoutStory_episodeInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
  itemId?: number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedCreateNestedManyWithoutQuest_objectiveInput
}

export type quest_objectiveCreateOrConnectWithoutStory_episodeInput = {
  where: Prisma.quest_objectiveWhereUniqueInput
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedCreateWithoutStory_episodeInput>
}

export type quest_objectiveUpsertWithoutStory_episodeInput = {
  update: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedUpdateWithoutStory_episodeInput>
  create: Prisma.XOR<Prisma.quest_objectiveCreateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedCreateWithoutStory_episodeInput>
  where?: Prisma.quest_objectiveWhereInput
}

export type quest_objectiveUpdateToOneWithWhereWithoutStory_episodeInput = {
  where?: Prisma.quest_objectiveWhereInput
  data: Prisma.XOR<Prisma.quest_objectiveUpdateWithoutStory_episodeInput, Prisma.quest_objectiveUncheckedUpdateWithoutStory_episodeInput>
}

export type quest_objectiveUpdateWithoutStory_episodeInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  quest?: Prisma.questUpdateOneWithoutQuest_objectiveNestedInput
  creature?: Prisma.creatureUpdateOneWithoutQuest_objectiveNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_objectiveNestedInput
  quest_objective_progress?: Prisma.quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateWithoutStory_episodeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput
}

export type quest_objectiveCreateManyCreatureInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  itemId?: number | null
}

export type quest_objectiveUpdateWithoutCreatureInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  quest?: Prisma.questUpdateOneWithoutQuest_objectiveNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_objectiveNestedInput
  quest_objective_progress?: Prisma.quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateWithoutCreatureInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateManyWithoutCreatureInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_objectiveCreateManyItemInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  questId?: number | null
  creatureId?: number | null
}

export type quest_objectiveUpdateWithoutItemInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  quest?: Prisma.questUpdateOneWithoutQuest_objectiveNestedInput
  creature?: Prisma.creatureUpdateOneWithoutQuest_objectiveNestedInput
  quest_objective_progress?: Prisma.quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  questId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type quest_objectiveCreateManyQuestInput = {
  id?: number
  description?: string | null
  objectiveType: $Enums.QuestObjectiveTypes
  target?: number | null
  targetAction?: string | null
  quantity?: number | null
  location?: $Enums.LocationTypes | null
  isRequired?: boolean
  creatureId?: number | null
  itemId?: number | null
}

export type quest_objectiveUpdateWithoutQuestInput = {
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  creature?: Prisma.creatureUpdateOneWithoutQuest_objectiveNestedInput
  item?: Prisma.itemUpdateOneWithoutQuest_objectiveNestedInput
  quest_objective_progress?: Prisma.quest_objective_progressUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  quest_objective_progress?: Prisma.quest_objective_progressUncheckedUpdateManyWithoutQuest_objectiveNestedInput
  story_episode?: Prisma.story_episodeUncheckedUpdateOneWithoutQuest_objectiveNestedInput
}

export type quest_objectiveUncheckedUpdateManyWithoutQuestInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  objectiveType?: Prisma.EnumQuestObjectiveTypesFieldUpdateOperationsInput | $Enums.QuestObjectiveTypes
  target?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  targetAction?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  quantity?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  location?: Prisma.NullableEnumLocationTypesFieldUpdateOperationsInput | $Enums.LocationTypes | null
  isRequired?: Prisma.BoolFieldUpdateOperationsInput | boolean
  creatureId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}


/**
 * Count Type Quest_objectiveCountOutputType
 */

export type Quest_objectiveCountOutputType = {
  quest_objective_progress: number
}

export type Quest_objectiveCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest_objective_progress?: boolean | Quest_objectiveCountOutputTypeCountQuest_objective_progressArgs
}

/**
 * Quest_objectiveCountOutputType without action
 */
export type Quest_objectiveCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Quest_objectiveCountOutputType
   */
  select?: Prisma.Quest_objectiveCountOutputTypeSelect<ExtArgs> | null
}

/**
 * Quest_objectiveCountOutputType without action
 */
export type Quest_objectiveCountOutputTypeCountQuest_objective_progressArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objective_progressWhereInput
}


export type quest_objectiveSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  description?: boolean
  objectiveType?: boolean
  target?: boolean
  targetAction?: boolean
  quantity?: boolean
  location?: boolean
  isRequired?: boolean
  questId?: boolean
  creatureId?: boolean
  itemId?: boolean
  quest?: boolean | Prisma.quest_objective$questArgs<ExtArgs>
  creature?: boolean | Prisma.quest_objective$creatureArgs<ExtArgs>
  item?: boolean | Prisma.quest_objective$itemArgs<ExtArgs>
  quest_objective_progress?: boolean | Prisma.quest_objective$quest_objective_progressArgs<ExtArgs>
  story_episode?: boolean | Prisma.quest_objective$story_episodeArgs<ExtArgs>
  _count?: boolean | Prisma.Quest_objectiveCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["quest_objective"]>



export type quest_objectiveSelectScalar = {
  id?: boolean
  description?: boolean
  objectiveType?: boolean
  target?: boolean
  targetAction?: boolean
  quantity?: boolean
  location?: boolean
  isRequired?: boolean
  questId?: boolean
  creatureId?: boolean
  itemId?: boolean
}

export type quest_objectiveOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "description" | "objectiveType" | "target" | "targetAction" | "quantity" | "location" | "isRequired" | "questId" | "creatureId" | "itemId", ExtArgs["result"]["quest_objective"]>
export type quest_objectiveInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  quest?: boolean | Prisma.quest_objective$questArgs<ExtArgs>
  creature?: boolean | Prisma.quest_objective$creatureArgs<ExtArgs>
  item?: boolean | Prisma.quest_objective$itemArgs<ExtArgs>
  quest_objective_progress?: boolean | Prisma.quest_objective$quest_objective_progressArgs<ExtArgs>
  story_episode?: boolean | Prisma.quest_objective$story_episodeArgs<ExtArgs>
  _count?: boolean | Prisma.Quest_objectiveCountOutputTypeDefaultArgs<ExtArgs>
}

export type $quest_objectivePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "quest_objective"
  objects: {
    quest: Prisma.$questPayload<ExtArgs> | null
    creature: Prisma.$creaturePayload<ExtArgs> | null
    item: Prisma.$itemPayload<ExtArgs> | null
    quest_objective_progress: Prisma.$quest_objective_progressPayload<ExtArgs>[]
    story_episode: Prisma.$story_episodePayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    description: string | null
    objectiveType: $Enums.QuestObjectiveTypes
    target: number | null
    targetAction: string | null
    quantity: number | null
    location: $Enums.LocationTypes | null
    isRequired: boolean
    questId: number | null
    creatureId: number | null
    itemId: number | null
  }, ExtArgs["result"]["quest_objective"]>
  composites: {}
}

export type quest_objectiveGetPayload<S extends boolean | null | undefined | quest_objectiveDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload, S>

export type quest_objectiveCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<quest_objectiveFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Quest_objectiveCountAggregateInputType | true
  }

export interface quest_objectiveDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['quest_objective'], meta: { name: 'quest_objective' } }
  /**
   * Find zero or one Quest_objective that matches the filter.
   * @param {quest_objectiveFindUniqueArgs} args - Arguments to find a Quest_objective
   * @example
   * // Get one Quest_objective
   * const quest_objective = await prisma.quest_objective.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends quest_objectiveFindUniqueArgs>(args: Prisma.SelectSubset<T, quest_objectiveFindUniqueArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Quest_objective that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {quest_objectiveFindUniqueOrThrowArgs} args - Arguments to find a Quest_objective
   * @example
   * // Get one Quest_objective
   * const quest_objective = await prisma.quest_objective.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends quest_objectiveFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, quest_objectiveFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_objective that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveFindFirstArgs} args - Arguments to find a Quest_objective
   * @example
   * // Get one Quest_objective
   * const quest_objective = await prisma.quest_objective.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends quest_objectiveFindFirstArgs>(args?: Prisma.SelectSubset<T, quest_objectiveFindFirstArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Quest_objective that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveFindFirstOrThrowArgs} args - Arguments to find a Quest_objective
   * @example
   * // Get one Quest_objective
   * const quest_objective = await prisma.quest_objective.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends quest_objectiveFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, quest_objectiveFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Quest_objectives that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Quest_objectives
   * const quest_objectives = await prisma.quest_objective.findMany()
   * 
   * // Get first 10 Quest_objectives
   * const quest_objectives = await prisma.quest_objective.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const quest_objectiveWithIdOnly = await prisma.quest_objective.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends quest_objectiveFindManyArgs>(args?: Prisma.SelectSubset<T, quest_objectiveFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Quest_objective.
   * @param {quest_objectiveCreateArgs} args - Arguments to create a Quest_objective.
   * @example
   * // Create one Quest_objective
   * const Quest_objective = await prisma.quest_objective.create({
   *   data: {
   *     // ... data to create a Quest_objective
   *   }
   * })
   * 
   */
  create<T extends quest_objectiveCreateArgs>(args: Prisma.SelectSubset<T, quest_objectiveCreateArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Quest_objectives.
   * @param {quest_objectiveCreateManyArgs} args - Arguments to create many Quest_objectives.
   * @example
   * // Create many Quest_objectives
   * const quest_objective = await prisma.quest_objective.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends quest_objectiveCreateManyArgs>(args?: Prisma.SelectSubset<T, quest_objectiveCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Quest_objective.
   * @param {quest_objectiveDeleteArgs} args - Arguments to delete one Quest_objective.
   * @example
   * // Delete one Quest_objective
   * const Quest_objective = await prisma.quest_objective.delete({
   *   where: {
   *     // ... filter to delete one Quest_objective
   *   }
   * })
   * 
   */
  delete<T extends quest_objectiveDeleteArgs>(args: Prisma.SelectSubset<T, quest_objectiveDeleteArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Quest_objective.
   * @param {quest_objectiveUpdateArgs} args - Arguments to update one Quest_objective.
   * @example
   * // Update one Quest_objective
   * const quest_objective = await prisma.quest_objective.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends quest_objectiveUpdateArgs>(args: Prisma.SelectSubset<T, quest_objectiveUpdateArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Quest_objectives.
   * @param {quest_objectiveDeleteManyArgs} args - Arguments to filter Quest_objectives to delete.
   * @example
   * // Delete a few Quest_objectives
   * const { count } = await prisma.quest_objective.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends quest_objectiveDeleteManyArgs>(args?: Prisma.SelectSubset<T, quest_objectiveDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Quest_objectives.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Quest_objectives
   * const quest_objective = await prisma.quest_objective.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends quest_objectiveUpdateManyArgs>(args: Prisma.SelectSubset<T, quest_objectiveUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Quest_objective.
   * @param {quest_objectiveUpsertArgs} args - Arguments to update or create a Quest_objective.
   * @example
   * // Update or create a Quest_objective
   * const quest_objective = await prisma.quest_objective.upsert({
   *   create: {
   *     // ... data to create a Quest_objective
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Quest_objective we want to update
   *   }
   * })
   */
  upsert<T extends quest_objectiveUpsertArgs>(args: Prisma.SelectSubset<T, quest_objectiveUpsertArgs<ExtArgs>>): Prisma.Prisma__quest_objectiveClient<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Quest_objectives.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveCountArgs} args - Arguments to filter Quest_objectives to count.
   * @example
   * // Count the number of Quest_objectives
   * const count = await prisma.quest_objective.count({
   *   where: {
   *     // ... the filter for the Quest_objectives we want to count
   *   }
   * })
  **/
  count<T extends quest_objectiveCountArgs>(
    args?: Prisma.Subset<T, quest_objectiveCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Quest_objectiveCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Quest_objective.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Quest_objectiveAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Quest_objectiveAggregateArgs>(args: Prisma.Subset<T, Quest_objectiveAggregateArgs>): Prisma.PrismaPromise<GetQuest_objectiveAggregateType<T>>

  /**
   * Group by Quest_objective.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {quest_objectiveGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends quest_objectiveGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: quest_objectiveGroupByArgs['orderBy'] }
      : { orderBy?: quest_objectiveGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, quest_objectiveGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQuest_objectiveGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the quest_objective model
 */
readonly fields: quest_objectiveFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for quest_objective.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__quest_objectiveClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  quest<T extends Prisma.quest_objective$questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objective$questArgs<ExtArgs>>): Prisma.Prisma__questClient<runtime.Types.Result.GetResult<Prisma.$questPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  creature<T extends Prisma.quest_objective$creatureArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objective$creatureArgs<ExtArgs>>): Prisma.Prisma__creatureClient<runtime.Types.Result.GetResult<Prisma.$creaturePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  item<T extends Prisma.quest_objective$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objective$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  quest_objective_progress<T extends Prisma.quest_objective$quest_objective_progressArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objective$quest_objective_progressArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objective_progressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  story_episode<T extends Prisma.quest_objective$story_episodeArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.quest_objective$story_episodeArgs<ExtArgs>>): Prisma.Prisma__story_episodeClient<runtime.Types.Result.GetResult<Prisma.$story_episodePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the quest_objective model
 */
export interface quest_objectiveFieldRefs {
  readonly id: Prisma.FieldRef<"quest_objective", 'Int'>
  readonly description: Prisma.FieldRef<"quest_objective", 'String'>
  readonly objectiveType: Prisma.FieldRef<"quest_objective", 'QuestObjectiveTypes'>
  readonly target: Prisma.FieldRef<"quest_objective", 'Int'>
  readonly targetAction: Prisma.FieldRef<"quest_objective", 'String'>
  readonly quantity: Prisma.FieldRef<"quest_objective", 'Int'>
  readonly location: Prisma.FieldRef<"quest_objective", 'LocationTypes'>
  readonly isRequired: Prisma.FieldRef<"quest_objective", 'Boolean'>
  readonly questId: Prisma.FieldRef<"quest_objective", 'Int'>
  readonly creatureId: Prisma.FieldRef<"quest_objective", 'Int'>
  readonly itemId: Prisma.FieldRef<"quest_objective", 'Int'>
}
    

// Custom InputTypes
/**
 * quest_objective findUnique
 */
export type quest_objectiveFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective to fetch.
   */
  where: Prisma.quest_objectiveWhereUniqueInput
}

/**
 * quest_objective findUniqueOrThrow
 */
export type quest_objectiveFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective to fetch.
   */
  where: Prisma.quest_objectiveWhereUniqueInput
}

/**
 * quest_objective findFirst
 */
export type quest_objectiveFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective to fetch.
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objectives to fetch.
   */
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_objectives.
   */
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objectives from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objectives.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_objectives.
   */
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * quest_objective findFirstOrThrow
 */
export type quest_objectiveFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter, which quest_objective to fetch.
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objectives to fetch.
   */
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for quest_objectives.
   */
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objectives from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objectives.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of quest_objectives.
   */
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * quest_objective findMany
 */
export type quest_objectiveFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter, which quest_objectives to fetch.
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of quest_objectives to fetch.
   */
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing quest_objectives.
   */
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` quest_objectives from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` quest_objectives.
   */
  skip?: number
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * quest_objective create
 */
export type quest_objectiveCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * The data needed to create a quest_objective.
   */
  data: Prisma.XOR<Prisma.quest_objectiveCreateInput, Prisma.quest_objectiveUncheckedCreateInput>
}

/**
 * quest_objective createMany
 */
export type quest_objectiveCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many quest_objectives.
   */
  data: Prisma.quest_objectiveCreateManyInput | Prisma.quest_objectiveCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * quest_objective update
 */
export type quest_objectiveUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * The data needed to update a quest_objective.
   */
  data: Prisma.XOR<Prisma.quest_objectiveUpdateInput, Prisma.quest_objectiveUncheckedUpdateInput>
  /**
   * Choose, which quest_objective to update.
   */
  where: Prisma.quest_objectiveWhereUniqueInput
}

/**
 * quest_objective updateMany
 */
export type quest_objectiveUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update quest_objectives.
   */
  data: Prisma.XOR<Prisma.quest_objectiveUpdateManyMutationInput, Prisma.quest_objectiveUncheckedUpdateManyInput>
  /**
   * Filter which quest_objectives to update
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * Limit how many quest_objectives to update.
   */
  limit?: number
}

/**
 * quest_objective upsert
 */
export type quest_objectiveUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * The filter to search for the quest_objective to update in case it exists.
   */
  where: Prisma.quest_objectiveWhereUniqueInput
  /**
   * In case the quest_objective found by the `where` argument doesn't exist, create a new quest_objective with this data.
   */
  create: Prisma.XOR<Prisma.quest_objectiveCreateInput, Prisma.quest_objectiveUncheckedCreateInput>
  /**
   * In case the quest_objective was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.quest_objectiveUpdateInput, Prisma.quest_objectiveUncheckedUpdateInput>
}

/**
 * quest_objective delete
 */
export type quest_objectiveDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  /**
   * Filter which quest_objective to delete.
   */
  where: Prisma.quest_objectiveWhereUniqueInput
}

/**
 * quest_objective deleteMany
 */
export type quest_objectiveDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which quest_objectives to delete
   */
  where?: Prisma.quest_objectiveWhereInput
  /**
   * Limit how many quest_objectives to delete.
   */
  limit?: number
}

/**
 * quest_objective.quest
 */
export type quest_objective$questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest
   */
  select?: Prisma.questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest
   */
  omit?: Prisma.questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.questInclude<ExtArgs> | null
  where?: Prisma.questWhereInput
}

/**
 * quest_objective.creature
 */
export type quest_objective$creatureArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the creature
   */
  select?: Prisma.creatureSelect<ExtArgs> | null
  /**
   * Omit specific fields from the creature
   */
  omit?: Prisma.creatureOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.creatureInclude<ExtArgs> | null
  where?: Prisma.creatureWhereInput
}

/**
 * quest_objective.item
 */
export type quest_objective$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * quest_objective.quest_objective_progress
 */
export type quest_objective$quest_objective_progressArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective_progress
   */
  select?: Prisma.quest_objective_progressSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective_progress
   */
  omit?: Prisma.quest_objective_progressOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objective_progressInclude<ExtArgs> | null
  where?: Prisma.quest_objective_progressWhereInput
  orderBy?: Prisma.quest_objective_progressOrderByWithRelationInput | Prisma.quest_objective_progressOrderByWithRelationInput[]
  cursor?: Prisma.quest_objective_progressWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_objective_progressScalarFieldEnum | Prisma.Quest_objective_progressScalarFieldEnum[]
}

/**
 * quest_objective.story_episode
 */
export type quest_objective$story_episodeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the story_episode
   */
  select?: Prisma.story_episodeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the story_episode
   */
  omit?: Prisma.story_episodeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.story_episodeInclude<ExtArgs> | null
  where?: Prisma.story_episodeWhereInput
}

/**
 * quest_objective without action
 */
export type quest_objectiveDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
}
