
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `poll_response` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model poll_response
 * 
 */
export type poll_responseModel = runtime.Types.Result.DefaultSelection<Prisma.$poll_responsePayload>

export type AggregatePoll_response = {
  _count: Poll_responseCountAggregateOutputType | null
  _avg: Poll_responseAvgAggregateOutputType | null
  _sum: Poll_responseSumAggregateOutputType | null
  _min: Poll_responseMinAggregateOutputType | null
  _max: Poll_responseMaxAggregateOutputType | null
}

export type Poll_responseAvgAggregateOutputType = {
  id: number | null
  userId: number | null
  pollId: number | null
}

export type Poll_responseSumAggregateOutputType = {
  id: number | null
  userId: number | null
  pollId: number | null
}

export type Poll_responseMinAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  pollId: number | null
}

export type Poll_responseMaxAggregateOutputType = {
  id: number | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: number | null
  pollId: number | null
}

export type Poll_responseCountAggregateOutputType = {
  id: number
  answer: number
  createdAt: number
  updatedAt: number
  userId: number
  pollId: number
  _all: number
}


export type Poll_responseAvgAggregateInputType = {
  id?: true
  userId?: true
  pollId?: true
}

export type Poll_responseSumAggregateInputType = {
  id?: true
  userId?: true
  pollId?: true
}

export type Poll_responseMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  pollId?: true
}

export type Poll_responseMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  pollId?: true
}

export type Poll_responseCountAggregateInputType = {
  id?: true
  answer?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  pollId?: true
  _all?: true
}

export type Poll_responseAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which poll_response to aggregate.
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of poll_responses to fetch.
   */
  orderBy?: Prisma.poll_responseOrderByWithRelationInput | Prisma.poll_responseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.poll_responseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` poll_responses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` poll_responses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned poll_responses
  **/
  _count?: true | Poll_responseCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Poll_responseAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Poll_responseSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Poll_responseMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Poll_responseMaxAggregateInputType
}

export type GetPoll_responseAggregateType<T extends Poll_responseAggregateArgs> = {
      [P in keyof T & keyof AggregatePoll_response]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePoll_response[P]>
    : Prisma.GetScalarType<T[P], AggregatePoll_response[P]>
}




export type poll_responseGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.poll_responseWhereInput
  orderBy?: Prisma.poll_responseOrderByWithAggregationInput | Prisma.poll_responseOrderByWithAggregationInput[]
  by: Prisma.Poll_responseScalarFieldEnum[] | Prisma.Poll_responseScalarFieldEnum
  having?: Prisma.poll_responseScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Poll_responseCountAggregateInputType | true
  _avg?: Poll_responseAvgAggregateInputType
  _sum?: Poll_responseSumAggregateInputType
  _min?: Poll_responseMinAggregateInputType
  _max?: Poll_responseMaxAggregateInputType
}

export type Poll_responseGroupByOutputType = {
  id: number
  answer: runtime.JsonValue
  createdAt: Date
  updatedAt: Date
  userId: number | null
  pollId: number | null
  _count: Poll_responseCountAggregateOutputType | null
  _avg: Poll_responseAvgAggregateOutputType | null
  _sum: Poll_responseSumAggregateOutputType | null
  _min: Poll_responseMinAggregateOutputType | null
  _max: Poll_responseMaxAggregateOutputType | null
}

type GetPoll_responseGroupByPayload<T extends poll_responseGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Poll_responseGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Poll_responseGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Poll_responseGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Poll_responseGroupByOutputType[P]>
      }
    >
  >



export type poll_responseWhereInput = {
  AND?: Prisma.poll_responseWhereInput | Prisma.poll_responseWhereInput[]
  OR?: Prisma.poll_responseWhereInput[]
  NOT?: Prisma.poll_responseWhereInput | Prisma.poll_responseWhereInput[]
  id?: Prisma.IntFilter<"poll_response"> | number
  answer?: Prisma.JsonFilter<"poll_response">
  createdAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  userId?: Prisma.IntNullableFilter<"poll_response"> | number | null
  pollId?: Prisma.IntNullableFilter<"poll_response"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  poll?: Prisma.XOR<Prisma.PollNullableScalarRelationFilter, Prisma.pollWhereInput> | null
}

export type poll_responseOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  answer?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  pollId?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.userOrderByWithRelationInput
  poll?: Prisma.pollOrderByWithRelationInput
}

export type poll_responseWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.poll_responseWhereInput | Prisma.poll_responseWhereInput[]
  OR?: Prisma.poll_responseWhereInput[]
  NOT?: Prisma.poll_responseWhereInput | Prisma.poll_responseWhereInput[]
  answer?: Prisma.JsonFilter<"poll_response">
  createdAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  userId?: Prisma.IntNullableFilter<"poll_response"> | number | null
  pollId?: Prisma.IntNullableFilter<"poll_response"> | number | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
  poll?: Prisma.XOR<Prisma.PollNullableScalarRelationFilter, Prisma.pollWhereInput> | null
}, "id">

export type poll_responseOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  answer?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  pollId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.poll_responseCountOrderByAggregateInput
  _avg?: Prisma.poll_responseAvgOrderByAggregateInput
  _max?: Prisma.poll_responseMaxOrderByAggregateInput
  _min?: Prisma.poll_responseMinOrderByAggregateInput
  _sum?: Prisma.poll_responseSumOrderByAggregateInput
}

export type poll_responseScalarWhereWithAggregatesInput = {
  AND?: Prisma.poll_responseScalarWhereWithAggregatesInput | Prisma.poll_responseScalarWhereWithAggregatesInput[]
  OR?: Prisma.poll_responseScalarWhereWithAggregatesInput[]
  NOT?: Prisma.poll_responseScalarWhereWithAggregatesInput | Prisma.poll_responseScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"poll_response"> | number
  answer?: Prisma.JsonWithAggregatesFilter<"poll_response">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"poll_response"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"poll_response"> | Date | string
  userId?: Prisma.IntNullableWithAggregatesFilter<"poll_response"> | number | null
  pollId?: Prisma.IntNullableWithAggregatesFilter<"poll_response"> | number | null
}

export type poll_responseCreateInput = {
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutPoll_responseInput
  poll?: Prisma.pollCreateNestedOneWithoutPoll_responseInput
}

export type poll_responseUncheckedCreateInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  pollId?: number | null
}

export type poll_responseUpdateInput = {
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutPoll_responseNestedInput
  poll?: Prisma.pollUpdateOneWithoutPoll_responseNestedInput
}

export type poll_responseUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  pollId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type poll_responseCreateManyInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
  pollId?: number | null
}

export type poll_responseUpdateManyMutationInput = {
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type poll_responseUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  pollId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type Poll_responseListRelationFilter = {
  every?: Prisma.poll_responseWhereInput
  some?: Prisma.poll_responseWhereInput
  none?: Prisma.poll_responseWhereInput
}

export type poll_responseOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type poll_responseCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  answer?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  pollId?: Prisma.SortOrder
}

export type poll_responseAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  pollId?: Prisma.SortOrder
}

export type poll_responseMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  pollId?: Prisma.SortOrder
}

export type poll_responseMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  pollId?: Prisma.SortOrder
}

export type poll_responseSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  pollId?: Prisma.SortOrder
}

export type poll_responseCreateNestedManyWithoutPollInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput> | Prisma.poll_responseCreateWithoutPollInput[] | Prisma.poll_responseUncheckedCreateWithoutPollInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutPollInput | Prisma.poll_responseCreateOrConnectWithoutPollInput[]
  createMany?: Prisma.poll_responseCreateManyPollInputEnvelope
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
}

export type poll_responseUncheckedCreateNestedManyWithoutPollInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput> | Prisma.poll_responseCreateWithoutPollInput[] | Prisma.poll_responseUncheckedCreateWithoutPollInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutPollInput | Prisma.poll_responseCreateOrConnectWithoutPollInput[]
  createMany?: Prisma.poll_responseCreateManyPollInputEnvelope
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
}

export type poll_responseUpdateManyWithoutPollNestedInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput> | Prisma.poll_responseCreateWithoutPollInput[] | Prisma.poll_responseUncheckedCreateWithoutPollInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutPollInput | Prisma.poll_responseCreateOrConnectWithoutPollInput[]
  upsert?: Prisma.poll_responseUpsertWithWhereUniqueWithoutPollInput | Prisma.poll_responseUpsertWithWhereUniqueWithoutPollInput[]
  createMany?: Prisma.poll_responseCreateManyPollInputEnvelope
  set?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  disconnect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  delete?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  update?: Prisma.poll_responseUpdateWithWhereUniqueWithoutPollInput | Prisma.poll_responseUpdateWithWhereUniqueWithoutPollInput[]
  updateMany?: Prisma.poll_responseUpdateManyWithWhereWithoutPollInput | Prisma.poll_responseUpdateManyWithWhereWithoutPollInput[]
  deleteMany?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
}

export type poll_responseUncheckedUpdateManyWithoutPollNestedInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput> | Prisma.poll_responseCreateWithoutPollInput[] | Prisma.poll_responseUncheckedCreateWithoutPollInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutPollInput | Prisma.poll_responseCreateOrConnectWithoutPollInput[]
  upsert?: Prisma.poll_responseUpsertWithWhereUniqueWithoutPollInput | Prisma.poll_responseUpsertWithWhereUniqueWithoutPollInput[]
  createMany?: Prisma.poll_responseCreateManyPollInputEnvelope
  set?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  disconnect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  delete?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  update?: Prisma.poll_responseUpdateWithWhereUniqueWithoutPollInput | Prisma.poll_responseUpdateWithWhereUniqueWithoutPollInput[]
  updateMany?: Prisma.poll_responseUpdateManyWithWhereWithoutPollInput | Prisma.poll_responseUpdateManyWithWhereWithoutPollInput[]
  deleteMany?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
}

export type poll_responseCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput> | Prisma.poll_responseCreateWithoutUserInput[] | Prisma.poll_responseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutUserInput | Prisma.poll_responseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.poll_responseCreateManyUserInputEnvelope
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
}

export type poll_responseUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput> | Prisma.poll_responseCreateWithoutUserInput[] | Prisma.poll_responseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutUserInput | Prisma.poll_responseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.poll_responseCreateManyUserInputEnvelope
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
}

export type poll_responseUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput> | Prisma.poll_responseCreateWithoutUserInput[] | Prisma.poll_responseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutUserInput | Prisma.poll_responseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.poll_responseUpsertWithWhereUniqueWithoutUserInput | Prisma.poll_responseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.poll_responseCreateManyUserInputEnvelope
  set?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  disconnect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  delete?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  update?: Prisma.poll_responseUpdateWithWhereUniqueWithoutUserInput | Prisma.poll_responseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.poll_responseUpdateManyWithWhereWithoutUserInput | Prisma.poll_responseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
}

export type poll_responseUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput> | Prisma.poll_responseCreateWithoutUserInput[] | Prisma.poll_responseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.poll_responseCreateOrConnectWithoutUserInput | Prisma.poll_responseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.poll_responseUpsertWithWhereUniqueWithoutUserInput | Prisma.poll_responseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.poll_responseCreateManyUserInputEnvelope
  set?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  disconnect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  delete?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  connect?: Prisma.poll_responseWhereUniqueInput | Prisma.poll_responseWhereUniqueInput[]
  update?: Prisma.poll_responseUpdateWithWhereUniqueWithoutUserInput | Prisma.poll_responseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.poll_responseUpdateManyWithWhereWithoutUserInput | Prisma.poll_responseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
}

export type poll_responseCreateWithoutPollInput = {
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutPoll_responseInput
}

export type poll_responseUncheckedCreateWithoutPollInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type poll_responseCreateOrConnectWithoutPollInput = {
  where: Prisma.poll_responseWhereUniqueInput
  create: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput>
}

export type poll_responseCreateManyPollInputEnvelope = {
  data: Prisma.poll_responseCreateManyPollInput | Prisma.poll_responseCreateManyPollInput[]
  skipDuplicates?: boolean
}

export type poll_responseUpsertWithWhereUniqueWithoutPollInput = {
  where: Prisma.poll_responseWhereUniqueInput
  update: Prisma.XOR<Prisma.poll_responseUpdateWithoutPollInput, Prisma.poll_responseUncheckedUpdateWithoutPollInput>
  create: Prisma.XOR<Prisma.poll_responseCreateWithoutPollInput, Prisma.poll_responseUncheckedCreateWithoutPollInput>
}

export type poll_responseUpdateWithWhereUniqueWithoutPollInput = {
  where: Prisma.poll_responseWhereUniqueInput
  data: Prisma.XOR<Prisma.poll_responseUpdateWithoutPollInput, Prisma.poll_responseUncheckedUpdateWithoutPollInput>
}

export type poll_responseUpdateManyWithWhereWithoutPollInput = {
  where: Prisma.poll_responseScalarWhereInput
  data: Prisma.XOR<Prisma.poll_responseUpdateManyMutationInput, Prisma.poll_responseUncheckedUpdateManyWithoutPollInput>
}

export type poll_responseScalarWhereInput = {
  AND?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
  OR?: Prisma.poll_responseScalarWhereInput[]
  NOT?: Prisma.poll_responseScalarWhereInput | Prisma.poll_responseScalarWhereInput[]
  id?: Prisma.IntFilter<"poll_response"> | number
  answer?: Prisma.JsonFilter<"poll_response">
  createdAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"poll_response"> | Date | string
  userId?: Prisma.IntNullableFilter<"poll_response"> | number | null
  pollId?: Prisma.IntNullableFilter<"poll_response"> | number | null
}

export type poll_responseCreateWithoutUserInput = {
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  poll?: Prisma.pollCreateNestedOneWithoutPoll_responseInput
}

export type poll_responseUncheckedCreateWithoutUserInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  pollId?: number | null
}

export type poll_responseCreateOrConnectWithoutUserInput = {
  where: Prisma.poll_responseWhereUniqueInput
  create: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput>
}

export type poll_responseCreateManyUserInputEnvelope = {
  data: Prisma.poll_responseCreateManyUserInput | Prisma.poll_responseCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type poll_responseUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.poll_responseWhereUniqueInput
  update: Prisma.XOR<Prisma.poll_responseUpdateWithoutUserInput, Prisma.poll_responseUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.poll_responseCreateWithoutUserInput, Prisma.poll_responseUncheckedCreateWithoutUserInput>
}

export type poll_responseUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.poll_responseWhereUniqueInput
  data: Prisma.XOR<Prisma.poll_responseUpdateWithoutUserInput, Prisma.poll_responseUncheckedUpdateWithoutUserInput>
}

export type poll_responseUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.poll_responseScalarWhereInput
  data: Prisma.XOR<Prisma.poll_responseUpdateManyMutationInput, Prisma.poll_responseUncheckedUpdateManyWithoutUserInput>
}

export type poll_responseCreateManyPollInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  userId?: number | null
}

export type poll_responseUpdateWithoutPollInput = {
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutPoll_responseNestedInput
}

export type poll_responseUncheckedUpdateWithoutPollInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type poll_responseUncheckedUpdateManyWithoutPollInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type poll_responseCreateManyUserInput = {
  id?: number
  answer:unknown
  createdAt?: Date | string
  updatedAt?: Date | string
  pollId?: number | null
}

export type poll_responseUpdateWithoutUserInput = {
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  poll?: Prisma.pollUpdateOneWithoutPoll_responseNestedInput
}

export type poll_responseUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  pollId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type poll_responseUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  answer?:unknown
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  pollId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type poll_responseSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  answer?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  pollId?: boolean
  user?: boolean | Prisma.poll_response$userArgs<ExtArgs>
  poll?: boolean | Prisma.poll_response$pollArgs<ExtArgs>
}, ExtArgs["result"]["poll_response"]>



export type poll_responseSelectScalar = {
  id?: boolean
  answer?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
  pollId?: boolean
}

export type poll_responseOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "answer" | "createdAt" | "updatedAt" | "userId" | "pollId", ExtArgs["result"]["poll_response"]>
export type poll_responseInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.poll_response$userArgs<ExtArgs>
  poll?: boolean | Prisma.poll_response$pollArgs<ExtArgs>
}

export type $poll_responsePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "poll_response"
  objects: {
    user: Prisma.$userPayload<ExtArgs> | null
    poll: Prisma.$pollPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    answer:unknown
    createdAt: Date
    updatedAt: Date
    userId: number | null
    pollId: number | null
  }, ExtArgs["result"]["poll_response"]>
  composites: {}
}

export type poll_responseGetPayload<S extends boolean | null | undefined | poll_responseDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$poll_responsePayload, S>

export type poll_responseCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<poll_responseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Poll_responseCountAggregateInputType | true
  }

export interface poll_responseDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['poll_response'], meta: { name: 'poll_response' } }
  /**
   * Find zero or one Poll_response that matches the filter.
   * @param {poll_responseFindUniqueArgs} args - Arguments to find a Poll_response
   * @example
   * // Get one Poll_response
   * const poll_response = await prisma.poll_response.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends poll_responseFindUniqueArgs>(args: Prisma.SelectSubset<T, poll_responseFindUniqueArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Poll_response that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {poll_responseFindUniqueOrThrowArgs} args - Arguments to find a Poll_response
   * @example
   * // Get one Poll_response
   * const poll_response = await prisma.poll_response.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends poll_responseFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, poll_responseFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Poll_response that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseFindFirstArgs} args - Arguments to find a Poll_response
   * @example
   * // Get one Poll_response
   * const poll_response = await prisma.poll_response.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends poll_responseFindFirstArgs>(args?: Prisma.SelectSubset<T, poll_responseFindFirstArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Poll_response that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseFindFirstOrThrowArgs} args - Arguments to find a Poll_response
   * @example
   * // Get one Poll_response
   * const poll_response = await prisma.poll_response.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends poll_responseFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, poll_responseFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Poll_responses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Poll_responses
   * const poll_responses = await prisma.poll_response.findMany()
   * 
   * // Get first 10 Poll_responses
   * const poll_responses = await prisma.poll_response.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const poll_responseWithIdOnly = await prisma.poll_response.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends poll_responseFindManyArgs>(args?: Prisma.SelectSubset<T, poll_responseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Poll_response.
   * @param {poll_responseCreateArgs} args - Arguments to create a Poll_response.
   * @example
   * // Create one Poll_response
   * const Poll_response = await prisma.poll_response.create({
   *   data: {
   *     // ... data to create a Poll_response
   *   }
   * })
   * 
   */
  create<T extends poll_responseCreateArgs>(args: Prisma.SelectSubset<T, poll_responseCreateArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Poll_responses.
   * @param {poll_responseCreateManyArgs} args - Arguments to create many Poll_responses.
   * @example
   * // Create many Poll_responses
   * const poll_response = await prisma.poll_response.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends poll_responseCreateManyArgs>(args?: Prisma.SelectSubset<T, poll_responseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Poll_response.
   * @param {poll_responseDeleteArgs} args - Arguments to delete one Poll_response.
   * @example
   * // Delete one Poll_response
   * const Poll_response = await prisma.poll_response.delete({
   *   where: {
   *     // ... filter to delete one Poll_response
   *   }
   * })
   * 
   */
  delete<T extends poll_responseDeleteArgs>(args: Prisma.SelectSubset<T, poll_responseDeleteArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Poll_response.
   * @param {poll_responseUpdateArgs} args - Arguments to update one Poll_response.
   * @example
   * // Update one Poll_response
   * const poll_response = await prisma.poll_response.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends poll_responseUpdateArgs>(args: Prisma.SelectSubset<T, poll_responseUpdateArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Poll_responses.
   * @param {poll_responseDeleteManyArgs} args - Arguments to filter Poll_responses to delete.
   * @example
   * // Delete a few Poll_responses
   * const { count } = await prisma.poll_response.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends poll_responseDeleteManyArgs>(args?: Prisma.SelectSubset<T, poll_responseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Poll_responses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Poll_responses
   * const poll_response = await prisma.poll_response.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends poll_responseUpdateManyArgs>(args: Prisma.SelectSubset<T, poll_responseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Poll_response.
   * @param {poll_responseUpsertArgs} args - Arguments to update or create a Poll_response.
   * @example
   * // Update or create a Poll_response
   * const poll_response = await prisma.poll_response.upsert({
   *   create: {
   *     // ... data to create a Poll_response
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Poll_response we want to update
   *   }
   * })
   */
  upsert<T extends poll_responseUpsertArgs>(args: Prisma.SelectSubset<T, poll_responseUpsertArgs<ExtArgs>>): Prisma.Prisma__poll_responseClient<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Poll_responses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseCountArgs} args - Arguments to filter Poll_responses to count.
   * @example
   * // Count the number of Poll_responses
   * const count = await prisma.poll_response.count({
   *   where: {
   *     // ... the filter for the Poll_responses we want to count
   *   }
   * })
  **/
  count<T extends poll_responseCountArgs>(
    args?: Prisma.Subset<T, poll_responseCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Poll_responseCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Poll_response.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Poll_responseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Poll_responseAggregateArgs>(args: Prisma.Subset<T, Poll_responseAggregateArgs>): Prisma.PrismaPromise<GetPoll_responseAggregateType<T>>

  /**
   * Group by Poll_response.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {poll_responseGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends poll_responseGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: poll_responseGroupByArgs['orderBy'] }
      : { orderBy?: poll_responseGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, poll_responseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPoll_responseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the poll_response model
 */
readonly fields: poll_responseFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for poll_response.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__poll_responseClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.poll_response$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.poll_response$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  poll<T extends Prisma.poll_response$pollArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.poll_response$pollArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the poll_response model
 */
export interface poll_responseFieldRefs {
  readonly id: Prisma.FieldRef<"poll_response", 'Int'>
  readonly answer: Prisma.FieldRef<"poll_response", 'Json'>
  readonly createdAt: Prisma.FieldRef<"poll_response", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"poll_response", 'DateTime'>
  readonly userId: Prisma.FieldRef<"poll_response", 'Int'>
  readonly pollId: Prisma.FieldRef<"poll_response", 'Int'>
}
    

// Custom InputTypes
/**
 * poll_response findUnique
 */
export type poll_responseFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter, which poll_response to fetch.
   */
  where: Prisma.poll_responseWhereUniqueInput
}

/**
 * poll_response findUniqueOrThrow
 */
export type poll_responseFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter, which poll_response to fetch.
   */
  where: Prisma.poll_responseWhereUniqueInput
}

/**
 * poll_response findFirst
 */
export type poll_responseFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter, which poll_response to fetch.
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of poll_responses to fetch.
   */
  orderBy?: Prisma.poll_responseOrderByWithRelationInput | Prisma.poll_responseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for poll_responses.
   */
  cursor?: Prisma.poll_responseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` poll_responses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` poll_responses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of poll_responses.
   */
  distinct?: Prisma.Poll_responseScalarFieldEnum | Prisma.Poll_responseScalarFieldEnum[]
}

/**
 * poll_response findFirstOrThrow
 */
export type poll_responseFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter, which poll_response to fetch.
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of poll_responses to fetch.
   */
  orderBy?: Prisma.poll_responseOrderByWithRelationInput | Prisma.poll_responseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for poll_responses.
   */
  cursor?: Prisma.poll_responseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` poll_responses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` poll_responses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of poll_responses.
   */
  distinct?: Prisma.Poll_responseScalarFieldEnum | Prisma.Poll_responseScalarFieldEnum[]
}

/**
 * poll_response findMany
 */
export type poll_responseFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter, which poll_responses to fetch.
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of poll_responses to fetch.
   */
  orderBy?: Prisma.poll_responseOrderByWithRelationInput | Prisma.poll_responseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing poll_responses.
   */
  cursor?: Prisma.poll_responseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` poll_responses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` poll_responses.
   */
  skip?: number
  distinct?: Prisma.Poll_responseScalarFieldEnum | Prisma.Poll_responseScalarFieldEnum[]
}

/**
 * poll_response create
 */
export type poll_responseCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * The data needed to create a poll_response.
   */
  data: Prisma.XOR<Prisma.poll_responseCreateInput, Prisma.poll_responseUncheckedCreateInput>
}

/**
 * poll_response createMany
 */
export type poll_responseCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many poll_responses.
   */
  data: Prisma.poll_responseCreateManyInput | Prisma.poll_responseCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * poll_response update
 */
export type poll_responseUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * The data needed to update a poll_response.
   */
  data: Prisma.XOR<Prisma.poll_responseUpdateInput, Prisma.poll_responseUncheckedUpdateInput>
  /**
   * Choose, which poll_response to update.
   */
  where: Prisma.poll_responseWhereUniqueInput
}

/**
 * poll_response updateMany
 */
export type poll_responseUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update poll_responses.
   */
  data: Prisma.XOR<Prisma.poll_responseUpdateManyMutationInput, Prisma.poll_responseUncheckedUpdateManyInput>
  /**
   * Filter which poll_responses to update
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * Limit how many poll_responses to update.
   */
  limit?: number
}

/**
 * poll_response upsert
 */
export type poll_responseUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * The filter to search for the poll_response to update in case it exists.
   */
  where: Prisma.poll_responseWhereUniqueInput
  /**
   * In case the poll_response found by the `where` argument doesn't exist, create a new poll_response with this data.
   */
  create: Prisma.XOR<Prisma.poll_responseCreateInput, Prisma.poll_responseUncheckedCreateInput>
  /**
   * In case the poll_response was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.poll_responseUpdateInput, Prisma.poll_responseUncheckedUpdateInput>
}

/**
 * poll_response delete
 */
export type poll_responseDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  /**
   * Filter which poll_response to delete.
   */
  where: Prisma.poll_responseWhereUniqueInput
}

/**
 * poll_response deleteMany
 */
export type poll_responseDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which poll_responses to delete
   */
  where?: Prisma.poll_responseWhereInput
  /**
   * Limit how many poll_responses to delete.
   */
  limit?: number
}

/**
 * poll_response.user
 */
export type poll_response$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * poll_response.poll
 */
export type poll_response$pollArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  where?: Prisma.pollWhereInput
}

/**
 * poll_response without action
 */
export type poll_responseDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
}
