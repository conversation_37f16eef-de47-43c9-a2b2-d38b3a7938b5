
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `poll` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model poll
 * 
 */
export type pollModel = runtime.Types.Result.DefaultSelection<Prisma.$pollPayload>

export type AggregatePoll = {
  _count: PollCountAggregateOutputType | null
  _avg: PollAvgAggregateOutputType | null
  _sum: PollSumAggregateOutputType | null
  _min: PollMinAggregateOutputType | null
  _max: PollMaxAggregateOutputType | null
}

export type PollAvgAggregateOutputType = {
  id: number | null
}

export type PollSumAggregateOutputType = {
  id: number | null
}

export type PollMinAggregateOutputType = {
  id: number | null
  title: string | null
  ended: boolean | null
  showResults: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PollMaxAggregateOutputType = {
  id: number | null
  title: string | null
  ended: boolean | null
  showResults: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PollCountAggregateOutputType = {
  id: number
  title: number
  ended: number
  showResults: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type PollAvgAggregateInputType = {
  id?: true
}

export type PollSumAggregateInputType = {
  id?: true
}

export type PollMinAggregateInputType = {
  id?: true
  title?: true
  ended?: true
  showResults?: true
  createdAt?: true
  updatedAt?: true
}

export type PollMaxAggregateInputType = {
  id?: true
  title?: true
  ended?: true
  showResults?: true
  createdAt?: true
  updatedAt?: true
}

export type PollCountAggregateInputType = {
  id?: true
  title?: true
  ended?: true
  showResults?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PollAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which poll to aggregate.
   */
  where?: Prisma.pollWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of polls to fetch.
   */
  orderBy?: Prisma.pollOrderByWithRelationInput | Prisma.pollOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.pollWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` polls from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` polls.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned polls
  **/
  _count?: true | PollCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PollAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PollSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PollMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PollMaxAggregateInputType
}

export type GetPollAggregateType<T extends PollAggregateArgs> = {
      [P in keyof T & keyof AggregatePoll]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePoll[P]>
    : Prisma.GetScalarType<T[P], AggregatePoll[P]>
}




export type pollGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.pollWhereInput
  orderBy?: Prisma.pollOrderByWithAggregationInput | Prisma.pollOrderByWithAggregationInput[]
  by: Prisma.PollScalarFieldEnum[] | Prisma.PollScalarFieldEnum
  having?: Prisma.pollScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PollCountAggregateInputType | true
  _avg?: PollAvgAggregateInputType
  _sum?: PollSumAggregateInputType
  _min?: PollMinAggregateInputType
  _max?: PollMaxAggregateInputType
}

export type PollGroupByOutputType = {
  id: number
  title: string
  ended: boolean
  showResults: boolean
  createdAt: Date
  updatedAt: Date
  _count: PollCountAggregateOutputType | null
  _avg: PollAvgAggregateOutputType | null
  _sum: PollSumAggregateOutputType | null
  _min: PollMinAggregateOutputType | null
  _max: PollMaxAggregateOutputType | null
}

type GetPollGroupByPayload<T extends pollGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PollGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PollGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PollGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PollGroupByOutputType[P]>
      }
    >
  >



export type pollWhereInput = {
  AND?: Prisma.pollWhereInput | Prisma.pollWhereInput[]
  OR?: Prisma.pollWhereInput[]
  NOT?: Prisma.pollWhereInput | Prisma.pollWhereInput[]
  id?: Prisma.IntFilter<"poll"> | number
  title?: Prisma.StringFilter<"poll"> | string
  ended?: Prisma.BoolFilter<"poll"> | boolean
  showResults?: Prisma.BoolFilter<"poll"> | boolean
  createdAt?: Prisma.DateTimeFilter<"poll"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"poll"> | Date | string
  poll_response?: Prisma.Poll_responseListRelationFilter
}

export type pollOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  ended?: Prisma.SortOrder
  showResults?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  poll_response?: Prisma.poll_responseOrderByRelationAggregateInput
  _relevance?: Prisma.pollOrderByRelevanceInput
}

export type pollWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.pollWhereInput | Prisma.pollWhereInput[]
  OR?: Prisma.pollWhereInput[]
  NOT?: Prisma.pollWhereInput | Prisma.pollWhereInput[]
  title?: Prisma.StringFilter<"poll"> | string
  ended?: Prisma.BoolFilter<"poll"> | boolean
  showResults?: Prisma.BoolFilter<"poll"> | boolean
  createdAt?: Prisma.DateTimeFilter<"poll"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"poll"> | Date | string
  poll_response?: Prisma.Poll_responseListRelationFilter
}, "id">

export type pollOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  ended?: Prisma.SortOrder
  showResults?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.pollCountOrderByAggregateInput
  _avg?: Prisma.pollAvgOrderByAggregateInput
  _max?: Prisma.pollMaxOrderByAggregateInput
  _min?: Prisma.pollMinOrderByAggregateInput
  _sum?: Prisma.pollSumOrderByAggregateInput
}

export type pollScalarWhereWithAggregatesInput = {
  AND?: Prisma.pollScalarWhereWithAggregatesInput | Prisma.pollScalarWhereWithAggregatesInput[]
  OR?: Prisma.pollScalarWhereWithAggregatesInput[]
  NOT?: Prisma.pollScalarWhereWithAggregatesInput | Prisma.pollScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"poll"> | number
  title?: Prisma.StringWithAggregatesFilter<"poll"> | string
  ended?: Prisma.BoolWithAggregatesFilter<"poll"> | boolean
  showResults?: Prisma.BoolWithAggregatesFilter<"poll"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"poll"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"poll"> | Date | string
}

export type pollCreateInput = {
  title: string
  ended?: boolean
  showResults?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  poll_response?: Prisma.poll_responseCreateNestedManyWithoutPollInput
}

export type pollUncheckedCreateInput = {
  id?: number
  title: string
  ended?: boolean
  showResults?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  poll_response?: Prisma.poll_responseUncheckedCreateNestedManyWithoutPollInput
}

export type pollUpdateInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  poll_response?: Prisma.poll_responseUpdateManyWithoutPollNestedInput
}

export type pollUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  poll_response?: Prisma.poll_responseUncheckedUpdateManyWithoutPollNestedInput
}

export type pollCreateManyInput = {
  id?: number
  title: string
  ended?: boolean
  showResults?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type pollUpdateManyMutationInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type pollUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type pollOrderByRelevanceInput = {
  fields: Prisma.pollOrderByRelevanceFieldEnum | Prisma.pollOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type pollCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  ended?: Prisma.SortOrder
  showResults?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type pollAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type pollMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  ended?: Prisma.SortOrder
  showResults?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type pollMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  ended?: Prisma.SortOrder
  showResults?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type pollSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type PollNullableScalarRelationFilter = {
  is?: Prisma.pollWhereInput | null
  isNot?: Prisma.pollWhereInput | null
}

export type pollCreateNestedOneWithoutPoll_responseInput = {
  create?: Prisma.XOR<Prisma.pollCreateWithoutPoll_responseInput, Prisma.pollUncheckedCreateWithoutPoll_responseInput>
  connectOrCreate?: Prisma.pollCreateOrConnectWithoutPoll_responseInput
  connect?: Prisma.pollWhereUniqueInput
}

export type pollUpdateOneWithoutPoll_responseNestedInput = {
  create?: Prisma.XOR<Prisma.pollCreateWithoutPoll_responseInput, Prisma.pollUncheckedCreateWithoutPoll_responseInput>
  connectOrCreate?: Prisma.pollCreateOrConnectWithoutPoll_responseInput
  upsert?: Prisma.pollUpsertWithoutPoll_responseInput
  disconnect?: Prisma.pollWhereInput | boolean
  delete?: Prisma.pollWhereInput | boolean
  connect?: Prisma.pollWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.pollUpdateToOneWithWhereWithoutPoll_responseInput, Prisma.pollUpdateWithoutPoll_responseInput>, Prisma.pollUncheckedUpdateWithoutPoll_responseInput>
}

export type pollCreateWithoutPoll_responseInput = {
  title: string
  ended?: boolean
  showResults?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type pollUncheckedCreateWithoutPoll_responseInput = {
  id?: number
  title: string
  ended?: boolean
  showResults?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type pollCreateOrConnectWithoutPoll_responseInput = {
  where: Prisma.pollWhereUniqueInput
  create: Prisma.XOR<Prisma.pollCreateWithoutPoll_responseInput, Prisma.pollUncheckedCreateWithoutPoll_responseInput>
}

export type pollUpsertWithoutPoll_responseInput = {
  update: Prisma.XOR<Prisma.pollUpdateWithoutPoll_responseInput, Prisma.pollUncheckedUpdateWithoutPoll_responseInput>
  create: Prisma.XOR<Prisma.pollCreateWithoutPoll_responseInput, Prisma.pollUncheckedCreateWithoutPoll_responseInput>
  where?: Prisma.pollWhereInput
}

export type pollUpdateToOneWithWhereWithoutPoll_responseInput = {
  where?: Prisma.pollWhereInput
  data: Prisma.XOR<Prisma.pollUpdateWithoutPoll_responseInput, Prisma.pollUncheckedUpdateWithoutPoll_responseInput>
}

export type pollUpdateWithoutPoll_responseInput = {
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type pollUncheckedUpdateWithoutPoll_responseInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  title?: Prisma.StringFieldUpdateOperationsInput | string
  ended?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showResults?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type PollCountOutputType
 */

export type PollCountOutputType = {
  poll_response: number
}

export type PollCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  poll_response?: boolean | PollCountOutputTypeCountPoll_responseArgs
}

/**
 * PollCountOutputType without action
 */
export type PollCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PollCountOutputType
   */
  select?: Prisma.PollCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PollCountOutputType without action
 */
export type PollCountOutputTypeCountPoll_responseArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.poll_responseWhereInput
}


export type pollSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  ended?: boolean
  showResults?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  poll_response?: boolean | Prisma.poll$poll_responseArgs<ExtArgs>
  _count?: boolean | Prisma.PollCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["poll"]>



export type pollSelectScalar = {
  id?: boolean
  title?: boolean
  ended?: boolean
  showResults?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type pollOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "ended" | "showResults" | "createdAt" | "updatedAt", ExtArgs["result"]["poll"]>
export type pollInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  poll_response?: boolean | Prisma.poll$poll_responseArgs<ExtArgs>
  _count?: boolean | Prisma.PollCountOutputTypeDefaultArgs<ExtArgs>
}

export type $pollPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "poll"
  objects: {
    poll_response: Prisma.$poll_responsePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    title: string
    ended: boolean
    showResults: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["poll"]>
  composites: {}
}

export type pollGetPayload<S extends boolean | null | undefined | pollDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$pollPayload, S>

export type pollCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<pollFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PollCountAggregateInputType | true
  }

export interface pollDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['poll'], meta: { name: 'poll' } }
  /**
   * Find zero or one Poll that matches the filter.
   * @param {pollFindUniqueArgs} args - Arguments to find a Poll
   * @example
   * // Get one Poll
   * const poll = await prisma.poll.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends pollFindUniqueArgs>(args: Prisma.SelectSubset<T, pollFindUniqueArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Poll that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {pollFindUniqueOrThrowArgs} args - Arguments to find a Poll
   * @example
   * // Get one Poll
   * const poll = await prisma.poll.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends pollFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, pollFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Poll that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollFindFirstArgs} args - Arguments to find a Poll
   * @example
   * // Get one Poll
   * const poll = await prisma.poll.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends pollFindFirstArgs>(args?: Prisma.SelectSubset<T, pollFindFirstArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Poll that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollFindFirstOrThrowArgs} args - Arguments to find a Poll
   * @example
   * // Get one Poll
   * const poll = await prisma.poll.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends pollFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, pollFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Polls that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Polls
   * const polls = await prisma.poll.findMany()
   * 
   * // Get first 10 Polls
   * const polls = await prisma.poll.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const pollWithIdOnly = await prisma.poll.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends pollFindManyArgs>(args?: Prisma.SelectSubset<T, pollFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Poll.
   * @param {pollCreateArgs} args - Arguments to create a Poll.
   * @example
   * // Create one Poll
   * const Poll = await prisma.poll.create({
   *   data: {
   *     // ... data to create a Poll
   *   }
   * })
   * 
   */
  create<T extends pollCreateArgs>(args: Prisma.SelectSubset<T, pollCreateArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Polls.
   * @param {pollCreateManyArgs} args - Arguments to create many Polls.
   * @example
   * // Create many Polls
   * const poll = await prisma.poll.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends pollCreateManyArgs>(args?: Prisma.SelectSubset<T, pollCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Poll.
   * @param {pollDeleteArgs} args - Arguments to delete one Poll.
   * @example
   * // Delete one Poll
   * const Poll = await prisma.poll.delete({
   *   where: {
   *     // ... filter to delete one Poll
   *   }
   * })
   * 
   */
  delete<T extends pollDeleteArgs>(args: Prisma.SelectSubset<T, pollDeleteArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Poll.
   * @param {pollUpdateArgs} args - Arguments to update one Poll.
   * @example
   * // Update one Poll
   * const poll = await prisma.poll.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends pollUpdateArgs>(args: Prisma.SelectSubset<T, pollUpdateArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Polls.
   * @param {pollDeleteManyArgs} args - Arguments to filter Polls to delete.
   * @example
   * // Delete a few Polls
   * const { count } = await prisma.poll.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends pollDeleteManyArgs>(args?: Prisma.SelectSubset<T, pollDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Polls.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Polls
   * const poll = await prisma.poll.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends pollUpdateManyArgs>(args: Prisma.SelectSubset<T, pollUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Poll.
   * @param {pollUpsertArgs} args - Arguments to update or create a Poll.
   * @example
   * // Update or create a Poll
   * const poll = await prisma.poll.upsert({
   *   create: {
   *     // ... data to create a Poll
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Poll we want to update
   *   }
   * })
   */
  upsert<T extends pollUpsertArgs>(args: Prisma.SelectSubset<T, pollUpsertArgs<ExtArgs>>): Prisma.Prisma__pollClient<runtime.Types.Result.GetResult<Prisma.$pollPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Polls.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollCountArgs} args - Arguments to filter Polls to count.
   * @example
   * // Count the number of Polls
   * const count = await prisma.poll.count({
   *   where: {
   *     // ... the filter for the Polls we want to count
   *   }
   * })
  **/
  count<T extends pollCountArgs>(
    args?: Prisma.Subset<T, pollCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PollCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Poll.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PollAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PollAggregateArgs>(args: Prisma.Subset<T, PollAggregateArgs>): Prisma.PrismaPromise<GetPollAggregateType<T>>

  /**
   * Group by Poll.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {pollGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends pollGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: pollGroupByArgs['orderBy'] }
      : { orderBy?: pollGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, pollGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPollGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the poll model
 */
readonly fields: pollFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for poll.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__pollClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  poll_response<T extends Prisma.poll$poll_responseArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.poll$poll_responseArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$poll_responsePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the poll model
 */
export interface pollFieldRefs {
  readonly id: Prisma.FieldRef<"poll", 'Int'>
  readonly title: Prisma.FieldRef<"poll", 'String'>
  readonly ended: Prisma.FieldRef<"poll", 'Boolean'>
  readonly showResults: Prisma.FieldRef<"poll", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"poll", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"poll", 'DateTime'>
}
    

// Custom InputTypes
/**
 * poll findUnique
 */
export type pollFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter, which poll to fetch.
   */
  where: Prisma.pollWhereUniqueInput
}

/**
 * poll findUniqueOrThrow
 */
export type pollFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter, which poll to fetch.
   */
  where: Prisma.pollWhereUniqueInput
}

/**
 * poll findFirst
 */
export type pollFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter, which poll to fetch.
   */
  where?: Prisma.pollWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of polls to fetch.
   */
  orderBy?: Prisma.pollOrderByWithRelationInput | Prisma.pollOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for polls.
   */
  cursor?: Prisma.pollWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` polls from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` polls.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of polls.
   */
  distinct?: Prisma.PollScalarFieldEnum | Prisma.PollScalarFieldEnum[]
}

/**
 * poll findFirstOrThrow
 */
export type pollFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter, which poll to fetch.
   */
  where?: Prisma.pollWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of polls to fetch.
   */
  orderBy?: Prisma.pollOrderByWithRelationInput | Prisma.pollOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for polls.
   */
  cursor?: Prisma.pollWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` polls from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` polls.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of polls.
   */
  distinct?: Prisma.PollScalarFieldEnum | Prisma.PollScalarFieldEnum[]
}

/**
 * poll findMany
 */
export type pollFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter, which polls to fetch.
   */
  where?: Prisma.pollWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of polls to fetch.
   */
  orderBy?: Prisma.pollOrderByWithRelationInput | Prisma.pollOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing polls.
   */
  cursor?: Prisma.pollWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` polls from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` polls.
   */
  skip?: number
  distinct?: Prisma.PollScalarFieldEnum | Prisma.PollScalarFieldEnum[]
}

/**
 * poll create
 */
export type pollCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * The data needed to create a poll.
   */
  data: Prisma.XOR<Prisma.pollCreateInput, Prisma.pollUncheckedCreateInput>
}

/**
 * poll createMany
 */
export type pollCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many polls.
   */
  data: Prisma.pollCreateManyInput | Prisma.pollCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * poll update
 */
export type pollUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * The data needed to update a poll.
   */
  data: Prisma.XOR<Prisma.pollUpdateInput, Prisma.pollUncheckedUpdateInput>
  /**
   * Choose, which poll to update.
   */
  where: Prisma.pollWhereUniqueInput
}

/**
 * poll updateMany
 */
export type pollUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update polls.
   */
  data: Prisma.XOR<Prisma.pollUpdateManyMutationInput, Prisma.pollUncheckedUpdateManyInput>
  /**
   * Filter which polls to update
   */
  where?: Prisma.pollWhereInput
  /**
   * Limit how many polls to update.
   */
  limit?: number
}

/**
 * poll upsert
 */
export type pollUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * The filter to search for the poll to update in case it exists.
   */
  where: Prisma.pollWhereUniqueInput
  /**
   * In case the poll found by the `where` argument doesn't exist, create a new poll with this data.
   */
  create: Prisma.XOR<Prisma.pollCreateInput, Prisma.pollUncheckedCreateInput>
  /**
   * In case the poll was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.pollUpdateInput, Prisma.pollUncheckedUpdateInput>
}

/**
 * poll delete
 */
export type pollDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
  /**
   * Filter which poll to delete.
   */
  where: Prisma.pollWhereUniqueInput
}

/**
 * poll deleteMany
 */
export type pollDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which polls to delete
   */
  where?: Prisma.pollWhereInput
  /**
   * Limit how many polls to delete.
   */
  limit?: number
}

/**
 * poll.poll_response
 */
export type poll$poll_responseArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll_response
   */
  select?: Prisma.poll_responseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll_response
   */
  omit?: Prisma.poll_responseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.poll_responseInclude<ExtArgs> | null
  where?: Prisma.poll_responseWhereInput
  orderBy?: Prisma.poll_responseOrderByWithRelationInput | Prisma.poll_responseOrderByWithRelationInput[]
  cursor?: Prisma.poll_responseWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Poll_responseScalarFieldEnum | Prisma.Poll_responseScalarFieldEnum[]
}

/**
 * poll without action
 */
export type pollDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the poll
   */
  select?: Prisma.pollSelect<ExtArgs> | null
  /**
   * Omit specific fields from the poll
   */
  omit?: Prisma.pollOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.pollInclude<ExtArgs> | null
}
