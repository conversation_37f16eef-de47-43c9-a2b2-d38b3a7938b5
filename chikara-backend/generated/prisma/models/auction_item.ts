
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `auction_item` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model auction_item
 * 
 */
export type auction_itemModel = runtime.Types.Result.DefaultSelection<Prisma.$auction_itemPayload>

export type AggregateAuction_item = {
  _count: Auction_itemCountAggregateOutputType | null
  _avg: Auction_itemAvgAggregateOutputType | null
  _sum: Auction_itemSumAggregateOutputType | null
  _min: Auction_itemMinAggregateOutputType | null
  _max: Auction_itemMaxAggregateOutputType | null
}

export type Auction_itemAvgAggregateOutputType = {
  id: number | null
  quantity: number | null
  deposit: number | null
  buyoutPrice: number | null
  itemId: number | null
  sellerId: number | null
}

export type Auction_itemSumAggregateOutputType = {
  id: number | null
  quantity: number | null
  deposit: number | null
  buyoutPrice: number | null
  itemId: number | null
  sellerId: number | null
}

export type Auction_itemMinAggregateOutputType = {
  id: number | null
  quantity: number | null
  deposit: number | null
  buyoutPrice: number | null
  endsAt: Date | null
  status: $Enums.AuctionItemStatus | null
  bankFunds: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  itemId: number | null
  sellerId: number | null
}

export type Auction_itemMaxAggregateOutputType = {
  id: number | null
  quantity: number | null
  deposit: number | null
  buyoutPrice: number | null
  endsAt: Date | null
  status: $Enums.AuctionItemStatus | null
  bankFunds: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  itemId: number | null
  sellerId: number | null
}

export type Auction_itemCountAggregateOutputType = {
  id: number
  quantity: number
  deposit: number
  buyoutPrice: number
  endsAt: number
  status: number
  bankFunds: number
  createdAt: number
  updatedAt: number
  itemId: number
  sellerId: number
  _all: number
}


export type Auction_itemAvgAggregateInputType = {
  id?: true
  quantity?: true
  deposit?: true
  buyoutPrice?: true
  itemId?: true
  sellerId?: true
}

export type Auction_itemSumAggregateInputType = {
  id?: true
  quantity?: true
  deposit?: true
  buyoutPrice?: true
  itemId?: true
  sellerId?: true
}

export type Auction_itemMinAggregateInputType = {
  id?: true
  quantity?: true
  deposit?: true
  buyoutPrice?: true
  endsAt?: true
  status?: true
  bankFunds?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
  sellerId?: true
}

export type Auction_itemMaxAggregateInputType = {
  id?: true
  quantity?: true
  deposit?: true
  buyoutPrice?: true
  endsAt?: true
  status?: true
  bankFunds?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
  sellerId?: true
}

export type Auction_itemCountAggregateInputType = {
  id?: true
  quantity?: true
  deposit?: true
  buyoutPrice?: true
  endsAt?: true
  status?: true
  bankFunds?: true
  createdAt?: true
  updatedAt?: true
  itemId?: true
  sellerId?: true
  _all?: true
}

export type Auction_itemAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which auction_item to aggregate.
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of auction_items to fetch.
   */
  orderBy?: Prisma.auction_itemOrderByWithRelationInput | Prisma.auction_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.auction_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` auction_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` auction_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned auction_items
  **/
  _count?: true | Auction_itemCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: Auction_itemAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: Auction_itemSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: Auction_itemMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: Auction_itemMaxAggregateInputType
}

export type GetAuction_itemAggregateType<T extends Auction_itemAggregateArgs> = {
      [P in keyof T & keyof AggregateAuction_item]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAuction_item[P]>
    : Prisma.GetScalarType<T[P], AggregateAuction_item[P]>
}




export type auction_itemGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.auction_itemWhereInput
  orderBy?: Prisma.auction_itemOrderByWithAggregationInput | Prisma.auction_itemOrderByWithAggregationInput[]
  by: Prisma.Auction_itemScalarFieldEnum[] | Prisma.Auction_itemScalarFieldEnum
  having?: Prisma.auction_itemScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: Auction_itemCountAggregateInputType | true
  _avg?: Auction_itemAvgAggregateInputType
  _sum?: Auction_itemSumAggregateInputType
  _min?: Auction_itemMinAggregateInputType
  _max?: Auction_itemMaxAggregateInputType
}

export type Auction_itemGroupByOutputType = {
  id: number
  quantity: number
  deposit: number
  buyoutPrice: number
  endsAt: Date
  status: $Enums.AuctionItemStatus
  bankFunds: boolean
  createdAt: Date
  updatedAt: Date
  itemId: number | null
  sellerId: number | null
  _count: Auction_itemCountAggregateOutputType | null
  _avg: Auction_itemAvgAggregateOutputType | null
  _sum: Auction_itemSumAggregateOutputType | null
  _min: Auction_itemMinAggregateOutputType | null
  _max: Auction_itemMaxAggregateOutputType | null
}

type GetAuction_itemGroupByPayload<T extends auction_itemGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<Auction_itemGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof Auction_itemGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], Auction_itemGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], Auction_itemGroupByOutputType[P]>
      }
    >
  >



export type auction_itemWhereInput = {
  AND?: Prisma.auction_itemWhereInput | Prisma.auction_itemWhereInput[]
  OR?: Prisma.auction_itemWhereInput[]
  NOT?: Prisma.auction_itemWhereInput | Prisma.auction_itemWhereInput[]
  id?: Prisma.IntFilter<"auction_item"> | number
  quantity?: Prisma.IntFilter<"auction_item"> | number
  deposit?: Prisma.IntFilter<"auction_item"> | number
  buyoutPrice?: Prisma.IntFilter<"auction_item"> | number
  endsAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  status?: Prisma.EnumAuctionItemStatusFilter<"auction_item"> | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFilter<"auction_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  itemId?: Prisma.IntNullableFilter<"auction_item"> | number | null
  sellerId?: Prisma.IntNullableFilter<"auction_item"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}

export type auction_itemOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  bankFunds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  sellerId?: Prisma.SortOrderInput | Prisma.SortOrder
  item?: Prisma.itemOrderByWithRelationInput
  user?: Prisma.userOrderByWithRelationInput
}

export type auction_itemWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.auction_itemWhereInput | Prisma.auction_itemWhereInput[]
  OR?: Prisma.auction_itemWhereInput[]
  NOT?: Prisma.auction_itemWhereInput | Prisma.auction_itemWhereInput[]
  quantity?: Prisma.IntFilter<"auction_item"> | number
  deposit?: Prisma.IntFilter<"auction_item"> | number
  buyoutPrice?: Prisma.IntFilter<"auction_item"> | number
  endsAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  status?: Prisma.EnumAuctionItemStatusFilter<"auction_item"> | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFilter<"auction_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  itemId?: Prisma.IntNullableFilter<"auction_item"> | number | null
  sellerId?: Prisma.IntNullableFilter<"auction_item"> | number | null
  item?: Prisma.XOR<Prisma.ItemNullableScalarRelationFilter, Prisma.itemWhereInput> | null
  user?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.userWhereInput> | null
}, "id">

export type auction_itemOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  bankFunds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrderInput | Prisma.SortOrder
  sellerId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.auction_itemCountOrderByAggregateInput
  _avg?: Prisma.auction_itemAvgOrderByAggregateInput
  _max?: Prisma.auction_itemMaxOrderByAggregateInput
  _min?: Prisma.auction_itemMinOrderByAggregateInput
  _sum?: Prisma.auction_itemSumOrderByAggregateInput
}

export type auction_itemScalarWhereWithAggregatesInput = {
  AND?: Prisma.auction_itemScalarWhereWithAggregatesInput | Prisma.auction_itemScalarWhereWithAggregatesInput[]
  OR?: Prisma.auction_itemScalarWhereWithAggregatesInput[]
  NOT?: Prisma.auction_itemScalarWhereWithAggregatesInput | Prisma.auction_itemScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"auction_item"> | number
  quantity?: Prisma.IntWithAggregatesFilter<"auction_item"> | number
  deposit?: Prisma.IntWithAggregatesFilter<"auction_item"> | number
  buyoutPrice?: Prisma.IntWithAggregatesFilter<"auction_item"> | number
  endsAt?: Prisma.DateTimeWithAggregatesFilter<"auction_item"> | Date | string
  status?: Prisma.EnumAuctionItemStatusWithAggregatesFilter<"auction_item"> | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolWithAggregatesFilter<"auction_item"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"auction_item"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"auction_item"> | Date | string
  itemId?: Prisma.IntNullableWithAggregatesFilter<"auction_item"> | number | null
  sellerId?: Prisma.IntNullableWithAggregatesFilter<"auction_item"> | number | null
}

export type auction_itemCreateInput = {
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutAuction_itemInput
  user?: Prisma.userCreateNestedOneWithoutAuction_itemInput
}

export type auction_itemUncheckedCreateInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
  sellerId?: number | null
}

export type auction_itemUpdateInput = {
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutAuction_itemNestedInput
  user?: Prisma.userUpdateOneWithoutAuction_itemNestedInput
}

export type auction_itemUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  sellerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type auction_itemCreateManyInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
  sellerId?: number | null
}

export type auction_itemUpdateManyMutationInput = {
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type auction_itemUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  sellerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type auction_itemCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  bankFunds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  sellerId?: Prisma.SortOrder
}

export type auction_itemAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  sellerId?: Prisma.SortOrder
}

export type auction_itemMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  bankFunds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  sellerId?: Prisma.SortOrder
}

export type auction_itemMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  endsAt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  bankFunds?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  sellerId?: Prisma.SortOrder
}

export type auction_itemSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  deposit?: Prisma.SortOrder
  buyoutPrice?: Prisma.SortOrder
  itemId?: Prisma.SortOrder
  sellerId?: Prisma.SortOrder
}

export type Auction_itemListRelationFilter = {
  every?: Prisma.auction_itemWhereInput
  some?: Prisma.auction_itemWhereInput
  none?: Prisma.auction_itemWhereInput
}

export type auction_itemOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EnumAuctionItemStatusFieldUpdateOperationsInput = {
  set?: $Enums.AuctionItemStatus
}

export type auction_itemCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput> | Prisma.auction_itemCreateWithoutItemInput[] | Prisma.auction_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutItemInput | Prisma.auction_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.auction_itemCreateManyItemInputEnvelope
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
}

export type auction_itemUncheckedCreateNestedManyWithoutItemInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput> | Prisma.auction_itemCreateWithoutItemInput[] | Prisma.auction_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutItemInput | Prisma.auction_itemCreateOrConnectWithoutItemInput[]
  createMany?: Prisma.auction_itemCreateManyItemInputEnvelope
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
}

export type auction_itemUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput> | Prisma.auction_itemCreateWithoutItemInput[] | Prisma.auction_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutItemInput | Prisma.auction_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.auction_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.auction_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.auction_itemCreateManyItemInputEnvelope
  set?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  disconnect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  delete?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  update?: Prisma.auction_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.auction_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.auction_itemUpdateManyWithWhereWithoutItemInput | Prisma.auction_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
}

export type auction_itemUncheckedUpdateManyWithoutItemNestedInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput> | Prisma.auction_itemCreateWithoutItemInput[] | Prisma.auction_itemUncheckedCreateWithoutItemInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutItemInput | Prisma.auction_itemCreateOrConnectWithoutItemInput[]
  upsert?: Prisma.auction_itemUpsertWithWhereUniqueWithoutItemInput | Prisma.auction_itemUpsertWithWhereUniqueWithoutItemInput[]
  createMany?: Prisma.auction_itemCreateManyItemInputEnvelope
  set?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  disconnect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  delete?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  update?: Prisma.auction_itemUpdateWithWhereUniqueWithoutItemInput | Prisma.auction_itemUpdateWithWhereUniqueWithoutItemInput[]
  updateMany?: Prisma.auction_itemUpdateManyWithWhereWithoutItemInput | Prisma.auction_itemUpdateManyWithWhereWithoutItemInput[]
  deleteMany?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
}

export type auction_itemCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput> | Prisma.auction_itemCreateWithoutUserInput[] | Prisma.auction_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutUserInput | Prisma.auction_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.auction_itemCreateManyUserInputEnvelope
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
}

export type auction_itemUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput> | Prisma.auction_itemCreateWithoutUserInput[] | Prisma.auction_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutUserInput | Prisma.auction_itemCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.auction_itemCreateManyUserInputEnvelope
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
}

export type auction_itemUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput> | Prisma.auction_itemCreateWithoutUserInput[] | Prisma.auction_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutUserInput | Prisma.auction_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.auction_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.auction_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.auction_itemCreateManyUserInputEnvelope
  set?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  disconnect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  delete?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  update?: Prisma.auction_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.auction_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.auction_itemUpdateManyWithWhereWithoutUserInput | Prisma.auction_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
}

export type auction_itemUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput> | Prisma.auction_itemCreateWithoutUserInput[] | Prisma.auction_itemUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.auction_itemCreateOrConnectWithoutUserInput | Prisma.auction_itemCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.auction_itemUpsertWithWhereUniqueWithoutUserInput | Prisma.auction_itemUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.auction_itemCreateManyUserInputEnvelope
  set?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  disconnect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  delete?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  connect?: Prisma.auction_itemWhereUniqueInput | Prisma.auction_itemWhereUniqueInput[]
  update?: Prisma.auction_itemUpdateWithWhereUniqueWithoutUserInput | Prisma.auction_itemUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.auction_itemUpdateManyWithWhereWithoutUserInput | Prisma.auction_itemUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
}

export type auction_itemCreateWithoutItemInput = {
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user?: Prisma.userCreateNestedOneWithoutAuction_itemInput
}

export type auction_itemUncheckedCreateWithoutItemInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sellerId?: number | null
}

export type auction_itemCreateOrConnectWithoutItemInput = {
  where: Prisma.auction_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput>
}

export type auction_itemCreateManyItemInputEnvelope = {
  data: Prisma.auction_itemCreateManyItemInput | Prisma.auction_itemCreateManyItemInput[]
  skipDuplicates?: boolean
}

export type auction_itemUpsertWithWhereUniqueWithoutItemInput = {
  where: Prisma.auction_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.auction_itemUpdateWithoutItemInput, Prisma.auction_itemUncheckedUpdateWithoutItemInput>
  create: Prisma.XOR<Prisma.auction_itemCreateWithoutItemInput, Prisma.auction_itemUncheckedCreateWithoutItemInput>
}

export type auction_itemUpdateWithWhereUniqueWithoutItemInput = {
  where: Prisma.auction_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.auction_itemUpdateWithoutItemInput, Prisma.auction_itemUncheckedUpdateWithoutItemInput>
}

export type auction_itemUpdateManyWithWhereWithoutItemInput = {
  where: Prisma.auction_itemScalarWhereInput
  data: Prisma.XOR<Prisma.auction_itemUpdateManyMutationInput, Prisma.auction_itemUncheckedUpdateManyWithoutItemInput>
}

export type auction_itemScalarWhereInput = {
  AND?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
  OR?: Prisma.auction_itemScalarWhereInput[]
  NOT?: Prisma.auction_itemScalarWhereInput | Prisma.auction_itemScalarWhereInput[]
  id?: Prisma.IntFilter<"auction_item"> | number
  quantity?: Prisma.IntFilter<"auction_item"> | number
  deposit?: Prisma.IntFilter<"auction_item"> | number
  buyoutPrice?: Prisma.IntFilter<"auction_item"> | number
  endsAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  status?: Prisma.EnumAuctionItemStatusFilter<"auction_item"> | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFilter<"auction_item"> | boolean
  createdAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"auction_item"> | Date | string
  itemId?: Prisma.IntNullableFilter<"auction_item"> | number | null
  sellerId?: Prisma.IntNullableFilter<"auction_item"> | number | null
}

export type auction_itemCreateWithoutUserInput = {
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  item?: Prisma.itemCreateNestedOneWithoutAuction_itemInput
}

export type auction_itemUncheckedCreateWithoutUserInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
}

export type auction_itemCreateOrConnectWithoutUserInput = {
  where: Prisma.auction_itemWhereUniqueInput
  create: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput>
}

export type auction_itemCreateManyUserInputEnvelope = {
  data: Prisma.auction_itemCreateManyUserInput | Prisma.auction_itemCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type auction_itemUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.auction_itemWhereUniqueInput
  update: Prisma.XOR<Prisma.auction_itemUpdateWithoutUserInput, Prisma.auction_itemUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.auction_itemCreateWithoutUserInput, Prisma.auction_itemUncheckedCreateWithoutUserInput>
}

export type auction_itemUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.auction_itemWhereUniqueInput
  data: Prisma.XOR<Prisma.auction_itemUpdateWithoutUserInput, Prisma.auction_itemUncheckedUpdateWithoutUserInput>
}

export type auction_itemUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.auction_itemScalarWhereInput
  data: Prisma.XOR<Prisma.auction_itemUpdateManyMutationInput, Prisma.auction_itemUncheckedUpdateManyWithoutUserInput>
}

export type auction_itemCreateManyItemInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sellerId?: number | null
}

export type auction_itemUpdateWithoutItemInput = {
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.userUpdateOneWithoutAuction_itemNestedInput
}

export type auction_itemUncheckedUpdateWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sellerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type auction_itemUncheckedUpdateManyWithoutItemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sellerId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type auction_itemCreateManyUserInput = {
  id?: number
  quantity?: number
  deposit: number
  buyoutPrice: number
  endsAt: Date | string
  status: $Enums.AuctionItemStatus
  bankFunds?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  itemId?: number | null
}

export type auction_itemUpdateWithoutUserInput = {
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  item?: Prisma.itemUpdateOneWithoutAuction_itemNestedInput
}

export type auction_itemUncheckedUpdateWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type auction_itemUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  deposit?: Prisma.IntFieldUpdateOperationsInput | number
  buyoutPrice?: Prisma.IntFieldUpdateOperationsInput | number
  endsAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.EnumAuctionItemStatusFieldUpdateOperationsInput | $Enums.AuctionItemStatus
  bankFunds?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  itemId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}



export type auction_itemSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  quantity?: boolean
  deposit?: boolean
  buyoutPrice?: boolean
  endsAt?: boolean
  status?: boolean
  bankFunds?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemId?: boolean
  sellerId?: boolean
  item?: boolean | Prisma.auction_item$itemArgs<ExtArgs>
  user?: boolean | Prisma.auction_item$userArgs<ExtArgs>
}, ExtArgs["result"]["auction_item"]>



export type auction_itemSelectScalar = {
  id?: boolean
  quantity?: boolean
  deposit?: boolean
  buyoutPrice?: boolean
  endsAt?: boolean
  status?: boolean
  bankFunds?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  itemId?: boolean
  sellerId?: boolean
}

export type auction_itemOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "quantity" | "deposit" | "buyoutPrice" | "endsAt" | "status" | "bankFunds" | "createdAt" | "updatedAt" | "itemId" | "sellerId", ExtArgs["result"]["auction_item"]>
export type auction_itemInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  item?: boolean | Prisma.auction_item$itemArgs<ExtArgs>
  user?: boolean | Prisma.auction_item$userArgs<ExtArgs>
}

export type $auction_itemPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "auction_item"
  objects: {
    item: Prisma.$itemPayload<ExtArgs> | null
    user: Prisma.$userPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    quantity: number
    deposit: number
    buyoutPrice: number
    endsAt: Date
    status: $Enums.AuctionItemStatus
    bankFunds: boolean
    createdAt: Date
    updatedAt: Date
    itemId: number | null
    sellerId: number | null
  }, ExtArgs["result"]["auction_item"]>
  composites: {}
}

export type auction_itemGetPayload<S extends boolean | null | undefined | auction_itemDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$auction_itemPayload, S>

export type auction_itemCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<auction_itemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: Auction_itemCountAggregateInputType | true
  }

export interface auction_itemDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['auction_item'], meta: { name: 'auction_item' } }
  /**
   * Find zero or one Auction_item that matches the filter.
   * @param {auction_itemFindUniqueArgs} args - Arguments to find a Auction_item
   * @example
   * // Get one Auction_item
   * const auction_item = await prisma.auction_item.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends auction_itemFindUniqueArgs>(args: Prisma.SelectSubset<T, auction_itemFindUniqueArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Auction_item that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {auction_itemFindUniqueOrThrowArgs} args - Arguments to find a Auction_item
   * @example
   * // Get one Auction_item
   * const auction_item = await prisma.auction_item.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends auction_itemFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, auction_itemFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Auction_item that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemFindFirstArgs} args - Arguments to find a Auction_item
   * @example
   * // Get one Auction_item
   * const auction_item = await prisma.auction_item.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends auction_itemFindFirstArgs>(args?: Prisma.SelectSubset<T, auction_itemFindFirstArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Auction_item that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemFindFirstOrThrowArgs} args - Arguments to find a Auction_item
   * @example
   * // Get one Auction_item
   * const auction_item = await prisma.auction_item.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends auction_itemFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, auction_itemFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Auction_items that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Auction_items
   * const auction_items = await prisma.auction_item.findMany()
   * 
   * // Get first 10 Auction_items
   * const auction_items = await prisma.auction_item.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const auction_itemWithIdOnly = await prisma.auction_item.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends auction_itemFindManyArgs>(args?: Prisma.SelectSubset<T, auction_itemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Auction_item.
   * @param {auction_itemCreateArgs} args - Arguments to create a Auction_item.
   * @example
   * // Create one Auction_item
   * const Auction_item = await prisma.auction_item.create({
   *   data: {
   *     // ... data to create a Auction_item
   *   }
   * })
   * 
   */
  create<T extends auction_itemCreateArgs>(args: Prisma.SelectSubset<T, auction_itemCreateArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Auction_items.
   * @param {auction_itemCreateManyArgs} args - Arguments to create many Auction_items.
   * @example
   * // Create many Auction_items
   * const auction_item = await prisma.auction_item.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends auction_itemCreateManyArgs>(args?: Prisma.SelectSubset<T, auction_itemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Auction_item.
   * @param {auction_itemDeleteArgs} args - Arguments to delete one Auction_item.
   * @example
   * // Delete one Auction_item
   * const Auction_item = await prisma.auction_item.delete({
   *   where: {
   *     // ... filter to delete one Auction_item
   *   }
   * })
   * 
   */
  delete<T extends auction_itemDeleteArgs>(args: Prisma.SelectSubset<T, auction_itemDeleteArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Auction_item.
   * @param {auction_itemUpdateArgs} args - Arguments to update one Auction_item.
   * @example
   * // Update one Auction_item
   * const auction_item = await prisma.auction_item.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends auction_itemUpdateArgs>(args: Prisma.SelectSubset<T, auction_itemUpdateArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Auction_items.
   * @param {auction_itemDeleteManyArgs} args - Arguments to filter Auction_items to delete.
   * @example
   * // Delete a few Auction_items
   * const { count } = await prisma.auction_item.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends auction_itemDeleteManyArgs>(args?: Prisma.SelectSubset<T, auction_itemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Auction_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Auction_items
   * const auction_item = await prisma.auction_item.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends auction_itemUpdateManyArgs>(args: Prisma.SelectSubset<T, auction_itemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Auction_item.
   * @param {auction_itemUpsertArgs} args - Arguments to update or create a Auction_item.
   * @example
   * // Update or create a Auction_item
   * const auction_item = await prisma.auction_item.upsert({
   *   create: {
   *     // ... data to create a Auction_item
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Auction_item we want to update
   *   }
   * })
   */
  upsert<T extends auction_itemUpsertArgs>(args: Prisma.SelectSubset<T, auction_itemUpsertArgs<ExtArgs>>): Prisma.Prisma__auction_itemClient<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Auction_items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemCountArgs} args - Arguments to filter Auction_items to count.
   * @example
   * // Count the number of Auction_items
   * const count = await prisma.auction_item.count({
   *   where: {
   *     // ... the filter for the Auction_items we want to count
   *   }
   * })
  **/
  count<T extends auction_itemCountArgs>(
    args?: Prisma.Subset<T, auction_itemCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], Auction_itemCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Auction_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {Auction_itemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends Auction_itemAggregateArgs>(args: Prisma.Subset<T, Auction_itemAggregateArgs>): Prisma.PrismaPromise<GetAuction_itemAggregateType<T>>

  /**
   * Group by Auction_item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {auction_itemGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends auction_itemGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: auction_itemGroupByArgs['orderBy'] }
      : { orderBy?: auction_itemGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, auction_itemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAuction_itemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the auction_item model
 */
readonly fields: auction_itemFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for auction_item.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__auction_itemClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  item<T extends Prisma.auction_item$itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.auction_item$itemArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.auction_item$userArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.auction_item$userArgs<ExtArgs>>): Prisma.Prisma__userClient<runtime.Types.Result.GetResult<Prisma.$userPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the auction_item model
 */
export interface auction_itemFieldRefs {
  readonly id: Prisma.FieldRef<"auction_item", 'Int'>
  readonly quantity: Prisma.FieldRef<"auction_item", 'Int'>
  readonly deposit: Prisma.FieldRef<"auction_item", 'Int'>
  readonly buyoutPrice: Prisma.FieldRef<"auction_item", 'Int'>
  readonly endsAt: Prisma.FieldRef<"auction_item", 'DateTime'>
  readonly status: Prisma.FieldRef<"auction_item", 'AuctionItemStatus'>
  readonly bankFunds: Prisma.FieldRef<"auction_item", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"auction_item", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"auction_item", 'DateTime'>
  readonly itemId: Prisma.FieldRef<"auction_item", 'Int'>
  readonly sellerId: Prisma.FieldRef<"auction_item", 'Int'>
}
    

// Custom InputTypes
/**
 * auction_item findUnique
 */
export type auction_itemFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter, which auction_item to fetch.
   */
  where: Prisma.auction_itemWhereUniqueInput
}

/**
 * auction_item findUniqueOrThrow
 */
export type auction_itemFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter, which auction_item to fetch.
   */
  where: Prisma.auction_itemWhereUniqueInput
}

/**
 * auction_item findFirst
 */
export type auction_itemFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter, which auction_item to fetch.
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of auction_items to fetch.
   */
  orderBy?: Prisma.auction_itemOrderByWithRelationInput | Prisma.auction_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for auction_items.
   */
  cursor?: Prisma.auction_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` auction_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` auction_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of auction_items.
   */
  distinct?: Prisma.Auction_itemScalarFieldEnum | Prisma.Auction_itemScalarFieldEnum[]
}

/**
 * auction_item findFirstOrThrow
 */
export type auction_itemFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter, which auction_item to fetch.
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of auction_items to fetch.
   */
  orderBy?: Prisma.auction_itemOrderByWithRelationInput | Prisma.auction_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for auction_items.
   */
  cursor?: Prisma.auction_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` auction_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` auction_items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of auction_items.
   */
  distinct?: Prisma.Auction_itemScalarFieldEnum | Prisma.Auction_itemScalarFieldEnum[]
}

/**
 * auction_item findMany
 */
export type auction_itemFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter, which auction_items to fetch.
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of auction_items to fetch.
   */
  orderBy?: Prisma.auction_itemOrderByWithRelationInput | Prisma.auction_itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing auction_items.
   */
  cursor?: Prisma.auction_itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` auction_items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` auction_items.
   */
  skip?: number
  distinct?: Prisma.Auction_itemScalarFieldEnum | Prisma.Auction_itemScalarFieldEnum[]
}

/**
 * auction_item create
 */
export type auction_itemCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * The data needed to create a auction_item.
   */
  data: Prisma.XOR<Prisma.auction_itemCreateInput, Prisma.auction_itemUncheckedCreateInput>
}

/**
 * auction_item createMany
 */
export type auction_itemCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many auction_items.
   */
  data: Prisma.auction_itemCreateManyInput | Prisma.auction_itemCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * auction_item update
 */
export type auction_itemUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * The data needed to update a auction_item.
   */
  data: Prisma.XOR<Prisma.auction_itemUpdateInput, Prisma.auction_itemUncheckedUpdateInput>
  /**
   * Choose, which auction_item to update.
   */
  where: Prisma.auction_itemWhereUniqueInput
}

/**
 * auction_item updateMany
 */
export type auction_itemUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update auction_items.
   */
  data: Prisma.XOR<Prisma.auction_itemUpdateManyMutationInput, Prisma.auction_itemUncheckedUpdateManyInput>
  /**
   * Filter which auction_items to update
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * Limit how many auction_items to update.
   */
  limit?: number
}

/**
 * auction_item upsert
 */
export type auction_itemUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * The filter to search for the auction_item to update in case it exists.
   */
  where: Prisma.auction_itemWhereUniqueInput
  /**
   * In case the auction_item found by the `where` argument doesn't exist, create a new auction_item with this data.
   */
  create: Prisma.XOR<Prisma.auction_itemCreateInput, Prisma.auction_itemUncheckedCreateInput>
  /**
   * In case the auction_item was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.auction_itemUpdateInput, Prisma.auction_itemUncheckedUpdateInput>
}

/**
 * auction_item delete
 */
export type auction_itemDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  /**
   * Filter which auction_item to delete.
   */
  where: Prisma.auction_itemWhereUniqueInput
}

/**
 * auction_item deleteMany
 */
export type auction_itemDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which auction_items to delete
   */
  where?: Prisma.auction_itemWhereInput
  /**
   * Limit how many auction_items to delete.
   */
  limit?: number
}

/**
 * auction_item.item
 */
export type auction_item$itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  where?: Prisma.itemWhereInput
}

/**
 * auction_item.user
 */
export type auction_item$userArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user
   */
  select?: Prisma.userSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user
   */
  omit?: Prisma.userOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.userInclude<ExtArgs> | null
  where?: Prisma.userWhereInput
}

/**
 * auction_item without action
 */
export type auction_itemDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
}
