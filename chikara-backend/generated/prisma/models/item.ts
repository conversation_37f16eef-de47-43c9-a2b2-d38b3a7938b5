
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `item` model and its related types.
 *
 * 🟢 You can import this file directly.
 */

import type * as PJTG from '../pjtg';
import type * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model item
 * 
 */
export type itemModel = runtime.Types.Result.DefaultSelection<Prisma.$itemPayload>

export type AggregateItem = {
  _count: ItemCountAggregateOutputType | null
  _avg: ItemAvgAggregateOutputType | null
  _sum: ItemSumAggregateOutputType | null
  _min: ItemMinAggregateOutputType | null
  _max: ItemMaxAggregateOutputType | null
}

export type ItemAvgAggregateOutputType = {
  id: number | null
  level: number | null
  cashValue: number | null
  damage: number | null
  armour: number | null
  health: number | null
  energy: number | null
  actionPoints: number | null
  baseAmmo: number | null
  recipeUnlockId: number | null
  petUnlockId: number | null
}

export type ItemSumAggregateOutputType = {
  id: number | null
  level: number | null
  cashValue: number | null
  damage: number | null
  armour: number | null
  health: number | null
  energy: number | null
  actionPoints: number | null
  baseAmmo: number | null
  recipeUnlockId: number | null
  petUnlockId: number | null
}

export type ItemMinAggregateOutputType = {
  id: number | null
  name: string | null
  itemType: $Enums.ItemTypes | null
  rarity: $Enums.ItemRarities | null
  level: number | null
  about: string | null
  cashValue: number | null
  image: string | null
  damage: number | null
  armour: number | null
  health: number | null
  energy: number | null
  actionPoints: number | null
  baseAmmo: number | null
  createdAt: Date | null
  updatedAt: Date | null
  recipeUnlockId: number | null
  petUnlockId: number | null
}

export type ItemMaxAggregateOutputType = {
  id: number | null
  name: string | null
  itemType: $Enums.ItemTypes | null
  rarity: $Enums.ItemRarities | null
  level: number | null
  about: string | null
  cashValue: number | null
  image: string | null
  damage: number | null
  armour: number | null
  health: number | null
  energy: number | null
  actionPoints: number | null
  baseAmmo: number | null
  createdAt: Date | null
  updatedAt: Date | null
  recipeUnlockId: number | null
  petUnlockId: number | null
}

export type ItemCountAggregateOutputType = {
  id: number
  name: number
  itemType: number
  rarity: number
  level: number
  about: number
  cashValue: number
  image: number
  damage: number
  armour: number
  health: number
  energy: number
  actionPoints: number
  baseAmmo: number
  itemEffects: number
  createdAt: number
  updatedAt: number
  recipeUnlockId: number
  petUnlockId: number
  _all: number
}


export type ItemAvgAggregateInputType = {
  id?: true
  level?: true
  cashValue?: true
  damage?: true
  armour?: true
  health?: true
  energy?: true
  actionPoints?: true
  baseAmmo?: true
  recipeUnlockId?: true
  petUnlockId?: true
}

export type ItemSumAggregateInputType = {
  id?: true
  level?: true
  cashValue?: true
  damage?: true
  armour?: true
  health?: true
  energy?: true
  actionPoints?: true
  baseAmmo?: true
  recipeUnlockId?: true
  petUnlockId?: true
}

export type ItemMinAggregateInputType = {
  id?: true
  name?: true
  itemType?: true
  rarity?: true
  level?: true
  about?: true
  cashValue?: true
  image?: true
  damage?: true
  armour?: true
  health?: true
  energy?: true
  actionPoints?: true
  baseAmmo?: true
  createdAt?: true
  updatedAt?: true
  recipeUnlockId?: true
  petUnlockId?: true
}

export type ItemMaxAggregateInputType = {
  id?: true
  name?: true
  itemType?: true
  rarity?: true
  level?: true
  about?: true
  cashValue?: true
  image?: true
  damage?: true
  armour?: true
  health?: true
  energy?: true
  actionPoints?: true
  baseAmmo?: true
  createdAt?: true
  updatedAt?: true
  recipeUnlockId?: true
  petUnlockId?: true
}

export type ItemCountAggregateInputType = {
  id?: true
  name?: true
  itemType?: true
  rarity?: true
  level?: true
  about?: true
  cashValue?: true
  image?: true
  damage?: true
  armour?: true
  health?: true
  energy?: true
  actionPoints?: true
  baseAmmo?: true
  itemEffects?: true
  createdAt?: true
  updatedAt?: true
  recipeUnlockId?: true
  petUnlockId?: true
  _all?: true
}

export type ItemAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which item to aggregate.
   */
  where?: Prisma.itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of items to fetch.
   */
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned items
  **/
  _count?: true | ItemCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ItemAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ItemSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ItemMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ItemMaxAggregateInputType
}

export type GetItemAggregateType<T extends ItemAggregateArgs> = {
      [P in keyof T & keyof AggregateItem]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateItem[P]>
    : Prisma.GetScalarType<T[P], AggregateItem[P]>
}




export type itemGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.itemWhereInput
  orderBy?: Prisma.itemOrderByWithAggregationInput | Prisma.itemOrderByWithAggregationInput[]
  by: Prisma.ItemScalarFieldEnum[] | Prisma.ItemScalarFieldEnum
  having?: Prisma.itemScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ItemCountAggregateInputType | true
  _avg?: ItemAvgAggregateInputType
  _sum?: ItemSumAggregateInputType
  _min?: ItemMinAggregateInputType
  _max?: ItemMaxAggregateInputType
}

export type ItemGroupByOutputType = {
  id: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level: number
  about: string | null
  cashValue: number | null
  image: string | null
  damage: number | null
  armour: number | null
  health: number | null
  energy: number | null
  actionPoints: number | null
  baseAmmo: number | null
  itemEffects: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  recipeUnlockId: number | null
  petUnlockId: number | null
  _count: ItemCountAggregateOutputType | null
  _avg: ItemAvgAggregateOutputType | null
  _sum: ItemSumAggregateOutputType | null
  _min: ItemMinAggregateOutputType | null
  _max: ItemMaxAggregateOutputType | null
}

type GetItemGroupByPayload<T extends itemGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ItemGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ItemGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ItemGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ItemGroupByOutputType[P]>
      }
    >
  >



export type itemWhereInput = {
  AND?: Prisma.itemWhereInput | Prisma.itemWhereInput[]
  OR?: Prisma.itemWhereInput[]
  NOT?: Prisma.itemWhereInput | Prisma.itemWhereInput[]
  id?: Prisma.IntFilter<"item"> | number
  name?: Prisma.StringFilter<"item"> | string
  itemType?: Prisma.EnumItemTypesFilter<"item"> | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFilter<"item"> | $Enums.ItemRarities
  level?: Prisma.IntFilter<"item"> | number
  about?: Prisma.StringNullableFilter<"item"> | string | null
  cashValue?: Prisma.IntNullableFilter<"item"> | number | null
  image?: Prisma.StringNullableFilter<"item"> | string | null
  damage?: Prisma.IntNullableFilter<"item"> | number | null
  armour?: Prisma.IntNullableFilter<"item"> | number | null
  health?: Prisma.IntNullableFilter<"item"> | number | null
  energy?: Prisma.IntNullableFilter<"item"> | number | null
  actionPoints?: Prisma.IntNullableFilter<"item"> | number | null
  baseAmmo?: Prisma.IntNullableFilter<"item"> | number | null
  itemEffects?: Prisma.JsonNullableFilter<"item">
  createdAt?: Prisma.DateTimeFilter<"item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"item"> | Date | string
  recipeUnlockId?: Prisma.IntNullableFilter<"item"> | number | null
  petUnlockId?: Prisma.IntNullableFilter<"item"> | number | null
  auction_item?: Prisma.Auction_itemListRelationFilter
  daily_mission?: Prisma.Daily_missionListRelationFilter
  drop_chance?: Prisma.Drop_chanceListRelationFilter
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeNullableScalarRelationFilter, Prisma.crafting_recipeWhereInput> | null
  pet?: Prisma.XOR<Prisma.PetNullableScalarRelationFilter, Prisma.petWhereInput> | null
  recipe_item?: Prisma.Recipe_itemListRelationFilter
  shop_listing?: Prisma.Shop_listingListRelationFilter
  user_item?: Prisma.User_itemListRelationFilter
  quest_reward?: Prisma.Quest_rewardListRelationFilter
  daily_quest?: Prisma.Daily_questListRelationFilter
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}

export type itemOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  rarity?: Prisma.SortOrder
  level?: Prisma.SortOrder
  about?: Prisma.SortOrderInput | Prisma.SortOrder
  cashValue?: Prisma.SortOrderInput | Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  damage?: Prisma.SortOrderInput | Prisma.SortOrder
  armour?: Prisma.SortOrderInput | Prisma.SortOrder
  health?: Prisma.SortOrderInput | Prisma.SortOrder
  energy?: Prisma.SortOrderInput | Prisma.SortOrder
  actionPoints?: Prisma.SortOrderInput | Prisma.SortOrder
  baseAmmo?: Prisma.SortOrderInput | Prisma.SortOrder
  itemEffects?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrderInput | Prisma.SortOrder
  petUnlockId?: Prisma.SortOrderInput | Prisma.SortOrder
  auction_item?: Prisma.auction_itemOrderByRelationAggregateInput
  daily_mission?: Prisma.daily_missionOrderByRelationAggregateInput
  drop_chance?: Prisma.drop_chanceOrderByRelationAggregateInput
  crafting_recipe?: Prisma.crafting_recipeOrderByWithRelationInput
  pet?: Prisma.petOrderByWithRelationInput
  recipe_item?: Prisma.recipe_itemOrderByRelationAggregateInput
  shop_listing?: Prisma.shop_listingOrderByRelationAggregateInput
  user_item?: Prisma.user_itemOrderByRelationAggregateInput
  quest_reward?: Prisma.quest_rewardOrderByRelationAggregateInput
  daily_quest?: Prisma.daily_questOrderByRelationAggregateInput
  quest_objective?: Prisma.quest_objectiveOrderByRelationAggregateInput
  _relevance?: Prisma.itemOrderByRelevanceInput
}

export type itemWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  recipeUnlockId?: number
  petUnlockId?: number
  AND?: Prisma.itemWhereInput | Prisma.itemWhereInput[]
  OR?: Prisma.itemWhereInput[]
  NOT?: Prisma.itemWhereInput | Prisma.itemWhereInput[]
  name?: Prisma.StringFilter<"item"> | string
  itemType?: Prisma.EnumItemTypesFilter<"item"> | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFilter<"item"> | $Enums.ItemRarities
  level?: Prisma.IntFilter<"item"> | number
  about?: Prisma.StringNullableFilter<"item"> | string | null
  cashValue?: Prisma.IntNullableFilter<"item"> | number | null
  image?: Prisma.StringNullableFilter<"item"> | string | null
  damage?: Prisma.IntNullableFilter<"item"> | number | null
  armour?: Prisma.IntNullableFilter<"item"> | number | null
  health?: Prisma.IntNullableFilter<"item"> | number | null
  energy?: Prisma.IntNullableFilter<"item"> | number | null
  actionPoints?: Prisma.IntNullableFilter<"item"> | number | null
  baseAmmo?: Prisma.IntNullableFilter<"item"> | number | null
  itemEffects?: Prisma.JsonNullableFilter<"item">
  createdAt?: Prisma.DateTimeFilter<"item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"item"> | Date | string
  auction_item?: Prisma.Auction_itemListRelationFilter
  daily_mission?: Prisma.Daily_missionListRelationFilter
  drop_chance?: Prisma.Drop_chanceListRelationFilter
  crafting_recipe?: Prisma.XOR<Prisma.Crafting_recipeNullableScalarRelationFilter, Prisma.crafting_recipeWhereInput> | null
  pet?: Prisma.XOR<Prisma.PetNullableScalarRelationFilter, Prisma.petWhereInput> | null
  recipe_item?: Prisma.Recipe_itemListRelationFilter
  shop_listing?: Prisma.Shop_listingListRelationFilter
  user_item?: Prisma.User_itemListRelationFilter
  quest_reward?: Prisma.Quest_rewardListRelationFilter
  daily_quest?: Prisma.Daily_questListRelationFilter
  quest_objective?: Prisma.Quest_objectiveListRelationFilter
}, "id" | "recipeUnlockId" | "petUnlockId">

export type itemOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  rarity?: Prisma.SortOrder
  level?: Prisma.SortOrder
  about?: Prisma.SortOrderInput | Prisma.SortOrder
  cashValue?: Prisma.SortOrderInput | Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  damage?: Prisma.SortOrderInput | Prisma.SortOrder
  armour?: Prisma.SortOrderInput | Prisma.SortOrder
  health?: Prisma.SortOrderInput | Prisma.SortOrder
  energy?: Prisma.SortOrderInput | Prisma.SortOrder
  actionPoints?: Prisma.SortOrderInput | Prisma.SortOrder
  baseAmmo?: Prisma.SortOrderInput | Prisma.SortOrder
  itemEffects?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrderInput | Prisma.SortOrder
  petUnlockId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.itemCountOrderByAggregateInput
  _avg?: Prisma.itemAvgOrderByAggregateInput
  _max?: Prisma.itemMaxOrderByAggregateInput
  _min?: Prisma.itemMinOrderByAggregateInput
  _sum?: Prisma.itemSumOrderByAggregateInput
}

export type itemScalarWhereWithAggregatesInput = {
  AND?: Prisma.itemScalarWhereWithAggregatesInput | Prisma.itemScalarWhereWithAggregatesInput[]
  OR?: Prisma.itemScalarWhereWithAggregatesInput[]
  NOT?: Prisma.itemScalarWhereWithAggregatesInput | Prisma.itemScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"item"> | number
  name?: Prisma.StringWithAggregatesFilter<"item"> | string
  itemType?: Prisma.EnumItemTypesWithAggregatesFilter<"item"> | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesWithAggregatesFilter<"item"> | $Enums.ItemRarities
  level?: Prisma.IntWithAggregatesFilter<"item"> | number
  about?: Prisma.StringNullableWithAggregatesFilter<"item"> | string | null
  cashValue?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  image?: Prisma.StringNullableWithAggregatesFilter<"item"> | string | null
  damage?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  armour?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  health?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  energy?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  actionPoints?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  baseAmmo?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  itemEffects?: Prisma.JsonNullableWithAggregatesFilter<"item">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"item"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"item"> | Date | string
  recipeUnlockId?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
  petUnlockId?: Prisma.IntNullableWithAggregatesFilter<"item"> | number | null
}

export type itemCreateInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateManyInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
}

export type itemUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type itemUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type ItemNullableScalarRelationFilter = {
  is?: Prisma.itemWhereInput | null
  isNot?: Prisma.itemWhereInput | null
}

export type ItemListRelationFilter = {
  every?: Prisma.itemWhereInput
  some?: Prisma.itemWhereInput
  none?: Prisma.itemWhereInput
}

export type itemOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type itemOrderByRelevanceInput = {
  fields: Prisma.itemOrderByRelevanceFieldEnum | Prisma.itemOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type itemCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  rarity?: Prisma.SortOrder
  level?: Prisma.SortOrder
  about?: Prisma.SortOrder
  cashValue?: Prisma.SortOrder
  image?: Prisma.SortOrder
  damage?: Prisma.SortOrder
  armour?: Prisma.SortOrder
  health?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  actionPoints?: Prisma.SortOrder
  baseAmmo?: Prisma.SortOrder
  itemEffects?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrder
  petUnlockId?: Prisma.SortOrder
}

export type itemAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  cashValue?: Prisma.SortOrder
  damage?: Prisma.SortOrder
  armour?: Prisma.SortOrder
  health?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  actionPoints?: Prisma.SortOrder
  baseAmmo?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrder
  petUnlockId?: Prisma.SortOrder
}

export type itemMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  rarity?: Prisma.SortOrder
  level?: Prisma.SortOrder
  about?: Prisma.SortOrder
  cashValue?: Prisma.SortOrder
  image?: Prisma.SortOrder
  damage?: Prisma.SortOrder
  armour?: Prisma.SortOrder
  health?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  actionPoints?: Prisma.SortOrder
  baseAmmo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrder
  petUnlockId?: Prisma.SortOrder
}

export type itemMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  itemType?: Prisma.SortOrder
  rarity?: Prisma.SortOrder
  level?: Prisma.SortOrder
  about?: Prisma.SortOrder
  cashValue?: Prisma.SortOrder
  image?: Prisma.SortOrder
  damage?: Prisma.SortOrder
  armour?: Prisma.SortOrder
  health?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  actionPoints?: Prisma.SortOrder
  baseAmmo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrder
  petUnlockId?: Prisma.SortOrder
}

export type itemSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  level?: Prisma.SortOrder
  cashValue?: Prisma.SortOrder
  damage?: Prisma.SortOrder
  armour?: Prisma.SortOrder
  health?: Prisma.SortOrder
  energy?: Prisma.SortOrder
  actionPoints?: Prisma.SortOrder
  baseAmmo?: Prisma.SortOrder
  recipeUnlockId?: Prisma.SortOrder
  petUnlockId?: Prisma.SortOrder
}

export type ItemScalarRelationFilter = {
  is?: Prisma.itemWhereInput
  isNot?: Prisma.itemWhereInput
}

export type itemCreateNestedOneWithoutAuction_itemInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutAuction_itemInput, Prisma.itemUncheckedCreateWithoutAuction_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutAuction_itemInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutAuction_itemNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutAuction_itemInput, Prisma.itemUncheckedCreateWithoutAuction_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutAuction_itemInput
  upsert?: Prisma.itemUpsertWithoutAuction_itemInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutAuction_itemInput, Prisma.itemUpdateWithoutAuction_itemInput>, Prisma.itemUncheckedUpdateWithoutAuction_itemInput>
}

export type itemCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.itemCreateWithoutCrafting_recipeInput[] | Prisma.itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.itemCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.itemCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
}

export type itemUncheckedCreateNestedManyWithoutCrafting_recipeInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.itemCreateWithoutCrafting_recipeInput[] | Prisma.itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.itemCreateOrConnectWithoutCrafting_recipeInput[]
  createMany?: Prisma.itemCreateManyCrafting_recipeInputEnvelope
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
}

export type itemUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.itemCreateWithoutCrafting_recipeInput[] | Prisma.itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.itemCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.itemUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.itemUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.itemCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  disconnect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  delete?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  update?: Prisma.itemUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.itemUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.itemUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.itemUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
}

export type itemUncheckedUpdateManyWithoutCrafting_recipeNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput> | Prisma.itemCreateWithoutCrafting_recipeInput[] | Prisma.itemUncheckedCreateWithoutCrafting_recipeInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutCrafting_recipeInput | Prisma.itemCreateOrConnectWithoutCrafting_recipeInput[]
  upsert?: Prisma.itemUpsertWithWhereUniqueWithoutCrafting_recipeInput | Prisma.itemUpsertWithWhereUniqueWithoutCrafting_recipeInput[]
  createMany?: Prisma.itemCreateManyCrafting_recipeInputEnvelope
  set?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  disconnect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  delete?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  update?: Prisma.itemUpdateWithWhereUniqueWithoutCrafting_recipeInput | Prisma.itemUpdateWithWhereUniqueWithoutCrafting_recipeInput[]
  updateMany?: Prisma.itemUpdateManyWithWhereWithoutCrafting_recipeInput | Prisma.itemUpdateManyWithWhereWithoutCrafting_recipeInput[]
  deleteMany?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
}

export type itemCreateNestedOneWithoutDaily_missionInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDaily_missionInput, Prisma.itemUncheckedCreateWithoutDaily_missionInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDaily_missionInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutDaily_missionNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDaily_missionInput, Prisma.itemUncheckedCreateWithoutDaily_missionInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDaily_missionInput
  upsert?: Prisma.itemUpsertWithoutDaily_missionInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutDaily_missionInput, Prisma.itemUpdateWithoutDaily_missionInput>, Prisma.itemUncheckedUpdateWithoutDaily_missionInput>
}

export type itemCreateNestedOneWithoutDrop_chanceInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDrop_chanceInput, Prisma.itemUncheckedCreateWithoutDrop_chanceInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDrop_chanceInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutDrop_chanceNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDrop_chanceInput, Prisma.itemUncheckedCreateWithoutDrop_chanceInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDrop_chanceInput
  upsert?: Prisma.itemUpsertWithoutDrop_chanceInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutDrop_chanceInput, Prisma.itemUpdateWithoutDrop_chanceInput>, Prisma.itemUncheckedUpdateWithoutDrop_chanceInput>
}

export type EnumItemTypesFieldUpdateOperationsInput = {
  set?: $Enums.ItemTypes
}

export type EnumItemRaritiesFieldUpdateOperationsInput = {
  set?: $Enums.ItemRarities
}

export type itemCreateNestedOneWithoutDaily_questInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDaily_questInput, Prisma.itemUncheckedCreateWithoutDaily_questInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDaily_questInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutDaily_questNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutDaily_questInput, Prisma.itemUncheckedCreateWithoutDaily_questInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutDaily_questInput
  upsert?: Prisma.itemUpsertWithoutDaily_questInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutDaily_questInput, Prisma.itemUpdateWithoutDaily_questInput>, Prisma.itemUncheckedUpdateWithoutDaily_questInput>
}

export type itemCreateNestedOneWithoutQuest_objectiveInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutQuest_objectiveInput, Prisma.itemUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutQuest_objectiveInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutQuest_objectiveNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutQuest_objectiveInput, Prisma.itemUncheckedCreateWithoutQuest_objectiveInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutQuest_objectiveInput
  upsert?: Prisma.itemUpsertWithoutQuest_objectiveInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutQuest_objectiveInput, Prisma.itemUpdateWithoutQuest_objectiveInput>, Prisma.itemUncheckedUpdateWithoutQuest_objectiveInput>
}

export type itemCreateNestedOneWithoutQuest_rewardInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutQuest_rewardInput, Prisma.itemUncheckedCreateWithoutQuest_rewardInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutQuest_rewardInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutQuest_rewardNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutQuest_rewardInput, Prisma.itemUncheckedCreateWithoutQuest_rewardInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutQuest_rewardInput
  upsert?: Prisma.itemUpsertWithoutQuest_rewardInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutQuest_rewardInput, Prisma.itemUpdateWithoutQuest_rewardInput>, Prisma.itemUncheckedUpdateWithoutQuest_rewardInput>
}

export type itemCreateNestedOneWithoutRecipe_itemInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutRecipe_itemInput, Prisma.itemUncheckedCreateWithoutRecipe_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutRecipe_itemInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneRequiredWithoutRecipe_itemNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutRecipe_itemInput, Prisma.itemUncheckedCreateWithoutRecipe_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutRecipe_itemInput
  upsert?: Prisma.itemUpsertWithoutRecipe_itemInput
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutRecipe_itemInput, Prisma.itemUpdateWithoutRecipe_itemInput>, Prisma.itemUncheckedUpdateWithoutRecipe_itemInput>
}

export type itemCreateNestedOneWithoutShop_listingInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutShop_listingInput, Prisma.itemUncheckedCreateWithoutShop_listingInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutShop_listingInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutShop_listingNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutShop_listingInput, Prisma.itemUncheckedCreateWithoutShop_listingInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutShop_listingInput
  upsert?: Prisma.itemUpsertWithoutShop_listingInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutShop_listingInput, Prisma.itemUpdateWithoutShop_listingInput>, Prisma.itemUncheckedUpdateWithoutShop_listingInput>
}

export type itemCreateNestedOneWithoutUser_itemInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutUser_itemInput, Prisma.itemUncheckedCreateWithoutUser_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutUser_itemInput
  connect?: Prisma.itemWhereUniqueInput
}

export type itemUpdateOneWithoutUser_itemNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutUser_itemInput, Prisma.itemUncheckedCreateWithoutUser_itemInput>
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutUser_itemInput
  upsert?: Prisma.itemUpsertWithoutUser_itemInput
  disconnect?: Prisma.itemWhereInput | boolean
  delete?: Prisma.itemWhereInput | boolean
  connect?: Prisma.itemWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.itemUpdateToOneWithWhereWithoutUser_itemInput, Prisma.itemUpdateWithoutUser_itemInput>, Prisma.itemUncheckedUpdateWithoutUser_itemInput>
}

export type itemCreateNestedManyWithoutPetInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput> | Prisma.itemCreateWithoutPetInput[] | Prisma.itemUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutPetInput | Prisma.itemCreateOrConnectWithoutPetInput[]
  createMany?: Prisma.itemCreateManyPetInputEnvelope
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
}

export type itemUncheckedCreateNestedManyWithoutPetInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput> | Prisma.itemCreateWithoutPetInput[] | Prisma.itemUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutPetInput | Prisma.itemCreateOrConnectWithoutPetInput[]
  createMany?: Prisma.itemCreateManyPetInputEnvelope
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
}

export type itemUpdateManyWithoutPetNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput> | Prisma.itemCreateWithoutPetInput[] | Prisma.itemUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutPetInput | Prisma.itemCreateOrConnectWithoutPetInput[]
  upsert?: Prisma.itemUpsertWithWhereUniqueWithoutPetInput | Prisma.itemUpsertWithWhereUniqueWithoutPetInput[]
  createMany?: Prisma.itemCreateManyPetInputEnvelope
  set?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  disconnect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  delete?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  update?: Prisma.itemUpdateWithWhereUniqueWithoutPetInput | Prisma.itemUpdateWithWhereUniqueWithoutPetInput[]
  updateMany?: Prisma.itemUpdateManyWithWhereWithoutPetInput | Prisma.itemUpdateManyWithWhereWithoutPetInput[]
  deleteMany?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
}

export type itemUncheckedUpdateManyWithoutPetNestedInput = {
  create?: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput> | Prisma.itemCreateWithoutPetInput[] | Prisma.itemUncheckedCreateWithoutPetInput[]
  connectOrCreate?: Prisma.itemCreateOrConnectWithoutPetInput | Prisma.itemCreateOrConnectWithoutPetInput[]
  upsert?: Prisma.itemUpsertWithWhereUniqueWithoutPetInput | Prisma.itemUpsertWithWhereUniqueWithoutPetInput[]
  createMany?: Prisma.itemCreateManyPetInputEnvelope
  set?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  disconnect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  delete?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  connect?: Prisma.itemWhereUniqueInput | Prisma.itemWhereUniqueInput[]
  update?: Prisma.itemUpdateWithWhereUniqueWithoutPetInput | Prisma.itemUpdateWithWhereUniqueWithoutPetInput[]
  updateMany?: Prisma.itemUpdateManyWithWhereWithoutPetInput | Prisma.itemUpdateManyWithWhereWithoutPetInput[]
  deleteMany?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
}

export type itemCreateWithoutAuction_itemInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutAuction_itemInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutAuction_itemInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutAuction_itemInput, Prisma.itemUncheckedCreateWithoutAuction_itemInput>
}

export type itemUpsertWithoutAuction_itemInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutAuction_itemInput, Prisma.itemUncheckedUpdateWithoutAuction_itemInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutAuction_itemInput, Prisma.itemUncheckedCreateWithoutAuction_itemInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutAuction_itemInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutAuction_itemInput, Prisma.itemUncheckedUpdateWithoutAuction_itemInput>
}

export type itemUpdateWithoutAuction_itemInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutAuction_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutCrafting_recipeInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutCrafting_recipeInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutCrafting_recipeInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput>
}

export type itemCreateManyCrafting_recipeInputEnvelope = {
  data: Prisma.itemCreateManyCrafting_recipeInput | Prisma.itemCreateManyCrafting_recipeInput[]
  skipDuplicates?: boolean
}

export type itemUpsertWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.itemWhereUniqueInput
  update: Prisma.XOR<Prisma.itemUpdateWithoutCrafting_recipeInput, Prisma.itemUncheckedUpdateWithoutCrafting_recipeInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutCrafting_recipeInput, Prisma.itemUncheckedCreateWithoutCrafting_recipeInput>
}

export type itemUpdateWithWhereUniqueWithoutCrafting_recipeInput = {
  where: Prisma.itemWhereUniqueInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutCrafting_recipeInput, Prisma.itemUncheckedUpdateWithoutCrafting_recipeInput>
}

export type itemUpdateManyWithWhereWithoutCrafting_recipeInput = {
  where: Prisma.itemScalarWhereInput
  data: Prisma.XOR<Prisma.itemUpdateManyMutationInput, Prisma.itemUncheckedUpdateManyWithoutCrafting_recipeInput>
}

export type itemScalarWhereInput = {
  AND?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
  OR?: Prisma.itemScalarWhereInput[]
  NOT?: Prisma.itemScalarWhereInput | Prisma.itemScalarWhereInput[]
  id?: Prisma.IntFilter<"item"> | number
  name?: Prisma.StringFilter<"item"> | string
  itemType?: Prisma.EnumItemTypesFilter<"item"> | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFilter<"item"> | $Enums.ItemRarities
  level?: Prisma.IntFilter<"item"> | number
  about?: Prisma.StringNullableFilter<"item"> | string | null
  cashValue?: Prisma.IntNullableFilter<"item"> | number | null
  image?: Prisma.StringNullableFilter<"item"> | string | null
  damage?: Prisma.IntNullableFilter<"item"> | number | null
  armour?: Prisma.IntNullableFilter<"item"> | number | null
  health?: Prisma.IntNullableFilter<"item"> | number | null
  energy?: Prisma.IntNullableFilter<"item"> | number | null
  actionPoints?: Prisma.IntNullableFilter<"item"> | number | null
  baseAmmo?: Prisma.IntNullableFilter<"item"> | number | null
  itemEffects?: Prisma.JsonNullableFilter<"item">
  createdAt?: Prisma.DateTimeFilter<"item"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"item"> | Date | string
  recipeUnlockId?: Prisma.IntNullableFilter<"item"> | number | null
  petUnlockId?: Prisma.IntNullableFilter<"item"> | number | null
}

export type itemCreateWithoutDaily_missionInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutDaily_missionInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutDaily_missionInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutDaily_missionInput, Prisma.itemUncheckedCreateWithoutDaily_missionInput>
}

export type itemUpsertWithoutDaily_missionInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutDaily_missionInput, Prisma.itemUncheckedUpdateWithoutDaily_missionInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutDaily_missionInput, Prisma.itemUncheckedCreateWithoutDaily_missionInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutDaily_missionInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutDaily_missionInput, Prisma.itemUncheckedUpdateWithoutDaily_missionInput>
}

export type itemUpdateWithoutDaily_missionInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutDaily_missionInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutDrop_chanceInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutDrop_chanceInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutDrop_chanceInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutDrop_chanceInput, Prisma.itemUncheckedCreateWithoutDrop_chanceInput>
}

export type itemUpsertWithoutDrop_chanceInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutDrop_chanceInput, Prisma.itemUncheckedUpdateWithoutDrop_chanceInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutDrop_chanceInput, Prisma.itemUncheckedCreateWithoutDrop_chanceInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutDrop_chanceInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutDrop_chanceInput, Prisma.itemUncheckedUpdateWithoutDrop_chanceInput>
}

export type itemUpdateWithoutDrop_chanceInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutDrop_chanceInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutDaily_questInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutDaily_questInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutDaily_questInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutDaily_questInput, Prisma.itemUncheckedCreateWithoutDaily_questInput>
}

export type itemUpsertWithoutDaily_questInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutDaily_questInput, Prisma.itemUncheckedUpdateWithoutDaily_questInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutDaily_questInput, Prisma.itemUncheckedCreateWithoutDaily_questInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutDaily_questInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutDaily_questInput, Prisma.itemUncheckedUpdateWithoutDaily_questInput>
}

export type itemUpdateWithoutDaily_questInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutDaily_questInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutQuest_objectiveInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutQuest_objectiveInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutQuest_objectiveInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutQuest_objectiveInput, Prisma.itemUncheckedCreateWithoutQuest_objectiveInput>
}

export type itemUpsertWithoutQuest_objectiveInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutQuest_objectiveInput, Prisma.itemUncheckedUpdateWithoutQuest_objectiveInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutQuest_objectiveInput, Prisma.itemUncheckedCreateWithoutQuest_objectiveInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutQuest_objectiveInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutQuest_objectiveInput, Prisma.itemUncheckedUpdateWithoutQuest_objectiveInput>
}

export type itemUpdateWithoutQuest_objectiveInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutQuest_objectiveInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutQuest_rewardInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutQuest_rewardInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutQuest_rewardInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutQuest_rewardInput, Prisma.itemUncheckedCreateWithoutQuest_rewardInput>
}

export type itemUpsertWithoutQuest_rewardInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutQuest_rewardInput, Prisma.itemUncheckedUpdateWithoutQuest_rewardInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutQuest_rewardInput, Prisma.itemUncheckedCreateWithoutQuest_rewardInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutQuest_rewardInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutQuest_rewardInput, Prisma.itemUncheckedUpdateWithoutQuest_rewardInput>
}

export type itemUpdateWithoutQuest_rewardInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutQuest_rewardInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutRecipe_itemInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutRecipe_itemInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutRecipe_itemInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutRecipe_itemInput, Prisma.itemUncheckedCreateWithoutRecipe_itemInput>
}

export type itemUpsertWithoutRecipe_itemInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutRecipe_itemInput, Prisma.itemUncheckedUpdateWithoutRecipe_itemInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutRecipe_itemInput, Prisma.itemUncheckedCreateWithoutRecipe_itemInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutRecipe_itemInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutRecipe_itemInput, Prisma.itemUncheckedUpdateWithoutRecipe_itemInput>
}

export type itemUpdateWithoutRecipe_itemInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutRecipe_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutShop_listingInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutShop_listingInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutShop_listingInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutShop_listingInput, Prisma.itemUncheckedCreateWithoutShop_listingInput>
}

export type itemUpsertWithoutShop_listingInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutShop_listingInput, Prisma.itemUncheckedUpdateWithoutShop_listingInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutShop_listingInput, Prisma.itemUncheckedCreateWithoutShop_listingInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutShop_listingInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutShop_listingInput, Prisma.itemUncheckedUpdateWithoutShop_listingInput>
}

export type itemUpdateWithoutShop_listingInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutShop_listingInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutUser_itemInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  pet?: Prisma.petCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutUser_itemInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  petUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutUser_itemInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutUser_itemInput, Prisma.itemUncheckedCreateWithoutUser_itemInput>
}

export type itemUpsertWithoutUser_itemInput = {
  update: Prisma.XOR<Prisma.itemUpdateWithoutUser_itemInput, Prisma.itemUncheckedUpdateWithoutUser_itemInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutUser_itemInput, Prisma.itemUncheckedCreateWithoutUser_itemInput>
  where?: Prisma.itemWhereInput
}

export type itemUpdateToOneWithWhereWithoutUser_itemInput = {
  where?: Prisma.itemWhereInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutUser_itemInput, Prisma.itemUncheckedUpdateWithoutUser_itemInput>
}

export type itemUpdateWithoutUser_itemInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutUser_itemInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemCreateWithoutPetInput = {
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  auction_item?: Prisma.auction_itemCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceCreateNestedManyWithoutItemInput
  crafting_recipe?: Prisma.crafting_recipeCreateNestedOneWithoutItemInput
  recipe_item?: Prisma.recipe_itemCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveCreateNestedManyWithoutItemInput
}

export type itemUncheckedCreateWithoutPetInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
  auction_item?: Prisma.auction_itemUncheckedCreateNestedManyWithoutItemInput
  daily_mission?: Prisma.daily_missionUncheckedCreateNestedManyWithoutItemInput
  drop_chance?: Prisma.drop_chanceUncheckedCreateNestedManyWithoutItemInput
  recipe_item?: Prisma.recipe_itemUncheckedCreateNestedManyWithoutItemInput
  shop_listing?: Prisma.shop_listingUncheckedCreateNestedManyWithoutItemInput
  user_item?: Prisma.user_itemUncheckedCreateNestedManyWithoutItemInput
  quest_reward?: Prisma.quest_rewardUncheckedCreateNestedManyWithoutItemInput
  daily_quest?: Prisma.daily_questUncheckedCreateNestedManyWithoutItemInput
  quest_objective?: Prisma.quest_objectiveUncheckedCreateNestedManyWithoutItemInput
}

export type itemCreateOrConnectWithoutPetInput = {
  where: Prisma.itemWhereUniqueInput
  create: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput>
}

export type itemCreateManyPetInputEnvelope = {
  data: Prisma.itemCreateManyPetInput | Prisma.itemCreateManyPetInput[]
  skipDuplicates?: boolean
}

export type itemUpsertWithWhereUniqueWithoutPetInput = {
  where: Prisma.itemWhereUniqueInput
  update: Prisma.XOR<Prisma.itemUpdateWithoutPetInput, Prisma.itemUncheckedUpdateWithoutPetInput>
  create: Prisma.XOR<Prisma.itemCreateWithoutPetInput, Prisma.itemUncheckedCreateWithoutPetInput>
}

export type itemUpdateWithWhereUniqueWithoutPetInput = {
  where: Prisma.itemWhereUniqueInput
  data: Prisma.XOR<Prisma.itemUpdateWithoutPetInput, Prisma.itemUncheckedUpdateWithoutPetInput>
}

export type itemUpdateManyWithWhereWithoutPetInput = {
  where: Prisma.itemScalarWhereInput
  data: Prisma.XOR<Prisma.itemUpdateManyMutationInput, Prisma.itemUncheckedUpdateManyWithoutPetInput>
}

export type itemCreateManyCrafting_recipeInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  petUnlockId?: number | null
}

export type itemUpdateWithoutCrafting_recipeInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  pet?: Prisma.petUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutCrafting_recipeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateManyWithoutCrafting_recipeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  petUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}

export type itemCreateManyPetInput = {
  id?: number
  name: string
  itemType: $Enums.ItemTypes
  rarity: $Enums.ItemRarities
  level?: number
  about?: string | null
  cashValue?: number | null
  image?: string | null
  damage?: number | null
  armour?: number | null
  health?: number | null
  energy?: number | null
  actionPoints?: number | null
  baseAmmo?: number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Date | string
  updatedAt?: Date | string
  recipeUnlockId?: number | null
}

export type itemUpdateWithoutPetInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  auction_item?: Prisma.auction_itemUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUpdateManyWithoutItemNestedInput
  crafting_recipe?: Prisma.crafting_recipeUpdateOneWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateWithoutPetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  auction_item?: Prisma.auction_itemUncheckedUpdateManyWithoutItemNestedInput
  daily_mission?: Prisma.daily_missionUncheckedUpdateManyWithoutItemNestedInput
  drop_chance?: Prisma.drop_chanceUncheckedUpdateManyWithoutItemNestedInput
  recipe_item?: Prisma.recipe_itemUncheckedUpdateManyWithoutItemNestedInput
  shop_listing?: Prisma.shop_listingUncheckedUpdateManyWithoutItemNestedInput
  user_item?: Prisma.user_itemUncheckedUpdateManyWithoutItemNestedInput
  quest_reward?: Prisma.quest_rewardUncheckedUpdateManyWithoutItemNestedInput
  daily_quest?: Prisma.daily_questUncheckedUpdateManyWithoutItemNestedInput
  quest_objective?: Prisma.quest_objectiveUncheckedUpdateManyWithoutItemNestedInput
}

export type itemUncheckedUpdateManyWithoutPetInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  itemType?: Prisma.EnumItemTypesFieldUpdateOperationsInput | $Enums.ItemTypes
  rarity?: Prisma.EnumItemRaritiesFieldUpdateOperationsInput | $Enums.ItemRarities
  level?: Prisma.IntFieldUpdateOperationsInput | number
  about?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cashValue?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  damage?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  armour?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  health?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  energy?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  actionPoints?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  baseAmmo?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  itemEffects?:PrismaJson.ItemEffects | Prisma.NullableJsonNullValueInput
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipeUnlockId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
}


/**
 * Count Type ItemCountOutputType
 */

export type ItemCountOutputType = {
  auction_item: number
  daily_mission: number
  drop_chance: number
  recipe_item: number
  shop_listing: number
  user_item: number
  quest_reward: number
  daily_quest: number
  quest_objective: number
}

export type ItemCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  auction_item?: boolean | ItemCountOutputTypeCountAuction_itemArgs
  daily_mission?: boolean | ItemCountOutputTypeCountDaily_missionArgs
  drop_chance?: boolean | ItemCountOutputTypeCountDrop_chanceArgs
  recipe_item?: boolean | ItemCountOutputTypeCountRecipe_itemArgs
  shop_listing?: boolean | ItemCountOutputTypeCountShop_listingArgs
  user_item?: boolean | ItemCountOutputTypeCountUser_itemArgs
  quest_reward?: boolean | ItemCountOutputTypeCountQuest_rewardArgs
  daily_quest?: boolean | ItemCountOutputTypeCountDaily_questArgs
  quest_objective?: boolean | ItemCountOutputTypeCountQuest_objectiveArgs
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ItemCountOutputType
   */
  select?: Prisma.ItemCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountAuction_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.auction_itemWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountDaily_missionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.daily_missionWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountDrop_chanceArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.drop_chanceWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountRecipe_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.recipe_itemWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountShop_listingArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.shop_listingWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountUser_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.user_itemWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountQuest_rewardArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_rewardWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountDaily_questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.daily_questWhereInput
}

/**
 * ItemCountOutputType without action
 */
export type ItemCountOutputTypeCountQuest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.quest_objectiveWhereInput
}


export type itemSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  itemType?: boolean
  rarity?: boolean
  level?: boolean
  about?: boolean
  cashValue?: boolean
  image?: boolean
  damage?: boolean
  armour?: boolean
  health?: boolean
  energy?: boolean
  actionPoints?: boolean
  baseAmmo?: boolean
  itemEffects?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipeUnlockId?: boolean
  petUnlockId?: boolean
  auction_item?: boolean | Prisma.item$auction_itemArgs<ExtArgs>
  daily_mission?: boolean | Prisma.item$daily_missionArgs<ExtArgs>
  drop_chance?: boolean | Prisma.item$drop_chanceArgs<ExtArgs>
  crafting_recipe?: boolean | Prisma.item$crafting_recipeArgs<ExtArgs>
  pet?: boolean | Prisma.item$petArgs<ExtArgs>
  recipe_item?: boolean | Prisma.item$recipe_itemArgs<ExtArgs>
  shop_listing?: boolean | Prisma.item$shop_listingArgs<ExtArgs>
  user_item?: boolean | Prisma.item$user_itemArgs<ExtArgs>
  quest_reward?: boolean | Prisma.item$quest_rewardArgs<ExtArgs>
  daily_quest?: boolean | Prisma.item$daily_questArgs<ExtArgs>
  quest_objective?: boolean | Prisma.item$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.ItemCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["item"]>



export type itemSelectScalar = {
  id?: boolean
  name?: boolean
  itemType?: boolean
  rarity?: boolean
  level?: boolean
  about?: boolean
  cashValue?: boolean
  image?: boolean
  damage?: boolean
  armour?: boolean
  health?: boolean
  energy?: boolean
  actionPoints?: boolean
  baseAmmo?: boolean
  itemEffects?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  recipeUnlockId?: boolean
  petUnlockId?: boolean
}

export type itemOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "itemType" | "rarity" | "level" | "about" | "cashValue" | "image" | "damage" | "armour" | "health" | "energy" | "actionPoints" | "baseAmmo" | "itemEffects" | "createdAt" | "updatedAt" | "recipeUnlockId" | "petUnlockId", ExtArgs["result"]["item"]>
export type itemInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  auction_item?: boolean | Prisma.item$auction_itemArgs<ExtArgs>
  daily_mission?: boolean | Prisma.item$daily_missionArgs<ExtArgs>
  drop_chance?: boolean | Prisma.item$drop_chanceArgs<ExtArgs>
  crafting_recipe?: boolean | Prisma.item$crafting_recipeArgs<ExtArgs>
  pet?: boolean | Prisma.item$petArgs<ExtArgs>
  recipe_item?: boolean | Prisma.item$recipe_itemArgs<ExtArgs>
  shop_listing?: boolean | Prisma.item$shop_listingArgs<ExtArgs>
  user_item?: boolean | Prisma.item$user_itemArgs<ExtArgs>
  quest_reward?: boolean | Prisma.item$quest_rewardArgs<ExtArgs>
  daily_quest?: boolean | Prisma.item$daily_questArgs<ExtArgs>
  quest_objective?: boolean | Prisma.item$quest_objectiveArgs<ExtArgs>
  _count?: boolean | Prisma.ItemCountOutputTypeDefaultArgs<ExtArgs>
}

export type $itemPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "item"
  objects: {
    auction_item: Prisma.$auction_itemPayload<ExtArgs>[]
    daily_mission: Prisma.$daily_missionPayload<ExtArgs>[]
    drop_chance: Prisma.$drop_chancePayload<ExtArgs>[]
    crafting_recipe: Prisma.$crafting_recipePayload<ExtArgs> | null
    pet: Prisma.$petPayload<ExtArgs> | null
    recipe_item: Prisma.$recipe_itemPayload<ExtArgs>[]
    shop_listing: Prisma.$shop_listingPayload<ExtArgs>[]
    user_item: Prisma.$user_itemPayload<ExtArgs>[]
    quest_reward: Prisma.$quest_rewardPayload<ExtArgs>[]
    daily_quest: Prisma.$daily_questPayload<ExtArgs>[]
    quest_objective: Prisma.$quest_objectivePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    name: string
    itemType: $Enums.ItemTypes
    rarity: $Enums.ItemRarities
    level: number
    about: string | null
    cashValue: number | null
    image: string | null
    damage: number | null
    armour: number | null
    health: number | null
    energy: number | null
    actionPoints: number | null
    baseAmmo: number | null
    /**
     * [ItemEffects]
     */
    itemEffects:PrismaJson.ItemEffects | null
    createdAt: Date
    updatedAt: Date
    recipeUnlockId: number | null
    petUnlockId: number | null
  }, ExtArgs["result"]["item"]>
  composites: {}
}

export type itemGetPayload<S extends boolean | null | undefined | itemDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$itemPayload, S>

export type itemCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<itemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ItemCountAggregateInputType | true
  }

export interface itemDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['item'], meta: { name: 'item' } }
  /**
   * Find zero or one Item that matches the filter.
   * @param {itemFindUniqueArgs} args - Arguments to find a Item
   * @example
   * // Get one Item
   * const item = await prisma.item.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends itemFindUniqueArgs>(args: Prisma.SelectSubset<T, itemFindUniqueArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Item that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {itemFindUniqueOrThrowArgs} args - Arguments to find a Item
   * @example
   * // Get one Item
   * const item = await prisma.item.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends itemFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, itemFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Item that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemFindFirstArgs} args - Arguments to find a Item
   * @example
   * // Get one Item
   * const item = await prisma.item.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends itemFindFirstArgs>(args?: Prisma.SelectSubset<T, itemFindFirstArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Item that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemFindFirstOrThrowArgs} args - Arguments to find a Item
   * @example
   * // Get one Item
   * const item = await prisma.item.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends itemFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, itemFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Items that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Items
   * const items = await prisma.item.findMany()
   * 
   * // Get first 10 Items
   * const items = await prisma.item.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const itemWithIdOnly = await prisma.item.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends itemFindManyArgs>(args?: Prisma.SelectSubset<T, itemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Item.
   * @param {itemCreateArgs} args - Arguments to create a Item.
   * @example
   * // Create one Item
   * const Item = await prisma.item.create({
   *   data: {
   *     // ... data to create a Item
   *   }
   * })
   * 
   */
  create<T extends itemCreateArgs>(args: Prisma.SelectSubset<T, itemCreateArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Items.
   * @param {itemCreateManyArgs} args - Arguments to create many Items.
   * @example
   * // Create many Items
   * const item = await prisma.item.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends itemCreateManyArgs>(args?: Prisma.SelectSubset<T, itemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Item.
   * @param {itemDeleteArgs} args - Arguments to delete one Item.
   * @example
   * // Delete one Item
   * const Item = await prisma.item.delete({
   *   where: {
   *     // ... filter to delete one Item
   *   }
   * })
   * 
   */
  delete<T extends itemDeleteArgs>(args: Prisma.SelectSubset<T, itemDeleteArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Item.
   * @param {itemUpdateArgs} args - Arguments to update one Item.
   * @example
   * // Update one Item
   * const item = await prisma.item.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends itemUpdateArgs>(args: Prisma.SelectSubset<T, itemUpdateArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Items.
   * @param {itemDeleteManyArgs} args - Arguments to filter Items to delete.
   * @example
   * // Delete a few Items
   * const { count } = await prisma.item.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends itemDeleteManyArgs>(args?: Prisma.SelectSubset<T, itemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Items
   * const item = await prisma.item.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends itemUpdateManyArgs>(args: Prisma.SelectSubset<T, itemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Item.
   * @param {itemUpsertArgs} args - Arguments to update or create a Item.
   * @example
   * // Update or create a Item
   * const item = await prisma.item.upsert({
   *   create: {
   *     // ... data to create a Item
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Item we want to update
   *   }
   * })
   */
  upsert<T extends itemUpsertArgs>(args: Prisma.SelectSubset<T, itemUpsertArgs<ExtArgs>>): Prisma.Prisma__itemClient<runtime.Types.Result.GetResult<Prisma.$itemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Items.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemCountArgs} args - Arguments to filter Items to count.
   * @example
   * // Count the number of Items
   * const count = await prisma.item.count({
   *   where: {
   *     // ... the filter for the Items we want to count
   *   }
   * })
  **/
  count<T extends itemCountArgs>(
    args?: Prisma.Subset<T, itemCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ItemCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ItemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ItemAggregateArgs>(args: Prisma.Subset<T, ItemAggregateArgs>): Prisma.PrismaPromise<GetItemAggregateType<T>>

  /**
   * Group by Item.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {itemGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends itemGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: itemGroupByArgs['orderBy'] }
      : { orderBy?: itemGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, itemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetItemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the item model
 */
readonly fields: itemFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for item.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__itemClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  auction_item<T extends Prisma.item$auction_itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$auction_itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$auction_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  daily_mission<T extends Prisma.item$daily_missionArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$daily_missionArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$daily_missionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  drop_chance<T extends Prisma.item$drop_chanceArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$drop_chanceArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$drop_chancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  crafting_recipe<T extends Prisma.item$crafting_recipeArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$crafting_recipeArgs<ExtArgs>>): Prisma.Prisma__crafting_recipeClient<runtime.Types.Result.GetResult<Prisma.$crafting_recipePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  pet<T extends Prisma.item$petArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$petArgs<ExtArgs>>): Prisma.Prisma__petClient<runtime.Types.Result.GetResult<Prisma.$petPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  recipe_item<T extends Prisma.item$recipe_itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$recipe_itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$recipe_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  shop_listing<T extends Prisma.item$shop_listingArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$shop_listingArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$shop_listingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user_item<T extends Prisma.item$user_itemArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$user_itemArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$user_itemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  quest_reward<T extends Prisma.item$quest_rewardArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$quest_rewardArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_rewardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  daily_quest<T extends Prisma.item$daily_questArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$daily_questArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$daily_questPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  quest_objective<T extends Prisma.item$quest_objectiveArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.item$quest_objectiveArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$quest_objectivePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the item model
 */
export interface itemFieldRefs {
  readonly id: Prisma.FieldRef<"item", 'Int'>
  readonly name: Prisma.FieldRef<"item", 'String'>
  readonly itemType: Prisma.FieldRef<"item", 'ItemTypes'>
  readonly rarity: Prisma.FieldRef<"item", 'ItemRarities'>
  readonly level: Prisma.FieldRef<"item", 'Int'>
  readonly about: Prisma.FieldRef<"item", 'String'>
  readonly cashValue: Prisma.FieldRef<"item", 'Int'>
  readonly image: Prisma.FieldRef<"item", 'String'>
  readonly damage: Prisma.FieldRef<"item", 'Int'>
  readonly armour: Prisma.FieldRef<"item", 'Int'>
  readonly health: Prisma.FieldRef<"item", 'Int'>
  readonly energy: Prisma.FieldRef<"item", 'Int'>
  readonly actionPoints: Prisma.FieldRef<"item", 'Int'>
  readonly baseAmmo: Prisma.FieldRef<"item", 'Int'>
  readonly itemEffects: Prisma.FieldRef<"item", 'Json'>
  readonly createdAt: Prisma.FieldRef<"item", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"item", 'DateTime'>
  readonly recipeUnlockId: Prisma.FieldRef<"item", 'Int'>
  readonly petUnlockId: Prisma.FieldRef<"item", 'Int'>
}
    

// Custom InputTypes
/**
 * item findUnique
 */
export type itemFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter, which item to fetch.
   */
  where: Prisma.itemWhereUniqueInput
}

/**
 * item findUniqueOrThrow
 */
export type itemFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter, which item to fetch.
   */
  where: Prisma.itemWhereUniqueInput
}

/**
 * item findFirst
 */
export type itemFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter, which item to fetch.
   */
  where?: Prisma.itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of items to fetch.
   */
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for items.
   */
  cursor?: Prisma.itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of items.
   */
  distinct?: Prisma.ItemScalarFieldEnum | Prisma.ItemScalarFieldEnum[]
}

/**
 * item findFirstOrThrow
 */
export type itemFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter, which item to fetch.
   */
  where?: Prisma.itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of items to fetch.
   */
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for items.
   */
  cursor?: Prisma.itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` items.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of items.
   */
  distinct?: Prisma.ItemScalarFieldEnum | Prisma.ItemScalarFieldEnum[]
}

/**
 * item findMany
 */
export type itemFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter, which items to fetch.
   */
  where?: Prisma.itemWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of items to fetch.
   */
  orderBy?: Prisma.itemOrderByWithRelationInput | Prisma.itemOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing items.
   */
  cursor?: Prisma.itemWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` items from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` items.
   */
  skip?: number
  distinct?: Prisma.ItemScalarFieldEnum | Prisma.ItemScalarFieldEnum[]
}

/**
 * item create
 */
export type itemCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * The data needed to create a item.
   */
  data: Prisma.XOR<Prisma.itemCreateInput, Prisma.itemUncheckedCreateInput>
}

/**
 * item createMany
 */
export type itemCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many items.
   */
  data: Prisma.itemCreateManyInput | Prisma.itemCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * item update
 */
export type itemUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * The data needed to update a item.
   */
  data: Prisma.XOR<Prisma.itemUpdateInput, Prisma.itemUncheckedUpdateInput>
  /**
   * Choose, which item to update.
   */
  where: Prisma.itemWhereUniqueInput
}

/**
 * item updateMany
 */
export type itemUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update items.
   */
  data: Prisma.XOR<Prisma.itemUpdateManyMutationInput, Prisma.itemUncheckedUpdateManyInput>
  /**
   * Filter which items to update
   */
  where?: Prisma.itemWhereInput
  /**
   * Limit how many items to update.
   */
  limit?: number
}

/**
 * item upsert
 */
export type itemUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * The filter to search for the item to update in case it exists.
   */
  where: Prisma.itemWhereUniqueInput
  /**
   * In case the item found by the `where` argument doesn't exist, create a new item with this data.
   */
  create: Prisma.XOR<Prisma.itemCreateInput, Prisma.itemUncheckedCreateInput>
  /**
   * In case the item was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.itemUpdateInput, Prisma.itemUncheckedUpdateInput>
}

/**
 * item delete
 */
export type itemDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
  /**
   * Filter which item to delete.
   */
  where: Prisma.itemWhereUniqueInput
}

/**
 * item deleteMany
 */
export type itemDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which items to delete
   */
  where?: Prisma.itemWhereInput
  /**
   * Limit how many items to delete.
   */
  limit?: number
}

/**
 * item.auction_item
 */
export type item$auction_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the auction_item
   */
  select?: Prisma.auction_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the auction_item
   */
  omit?: Prisma.auction_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.auction_itemInclude<ExtArgs> | null
  where?: Prisma.auction_itemWhereInput
  orderBy?: Prisma.auction_itemOrderByWithRelationInput | Prisma.auction_itemOrderByWithRelationInput[]
  cursor?: Prisma.auction_itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Auction_itemScalarFieldEnum | Prisma.Auction_itemScalarFieldEnum[]
}

/**
 * item.daily_mission
 */
export type item$daily_missionArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_mission
   */
  select?: Prisma.daily_missionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_mission
   */
  omit?: Prisma.daily_missionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_missionInclude<ExtArgs> | null
  where?: Prisma.daily_missionWhereInput
  orderBy?: Prisma.daily_missionOrderByWithRelationInput | Prisma.daily_missionOrderByWithRelationInput[]
  cursor?: Prisma.daily_missionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Daily_missionScalarFieldEnum | Prisma.Daily_missionScalarFieldEnum[]
}

/**
 * item.drop_chance
 */
export type item$drop_chanceArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the drop_chance
   */
  select?: Prisma.drop_chanceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the drop_chance
   */
  omit?: Prisma.drop_chanceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.drop_chanceInclude<ExtArgs> | null
  where?: Prisma.drop_chanceWhereInput
  orderBy?: Prisma.drop_chanceOrderByWithRelationInput | Prisma.drop_chanceOrderByWithRelationInput[]
  cursor?: Prisma.drop_chanceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Drop_chanceScalarFieldEnum | Prisma.Drop_chanceScalarFieldEnum[]
}

/**
 * item.crafting_recipe
 */
export type item$crafting_recipeArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the crafting_recipe
   */
  select?: Prisma.crafting_recipeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the crafting_recipe
   */
  omit?: Prisma.crafting_recipeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.crafting_recipeInclude<ExtArgs> | null
  where?: Prisma.crafting_recipeWhereInput
}

/**
 * item.pet
 */
export type item$petArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the pet
   */
  select?: Prisma.petSelect<ExtArgs> | null
  /**
   * Omit specific fields from the pet
   */
  omit?: Prisma.petOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.petInclude<ExtArgs> | null
  where?: Prisma.petWhereInput
}

/**
 * item.recipe_item
 */
export type item$recipe_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the recipe_item
   */
  select?: Prisma.recipe_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the recipe_item
   */
  omit?: Prisma.recipe_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.recipe_itemInclude<ExtArgs> | null
  where?: Prisma.recipe_itemWhereInput
  orderBy?: Prisma.recipe_itemOrderByWithRelationInput | Prisma.recipe_itemOrderByWithRelationInput[]
  cursor?: Prisma.recipe_itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Recipe_itemScalarFieldEnum | Prisma.Recipe_itemScalarFieldEnum[]
}

/**
 * item.shop_listing
 */
export type item$shop_listingArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the shop_listing
   */
  select?: Prisma.shop_listingSelect<ExtArgs> | null
  /**
   * Omit specific fields from the shop_listing
   */
  omit?: Prisma.shop_listingOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.shop_listingInclude<ExtArgs> | null
  where?: Prisma.shop_listingWhereInput
  orderBy?: Prisma.shop_listingOrderByWithRelationInput | Prisma.shop_listingOrderByWithRelationInput[]
  cursor?: Prisma.shop_listingWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Shop_listingScalarFieldEnum | Prisma.Shop_listingScalarFieldEnum[]
}

/**
 * item.user_item
 */
export type item$user_itemArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the user_item
   */
  select?: Prisma.user_itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the user_item
   */
  omit?: Prisma.user_itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.user_itemInclude<ExtArgs> | null
  where?: Prisma.user_itemWhereInput
  orderBy?: Prisma.user_itemOrderByWithRelationInput | Prisma.user_itemOrderByWithRelationInput[]
  cursor?: Prisma.user_itemWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.User_itemScalarFieldEnum | Prisma.User_itemScalarFieldEnum[]
}

/**
 * item.quest_reward
 */
export type item$quest_rewardArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_reward
   */
  select?: Prisma.quest_rewardSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_reward
   */
  omit?: Prisma.quest_rewardOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_rewardInclude<ExtArgs> | null
  where?: Prisma.quest_rewardWhereInput
  orderBy?: Prisma.quest_rewardOrderByWithRelationInput | Prisma.quest_rewardOrderByWithRelationInput[]
  cursor?: Prisma.quest_rewardWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_rewardScalarFieldEnum | Prisma.Quest_rewardScalarFieldEnum[]
}

/**
 * item.daily_quest
 */
export type item$daily_questArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the daily_quest
   */
  select?: Prisma.daily_questSelect<ExtArgs> | null
  /**
   * Omit specific fields from the daily_quest
   */
  omit?: Prisma.daily_questOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.daily_questInclude<ExtArgs> | null
  where?: Prisma.daily_questWhereInput
  orderBy?: Prisma.daily_questOrderByWithRelationInput | Prisma.daily_questOrderByWithRelationInput[]
  cursor?: Prisma.daily_questWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Daily_questScalarFieldEnum | Prisma.Daily_questScalarFieldEnum[]
}

/**
 * item.quest_objective
 */
export type item$quest_objectiveArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the quest_objective
   */
  select?: Prisma.quest_objectiveSelect<ExtArgs> | null
  /**
   * Omit specific fields from the quest_objective
   */
  omit?: Prisma.quest_objectiveOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.quest_objectiveInclude<ExtArgs> | null
  where?: Prisma.quest_objectiveWhereInput
  orderBy?: Prisma.quest_objectiveOrderByWithRelationInput | Prisma.quest_objectiveOrderByWithRelationInput[]
  cursor?: Prisma.quest_objectiveWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.Quest_objectiveScalarFieldEnum | Prisma.Quest_objectiveScalarFieldEnum[]
}

/**
 * item without action
 */
export type itemDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the item
   */
  select?: Prisma.itemSelect<ExtArgs> | null
  /**
   * Omit specific fields from the item
   */
  omit?: Prisma.itemOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.itemInclude<ExtArgs> | null
}
