
/* !!! This is code generated by <PERSON><PERSON><PERSON>. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/

export const CraftingSkills = {
  fabrication: 'fabrication',
  electronics: 'electronics',
  chemistry: 'chemistry',
  outfitting: 'outfitting'
} as const

export type CraftingSkills = (typeof CraftingSkills)[keyof typeof CraftingSkills]


export const BankTransactionTypes = {
  bank_deposit: 'bank_deposit',
  bank_withdrawal: 'bank_withdrawal',
  bank_transfer: 'bank_transfer',
  shop_purchase: 'shop_purchase',
  player_trade: 'player_trade'
} as const

export type BankTransactionTypes = (typeof BankTransactionTypes)[keyof typeof BankTransactionTypes]


export const QuestObjectiveTypes = {
  DEFEAT_NPC: 'DEFEAT_NPC',
  DEFEAT_NPC_IN_TURNS: 'DEFEAT_NPC_IN_TURNS',
  DEFEAT_NPC_WITH_LOW_DAMAGE: 'DEFEAT_NPC_WITH_LOW_DAMAGE',
  DEFEAT_BOSS: 'DEFEAT_BOSS',
  DEFEAT_PLAYER: 'DEFEAT_PLAYER',
  PVP_POST_BATTLE_CHOICE: 'PVP_POST_BATTLE_CHOICE',
  DEFEAT_PLAYER_XNAME: 'DEFEAT_PLAYER_XNAME',
  DEFEAT_SPECIFIC_PLAYER: 'DEFEAT_SPECIFIC_PLAYER',
  WIN_BATTLE: 'WIN_BATTLE',
  USE_ABILITY: 'USE_ABILITY',
  ACQUIRE_ITEM: 'ACQUIRE_ITEM',
  CRAFT_ITEM: 'CRAFT_ITEM',
  DELIVER_ITEM: 'DELIVER_ITEM',
  GATHER_RESOURCES: 'GATHER_RESOURCES',
  PLACE_BOUNTY: 'PLACE_BOUNTY',
  COLLECT_BOUNTY_REWARD: 'COLLECT_BOUNTY_REWARD',
  COMPLETE_MISSIONS: 'COMPLETE_MISSIONS',
  DONATE_TO_SHRINE: 'DONATE_TO_SHRINE',
  VOTE_ON_SUGGESTION: 'VOTE_ON_SUGGESTION',
  CHARACTER_ENCOUNTERS: 'CHARACTER_ENCOUNTERS',
  TRAIN_STATS: 'TRAIN_STATS',
  GAMBLING_SLOTS: 'GAMBLING_SLOTS',
  COMPLETE_STORY_EPISODE: 'COMPLETE_STORY_EPISODE',
  UNIQUE_OBJECTIVE: 'UNIQUE_OBJECTIVE'
} as const

export type QuestObjectiveTypes = (typeof QuestObjectiveTypes)[keyof typeof QuestObjectiveTypes]


export const GangInviteTypes = {
  invite: 'invite',
  inviteRequest: 'inviteRequest'
} as const

export type GangInviteTypes = (typeof GangInviteTypes)[keyof typeof GangInviteTypes]


export const QuestProgressStatus = {
  complete: 'complete',
  in_progress: 'in_progress',
  ready_to_complete: 'ready_to_complete'
} as const

export type QuestProgressStatus = (typeof QuestProgressStatus)[keyof typeof QuestProgressStatus]


export const RecipeItemTypes = {
  input: 'input',
  output: 'output'
} as const

export type RecipeItemTypes = (typeof RecipeItemTypes)[keyof typeof RecipeItemTypes]


export const SuggestionVoteTypes = {
  upvote: 'upvote',
  downvote: 'downvote'
} as const

export type SuggestionVoteTypes = (typeof SuggestionVoteTypes)[keyof typeof SuggestionVoteTypes]


export const DropChanceTypes = {
  creature: 'creature',
  boss: 'boss',
  daily: 'daily',
  roguelike: 'roguelike',
  quest: 'quest',
  scavenge: 'scavenge',
  character_encounter: 'character_encounter'
} as const

export type DropChanceTypes = (typeof DropChanceTypes)[keyof typeof DropChanceTypes]


export const EquipSlots = {
  head: 'head',
  chest: 'chest',
  hands: 'hands',
  legs: 'legs',
  feet: 'feet',
  finger: 'finger',
  offhand: 'offhand',
  shield: 'shield',
  weapon: 'weapon',
  ranged: 'ranged'
} as const

export type EquipSlots = (typeof EquipSlots)[keyof typeof EquipSlots]


export const ItemQuality = {
  shoddy: 'shoddy',
  normal: 'normal',
  fine: 'fine',
  excellent: 'excellent',
  superior: 'superior',
  perfect: 'perfect',
  masterwork: 'masterwork'
} as const

export type ItemQuality = (typeof ItemQuality)[keyof typeof ItemQuality]


export const ItemTypes = {
  weapon: 'weapon',
  ranged: 'ranged',
  head: 'head',
  chest: 'chest',
  hands: 'hands',
  legs: 'legs',
  feet: 'feet',
  finger: 'finger',
  offhand: 'offhand',
  shield: 'shield',
  consumable: 'consumable',
  crafting: 'crafting',
  junk: 'junk',
  quest: 'quest',
  special: 'special',
  recipe: 'recipe',
  upgrade: 'upgrade',
  pet: 'pet',
  pet_food: 'pet_food'
} as const

export type ItemTypes = (typeof ItemTypes)[keyof typeof ItemTypes]


export const ShopTypes = {
  general: 'general',
  weapon: 'weapon',
  armour: 'armour',
  food: 'food',
  furniture: 'furniture',
  gang: 'gang'
} as const

export type ShopTypes = (typeof ShopTypes)[keyof typeof ShopTypes]


export const TalentTree = {
  strength: 'strength',
  intelligence: 'intelligence',
  dexterity: 'dexterity',
  defence: 'defence',
  endurance: 'endurance',
  vitality: 'vitality',
  mining: 'mining',
  scavenging: 'scavenging',
  foraging: 'foraging',
  fabrication: 'fabrication',
  outfitting: 'outfitting',
  chemistry: 'chemistry',
  electronics: 'electronics'
} as const

export type TalentTree = (typeof TalentTree)[keyof typeof TalentTree]


export const ItemRarities = {
  novice: 'novice',
  standard: 'standard',
  enhanced: 'enhanced',
  specialist: 'specialist',
  military: 'military',
  legendary: 'legendary'
} as const

export type ItemRarities = (typeof ItemRarities)[keyof typeof ItemRarities]


export const SuggestionStates = {
  New: 'New',
  Accepted: 'Accepted',
  Completed: 'Completed',
  Denied: 'Denied'
} as const

export type SuggestionStates = (typeof SuggestionStates)[keyof typeof SuggestionStates]


export const ShopListingCurrency = {
  yen: 'yen',
  gangCreds: 'gangCreds',
  classPoints: 'classPoints'
} as const

export type ShopListingCurrency = (typeof ShopListingCurrency)[keyof typeof ShopListingCurrency]


export const AuctionItemStatus = {
  complete: 'complete',
  in_progress: 'in_progress',
  expired: 'expired',
  cancelled: 'cancelled'
} as const

export type AuctionItemStatus = (typeof AuctionItemStatus)[keyof typeof AuctionItemStatus]


export const QuestTargetAction = {
  mug: 'mug',
  cripple: 'cripple',
  leave: 'leave'
} as const

export type QuestTargetAction = (typeof QuestTargetAction)[keyof typeof QuestTargetAction]


export const ExploreNodeLocation = {
  shibuya: 'shibuya',
  shinjuku: 'shinjuku',
  bunkyo: 'bunkyo',
  chiyoda: 'chiyoda',
  minato: 'minato'
} as const

export type ExploreNodeLocation = (typeof ExploreNodeLocation)[keyof typeof ExploreNodeLocation]


export const TravelMethod = {
  walk: 'walk',
  bus: 'bus'
} as const

export type TravelMethod = (typeof TravelMethod)[keyof typeof TravelMethod]


export const LocationTypes = {
  church: 'church',
  shrine: 'shrine',
  mall: 'mall',
  alley: 'alley',
  school: 'school',
  sewers: 'sewers',
  themepark: 'themepark',
  shibuya: 'shibuya',
  shinjuku: 'shinjuku',
  bunkyo: 'bunkyo',
  chiyoda: 'chiyoda',
  minato: 'minato',
  any: 'any'
} as const

export type LocationTypes = (typeof LocationTypes)[keyof typeof LocationTypes]


export const CreatureStatTypes = {
  balanced: 'balanced',
  tank: 'tank',
  dps: 'dps'
} as const

export type CreatureStatTypes = (typeof CreatureStatTypes)[keyof typeof CreatureStatTypes]


export const UserTypes = {
  student: 'student',
  prefect: 'prefect',
  admin: 'admin',
  guest: 'guest'
} as const

export type UserTypes = (typeof UserTypes)[keyof typeof UserTypes]


export const HospitalisedHealingTypes = {
  full: 'full',
  injury: 'injury'
} as const

export type HospitalisedHealingTypes = (typeof HospitalisedHealingTypes)[keyof typeof HospitalisedHealingTypes]


export const StatusEffectType = {
  BUFF: 'BUFF',
  DEBUFF: 'DEBUFF',
  NEUTRAL: 'NEUTRAL'
} as const

export type StatusEffectType = (typeof StatusEffectType)[keyof typeof StatusEffectType]


export const StatusEffectTier = {
  Minor: 'Minor',
  Moderate: 'Moderate',
  Severe: 'Severe',
  Critical: 'Critical'
} as const

export type StatusEffectTier = (typeof StatusEffectTier)[keyof typeof StatusEffectTier]


export const StatusEffectModifierType = {
  add: 'add',
  multiply: 'multiply',
  divide: 'divide'
} as const

export type StatusEffectModifierType = (typeof StatusEffectModifierType)[keyof typeof StatusEffectModifierType]


export const QuestRewardType = {
  ITEM: 'ITEM',
  TALENT_POINTS: 'TALENT_POINTS',
  GANG_CREDS: 'GANG_CREDS',
  CLASS_POINTS: 'CLASS_POINTS'
} as const

export type QuestRewardType = (typeof QuestRewardType)[keyof typeof QuestRewardType]


export const SkillType = {
  mining: 'mining',
  scavenging: 'scavenging',
  foraging: 'foraging',
  fabrication: 'fabrication',
  outfitting: 'outfitting',
  chemistry: 'chemistry',
  electronics: 'electronics',
  strength: 'strength',
  intelligence: 'intelligence',
  dexterity: 'dexterity',
  defence: 'defence',
  endurance: 'endurance',
  vitality: 'vitality'
} as const

export type SkillType = (typeof SkillType)[keyof typeof SkillType]


export const ExploreNodeType = {
  SHOP: 'SHOP',
  HOUSING: 'HOUSING',
  BATTLE: 'BATTLE',
  CHARACTER_ENCOUNTER: 'CHARACTER_ENCOUNTER',
  ACTION: 'ACTION',
  CONDITION: 'CONDITION',
  CHOICE: 'CHOICE',
  STORY: 'STORY',
  MINING_NODE: 'MINING_NODE',
  SCAVENGE_NODE: 'SCAVENGE_NODE',
  FORAGING_NODE: 'FORAGING_NODE'
} as const

export type ExploreNodeType = (typeof ExploreNodeType)[keyof typeof ExploreNodeType]


export const ExploreNodeStatus = {
  completed: 'completed',
  available: 'available',
  locked: 'locked',
  current: 'current'
} as const

export type ExploreNodeStatus = (typeof ExploreNodeStatus)[keyof typeof ExploreNodeStatus]


export const TalentType = {
  passive: 'passive',
  ability: 'ability'
} as const

export type TalentType = (typeof TalentType)[keyof typeof TalentType]


export const StoryEpisodeType = {
  NARRATIVE: 'NARRATIVE',
  CHOICE: 'CHOICE',
  BATTLE: 'BATTLE'
} as const

export type StoryEpisodeType = (typeof StoryEpisodeType)[keyof typeof StoryEpisodeType]
