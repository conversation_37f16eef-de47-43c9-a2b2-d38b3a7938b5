
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the browser.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/index-browser"

export type * from '../models.js'
export type * from './prismaNamespace.js'

export const Decimal = runtime.Decimal


export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.DbNull),
  JsonNull: runtime.objectEnumValues.classes.JsonNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.JsonNull),
  AnyNull: runtime.objectEnumValues.classes.AnyNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.AnyNull),
}
/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull
/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull
/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull


export const ModelName = {
  action_log: 'action_log',
  game_config: 'game_config',
  auction_item: 'auction_item',
  bank_transaction: 'bank_transaction',
  battle_log: 'battle_log',
  bounty: 'bounty',
  chat_message: 'chat_message',
  chat_room: 'chat_room',
  crafting_recipe: 'crafting_recipe',
  creature: 'creature',
  daily_mission: 'daily_mission',
  drop_chance: 'drop_chance',
  equipped_item: 'equipped_item',
  game_stats: 'game_stats',
  gang: 'gang',
  gang_invite: 'gang_invite',
  gang_log: 'gang_log',
  gang_member: 'gang_member',
  item: 'item',
  job: 'job',
  lottery: 'lottery',
  lottery_entry: 'lottery_entry',
  notification: 'notification',
  poll: 'poll',
  poll_response: 'poll_response',
  private_message: 'private_message',
  profile_comment: 'profile_comment',
  push_token: 'push_token',
  daily_quest: 'daily_quest',
  quest: 'quest',
  quest_progress: 'quest_progress',
  quest_objective: 'quest_objective',
  quest_objective_progress: 'quest_objective_progress',
  quest_reward: 'quest_reward',
  recipe_item: 'recipe_item',
  registration_code: 'registration_code',
  shop: 'shop',
  shop_listing: 'shop_listing',
  shrine_donation: 'shrine_donation',
  shrine_goal: 'shrine_goal',
  suggestion: 'suggestion',
  suggestion_comment: 'suggestion_comment',
  suggestion_vote: 'suggestion_vote',
  talent: 'talent',
  trader_rep: 'trader_rep',
  user: 'user',
  user_equipped_abilities: 'user_equipped_abilities',
  session: 'session',
  account: 'account',
  verification: 'verification',
  user_item: 'user_item',
  user_recipe: 'user_recipe',
  user_completed_course: 'user_completed_course',
  user_talent: 'user_talent',
  user_achievements: 'user_achievements',
  user_crafting_queue: 'user_crafting_queue',
  property: 'property',
  user_property: 'user_property',
  pet: 'pet',
  user_pet: 'user_pet',
  friend_request: 'friend_request',
  friend: 'friend',
  rival: 'rival',
  status_effect: 'status_effect',
  user_status_effect: 'user_status_effect',
  user_skill: 'user_skill',
  explore_static_node: 'explore_static_node',
  explore_player_node: 'explore_player_node',
  story_season: 'story_season',
  story_chapter: 'story_chapter',
  story_episode: 'story_episode'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]

/*
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const Action_logScalarFieldEnum = {
  id: 'id',
  logType: 'logType',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  playerId: 'playerId',
  secondPartyId: 'secondPartyId'
} as const

export type Action_logScalarFieldEnum = (typeof Action_logScalarFieldEnum)[keyof typeof Action_logScalarFieldEnum]


export const Game_configScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  category: 'category',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Game_configScalarFieldEnum = (typeof Game_configScalarFieldEnum)[keyof typeof Game_configScalarFieldEnum]


export const Auction_itemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  deposit: 'deposit',
  buyoutPrice: 'buyoutPrice',
  endsAt: 'endsAt',
  status: 'status',
  bankFunds: 'bankFunds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemId: 'itemId',
  sellerId: 'sellerId'
} as const

export type Auction_itemScalarFieldEnum = (typeof Auction_itemScalarFieldEnum)[keyof typeof Auction_itemScalarFieldEnum]


export const Bank_transactionScalarFieldEnum = {
  id: 'id',
  transaction_type: 'transaction_type',
  cash: 'cash',
  transactionFee: 'transactionFee',
  initiatorCashBalance: 'initiatorCashBalance',
  initiatorBankBalance: 'initiatorBankBalance',
  secondPartyCashBalance: 'secondPartyCashBalance',
  secondPartyBankBalance: 'secondPartyBankBalance',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  initiatorId: 'initiatorId',
  secondPartyId: 'secondPartyId',
  userId: 'userId'
} as const

export type Bank_transactionScalarFieldEnum = (typeof Bank_transactionScalarFieldEnum)[keyof typeof Bank_transactionScalarFieldEnum]


export const Battle_logScalarFieldEnum = {
  id: 'id',
  victory: 'victory',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  attackerId: 'attackerId',
  defenderId: 'defenderId'
} as const

export type Battle_logScalarFieldEnum = (typeof Battle_logScalarFieldEnum)[keyof typeof Battle_logScalarFieldEnum]


export const BountyScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  reason: 'reason',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  placerId: 'placerId',
  targetId: 'targetId',
  claimedById: 'claimedById'
} as const

export type BountyScalarFieldEnum = (typeof BountyScalarFieldEnum)[keyof typeof BountyScalarFieldEnum]


export const Chat_messageScalarFieldEnum = {
  id: 'id',
  message: 'message',
  hidden: 'hidden',
  announcementType: 'announcementType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  chatRoomId: 'chatRoomId',
  userId: 'userId',
  parentMessageId: 'parentMessageId'
} as const

export type Chat_messageScalarFieldEnum = (typeof Chat_messageScalarFieldEnum)[keyof typeof Chat_messageScalarFieldEnum]


export const Chat_roomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  gangId: 'gangId'
} as const

export type Chat_roomScalarFieldEnum = (typeof Chat_roomScalarFieldEnum)[keyof typeof Chat_roomScalarFieldEnum]


export const Crafting_recipeScalarFieldEnum = {
  id: 'id',
  cost: 'cost',
  craftTime: 'craftTime',
  isUnlockable: 'isUnlockable',
  requiredSkillType: 'requiredSkillType',
  requiredSkillLevel: 'requiredSkillLevel'
} as const

export type Crafting_recipeScalarFieldEnum = (typeof Crafting_recipeScalarFieldEnum)[keyof typeof Crafting_recipeScalarFieldEnum]


export const CreatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  minFloor: 'minFloor',
  maxFloor: 'maxFloor',
  boss: 'boss',
  health: 'health',
  currentHealth: 'currentHealth',
  strength: 'strength',
  defence: 'defence',
  weaponDamage: 'weaponDamage',
  location: 'location',
  statType: 'statType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type CreatureScalarFieldEnum = (typeof CreatureScalarFieldEnum)[keyof typeof CreatureScalarFieldEnum]


export const Daily_missionScalarFieldEnum = {
  id: 'id',
  tier: 'tier',
  missionName: 'missionName',
  description: 'description',
  missionDate: 'missionDate',
  duration: 'duration',
  minCashReward: 'minCashReward',
  maxCashReward: 'maxCashReward',
  minExpReward: 'minExpReward',
  maxExpReward: 'maxExpReward',
  levelReq: 'levelReq',
  hoursReq: 'hoursReq',
  itemRewardQuantity: 'itemRewardQuantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemRewardId: 'itemRewardId'
} as const

export type Daily_missionScalarFieldEnum = (typeof Daily_missionScalarFieldEnum)[keyof typeof Daily_missionScalarFieldEnum]


export const Drop_chanceScalarFieldEnum = {
  id: 'id',
  dropRate: 'dropRate',
  dropChanceType: 'dropChanceType',
  quantity: 'quantity',
  location: 'location',
  minLevel: 'minLevel',
  maxLevel: 'maxLevel',
  scavengeType: 'scavengeType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemId: 'itemId'
} as const

export type Drop_chanceScalarFieldEnum = (typeof Drop_chanceScalarFieldEnum)[keyof typeof Drop_chanceScalarFieldEnum]


export const Equipped_itemScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  slot: 'slot',
  userItemId: 'userItemId'
} as const

export type Equipped_itemScalarFieldEnum = (typeof Equipped_itemScalarFieldEnum)[keyof typeof Equipped_itemScalarFieldEnum]


export const Game_statsScalarFieldEnum = {
  id: 'id',
  stats_type: 'stats_type',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  playerId: 'playerId'
} as const

export type Game_statsScalarFieldEnum = (typeof Game_statsScalarFieldEnum)[keyof typeof Game_statsScalarFieldEnum]


export const GangScalarFieldEnum = {
  id: 'id',
  name: 'name',
  about: 'about',
  avatar: 'avatar',
  treasury_balance: 'treasury_balance',
  hideout_level: 'hideout_level',
  materialsResource: 'materialsResource',
  essenceResource: 'essenceResource',
  dailyEssenceGained: 'dailyEssenceGained',
  toolsResource: 'toolsResource',
  techResource: 'techResource',
  weeklyRespect: 'weeklyRespect',
  totalRespect: 'totalRespect',
  gangMOTD: 'gangMOTD',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId'
} as const

export type GangScalarFieldEnum = (typeof GangScalarFieldEnum)[keyof typeof GangScalarFieldEnum]


export const Gang_inviteScalarFieldEnum = {
  id: 'id',
  inviteType: 'inviteType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  senderId: 'senderId',
  recipientId: 'recipientId'
} as const

export type Gang_inviteScalarFieldEnum = (typeof Gang_inviteScalarFieldEnum)[keyof typeof Gang_inviteScalarFieldEnum]


export const Gang_logScalarFieldEnum = {
  id: 'id',
  action: 'action',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  gangMemberId: 'gangMemberId',
  secondPartyId: 'secondPartyId'
} as const

export type Gang_logScalarFieldEnum = (typeof Gang_logScalarFieldEnum)[keyof typeof Gang_logScalarFieldEnum]


export const Gang_memberScalarFieldEnum = {
  id: 'id',
  rank: 'rank',
  payoutShare: 'payoutShare',
  weeklyMaterials: 'weeklyMaterials',
  weeklyEssence: 'weeklyEssence',
  weeklyTools: 'weeklyTools',
  weeklyRespect: 'weeklyRespect',
  totalContribution: 'totalContribution',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  userId: 'userId'
} as const

export type Gang_memberScalarFieldEnum = (typeof Gang_memberScalarFieldEnum)[keyof typeof Gang_memberScalarFieldEnum]


export const ItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  itemType: 'itemType',
  rarity: 'rarity',
  level: 'level',
  about: 'about',
  cashValue: 'cashValue',
  image: 'image',
  damage: 'damage',
  armour: 'armour',
  health: 'health',
  energy: 'energy',
  actionPoints: 'actionPoints',
  baseAmmo: 'baseAmmo',
  itemEffects: 'itemEffects',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  recipeUnlockId: 'recipeUnlockId',
  petUnlockId: 'petUnlockId'
} as const

export type ItemScalarFieldEnum = (typeof ItemScalarFieldEnum)[keyof typeof ItemScalarFieldEnum]


export const JobScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  avatar: 'avatar',
  payFormula: 'payFormula',
  strengthFormula: 'strengthFormula',
  intelligenceFormula: 'intelligenceFormula',
  dexterityFormula: 'dexterityFormula',
  defenceFormula: 'defenceFormula',
  enduranceFormula: 'enduranceFormula',
  vitalityFormula: 'vitalityFormula',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type JobScalarFieldEnum = (typeof JobScalarFieldEnum)[keyof typeof JobScalarFieldEnum]


export const LotteryScalarFieldEnum = {
  id: 'id',
  drawDate: 'drawDate',
  prizeAmount: 'prizeAmount',
  entries: 'entries',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  winnerId: 'winnerId'
} as const

export type LotteryScalarFieldEnum = (typeof LotteryScalarFieldEnum)[keyof typeof LotteryScalarFieldEnum]


export const Lottery_entryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lotteryId: 'lotteryId',
  userId: 'userId'
} as const

export type Lottery_entryScalarFieldEnum = (typeof Lottery_entryScalarFieldEnum)[keyof typeof Lottery_entryScalarFieldEnum]


export const NotificationScalarFieldEnum = {
  id: 'id',
  notificationType: 'notificationType',
  details: 'details',
  read: 'read',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type NotificationScalarFieldEnum = (typeof NotificationScalarFieldEnum)[keyof typeof NotificationScalarFieldEnum]


export const PollScalarFieldEnum = {
  id: 'id',
  title: 'title',
  ended: 'ended',
  showResults: 'showResults',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PollScalarFieldEnum = (typeof PollScalarFieldEnum)[keyof typeof PollScalarFieldEnum]


export const Poll_responseScalarFieldEnum = {
  id: 'id',
  answer: 'answer',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  pollId: 'pollId'
} as const

export type Poll_responseScalarFieldEnum = (typeof Poll_responseScalarFieldEnum)[keyof typeof Poll_responseScalarFieldEnum]


export const Private_messageScalarFieldEnum = {
  id: 'id',
  message: 'message',
  read: 'read',
  isGlobal: 'isGlobal',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Private_messageScalarFieldEnum = (typeof Private_messageScalarFieldEnum)[keyof typeof Private_messageScalarFieldEnum]


export const Profile_commentScalarFieldEnum = {
  id: 'id',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Profile_commentScalarFieldEnum = (typeof Profile_commentScalarFieldEnum)[keyof typeof Profile_commentScalarFieldEnum]


export const Push_tokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type Push_tokenScalarFieldEnum = (typeof Push_tokenScalarFieldEnum)[keyof typeof Push_tokenScalarFieldEnum]


export const Daily_questScalarFieldEnum = {
  id: 'id',
  objectiveType: 'objectiveType',
  target: 'target',
  targetAction: 'targetAction',
  quantity: 'quantity',
  questStatus: 'questStatus',
  count: 'count',
  cashReward: 'cashReward',
  xpReward: 'xpReward',
  itemRewardQuantity: 'itemRewardQuantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemRewardId: 'itemRewardId',
  userId: 'userId'
} as const

export type Daily_questScalarFieldEnum = (typeof Daily_questScalarFieldEnum)[keyof typeof Daily_questScalarFieldEnum]


export const QuestScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  questInfo: 'questInfo',
  levelReq: 'levelReq',
  disabled: 'disabled',
  questChainName: 'questChainName',
  xpReward: 'xpReward',
  cashReward: 'cashReward',
  repReward: 'repReward',
  talentPointReward: 'talentPointReward',
  shopId: 'shopId',
  requiredQuestId: 'requiredQuestId',
  isStoryQuest: 'isStoryQuest',
  chapterId: 'chapterId',
  orderInChapter: 'orderInChapter'
} as const

export type QuestScalarFieldEnum = (typeof QuestScalarFieldEnum)[keyof typeof QuestScalarFieldEnum]


export const Quest_progressScalarFieldEnum = {
  id: 'id',
  questStatus: 'questStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questId: 'questId',
  userId: 'userId'
} as const

export type Quest_progressScalarFieldEnum = (typeof Quest_progressScalarFieldEnum)[keyof typeof Quest_progressScalarFieldEnum]


export const Quest_objectiveScalarFieldEnum = {
  id: 'id',
  description: 'description',
  objectiveType: 'objectiveType',
  target: 'target',
  targetAction: 'targetAction',
  quantity: 'quantity',
  location: 'location',
  isRequired: 'isRequired',
  questId: 'questId',
  creatureId: 'creatureId',
  itemId: 'itemId'
} as const

export type Quest_objectiveScalarFieldEnum = (typeof Quest_objectiveScalarFieldEnum)[keyof typeof Quest_objectiveScalarFieldEnum]


export const Quest_objective_progressScalarFieldEnum = {
  id: 'id',
  count: 'count',
  status: 'status',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questObjectiveId: 'questObjectiveId'
} as const

export type Quest_objective_progressScalarFieldEnum = (typeof Quest_objective_progressScalarFieldEnum)[keyof typeof Quest_objective_progressScalarFieldEnum]


export const Quest_rewardScalarFieldEnum = {
  id: 'id',
  rewardType: 'rewardType',
  quantity: 'quantity',
  isChoice: 'isChoice',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questId: 'questId',
  dailyQuestId: 'dailyQuestId',
  itemId: 'itemId'
} as const

export type Quest_rewardScalarFieldEnum = (typeof Quest_rewardScalarFieldEnum)[keyof typeof Quest_rewardScalarFieldEnum]


export const Recipe_itemScalarFieldEnum = {
  count: 'count',
  itemType: 'itemType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  craftingRecipeId: 'craftingRecipeId',
  itemId: 'itemId'
} as const

export type Recipe_itemScalarFieldEnum = (typeof Recipe_itemScalarFieldEnum)[keyof typeof Recipe_itemScalarFieldEnum]


export const Registration_codeScalarFieldEnum = {
  id: 'id',
  code: 'code',
  note: 'note',
  unlimitedUse: 'unlimitedUse',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  referrerId: 'referrerId',
  claimerId: 'claimerId'
} as const

export type Registration_codeScalarFieldEnum = (typeof Registration_codeScalarFieldEnum)[keyof typeof Registration_codeScalarFieldEnum]


export const ShopScalarFieldEnum = {
  id: 'id',
  name: 'name',
  shopType: 'shopType',
  avatar: 'avatar',
  description: 'description',
  disabled: 'disabled'
} as const

export type ShopScalarFieldEnum = (typeof ShopScalarFieldEnum)[keyof typeof ShopScalarFieldEnum]


export const Shop_listingScalarFieldEnum = {
  id: 'id',
  customCost: 'customCost',
  repRequired: 'repRequired',
  stock: 'stock',
  currency: 'currency',
  shopId: 'shopId',
  itemId: 'itemId'
} as const

export type Shop_listingScalarFieldEnum = (typeof Shop_listingScalarFieldEnum)[keyof typeof Shop_listingScalarFieldEnum]


export const Shrine_donationScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  date: 'date',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  shrineGoalId: 'shrineGoalId',
  userId: 'userId'
} as const

export type Shrine_donationScalarFieldEnum = (typeof Shrine_donationScalarFieldEnum)[keyof typeof Shrine_donationScalarFieldEnum]


export const Shrine_goalScalarFieldEnum = {
  id: 'id',
  donationGoal: 'donationGoal',
  donationAmount: 'donationAmount',
  goalReached: 'goalReached',
  goalDate: 'goalDate',
  buffRewards: 'buffRewards',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Shrine_goalScalarFieldEnum = (typeof Shrine_goalScalarFieldEnum)[keyof typeof Shrine_goalScalarFieldEnum]


export const SuggestionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  state: 'state',
  upvotes: 'upvotes',
  downvotes: 'downvotes',
  totalComments: 'totalComments',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type SuggestionScalarFieldEnum = (typeof SuggestionScalarFieldEnum)[keyof typeof SuggestionScalarFieldEnum]


export const Suggestion_commentScalarFieldEnum = {
  id: 'id',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  suggestionId: 'suggestionId'
} as const

export type Suggestion_commentScalarFieldEnum = (typeof Suggestion_commentScalarFieldEnum)[keyof typeof Suggestion_commentScalarFieldEnum]


export const Suggestion_voteScalarFieldEnum = {
  id: 'id',
  voteType: 'voteType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  suggestionId: 'suggestionId'
} as const

export type Suggestion_voteScalarFieldEnum = (typeof Suggestion_voteScalarFieldEnum)[keyof typeof Suggestion_voteScalarFieldEnum]


export const TalentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  tree: 'tree',
  talentType: 'talentType',
  description: 'description',
  skillLevelRequired: 'skillLevelRequired',
  pointsInTreeRequired: 'pointsInTreeRequired',
  pointsCost: 'pointsCost',
  maxPoints: 'maxPoints',
  staminaCost: 'staminaCost',
  tier1Modifier: 'tier1Modifier',
  tier2Modifier: 'tier2Modifier',
  tier3Modifier: 'tier3Modifier',
  secondaryModifier: 'secondaryModifier'
} as const

export type TalentScalarFieldEnum = (typeof TalentScalarFieldEnum)[keyof typeof TalentScalarFieldEnum]


export const Trader_repScalarFieldEnum = {
  id: 'id',
  reputationLevel: 'reputationLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  shopId: 'shopId',
  userId: 'userId'
} as const

export type Trader_repScalarFieldEnum = (typeof Trader_repScalarFieldEnum)[keyof typeof Trader_repScalarFieldEnum]


export const UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  displayUsername: 'displayUsername',
  about: 'about',
  email: 'email',
  emailVerified: 'emailVerified',
  banned: 'banned',
  banReason: 'banReason',
  banExpires: 'banExpires',
  usernameSet: 'usernameSet',
  cash: 'cash',
  bank_balance: 'bank_balance',
  last_activity: 'last_activity',
  chatBannedUntil: 'chatBannedUntil',
  userType: 'userType',
  avatar: 'avatar',
  profileBanner: 'profileBanner',
  jobLevel: 'jobLevel',
  jobPayoutHour: 'jobPayoutHour',
  blockNextJobPayout: 'blockNextJobPayout',
  roguelikeMap: 'roguelikeMap',
  roguelikeLevel: 'roguelikeLevel',
  roguelikeHighscore: 'roguelikeHighscore',
  jailedUntil: 'jailedUntil',
  jailReason: 'jailReason',
  hospitalisedUntil: 'hospitalisedUntil',
  hospitalisedHealingType: 'hospitalisedHealingType',
  hospitalisedReason: 'hospitalisedReason',
  energy: 'energy',
  lastEnergyTick: 'lastEnergyTick',
  focus: 'focus',
  dailyFatigueUsed: 'dailyFatigueUsed',
  lastFatigueReset: 'lastFatigueReset',
  level: 'level',
  xp: 'xp',
  actionPoints: 'actionPoints',
  nextAPTick: 'nextAPTick',
  maxActionPoints: 'maxActionPoints',
  currentHealth: 'currentHealth',
  nextHPTick: 'nextHPTick',
  health: 'health',
  talentPoints: 'talentPoints',
  activeCourseId: 'activeCourseId',
  courseEnds: 'courseEnds',
  class: 'class',
  classPoints: 'classPoints',
  adminNotes: 'adminNotes',
  combatLevel: 'combatLevel',
  discordID: 'discordID',
  currentMission: 'currentMission',
  missionEnds: 'missionEnds',
  profileDetailBanUntil: 'profileDetailBanUntil',
  weeklyBuyLimitRemaining: 'weeklyBuyLimitRemaining',
  dailyQuestsRewardClaimed: 'dailyQuestsRewardClaimed',
  currentMapLocation: 'currentMapLocation',
  travelStartTime: 'travelStartTime',
  travelEndTime: 'travelEndTime',
  travelMethod: 'travelMethod',
  defeatedNpcs: 'defeatedNpcs',
  pushNotificationsEnabled: 'pushNotificationsEnabled',
  gangCreds: 'gangCreds',
  lastNewsIDRead: 'lastNewsIDRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  jobId: 'jobId',
  referrerId: 'referrerId',
  statusMessage: 'statusMessage',
  statusMessageUpdatedAt: 'statusMessageUpdatedAt',
  showLastOnline: 'showLastOnline'
} as const

export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


export const User_equipped_abilitiesScalarFieldEnum = {
  userId: 'userId',
  equippedAbility1Id: 'equippedAbility1Id',
  equippedAbility2Id: 'equippedAbility2Id',
  equippedAbility3Id: 'equippedAbility3Id',
  equippedAbility4Id: 'equippedAbility4Id'
} as const

export type User_equipped_abilitiesScalarFieldEnum = (typeof User_equipped_abilitiesScalarFieldEnum)[keyof typeof User_equipped_abilitiesScalarFieldEnum]


export const SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  token: 'token',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  impersonatedBy: 'impersonatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type SessionScalarFieldEnum = (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]


export const AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type AccountScalarFieldEnum = (typeof AccountScalarFieldEnum)[keyof typeof AccountScalarFieldEnum]


export const VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type VerificationScalarFieldEnum = (typeof VerificationScalarFieldEnum)[keyof typeof VerificationScalarFieldEnum]


export const User_itemScalarFieldEnum = {
  id: 'id',
  count: 'count',
  upgradeLevel: 'upgradeLevel',
  quality: 'quality',
  isTradeable: 'isTradeable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  itemId: 'itemId'
} as const

export type User_itemScalarFieldEnum = (typeof User_itemScalarFieldEnum)[keyof typeof User_itemScalarFieldEnum]


export const User_recipeScalarFieldEnum = {
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  craftingRecipeId: 'craftingRecipeId',
  userId: 'userId'
} as const

export type User_recipeScalarFieldEnum = (typeof User_recipeScalarFieldEnum)[keyof typeof User_recipeScalarFieldEnum]


export const User_completed_courseScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type User_completed_courseScalarFieldEnum = (typeof User_completed_courseScalarFieldEnum)[keyof typeof User_completed_courseScalarFieldEnum]


export const User_talentScalarFieldEnum = {
  level: 'level',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  talentId: 'talentId',
  userId: 'userId'
} as const

export type User_talentScalarFieldEnum = (typeof User_talentScalarFieldEnum)[keyof typeof User_talentScalarFieldEnum]


export const User_achievementsScalarFieldEnum = {
  userId: 'userId',
  battleWins: 'battleWins',
  npcBattleWins: 'npcBattleWins',
  craftsCompleted: 'craftsCompleted',
  marketItemsSold: 'marketItemsSold',
  marketMoneyMade: 'marketMoneyMade',
  totalMuggingGain: 'totalMuggingGain',
  totalMuggingLoss: 'totalMuggingLoss',
  totalCasinoProfitLoss: 'totalCasinoProfitLoss',
  questsCompleted: 'questsCompleted',
  dailyQuestsCompleted: 'dailyQuestsCompleted',
  coursesCompleted: 'coursesCompleted',
  roguelikeNodesCompleted: 'roguelikeNodesCompleted',
  roguelikeMapsCompleted: 'roguelikeMapsCompleted',
  examsCompleted: 'examsCompleted',
  totalBountyRewards: 'totalBountyRewards',
  totalBountyPlaced: 'totalBountyPlaced',
  totalMissionHours: 'totalMissionHours',
  suggestionsVoted: 'suggestionsVoted',
  encountersCompleted: 'encountersCompleted'
} as const

export type User_achievementsScalarFieldEnum = (typeof User_achievementsScalarFieldEnum)[keyof typeof User_achievementsScalarFieldEnum]


export const User_crafting_queueScalarFieldEnum = {
  id: 'id',
  startedAt: 'startedAt',
  endsAt: 'endsAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  craftingRecipeId: 'craftingRecipeId'
} as const

export type User_crafting_queueScalarFieldEnum = (typeof User_crafting_queueScalarFieldEnum)[keyof typeof User_crafting_queueScalarFieldEnum]


export const PropertyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  propertyType: 'propertyType',
  cost: 'cost',
  upkeep: 'upkeep',
  slots: 'slots',
  buffs: 'buffs',
  description: 'description',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PropertyScalarFieldEnum = (typeof PropertyScalarFieldEnum)[keyof typeof PropertyScalarFieldEnum]


export const User_propertyScalarFieldEnum = {
  id: 'id',
  purchaseDate: 'purchaseDate',
  lastUpkeepPaid: 'lastUpkeepPaid',
  furniture: 'furniture',
  customization: 'customization',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  propertyId: 'propertyId'
} as const

export type User_propertyScalarFieldEnum = (typeof User_propertyScalarFieldEnum)[keyof typeof User_propertyScalarFieldEnum]


export const PetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  species: 'species',
  maxLevel: 'maxLevel',
  evolution_stages: 'evolution_stages',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PetScalarFieldEnum = (typeof PetScalarFieldEnum)[keyof typeof PetScalarFieldEnum]


export const User_petScalarFieldEnum = {
  id: 'id',
  name: 'name',
  level: 'level',
  isActive: 'isActive',
  xp: 'xp',
  nextLevelXp: 'nextLevelXp',
  happiness: 'happiness',
  energy: 'energy',
  evolution: 'evolution',
  userId: 'userId',
  petId: 'petId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type User_petScalarFieldEnum = (typeof User_petScalarFieldEnum)[keyof typeof User_petScalarFieldEnum]


export const Friend_requestScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Friend_requestScalarFieldEnum = (typeof Friend_requestScalarFieldEnum)[keyof typeof Friend_requestScalarFieldEnum]


export const FriendScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  friendId: 'friendId',
  note: 'note'
} as const

export type FriendScalarFieldEnum = (typeof FriendScalarFieldEnum)[keyof typeof FriendScalarFieldEnum]


export const RivalScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  rivalId: 'rivalId',
  note: 'note'
} as const

export type RivalScalarFieldEnum = (typeof RivalScalarFieldEnum)[keyof typeof RivalScalarFieldEnum]


export const Status_effectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  source: 'source',
  effectType: 'effectType',
  category: 'category',
  tier: 'tier',
  duration: 'duration',
  modifier: 'modifier',
  modifierType: 'modifierType',
  stackable: 'stackable',
  maxStacks: 'maxStacks',
  disabled: 'disabled',
  description: 'description'
} as const

export type Status_effectScalarFieldEnum = (typeof Status_effectScalarFieldEnum)[keyof typeof Status_effectScalarFieldEnum]


export const User_status_effectScalarFieldEnum = {
  id: 'id',
  stacks: 'stacks',
  endsAt: 'endsAt',
  customName: 'customName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  effectId: 'effectId',
  userId: 'userId'
} as const

export type User_status_effectScalarFieldEnum = (typeof User_status_effectScalarFieldEnum)[keyof typeof User_status_effectScalarFieldEnum]


export const User_skillScalarFieldEnum = {
  id: 'id',
  skillType: 'skillType',
  level: 'level',
  experience: 'experience',
  talentPoints: 'talentPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type User_skillScalarFieldEnum = (typeof User_skillScalarFieldEnum)[keyof typeof User_skillScalarFieldEnum]


export const Explore_static_nodeScalarFieldEnum = {
  id: 'id',
  nodeType: 'nodeType',
  title: 'title',
  description: 'description',
  position: 'position',
  metadata: 'metadata',
  location: 'location',
  shopId: 'shopId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Explore_static_nodeScalarFieldEnum = (typeof Explore_static_nodeScalarFieldEnum)[keyof typeof Explore_static_nodeScalarFieldEnum]


export const Explore_player_nodeScalarFieldEnum = {
  id: 'id',
  nodeType: 'nodeType',
  title: 'title',
  description: 'description',
  position: 'position',
  metadata: 'metadata',
  location: 'location',
  status: 'status',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type Explore_player_nodeScalarFieldEnum = (typeof Explore_player_nodeScalarFieldEnum)[keyof typeof Explore_player_nodeScalarFieldEnum]


export const Story_seasonScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  requiredLevel: 'requiredLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_seasonScalarFieldEnum = (typeof Story_seasonScalarFieldEnum)[keyof typeof Story_seasonScalarFieldEnum]


export const Story_chapterScalarFieldEnum = {
  id: 'id',
  seasonId: 'seasonId',
  name: 'name',
  description: 'description',
  order: 'order',
  unlockDate: 'unlockDate',
  requiredLevel: 'requiredLevel',
  requiredChapterIds: 'requiredChapterIds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_chapterScalarFieldEnum = (typeof Story_chapterScalarFieldEnum)[keyof typeof Story_chapterScalarFieldEnum]


export const Story_episodeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  episodeType: 'episodeType',
  exploreLocation: 'exploreLocation',
  content: 'content',
  choices: 'choices',
  objectiveId: 'objectiveId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_episodeScalarFieldEnum = (typeof Story_episodeScalarFieldEnum)[keyof typeof Story_episodeScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const JsonNullValueInput = {
  JsonNull: JsonNull
} as const

export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


export const NullableJsonNullValueInput = {
  DbNull: DbNull,
  JsonNull: JsonNull
} as const

export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


export const JsonNullValueFilter = {
  DbNull: DbNull,
  JsonNull: JsonNull,
  AnyNull: AnyNull
} as const

export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


export const action_logOrderByRelevanceFieldEnum = {
  logType: 'logType'
} as const

export type action_logOrderByRelevanceFieldEnum = (typeof action_logOrderByRelevanceFieldEnum)[keyof typeof action_logOrderByRelevanceFieldEnum]


export const game_configOrderByRelevanceFieldEnum = {
  key: 'key',
  category: 'category'
} as const

export type game_configOrderByRelevanceFieldEnum = (typeof game_configOrderByRelevanceFieldEnum)[keyof typeof game_configOrderByRelevanceFieldEnum]


export const bountyOrderByRelevanceFieldEnum = {
  reason: 'reason'
} as const

export type bountyOrderByRelevanceFieldEnum = (typeof bountyOrderByRelevanceFieldEnum)[keyof typeof bountyOrderByRelevanceFieldEnum]


export const chat_messageOrderByRelevanceFieldEnum = {
  message: 'message',
  announcementType: 'announcementType'
} as const

export type chat_messageOrderByRelevanceFieldEnum = (typeof chat_messageOrderByRelevanceFieldEnum)[keyof typeof chat_messageOrderByRelevanceFieldEnum]


export const chat_roomOrderByRelevanceFieldEnum = {
  name: 'name'
} as const

export type chat_roomOrderByRelevanceFieldEnum = (typeof chat_roomOrderByRelevanceFieldEnum)[keyof typeof chat_roomOrderByRelevanceFieldEnum]


export const creatureOrderByRelevanceFieldEnum = {
  name: 'name',
  image: 'image'
} as const

export type creatureOrderByRelevanceFieldEnum = (typeof creatureOrderByRelevanceFieldEnum)[keyof typeof creatureOrderByRelevanceFieldEnum]


export const daily_missionOrderByRelevanceFieldEnum = {
  missionName: 'missionName',
  description: 'description'
} as const

export type daily_missionOrderByRelevanceFieldEnum = (typeof daily_missionOrderByRelevanceFieldEnum)[keyof typeof daily_missionOrderByRelevanceFieldEnum]


export const drop_chanceOrderByRelevanceFieldEnum = {
  scavengeType: 'scavengeType'
} as const

export type drop_chanceOrderByRelevanceFieldEnum = (typeof drop_chanceOrderByRelevanceFieldEnum)[keyof typeof drop_chanceOrderByRelevanceFieldEnum]


export const game_statsOrderByRelevanceFieldEnum = {
  stats_type: 'stats_type',
  info: 'info'
} as const

export type game_statsOrderByRelevanceFieldEnum = (typeof game_statsOrderByRelevanceFieldEnum)[keyof typeof game_statsOrderByRelevanceFieldEnum]


export const gangOrderByRelevanceFieldEnum = {
  name: 'name',
  about: 'about',
  avatar: 'avatar',
  gangMOTD: 'gangMOTD'
} as const

export type gangOrderByRelevanceFieldEnum = (typeof gangOrderByRelevanceFieldEnum)[keyof typeof gangOrderByRelevanceFieldEnum]


export const gang_logOrderByRelevanceFieldEnum = {
  action: 'action',
  info: 'info'
} as const

export type gang_logOrderByRelevanceFieldEnum = (typeof gang_logOrderByRelevanceFieldEnum)[keyof typeof gang_logOrderByRelevanceFieldEnum]


export const itemOrderByRelevanceFieldEnum = {
  name: 'name',
  about: 'about',
  image: 'image'
} as const

export type itemOrderByRelevanceFieldEnum = (typeof itemOrderByRelevanceFieldEnum)[keyof typeof itemOrderByRelevanceFieldEnum]


export const jobOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  avatar: 'avatar',
  payFormula: 'payFormula',
  strengthFormula: 'strengthFormula',
  intelligenceFormula: 'intelligenceFormula',
  dexterityFormula: 'dexterityFormula',
  defenceFormula: 'defenceFormula',
  enduranceFormula: 'enduranceFormula',
  vitalityFormula: 'vitalityFormula'
} as const

export type jobOrderByRelevanceFieldEnum = (typeof jobOrderByRelevanceFieldEnum)[keyof typeof jobOrderByRelevanceFieldEnum]


export const notificationOrderByRelevanceFieldEnum = {
  notificationType: 'notificationType',
  details: 'details'
} as const

export type notificationOrderByRelevanceFieldEnum = (typeof notificationOrderByRelevanceFieldEnum)[keyof typeof notificationOrderByRelevanceFieldEnum]


export const pollOrderByRelevanceFieldEnum = {
  title: 'title'
} as const

export type pollOrderByRelevanceFieldEnum = (typeof pollOrderByRelevanceFieldEnum)[keyof typeof pollOrderByRelevanceFieldEnum]


export const private_messageOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type private_messageOrderByRelevanceFieldEnum = (typeof private_messageOrderByRelevanceFieldEnum)[keyof typeof private_messageOrderByRelevanceFieldEnum]


export const profile_commentOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type profile_commentOrderByRelevanceFieldEnum = (typeof profile_commentOrderByRelevanceFieldEnum)[keyof typeof profile_commentOrderByRelevanceFieldEnum]


export const push_tokenOrderByRelevanceFieldEnum = {
  token: 'token'
} as const

export type push_tokenOrderByRelevanceFieldEnum = (typeof push_tokenOrderByRelevanceFieldEnum)[keyof typeof push_tokenOrderByRelevanceFieldEnum]


export const daily_questOrderByRelevanceFieldEnum = {
  targetAction: 'targetAction'
} as const

export type daily_questOrderByRelevanceFieldEnum = (typeof daily_questOrderByRelevanceFieldEnum)[keyof typeof daily_questOrderByRelevanceFieldEnum]


export const questOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  questInfo: 'questInfo',
  questChainName: 'questChainName'
} as const

export type questOrderByRelevanceFieldEnum = (typeof questOrderByRelevanceFieldEnum)[keyof typeof questOrderByRelevanceFieldEnum]


export const quest_objectiveOrderByRelevanceFieldEnum = {
  description: 'description',
  targetAction: 'targetAction'
} as const

export type quest_objectiveOrderByRelevanceFieldEnum = (typeof quest_objectiveOrderByRelevanceFieldEnum)[keyof typeof quest_objectiveOrderByRelevanceFieldEnum]


export const registration_codeOrderByRelevanceFieldEnum = {
  code: 'code',
  note: 'note'
} as const

export type registration_codeOrderByRelevanceFieldEnum = (typeof registration_codeOrderByRelevanceFieldEnum)[keyof typeof registration_codeOrderByRelevanceFieldEnum]


export const shopOrderByRelevanceFieldEnum = {
  name: 'name',
  avatar: 'avatar',
  description: 'description'
} as const

export type shopOrderByRelevanceFieldEnum = (typeof shopOrderByRelevanceFieldEnum)[keyof typeof shopOrderByRelevanceFieldEnum]


export const suggestionOrderByRelevanceFieldEnum = {
  title: 'title',
  content: 'content'
} as const

export type suggestionOrderByRelevanceFieldEnum = (typeof suggestionOrderByRelevanceFieldEnum)[keyof typeof suggestionOrderByRelevanceFieldEnum]


export const suggestion_commentOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type suggestion_commentOrderByRelevanceFieldEnum = (typeof suggestion_commentOrderByRelevanceFieldEnum)[keyof typeof suggestion_commentOrderByRelevanceFieldEnum]


export const talentOrderByRelevanceFieldEnum = {
  name: 'name',
  displayName: 'displayName',
  description: 'description'
} as const

export type talentOrderByRelevanceFieldEnum = (typeof talentOrderByRelevanceFieldEnum)[keyof typeof talentOrderByRelevanceFieldEnum]


export const userOrderByRelevanceFieldEnum = {
  username: 'username',
  displayUsername: 'displayUsername',
  about: 'about',
  email: 'email',
  banReason: 'banReason',
  avatar: 'avatar',
  profileBanner: 'profileBanner',
  jailReason: 'jailReason',
  hospitalisedReason: 'hospitalisedReason',
  class: 'class',
  adminNotes: 'adminNotes',
  discordID: 'discordID',
  statusMessage: 'statusMessage'
} as const

export type userOrderByRelevanceFieldEnum = (typeof userOrderByRelevanceFieldEnum)[keyof typeof userOrderByRelevanceFieldEnum]


export const sessionOrderByRelevanceFieldEnum = {
  token: 'token',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  impersonatedBy: 'impersonatedBy'
} as const

export type sessionOrderByRelevanceFieldEnum = (typeof sessionOrderByRelevanceFieldEnum)[keyof typeof sessionOrderByRelevanceFieldEnum]


export const accountOrderByRelevanceFieldEnum = {
  accountId: 'accountId',
  providerId: 'providerId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  scope: 'scope',
  password: 'password'
} as const

export type accountOrderByRelevanceFieldEnum = (typeof accountOrderByRelevanceFieldEnum)[keyof typeof accountOrderByRelevanceFieldEnum]


export const verificationOrderByRelevanceFieldEnum = {
  identifier: 'identifier',
  value: 'value'
} as const

export type verificationOrderByRelevanceFieldEnum = (typeof verificationOrderByRelevanceFieldEnum)[keyof typeof verificationOrderByRelevanceFieldEnum]


export const propertyOrderByRelevanceFieldEnum = {
  name: 'name',
  propertyType: 'propertyType',
  description: 'description',
  image: 'image'
} as const

export type propertyOrderByRelevanceFieldEnum = (typeof propertyOrderByRelevanceFieldEnum)[keyof typeof propertyOrderByRelevanceFieldEnum]


export const petOrderByRelevanceFieldEnum = {
  name: 'name',
  species: 'species'
} as const

export type petOrderByRelevanceFieldEnum = (typeof petOrderByRelevanceFieldEnum)[keyof typeof petOrderByRelevanceFieldEnum]


export const user_petOrderByRelevanceFieldEnum = {
  name: 'name'
} as const

export type user_petOrderByRelevanceFieldEnum = (typeof user_petOrderByRelevanceFieldEnum)[keyof typeof user_petOrderByRelevanceFieldEnum]


export const friendOrderByRelevanceFieldEnum = {
  note: 'note'
} as const

export type friendOrderByRelevanceFieldEnum = (typeof friendOrderByRelevanceFieldEnum)[keyof typeof friendOrderByRelevanceFieldEnum]


export const rivalOrderByRelevanceFieldEnum = {
  note: 'note'
} as const

export type rivalOrderByRelevanceFieldEnum = (typeof rivalOrderByRelevanceFieldEnum)[keyof typeof rivalOrderByRelevanceFieldEnum]


export const status_effectOrderByRelevanceFieldEnum = {
  name: 'name',
  source: 'source',
  category: 'category',
  description: 'description'
} as const

export type status_effectOrderByRelevanceFieldEnum = (typeof status_effectOrderByRelevanceFieldEnum)[keyof typeof status_effectOrderByRelevanceFieldEnum]


export const user_status_effectOrderByRelevanceFieldEnum = {
  customName: 'customName'
} as const

export type user_status_effectOrderByRelevanceFieldEnum = (typeof user_status_effectOrderByRelevanceFieldEnum)[keyof typeof user_status_effectOrderByRelevanceFieldEnum]


export const explore_static_nodeOrderByRelevanceFieldEnum = {
  title: 'title',
  description: 'description'
} as const

export type explore_static_nodeOrderByRelevanceFieldEnum = (typeof explore_static_nodeOrderByRelevanceFieldEnum)[keyof typeof explore_static_nodeOrderByRelevanceFieldEnum]


export const explore_player_nodeOrderByRelevanceFieldEnum = {
  title: 'title',
  description: 'description'
} as const

export type explore_player_nodeOrderByRelevanceFieldEnum = (typeof explore_player_nodeOrderByRelevanceFieldEnum)[keyof typeof explore_player_nodeOrderByRelevanceFieldEnum]


export const story_seasonOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_seasonOrderByRelevanceFieldEnum = (typeof story_seasonOrderByRelevanceFieldEnum)[keyof typeof story_seasonOrderByRelevanceFieldEnum]


export const story_chapterOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_chapterOrderByRelevanceFieldEnum = (typeof story_chapterOrderByRelevanceFieldEnum)[keyof typeof story_chapterOrderByRelevanceFieldEnum]


export const story_episodeOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_episodeOrderByRelevanceFieldEnum = (typeof story_episodeOrderByRelevanceFieldEnum)[keyof typeof story_episodeOrderByRelevanceFieldEnum]

