
/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/client"
import type * as Prisma from "../models.js"
import { type PrismaClient } from "./class.js"

export type * from '../models.js'

export type DMMF = typeof runtime.DMMF

export type PrismaPromise<T> = runtime.Types.Public.PrismaPromise<T>

/**
 * Prisma Errors
 */

export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
export type PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError

export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
export type PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError

export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
export type PrismaClientRustPanicError = runtime.PrismaClientRustPanicError

export const PrismaClientInitializationError = runtime.PrismaClientInitializationError
export type PrismaClientInitializationError = runtime.PrismaClientInitializationError

export const PrismaClientValidationError = runtime.PrismaClientValidationError
export type PrismaClientValidationError = runtime.PrismaClientValidationError

/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag
export const empty = runtime.empty
export const join = runtime.join
export const raw = runtime.raw
export const Sql = runtime.Sql
export type Sql = runtime.Sql



/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal
export type Decimal = runtime.Decimal

export type DecimalJsLike = runtime.DecimalJsLike

/**
 * Metrics
 */
export type Metrics = runtime.Metrics
export type Metric<T> = runtime.Metric<T>
export type MetricHistogram = runtime.MetricHistogram
export type MetricHistogramBucket = runtime.MetricHistogramBucket

/**
* Extensions
*/
export type Extension = runtime.Types.Extensions.UserArgs
export const getExtensionContext = runtime.Extensions.getExtensionContext
export type Args<T, F extends runtime.Operation> = runtime.Types.Public.Args<T, F>
export type Payload<T, F extends runtime.Operation = never> = runtime.Types.Public.Payload<T, F>
export type Result<T, A, F extends runtime.Operation> = runtime.Types.Public.Result<T, A, F>
export type Exact<A, W> = runtime.Types.Public.Exact<A, W>

export type PrismaVersion = {
  client: string
  engine: string
}

/**
 * Prisma Client JS version: 6.17.1
 * Query Engine version: 272a37d34178c2894197e17273bf937f25acdeac
 */
export const prismaVersion: PrismaVersion = {
  client: "6.17.1",
  engine: "272a37d34178c2894197e17273bf937f25acdeac"
}

/**
 * Utility Types
 */

export type JsonObject = runtime.JsonObject
export type JsonArray = runtime.JsonArray
export type JsonValue = runtime.JsonValue
export type InputJsonObject = runtime.InputJsonObject
export type InputJsonArray = runtime.InputJsonArray
export type InputJsonValue = runtime.InputJsonValue


export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.DbNull),
  JsonNull: runtime.objectEnumValues.classes.JsonNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.JsonNull),
  AnyNull: runtime.objectEnumValues.classes.AnyNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.AnyNull),
}
/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull
/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull
/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull


type SelectAndInclude = {
  select: any
  include: any
}

type SelectAndOmit = {
  select: any
  omit: any
}

/**
 * From T, pick a set of properties whose keys are in the union K
 */
type Prisma__Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

export type Enumerable<T> = T | Array<T>;

/**
 * Subset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
 */
export type Subset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never;
};

/**
 * SelectSubset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
 * Additionally, it validates, if both select and include are present. If the case, it errors.
 */
export type SelectSubset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  (T extends SelectAndInclude
    ? 'Please either choose `select` or `include`.'
    : T extends SelectAndOmit
      ? 'Please either choose `select` or `omit`.'
      : {})

/**
 * Subset + Intersection
 * @desc From `T` pick properties that exist in `U` and intersect `K`
 */
export type SubsetIntersection<T, U, K> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  K

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

/**
 * XOR is needed to have a real mutually exclusive union type
 * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
 */
export type XOR<T, U> =
  T extends object ?
  U extends object ?
    (Without<T, U> & U) | (Without<U, T> & T)
  : U : T


/**
 * Is T a Record?
 */
type IsObject<T extends any> = T extends Array<any>
? False
: T extends Date
? False
: T extends Uint8Array
? False
: T extends BigInt
? False
: T extends object
? True
: False


/**
 * If it's T[], return T
 */
export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

/**
 * From ts-toolbelt
 */

type __Either<O extends object, K extends Key> = Omit<O, K> &
  {
    // Merge all but K
    [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
  }[K]

type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

type _Either<
  O extends object,
  K extends Key,
  strict extends Boolean
> = {
  1: EitherStrict<O, K>
  0: EitherLoose<O, K>
}[strict]

export type Either<
  O extends object,
  K extends Key,
  strict extends Boolean = 1
> = O extends unknown ? _Either<O, K, strict> : never

export type Union = any

export type PatchUndefined<O extends object, O1 extends object> = {
  [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
} & {}

/** Helper Types for "Merge" **/
export type IntersectOf<U extends Union> = (
  U extends unknown ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never

export type Overwrite<O extends object, O1 extends object> = {
    [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
} & {};

type _Merge<U extends object> = IntersectOf<Overwrite<U, {
    [K in keyof U]-?: At<U, K>;
}>>;

type Key = string | number | symbol;
type AtStrict<O extends object, K extends Key> = O[K & keyof O];
type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
    1: AtStrict<O, K>;
    0: AtLoose<O, K>;
}[strict];

export type ComputeRaw<A extends any> = A extends Function ? A : {
  [K in keyof A]: A[K];
} & {};

export type OptionalFlat<O> = {
  [K in keyof O]?: O[K];
} & {};

type _Record<K extends keyof any, T> = {
  [P in K]: T;
};

// cause typescript not to expand types and preserve names
type NoExpand<T> = T extends unknown ? T : never;

// this type assumes the passed object is entirely optional
export type AtLeast<O extends object, K extends string> = NoExpand<
  O extends unknown
  ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
    | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
  : never>;

type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
/** End Helper Types for "Merge" **/

export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

export type Boolean = True | False

export type True = 1

export type False = 0

export type Not<B extends Boolean> = {
  0: 1
  1: 0
}[B]

export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
  ? 0 // anything `never` is false
  : A1 extends A2
  ? 1
  : 0

export type Has<U extends Union, U1 extends Union> = Not<
  Extends<Exclude<U1, U>, U1>
>

export type Or<B1 extends Boolean, B2 extends Boolean> = {
  0: {
    0: 0
    1: 1
  }
  1: {
    0: 1
    1: 1
  }
}[B1][B2]

export type Keys<U extends Union> = U extends unknown ? keyof U : never

export type GetScalarType<T, O> = O extends object ? {
  [P in keyof T]: P extends keyof O
    ? O[P]
    : never
} : never

type FieldPaths<
  T,
  U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
> = IsObject<T> extends True ? U : T

export type GetHavingFields<T> = {
  [K in keyof T]: Or<
    Or<Extends<'OR', K>, Extends<'AND', K>>,
    Extends<'NOT', K>
  > extends True
    ? // infer is only needed to not hit TS limit
      // based on the brilliant idea of Pierre-Antoine Mills
      // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
      T[K] extends infer TK
      ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
      : never
    : {} extends FieldPaths<T[K]>
    ? never
    : K
}[keyof T]

/**
 * Convert tuple to union
 */
type _TupleToUnion<T> = T extends (infer E)[] ? E : never
type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
export type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

/**
 * Like `Pick`, but additionally can also accept an array of keys
 */
export type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

/**
 * Exclude all keys with underscores
 */
export type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


export const ModelName = {
  action_log: 'action_log',
  game_config: 'game_config',
  auction_item: 'auction_item',
  bank_transaction: 'bank_transaction',
  battle_log: 'battle_log',
  bounty: 'bounty',
  chat_message: 'chat_message',
  chat_room: 'chat_room',
  crafting_recipe: 'crafting_recipe',
  creature: 'creature',
  daily_mission: 'daily_mission',
  drop_chance: 'drop_chance',
  equipped_item: 'equipped_item',
  game_stats: 'game_stats',
  gang: 'gang',
  gang_invite: 'gang_invite',
  gang_log: 'gang_log',
  gang_member: 'gang_member',
  item: 'item',
  job: 'job',
  lottery: 'lottery',
  lottery_entry: 'lottery_entry',
  notification: 'notification',
  poll: 'poll',
  poll_response: 'poll_response',
  private_message: 'private_message',
  profile_comment: 'profile_comment',
  push_token: 'push_token',
  daily_quest: 'daily_quest',
  quest: 'quest',
  quest_progress: 'quest_progress',
  quest_objective: 'quest_objective',
  quest_objective_progress: 'quest_objective_progress',
  quest_reward: 'quest_reward',
  recipe_item: 'recipe_item',
  registration_code: 'registration_code',
  shop: 'shop',
  shop_listing: 'shop_listing',
  shrine_donation: 'shrine_donation',
  shrine_goal: 'shrine_goal',
  suggestion: 'suggestion',
  suggestion_comment: 'suggestion_comment',
  suggestion_vote: 'suggestion_vote',
  talent: 'talent',
  trader_rep: 'trader_rep',
  user: 'user',
  user_equipped_abilities: 'user_equipped_abilities',
  session: 'session',
  account: 'account',
  verification: 'verification',
  user_item: 'user_item',
  user_recipe: 'user_recipe',
  user_completed_course: 'user_completed_course',
  user_talent: 'user_talent',
  user_achievements: 'user_achievements',
  user_crafting_queue: 'user_crafting_queue',
  property: 'property',
  user_property: 'user_property',
  pet: 'pet',
  user_pet: 'user_pet',
  friend_request: 'friend_request',
  friend: 'friend',
  rival: 'rival',
  status_effect: 'status_effect',
  user_status_effect: 'user_status_effect',
  user_skill: 'user_skill',
  explore_static_node: 'explore_static_node',
  explore_player_node: 'explore_player_node',
  story_season: 'story_season',
  story_chapter: 'story_chapter',
  story_episode: 'story_episode'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]



export interface TypeMapCb<GlobalOmitOptions = {}> extends runtime.Types.Utils.Fn<{extArgs: runtime.Types.Extensions.InternalArgs }, runtime.Types.Utils.Record<string, any>> {
  returns: TypeMap<this['params']['extArgs'], GlobalOmitOptions>
}

export type TypeMap<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
  globalOmitOptions: {
    omit: GlobalOmitOptions
  }
  meta: {
    modelProps: "action_log" | "game_config" | "auction_item" | "bank_transaction" | "battle_log" | "bounty" | "chat_message" | "chat_room" | "crafting_recipe" | "creature" | "daily_mission" | "drop_chance" | "equipped_item" | "game_stats" | "gang" | "gang_invite" | "gang_log" | "gang_member" | "item" | "job" | "lottery" | "lottery_entry" | "notification" | "poll" | "poll_response" | "private_message" | "profile_comment" | "push_token" | "daily_quest" | "quest" | "quest_progress" | "quest_objective" | "quest_objective_progress" | "quest_reward" | "recipe_item" | "registration_code" | "shop" | "shop_listing" | "shrine_donation" | "shrine_goal" | "suggestion" | "suggestion_comment" | "suggestion_vote" | "talent" | "trader_rep" | "user" | "user_equipped_abilities" | "session" | "account" | "verification" | "user_item" | "user_recipe" | "user_completed_course" | "user_talent" | "user_achievements" | "user_crafting_queue" | "property" | "user_property" | "pet" | "user_pet" | "friend_request" | "friend" | "rival" | "status_effect" | "user_status_effect" | "user_skill" | "explore_static_node" | "explore_player_node" | "story_season" | "story_chapter" | "story_episode"
    txIsolationLevel: TransactionIsolationLevel
  }
  model: {
    action_log: {
      payload: Prisma.$action_logPayload<ExtArgs>
      fields: Prisma.action_logFieldRefs
      operations: {
        findUnique: {
          args: Prisma.action_logFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.action_logFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        findFirst: {
          args: Prisma.action_logFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.action_logFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        findMany: {
          args: Prisma.action_logFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>[]
        }
        create: {
          args: Prisma.action_logCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        createMany: {
          args: Prisma.action_logCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.action_logDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        update: {
          args: Prisma.action_logUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        deleteMany: {
          args: Prisma.action_logDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.action_logUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.action_logUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$action_logPayload>
        }
        aggregate: {
          args: Prisma.Action_logAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAction_log>
        }
        groupBy: {
          args: Prisma.action_logGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Action_logGroupByOutputType>[]
        }
        count: {
          args: Prisma.action_logCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Action_logCountAggregateOutputType> | number
        }
      }
    }
    game_config: {
      payload: Prisma.$game_configPayload<ExtArgs>
      fields: Prisma.game_configFieldRefs
      operations: {
        findUnique: {
          args: Prisma.game_configFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.game_configFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        findFirst: {
          args: Prisma.game_configFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.game_configFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        findMany: {
          args: Prisma.game_configFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>[]
        }
        create: {
          args: Prisma.game_configCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        createMany: {
          args: Prisma.game_configCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.game_configDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        update: {
          args: Prisma.game_configUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        deleteMany: {
          args: Prisma.game_configDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.game_configUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.game_configUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_configPayload>
        }
        aggregate: {
          args: Prisma.Game_configAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGame_config>
        }
        groupBy: {
          args: Prisma.game_configGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Game_configGroupByOutputType>[]
        }
        count: {
          args: Prisma.game_configCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Game_configCountAggregateOutputType> | number
        }
      }
    }
    auction_item: {
      payload: Prisma.$auction_itemPayload<ExtArgs>
      fields: Prisma.auction_itemFieldRefs
      operations: {
        findUnique: {
          args: Prisma.auction_itemFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.auction_itemFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        findFirst: {
          args: Prisma.auction_itemFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.auction_itemFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        findMany: {
          args: Prisma.auction_itemFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>[]
        }
        create: {
          args: Prisma.auction_itemCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        createMany: {
          args: Prisma.auction_itemCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.auction_itemDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        update: {
          args: Prisma.auction_itemUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        deleteMany: {
          args: Prisma.auction_itemDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.auction_itemUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.auction_itemUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$auction_itemPayload>
        }
        aggregate: {
          args: Prisma.Auction_itemAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAuction_item>
        }
        groupBy: {
          args: Prisma.auction_itemGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Auction_itemGroupByOutputType>[]
        }
        count: {
          args: Prisma.auction_itemCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Auction_itemCountAggregateOutputType> | number
        }
      }
    }
    bank_transaction: {
      payload: Prisma.$bank_transactionPayload<ExtArgs>
      fields: Prisma.bank_transactionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.bank_transactionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.bank_transactionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        findFirst: {
          args: Prisma.bank_transactionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.bank_transactionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        findMany: {
          args: Prisma.bank_transactionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>[]
        }
        create: {
          args: Prisma.bank_transactionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        createMany: {
          args: Prisma.bank_transactionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.bank_transactionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        update: {
          args: Prisma.bank_transactionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        deleteMany: {
          args: Prisma.bank_transactionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.bank_transactionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.bank_transactionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bank_transactionPayload>
        }
        aggregate: {
          args: Prisma.Bank_transactionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateBank_transaction>
        }
        groupBy: {
          args: Prisma.bank_transactionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Bank_transactionGroupByOutputType>[]
        }
        count: {
          args: Prisma.bank_transactionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Bank_transactionCountAggregateOutputType> | number
        }
      }
    }
    battle_log: {
      payload: Prisma.$battle_logPayload<ExtArgs>
      fields: Prisma.battle_logFieldRefs
      operations: {
        findUnique: {
          args: Prisma.battle_logFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.battle_logFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        findFirst: {
          args: Prisma.battle_logFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.battle_logFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        findMany: {
          args: Prisma.battle_logFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>[]
        }
        create: {
          args: Prisma.battle_logCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        createMany: {
          args: Prisma.battle_logCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.battle_logDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        update: {
          args: Prisma.battle_logUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        deleteMany: {
          args: Prisma.battle_logDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.battle_logUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.battle_logUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$battle_logPayload>
        }
        aggregate: {
          args: Prisma.Battle_logAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateBattle_log>
        }
        groupBy: {
          args: Prisma.battle_logGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Battle_logGroupByOutputType>[]
        }
        count: {
          args: Prisma.battle_logCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Battle_logCountAggregateOutputType> | number
        }
      }
    }
    bounty: {
      payload: Prisma.$bountyPayload<ExtArgs>
      fields: Prisma.bountyFieldRefs
      operations: {
        findUnique: {
          args: Prisma.bountyFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.bountyFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        findFirst: {
          args: Prisma.bountyFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.bountyFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        findMany: {
          args: Prisma.bountyFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>[]
        }
        create: {
          args: Prisma.bountyCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        createMany: {
          args: Prisma.bountyCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.bountyDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        update: {
          args: Prisma.bountyUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        deleteMany: {
          args: Prisma.bountyDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.bountyUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.bountyUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$bountyPayload>
        }
        aggregate: {
          args: Prisma.BountyAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateBounty>
        }
        groupBy: {
          args: Prisma.bountyGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.BountyGroupByOutputType>[]
        }
        count: {
          args: Prisma.bountyCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.BountyCountAggregateOutputType> | number
        }
      }
    }
    chat_message: {
      payload: Prisma.$chat_messagePayload<ExtArgs>
      fields: Prisma.chat_messageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.chat_messageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.chat_messageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        findFirst: {
          args: Prisma.chat_messageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.chat_messageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        findMany: {
          args: Prisma.chat_messageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>[]
        }
        create: {
          args: Prisma.chat_messageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        createMany: {
          args: Prisma.chat_messageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.chat_messageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        update: {
          args: Prisma.chat_messageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        deleteMany: {
          args: Prisma.chat_messageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.chat_messageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.chat_messageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_messagePayload>
        }
        aggregate: {
          args: Prisma.Chat_messageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateChat_message>
        }
        groupBy: {
          args: Prisma.chat_messageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Chat_messageGroupByOutputType>[]
        }
        count: {
          args: Prisma.chat_messageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Chat_messageCountAggregateOutputType> | number
        }
      }
    }
    chat_room: {
      payload: Prisma.$chat_roomPayload<ExtArgs>
      fields: Prisma.chat_roomFieldRefs
      operations: {
        findUnique: {
          args: Prisma.chat_roomFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.chat_roomFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        findFirst: {
          args: Prisma.chat_roomFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.chat_roomFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        findMany: {
          args: Prisma.chat_roomFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>[]
        }
        create: {
          args: Prisma.chat_roomCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        createMany: {
          args: Prisma.chat_roomCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.chat_roomDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        update: {
          args: Prisma.chat_roomUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        deleteMany: {
          args: Prisma.chat_roomDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.chat_roomUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.chat_roomUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$chat_roomPayload>
        }
        aggregate: {
          args: Prisma.Chat_roomAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateChat_room>
        }
        groupBy: {
          args: Prisma.chat_roomGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Chat_roomGroupByOutputType>[]
        }
        count: {
          args: Prisma.chat_roomCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Chat_roomCountAggregateOutputType> | number
        }
      }
    }
    crafting_recipe: {
      payload: Prisma.$crafting_recipePayload<ExtArgs>
      fields: Prisma.crafting_recipeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.crafting_recipeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.crafting_recipeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        findFirst: {
          args: Prisma.crafting_recipeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.crafting_recipeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        findMany: {
          args: Prisma.crafting_recipeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>[]
        }
        create: {
          args: Prisma.crafting_recipeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        createMany: {
          args: Prisma.crafting_recipeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.crafting_recipeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        update: {
          args: Prisma.crafting_recipeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        deleteMany: {
          args: Prisma.crafting_recipeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.crafting_recipeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.crafting_recipeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$crafting_recipePayload>
        }
        aggregate: {
          args: Prisma.Crafting_recipeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateCrafting_recipe>
        }
        groupBy: {
          args: Prisma.crafting_recipeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Crafting_recipeGroupByOutputType>[]
        }
        count: {
          args: Prisma.crafting_recipeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Crafting_recipeCountAggregateOutputType> | number
        }
      }
    }
    creature: {
      payload: Prisma.$creaturePayload<ExtArgs>
      fields: Prisma.creatureFieldRefs
      operations: {
        findUnique: {
          args: Prisma.creatureFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.creatureFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        findFirst: {
          args: Prisma.creatureFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.creatureFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        findMany: {
          args: Prisma.creatureFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>[]
        }
        create: {
          args: Prisma.creatureCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        createMany: {
          args: Prisma.creatureCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.creatureDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        update: {
          args: Prisma.creatureUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        deleteMany: {
          args: Prisma.creatureDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.creatureUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.creatureUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$creaturePayload>
        }
        aggregate: {
          args: Prisma.CreatureAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateCreature>
        }
        groupBy: {
          args: Prisma.creatureGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.CreatureGroupByOutputType>[]
        }
        count: {
          args: Prisma.creatureCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.CreatureCountAggregateOutputType> | number
        }
      }
    }
    daily_mission: {
      payload: Prisma.$daily_missionPayload<ExtArgs>
      fields: Prisma.daily_missionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.daily_missionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.daily_missionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        findFirst: {
          args: Prisma.daily_missionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.daily_missionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        findMany: {
          args: Prisma.daily_missionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>[]
        }
        create: {
          args: Prisma.daily_missionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        createMany: {
          args: Prisma.daily_missionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.daily_missionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        update: {
          args: Prisma.daily_missionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        deleteMany: {
          args: Prisma.daily_missionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.daily_missionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.daily_missionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_missionPayload>
        }
        aggregate: {
          args: Prisma.Daily_missionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDaily_mission>
        }
        groupBy: {
          args: Prisma.daily_missionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Daily_missionGroupByOutputType>[]
        }
        count: {
          args: Prisma.daily_missionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Daily_missionCountAggregateOutputType> | number
        }
      }
    }
    drop_chance: {
      payload: Prisma.$drop_chancePayload<ExtArgs>
      fields: Prisma.drop_chanceFieldRefs
      operations: {
        findUnique: {
          args: Prisma.drop_chanceFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.drop_chanceFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        findFirst: {
          args: Prisma.drop_chanceFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.drop_chanceFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        findMany: {
          args: Prisma.drop_chanceFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>[]
        }
        create: {
          args: Prisma.drop_chanceCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        createMany: {
          args: Prisma.drop_chanceCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.drop_chanceDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        update: {
          args: Prisma.drop_chanceUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        deleteMany: {
          args: Prisma.drop_chanceDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.drop_chanceUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.drop_chanceUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$drop_chancePayload>
        }
        aggregate: {
          args: Prisma.Drop_chanceAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDrop_chance>
        }
        groupBy: {
          args: Prisma.drop_chanceGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Drop_chanceGroupByOutputType>[]
        }
        count: {
          args: Prisma.drop_chanceCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Drop_chanceCountAggregateOutputType> | number
        }
      }
    }
    equipped_item: {
      payload: Prisma.$equipped_itemPayload<ExtArgs>
      fields: Prisma.equipped_itemFieldRefs
      operations: {
        findUnique: {
          args: Prisma.equipped_itemFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.equipped_itemFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        findFirst: {
          args: Prisma.equipped_itemFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.equipped_itemFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        findMany: {
          args: Prisma.equipped_itemFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>[]
        }
        create: {
          args: Prisma.equipped_itemCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        createMany: {
          args: Prisma.equipped_itemCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.equipped_itemDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        update: {
          args: Prisma.equipped_itemUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        deleteMany: {
          args: Prisma.equipped_itemDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.equipped_itemUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.equipped_itemUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$equipped_itemPayload>
        }
        aggregate: {
          args: Prisma.Equipped_itemAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateEquipped_item>
        }
        groupBy: {
          args: Prisma.equipped_itemGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Equipped_itemGroupByOutputType>[]
        }
        count: {
          args: Prisma.equipped_itemCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Equipped_itemCountAggregateOutputType> | number
        }
      }
    }
    game_stats: {
      payload: Prisma.$game_statsPayload<ExtArgs>
      fields: Prisma.game_statsFieldRefs
      operations: {
        findUnique: {
          args: Prisma.game_statsFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.game_statsFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        findFirst: {
          args: Prisma.game_statsFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.game_statsFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        findMany: {
          args: Prisma.game_statsFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>[]
        }
        create: {
          args: Prisma.game_statsCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        createMany: {
          args: Prisma.game_statsCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.game_statsDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        update: {
          args: Prisma.game_statsUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        deleteMany: {
          args: Prisma.game_statsDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.game_statsUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.game_statsUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$game_statsPayload>
        }
        aggregate: {
          args: Prisma.Game_statsAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGame_stats>
        }
        groupBy: {
          args: Prisma.game_statsGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Game_statsGroupByOutputType>[]
        }
        count: {
          args: Prisma.game_statsCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Game_statsCountAggregateOutputType> | number
        }
      }
    }
    gang: {
      payload: Prisma.$gangPayload<ExtArgs>
      fields: Prisma.gangFieldRefs
      operations: {
        findUnique: {
          args: Prisma.gangFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.gangFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        findFirst: {
          args: Prisma.gangFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.gangFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        findMany: {
          args: Prisma.gangFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>[]
        }
        create: {
          args: Prisma.gangCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        createMany: {
          args: Prisma.gangCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.gangDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        update: {
          args: Prisma.gangUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        deleteMany: {
          args: Prisma.gangDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.gangUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.gangUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gangPayload>
        }
        aggregate: {
          args: Prisma.GangAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGang>
        }
        groupBy: {
          args: Prisma.gangGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.GangGroupByOutputType>[]
        }
        count: {
          args: Prisma.gangCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.GangCountAggregateOutputType> | number
        }
      }
    }
    gang_invite: {
      payload: Prisma.$gang_invitePayload<ExtArgs>
      fields: Prisma.gang_inviteFieldRefs
      operations: {
        findUnique: {
          args: Prisma.gang_inviteFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.gang_inviteFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        findFirst: {
          args: Prisma.gang_inviteFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.gang_inviteFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        findMany: {
          args: Prisma.gang_inviteFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>[]
        }
        create: {
          args: Prisma.gang_inviteCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        createMany: {
          args: Prisma.gang_inviteCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.gang_inviteDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        update: {
          args: Prisma.gang_inviteUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        deleteMany: {
          args: Prisma.gang_inviteDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.gang_inviteUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.gang_inviteUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_invitePayload>
        }
        aggregate: {
          args: Prisma.Gang_inviteAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGang_invite>
        }
        groupBy: {
          args: Prisma.gang_inviteGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_inviteGroupByOutputType>[]
        }
        count: {
          args: Prisma.gang_inviteCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_inviteCountAggregateOutputType> | number
        }
      }
    }
    gang_log: {
      payload: Prisma.$gang_logPayload<ExtArgs>
      fields: Prisma.gang_logFieldRefs
      operations: {
        findUnique: {
          args: Prisma.gang_logFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.gang_logFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        findFirst: {
          args: Prisma.gang_logFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.gang_logFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        findMany: {
          args: Prisma.gang_logFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>[]
        }
        create: {
          args: Prisma.gang_logCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        createMany: {
          args: Prisma.gang_logCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.gang_logDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        update: {
          args: Prisma.gang_logUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        deleteMany: {
          args: Prisma.gang_logDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.gang_logUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.gang_logUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_logPayload>
        }
        aggregate: {
          args: Prisma.Gang_logAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGang_log>
        }
        groupBy: {
          args: Prisma.gang_logGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_logGroupByOutputType>[]
        }
        count: {
          args: Prisma.gang_logCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_logCountAggregateOutputType> | number
        }
      }
    }
    gang_member: {
      payload: Prisma.$gang_memberPayload<ExtArgs>
      fields: Prisma.gang_memberFieldRefs
      operations: {
        findUnique: {
          args: Prisma.gang_memberFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.gang_memberFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        findFirst: {
          args: Prisma.gang_memberFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.gang_memberFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        findMany: {
          args: Prisma.gang_memberFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>[]
        }
        create: {
          args: Prisma.gang_memberCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        createMany: {
          args: Prisma.gang_memberCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.gang_memberDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        update: {
          args: Prisma.gang_memberUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        deleteMany: {
          args: Prisma.gang_memberDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.gang_memberUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.gang_memberUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$gang_memberPayload>
        }
        aggregate: {
          args: Prisma.Gang_memberAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGang_member>
        }
        groupBy: {
          args: Prisma.gang_memberGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_memberGroupByOutputType>[]
        }
        count: {
          args: Prisma.gang_memberCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Gang_memberCountAggregateOutputType> | number
        }
      }
    }
    item: {
      payload: Prisma.$itemPayload<ExtArgs>
      fields: Prisma.itemFieldRefs
      operations: {
        findUnique: {
          args: Prisma.itemFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.itemFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        findFirst: {
          args: Prisma.itemFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.itemFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        findMany: {
          args: Prisma.itemFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>[]
        }
        create: {
          args: Prisma.itemCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        createMany: {
          args: Prisma.itemCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.itemDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        update: {
          args: Prisma.itemUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        deleteMany: {
          args: Prisma.itemDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.itemUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.itemUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$itemPayload>
        }
        aggregate: {
          args: Prisma.ItemAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateItem>
        }
        groupBy: {
          args: Prisma.itemGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ItemGroupByOutputType>[]
        }
        count: {
          args: Prisma.itemCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ItemCountAggregateOutputType> | number
        }
      }
    }
    job: {
      payload: Prisma.$jobPayload<ExtArgs>
      fields: Prisma.jobFieldRefs
      operations: {
        findUnique: {
          args: Prisma.jobFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.jobFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        findFirst: {
          args: Prisma.jobFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.jobFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        findMany: {
          args: Prisma.jobFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>[]
        }
        create: {
          args: Prisma.jobCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        createMany: {
          args: Prisma.jobCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.jobDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        update: {
          args: Prisma.jobUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        deleteMany: {
          args: Prisma.jobDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.jobUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.jobUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$jobPayload>
        }
        aggregate: {
          args: Prisma.JobAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateJob>
        }
        groupBy: {
          args: Prisma.jobGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.JobGroupByOutputType>[]
        }
        count: {
          args: Prisma.jobCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.JobCountAggregateOutputType> | number
        }
      }
    }
    lottery: {
      payload: Prisma.$lotteryPayload<ExtArgs>
      fields: Prisma.lotteryFieldRefs
      operations: {
        findUnique: {
          args: Prisma.lotteryFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.lotteryFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        findFirst: {
          args: Prisma.lotteryFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.lotteryFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        findMany: {
          args: Prisma.lotteryFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>[]
        }
        create: {
          args: Prisma.lotteryCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        createMany: {
          args: Prisma.lotteryCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.lotteryDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        update: {
          args: Prisma.lotteryUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        deleteMany: {
          args: Prisma.lotteryDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.lotteryUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.lotteryUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lotteryPayload>
        }
        aggregate: {
          args: Prisma.LotteryAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLottery>
        }
        groupBy: {
          args: Prisma.lotteryGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LotteryGroupByOutputType>[]
        }
        count: {
          args: Prisma.lotteryCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LotteryCountAggregateOutputType> | number
        }
      }
    }
    lottery_entry: {
      payload: Prisma.$lottery_entryPayload<ExtArgs>
      fields: Prisma.lottery_entryFieldRefs
      operations: {
        findUnique: {
          args: Prisma.lottery_entryFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.lottery_entryFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        findFirst: {
          args: Prisma.lottery_entryFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.lottery_entryFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        findMany: {
          args: Prisma.lottery_entryFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>[]
        }
        create: {
          args: Prisma.lottery_entryCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        createMany: {
          args: Prisma.lottery_entryCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.lottery_entryDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        update: {
          args: Prisma.lottery_entryUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        deleteMany: {
          args: Prisma.lottery_entryDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.lottery_entryUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.lottery_entryUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$lottery_entryPayload>
        }
        aggregate: {
          args: Prisma.Lottery_entryAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLottery_entry>
        }
        groupBy: {
          args: Prisma.lottery_entryGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Lottery_entryGroupByOutputType>[]
        }
        count: {
          args: Prisma.lottery_entryCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Lottery_entryCountAggregateOutputType> | number
        }
      }
    }
    notification: {
      payload: Prisma.$notificationPayload<ExtArgs>
      fields: Prisma.notificationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.notificationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.notificationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        findFirst: {
          args: Prisma.notificationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.notificationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        findMany: {
          args: Prisma.notificationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>[]
        }
        create: {
          args: Prisma.notificationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        createMany: {
          args: Prisma.notificationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.notificationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        update: {
          args: Prisma.notificationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        deleteMany: {
          args: Prisma.notificationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.notificationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.notificationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$notificationPayload>
        }
        aggregate: {
          args: Prisma.NotificationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateNotification>
        }
        groupBy: {
          args: Prisma.notificationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NotificationGroupByOutputType>[]
        }
        count: {
          args: Prisma.notificationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NotificationCountAggregateOutputType> | number
        }
      }
    }
    poll: {
      payload: Prisma.$pollPayload<ExtArgs>
      fields: Prisma.pollFieldRefs
      operations: {
        findUnique: {
          args: Prisma.pollFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.pollFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        findFirst: {
          args: Prisma.pollFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.pollFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        findMany: {
          args: Prisma.pollFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>[]
        }
        create: {
          args: Prisma.pollCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        createMany: {
          args: Prisma.pollCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.pollDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        update: {
          args: Prisma.pollUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        deleteMany: {
          args: Prisma.pollDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.pollUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.pollUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$pollPayload>
        }
        aggregate: {
          args: Prisma.PollAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePoll>
        }
        groupBy: {
          args: Prisma.pollGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PollGroupByOutputType>[]
        }
        count: {
          args: Prisma.pollCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PollCountAggregateOutputType> | number
        }
      }
    }
    poll_response: {
      payload: Prisma.$poll_responsePayload<ExtArgs>
      fields: Prisma.poll_responseFieldRefs
      operations: {
        findUnique: {
          args: Prisma.poll_responseFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.poll_responseFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        findFirst: {
          args: Prisma.poll_responseFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.poll_responseFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        findMany: {
          args: Prisma.poll_responseFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>[]
        }
        create: {
          args: Prisma.poll_responseCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        createMany: {
          args: Prisma.poll_responseCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.poll_responseDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        update: {
          args: Prisma.poll_responseUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        deleteMany: {
          args: Prisma.poll_responseDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.poll_responseUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.poll_responseUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$poll_responsePayload>
        }
        aggregate: {
          args: Prisma.Poll_responseAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePoll_response>
        }
        groupBy: {
          args: Prisma.poll_responseGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Poll_responseGroupByOutputType>[]
        }
        count: {
          args: Prisma.poll_responseCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Poll_responseCountAggregateOutputType> | number
        }
      }
    }
    private_message: {
      payload: Prisma.$private_messagePayload<ExtArgs>
      fields: Prisma.private_messageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.private_messageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.private_messageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        findFirst: {
          args: Prisma.private_messageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.private_messageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        findMany: {
          args: Prisma.private_messageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>[]
        }
        create: {
          args: Prisma.private_messageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        createMany: {
          args: Prisma.private_messageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.private_messageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        update: {
          args: Prisma.private_messageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        deleteMany: {
          args: Prisma.private_messageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.private_messageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.private_messageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$private_messagePayload>
        }
        aggregate: {
          args: Prisma.Private_messageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePrivate_message>
        }
        groupBy: {
          args: Prisma.private_messageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Private_messageGroupByOutputType>[]
        }
        count: {
          args: Prisma.private_messageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Private_messageCountAggregateOutputType> | number
        }
      }
    }
    profile_comment: {
      payload: Prisma.$profile_commentPayload<ExtArgs>
      fields: Prisma.profile_commentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.profile_commentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.profile_commentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        findFirst: {
          args: Prisma.profile_commentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.profile_commentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        findMany: {
          args: Prisma.profile_commentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>[]
        }
        create: {
          args: Prisma.profile_commentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        createMany: {
          args: Prisma.profile_commentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.profile_commentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        update: {
          args: Prisma.profile_commentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        deleteMany: {
          args: Prisma.profile_commentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.profile_commentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.profile_commentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$profile_commentPayload>
        }
        aggregate: {
          args: Prisma.Profile_commentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateProfile_comment>
        }
        groupBy: {
          args: Prisma.profile_commentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Profile_commentGroupByOutputType>[]
        }
        count: {
          args: Prisma.profile_commentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Profile_commentCountAggregateOutputType> | number
        }
      }
    }
    push_token: {
      payload: Prisma.$push_tokenPayload<ExtArgs>
      fields: Prisma.push_tokenFieldRefs
      operations: {
        findUnique: {
          args: Prisma.push_tokenFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.push_tokenFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        findFirst: {
          args: Prisma.push_tokenFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.push_tokenFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        findMany: {
          args: Prisma.push_tokenFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>[]
        }
        create: {
          args: Prisma.push_tokenCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        createMany: {
          args: Prisma.push_tokenCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.push_tokenDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        update: {
          args: Prisma.push_tokenUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        deleteMany: {
          args: Prisma.push_tokenDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.push_tokenUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.push_tokenUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$push_tokenPayload>
        }
        aggregate: {
          args: Prisma.Push_tokenAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePush_token>
        }
        groupBy: {
          args: Prisma.push_tokenGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Push_tokenGroupByOutputType>[]
        }
        count: {
          args: Prisma.push_tokenCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Push_tokenCountAggregateOutputType> | number
        }
      }
    }
    daily_quest: {
      payload: Prisma.$daily_questPayload<ExtArgs>
      fields: Prisma.daily_questFieldRefs
      operations: {
        findUnique: {
          args: Prisma.daily_questFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.daily_questFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        findFirst: {
          args: Prisma.daily_questFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.daily_questFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        findMany: {
          args: Prisma.daily_questFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>[]
        }
        create: {
          args: Prisma.daily_questCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        createMany: {
          args: Prisma.daily_questCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.daily_questDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        update: {
          args: Prisma.daily_questUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        deleteMany: {
          args: Prisma.daily_questDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.daily_questUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.daily_questUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$daily_questPayload>
        }
        aggregate: {
          args: Prisma.Daily_questAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDaily_quest>
        }
        groupBy: {
          args: Prisma.daily_questGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Daily_questGroupByOutputType>[]
        }
        count: {
          args: Prisma.daily_questCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Daily_questCountAggregateOutputType> | number
        }
      }
    }
    quest: {
      payload: Prisma.$questPayload<ExtArgs>
      fields: Prisma.questFieldRefs
      operations: {
        findUnique: {
          args: Prisma.questFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.questFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        findFirst: {
          args: Prisma.questFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.questFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        findMany: {
          args: Prisma.questFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>[]
        }
        create: {
          args: Prisma.questCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        createMany: {
          args: Prisma.questCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.questDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        update: {
          args: Prisma.questUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        deleteMany: {
          args: Prisma.questDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.questUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.questUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$questPayload>
        }
        aggregate: {
          args: Prisma.QuestAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateQuest>
        }
        groupBy: {
          args: Prisma.questGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.QuestGroupByOutputType>[]
        }
        count: {
          args: Prisma.questCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.QuestCountAggregateOutputType> | number
        }
      }
    }
    quest_progress: {
      payload: Prisma.$quest_progressPayload<ExtArgs>
      fields: Prisma.quest_progressFieldRefs
      operations: {
        findUnique: {
          args: Prisma.quest_progressFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.quest_progressFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        findFirst: {
          args: Prisma.quest_progressFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.quest_progressFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        findMany: {
          args: Prisma.quest_progressFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>[]
        }
        create: {
          args: Prisma.quest_progressCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        createMany: {
          args: Prisma.quest_progressCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.quest_progressDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        update: {
          args: Prisma.quest_progressUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        deleteMany: {
          args: Prisma.quest_progressDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.quest_progressUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.quest_progressUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_progressPayload>
        }
        aggregate: {
          args: Prisma.Quest_progressAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateQuest_progress>
        }
        groupBy: {
          args: Prisma.quest_progressGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_progressGroupByOutputType>[]
        }
        count: {
          args: Prisma.quest_progressCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_progressCountAggregateOutputType> | number
        }
      }
    }
    quest_objective: {
      payload: Prisma.$quest_objectivePayload<ExtArgs>
      fields: Prisma.quest_objectiveFieldRefs
      operations: {
        findUnique: {
          args: Prisma.quest_objectiveFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.quest_objectiveFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        findFirst: {
          args: Prisma.quest_objectiveFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.quest_objectiveFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        findMany: {
          args: Prisma.quest_objectiveFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>[]
        }
        create: {
          args: Prisma.quest_objectiveCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        createMany: {
          args: Prisma.quest_objectiveCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.quest_objectiveDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        update: {
          args: Prisma.quest_objectiveUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        deleteMany: {
          args: Prisma.quest_objectiveDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.quest_objectiveUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.quest_objectiveUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objectivePayload>
        }
        aggregate: {
          args: Prisma.Quest_objectiveAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateQuest_objective>
        }
        groupBy: {
          args: Prisma.quest_objectiveGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_objectiveGroupByOutputType>[]
        }
        count: {
          args: Prisma.quest_objectiveCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_objectiveCountAggregateOutputType> | number
        }
      }
    }
    quest_objective_progress: {
      payload: Prisma.$quest_objective_progressPayload<ExtArgs>
      fields: Prisma.quest_objective_progressFieldRefs
      operations: {
        findUnique: {
          args: Prisma.quest_objective_progressFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.quest_objective_progressFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        findFirst: {
          args: Prisma.quest_objective_progressFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.quest_objective_progressFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        findMany: {
          args: Prisma.quest_objective_progressFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>[]
        }
        create: {
          args: Prisma.quest_objective_progressCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        createMany: {
          args: Prisma.quest_objective_progressCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.quest_objective_progressDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        update: {
          args: Prisma.quest_objective_progressUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        deleteMany: {
          args: Prisma.quest_objective_progressDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.quest_objective_progressUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.quest_objective_progressUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_objective_progressPayload>
        }
        aggregate: {
          args: Prisma.Quest_objective_progressAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateQuest_objective_progress>
        }
        groupBy: {
          args: Prisma.quest_objective_progressGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_objective_progressGroupByOutputType>[]
        }
        count: {
          args: Prisma.quest_objective_progressCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_objective_progressCountAggregateOutputType> | number
        }
      }
    }
    quest_reward: {
      payload: Prisma.$quest_rewardPayload<ExtArgs>
      fields: Prisma.quest_rewardFieldRefs
      operations: {
        findUnique: {
          args: Prisma.quest_rewardFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.quest_rewardFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        findFirst: {
          args: Prisma.quest_rewardFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.quest_rewardFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        findMany: {
          args: Prisma.quest_rewardFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>[]
        }
        create: {
          args: Prisma.quest_rewardCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        createMany: {
          args: Prisma.quest_rewardCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.quest_rewardDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        update: {
          args: Prisma.quest_rewardUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        deleteMany: {
          args: Prisma.quest_rewardDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.quest_rewardUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.quest_rewardUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$quest_rewardPayload>
        }
        aggregate: {
          args: Prisma.Quest_rewardAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateQuest_reward>
        }
        groupBy: {
          args: Prisma.quest_rewardGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_rewardGroupByOutputType>[]
        }
        count: {
          args: Prisma.quest_rewardCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Quest_rewardCountAggregateOutputType> | number
        }
      }
    }
    recipe_item: {
      payload: Prisma.$recipe_itemPayload<ExtArgs>
      fields: Prisma.recipe_itemFieldRefs
      operations: {
        findUnique: {
          args: Prisma.recipe_itemFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.recipe_itemFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        findFirst: {
          args: Prisma.recipe_itemFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.recipe_itemFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        findMany: {
          args: Prisma.recipe_itemFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>[]
        }
        create: {
          args: Prisma.recipe_itemCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        createMany: {
          args: Prisma.recipe_itemCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.recipe_itemDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        update: {
          args: Prisma.recipe_itemUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        deleteMany: {
          args: Prisma.recipe_itemDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.recipe_itemUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.recipe_itemUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$recipe_itemPayload>
        }
        aggregate: {
          args: Prisma.Recipe_itemAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRecipe_item>
        }
        groupBy: {
          args: Prisma.recipe_itemGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Recipe_itemGroupByOutputType>[]
        }
        count: {
          args: Prisma.recipe_itemCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Recipe_itemCountAggregateOutputType> | number
        }
      }
    }
    registration_code: {
      payload: Prisma.$registration_codePayload<ExtArgs>
      fields: Prisma.registration_codeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.registration_codeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.registration_codeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        findFirst: {
          args: Prisma.registration_codeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.registration_codeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        findMany: {
          args: Prisma.registration_codeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>[]
        }
        create: {
          args: Prisma.registration_codeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        createMany: {
          args: Prisma.registration_codeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.registration_codeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        update: {
          args: Prisma.registration_codeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        deleteMany: {
          args: Prisma.registration_codeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.registration_codeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.registration_codeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$registration_codePayload>
        }
        aggregate: {
          args: Prisma.Registration_codeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRegistration_code>
        }
        groupBy: {
          args: Prisma.registration_codeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Registration_codeGroupByOutputType>[]
        }
        count: {
          args: Prisma.registration_codeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Registration_codeCountAggregateOutputType> | number
        }
      }
    }
    shop: {
      payload: Prisma.$shopPayload<ExtArgs>
      fields: Prisma.shopFieldRefs
      operations: {
        findUnique: {
          args: Prisma.shopFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.shopFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        findFirst: {
          args: Prisma.shopFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.shopFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        findMany: {
          args: Prisma.shopFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>[]
        }
        create: {
          args: Prisma.shopCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        createMany: {
          args: Prisma.shopCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.shopDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        update: {
          args: Prisma.shopUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        deleteMany: {
          args: Prisma.shopDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.shopUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.shopUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shopPayload>
        }
        aggregate: {
          args: Prisma.ShopAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateShop>
        }
        groupBy: {
          args: Prisma.shopGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ShopGroupByOutputType>[]
        }
        count: {
          args: Prisma.shopCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ShopCountAggregateOutputType> | number
        }
      }
    }
    shop_listing: {
      payload: Prisma.$shop_listingPayload<ExtArgs>
      fields: Prisma.shop_listingFieldRefs
      operations: {
        findUnique: {
          args: Prisma.shop_listingFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.shop_listingFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        findFirst: {
          args: Prisma.shop_listingFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.shop_listingFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        findMany: {
          args: Prisma.shop_listingFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>[]
        }
        create: {
          args: Prisma.shop_listingCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        createMany: {
          args: Prisma.shop_listingCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.shop_listingDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        update: {
          args: Prisma.shop_listingUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        deleteMany: {
          args: Prisma.shop_listingDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.shop_listingUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.shop_listingUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shop_listingPayload>
        }
        aggregate: {
          args: Prisma.Shop_listingAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateShop_listing>
        }
        groupBy: {
          args: Prisma.shop_listingGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shop_listingGroupByOutputType>[]
        }
        count: {
          args: Prisma.shop_listingCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shop_listingCountAggregateOutputType> | number
        }
      }
    }
    shrine_donation: {
      payload: Prisma.$shrine_donationPayload<ExtArgs>
      fields: Prisma.shrine_donationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.shrine_donationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.shrine_donationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        findFirst: {
          args: Prisma.shrine_donationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.shrine_donationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        findMany: {
          args: Prisma.shrine_donationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>[]
        }
        create: {
          args: Prisma.shrine_donationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        createMany: {
          args: Prisma.shrine_donationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.shrine_donationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        update: {
          args: Prisma.shrine_donationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        deleteMany: {
          args: Prisma.shrine_donationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.shrine_donationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.shrine_donationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_donationPayload>
        }
        aggregate: {
          args: Prisma.Shrine_donationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateShrine_donation>
        }
        groupBy: {
          args: Prisma.shrine_donationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shrine_donationGroupByOutputType>[]
        }
        count: {
          args: Prisma.shrine_donationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shrine_donationCountAggregateOutputType> | number
        }
      }
    }
    shrine_goal: {
      payload: Prisma.$shrine_goalPayload<ExtArgs>
      fields: Prisma.shrine_goalFieldRefs
      operations: {
        findUnique: {
          args: Prisma.shrine_goalFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.shrine_goalFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        findFirst: {
          args: Prisma.shrine_goalFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.shrine_goalFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        findMany: {
          args: Prisma.shrine_goalFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>[]
        }
        create: {
          args: Prisma.shrine_goalCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        createMany: {
          args: Prisma.shrine_goalCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.shrine_goalDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        update: {
          args: Prisma.shrine_goalUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        deleteMany: {
          args: Prisma.shrine_goalDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.shrine_goalUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.shrine_goalUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$shrine_goalPayload>
        }
        aggregate: {
          args: Prisma.Shrine_goalAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateShrine_goal>
        }
        groupBy: {
          args: Prisma.shrine_goalGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shrine_goalGroupByOutputType>[]
        }
        count: {
          args: Prisma.shrine_goalCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Shrine_goalCountAggregateOutputType> | number
        }
      }
    }
    suggestion: {
      payload: Prisma.$suggestionPayload<ExtArgs>
      fields: Prisma.suggestionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.suggestionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.suggestionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        findFirst: {
          args: Prisma.suggestionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.suggestionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        findMany: {
          args: Prisma.suggestionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>[]
        }
        create: {
          args: Prisma.suggestionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        createMany: {
          args: Prisma.suggestionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.suggestionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        update: {
          args: Prisma.suggestionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        deleteMany: {
          args: Prisma.suggestionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.suggestionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.suggestionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestionPayload>
        }
        aggregate: {
          args: Prisma.SuggestionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSuggestion>
        }
        groupBy: {
          args: Prisma.suggestionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SuggestionGroupByOutputType>[]
        }
        count: {
          args: Prisma.suggestionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SuggestionCountAggregateOutputType> | number
        }
      }
    }
    suggestion_comment: {
      payload: Prisma.$suggestion_commentPayload<ExtArgs>
      fields: Prisma.suggestion_commentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.suggestion_commentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.suggestion_commentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        findFirst: {
          args: Prisma.suggestion_commentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.suggestion_commentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        findMany: {
          args: Prisma.suggestion_commentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>[]
        }
        create: {
          args: Prisma.suggestion_commentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        createMany: {
          args: Prisma.suggestion_commentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.suggestion_commentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        update: {
          args: Prisma.suggestion_commentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        deleteMany: {
          args: Prisma.suggestion_commentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.suggestion_commentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.suggestion_commentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_commentPayload>
        }
        aggregate: {
          args: Prisma.Suggestion_commentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSuggestion_comment>
        }
        groupBy: {
          args: Prisma.suggestion_commentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Suggestion_commentGroupByOutputType>[]
        }
        count: {
          args: Prisma.suggestion_commentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Suggestion_commentCountAggregateOutputType> | number
        }
      }
    }
    suggestion_vote: {
      payload: Prisma.$suggestion_votePayload<ExtArgs>
      fields: Prisma.suggestion_voteFieldRefs
      operations: {
        findUnique: {
          args: Prisma.suggestion_voteFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.suggestion_voteFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        findFirst: {
          args: Prisma.suggestion_voteFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.suggestion_voteFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        findMany: {
          args: Prisma.suggestion_voteFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>[]
        }
        create: {
          args: Prisma.suggestion_voteCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        createMany: {
          args: Prisma.suggestion_voteCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.suggestion_voteDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        update: {
          args: Prisma.suggestion_voteUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        deleteMany: {
          args: Prisma.suggestion_voteDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.suggestion_voteUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.suggestion_voteUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$suggestion_votePayload>
        }
        aggregate: {
          args: Prisma.Suggestion_voteAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSuggestion_vote>
        }
        groupBy: {
          args: Prisma.suggestion_voteGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Suggestion_voteGroupByOutputType>[]
        }
        count: {
          args: Prisma.suggestion_voteCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Suggestion_voteCountAggregateOutputType> | number
        }
      }
    }
    talent: {
      payload: Prisma.$talentPayload<ExtArgs>
      fields: Prisma.talentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.talentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.talentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        findFirst: {
          args: Prisma.talentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.talentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        findMany: {
          args: Prisma.talentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>[]
        }
        create: {
          args: Prisma.talentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        createMany: {
          args: Prisma.talentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.talentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        update: {
          args: Prisma.talentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        deleteMany: {
          args: Prisma.talentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.talentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.talentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$talentPayload>
        }
        aggregate: {
          args: Prisma.TalentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTalent>
        }
        groupBy: {
          args: Prisma.talentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TalentGroupByOutputType>[]
        }
        count: {
          args: Prisma.talentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TalentCountAggregateOutputType> | number
        }
      }
    }
    trader_rep: {
      payload: Prisma.$trader_repPayload<ExtArgs>
      fields: Prisma.trader_repFieldRefs
      operations: {
        findUnique: {
          args: Prisma.trader_repFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.trader_repFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        findFirst: {
          args: Prisma.trader_repFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.trader_repFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        findMany: {
          args: Prisma.trader_repFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>[]
        }
        create: {
          args: Prisma.trader_repCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        createMany: {
          args: Prisma.trader_repCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.trader_repDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        update: {
          args: Prisma.trader_repUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        deleteMany: {
          args: Prisma.trader_repDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.trader_repUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.trader_repUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$trader_repPayload>
        }
        aggregate: {
          args: Prisma.Trader_repAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTrader_rep>
        }
        groupBy: {
          args: Prisma.trader_repGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Trader_repGroupByOutputType>[]
        }
        count: {
          args: Prisma.trader_repCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Trader_repCountAggregateOutputType> | number
        }
      }
    }
    user: {
      payload: Prisma.$userPayload<ExtArgs>
      fields: Prisma.userFieldRefs
      operations: {
        findUnique: {
          args: Prisma.userFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.userFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        findFirst: {
          args: Prisma.userFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.userFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        findMany: {
          args: Prisma.userFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>[]
        }
        create: {
          args: Prisma.userCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        createMany: {
          args: Prisma.userCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.userDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        update: {
          args: Prisma.userUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        deleteMany: {
          args: Prisma.userDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.userUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.userUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$userPayload>
        }
        aggregate: {
          args: Prisma.UserAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser>
        }
        groupBy: {
          args: Prisma.userGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserGroupByOutputType>[]
        }
        count: {
          args: Prisma.userCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserCountAggregateOutputType> | number
        }
      }
    }
    user_equipped_abilities: {
      payload: Prisma.$user_equipped_abilitiesPayload<ExtArgs>
      fields: Prisma.user_equipped_abilitiesFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_equipped_abilitiesFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_equipped_abilitiesFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        findFirst: {
          args: Prisma.user_equipped_abilitiesFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_equipped_abilitiesFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        findMany: {
          args: Prisma.user_equipped_abilitiesFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>[]
        }
        create: {
          args: Prisma.user_equipped_abilitiesCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        createMany: {
          args: Prisma.user_equipped_abilitiesCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_equipped_abilitiesDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        update: {
          args: Prisma.user_equipped_abilitiesUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        deleteMany: {
          args: Prisma.user_equipped_abilitiesDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_equipped_abilitiesUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_equipped_abilitiesUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_equipped_abilitiesPayload>
        }
        aggregate: {
          args: Prisma.User_equipped_abilitiesAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_equipped_abilities>
        }
        groupBy: {
          args: Prisma.user_equipped_abilitiesGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_equipped_abilitiesGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_equipped_abilitiesCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_equipped_abilitiesCountAggregateOutputType> | number
        }
      }
    }
    session: {
      payload: Prisma.$sessionPayload<ExtArgs>
      fields: Prisma.sessionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.sessionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.sessionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        findFirst: {
          args: Prisma.sessionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.sessionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        findMany: {
          args: Prisma.sessionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>[]
        }
        create: {
          args: Prisma.sessionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        createMany: {
          args: Prisma.sessionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.sessionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        update: {
          args: Prisma.sessionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        deleteMany: {
          args: Prisma.sessionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.sessionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.sessionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$sessionPayload>
        }
        aggregate: {
          args: Prisma.SessionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSession>
        }
        groupBy: {
          args: Prisma.sessionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionGroupByOutputType>[]
        }
        count: {
          args: Prisma.sessionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionCountAggregateOutputType> | number
        }
      }
    }
    account: {
      payload: Prisma.$accountPayload<ExtArgs>
      fields: Prisma.accountFieldRefs
      operations: {
        findUnique: {
          args: Prisma.accountFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.accountFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        findFirst: {
          args: Prisma.accountFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.accountFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        findMany: {
          args: Prisma.accountFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>[]
        }
        create: {
          args: Prisma.accountCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        createMany: {
          args: Prisma.accountCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.accountDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        update: {
          args: Prisma.accountUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        deleteMany: {
          args: Prisma.accountDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.accountUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.accountUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$accountPayload>
        }
        aggregate: {
          args: Prisma.AccountAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAccount>
        }
        groupBy: {
          args: Prisma.accountGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AccountGroupByOutputType>[]
        }
        count: {
          args: Prisma.accountCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AccountCountAggregateOutputType> | number
        }
      }
    }
    verification: {
      payload: Prisma.$verificationPayload<ExtArgs>
      fields: Prisma.verificationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.verificationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.verificationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        findFirst: {
          args: Prisma.verificationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.verificationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        findMany: {
          args: Prisma.verificationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>[]
        }
        create: {
          args: Prisma.verificationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        createMany: {
          args: Prisma.verificationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.verificationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        update: {
          args: Prisma.verificationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        deleteMany: {
          args: Prisma.verificationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.verificationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.verificationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$verificationPayload>
        }
        aggregate: {
          args: Prisma.VerificationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateVerification>
        }
        groupBy: {
          args: Prisma.verificationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.VerificationGroupByOutputType>[]
        }
        count: {
          args: Prisma.verificationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.VerificationCountAggregateOutputType> | number
        }
      }
    }
    user_item: {
      payload: Prisma.$user_itemPayload<ExtArgs>
      fields: Prisma.user_itemFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_itemFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_itemFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        findFirst: {
          args: Prisma.user_itemFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_itemFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        findMany: {
          args: Prisma.user_itemFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>[]
        }
        create: {
          args: Prisma.user_itemCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        createMany: {
          args: Prisma.user_itemCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_itemDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        update: {
          args: Prisma.user_itemUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        deleteMany: {
          args: Prisma.user_itemDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_itemUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_itemUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_itemPayload>
        }
        aggregate: {
          args: Prisma.User_itemAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_item>
        }
        groupBy: {
          args: Prisma.user_itemGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_itemGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_itemCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_itemCountAggregateOutputType> | number
        }
      }
    }
    user_recipe: {
      payload: Prisma.$user_recipePayload<ExtArgs>
      fields: Prisma.user_recipeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_recipeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_recipeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        findFirst: {
          args: Prisma.user_recipeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_recipeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        findMany: {
          args: Prisma.user_recipeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>[]
        }
        create: {
          args: Prisma.user_recipeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        createMany: {
          args: Prisma.user_recipeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_recipeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        update: {
          args: Prisma.user_recipeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        deleteMany: {
          args: Prisma.user_recipeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_recipeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_recipeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_recipePayload>
        }
        aggregate: {
          args: Prisma.User_recipeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_recipe>
        }
        groupBy: {
          args: Prisma.user_recipeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_recipeGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_recipeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_recipeCountAggregateOutputType> | number
        }
      }
    }
    user_completed_course: {
      payload: Prisma.$user_completed_coursePayload<ExtArgs>
      fields: Prisma.user_completed_courseFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_completed_courseFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_completed_courseFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        findFirst: {
          args: Prisma.user_completed_courseFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_completed_courseFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        findMany: {
          args: Prisma.user_completed_courseFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>[]
        }
        create: {
          args: Prisma.user_completed_courseCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        createMany: {
          args: Prisma.user_completed_courseCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_completed_courseDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        update: {
          args: Prisma.user_completed_courseUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        deleteMany: {
          args: Prisma.user_completed_courseDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_completed_courseUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_completed_courseUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_completed_coursePayload>
        }
        aggregate: {
          args: Prisma.User_completed_courseAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_completed_course>
        }
        groupBy: {
          args: Prisma.user_completed_courseGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_completed_courseGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_completed_courseCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_completed_courseCountAggregateOutputType> | number
        }
      }
    }
    user_talent: {
      payload: Prisma.$user_talentPayload<ExtArgs>
      fields: Prisma.user_talentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_talentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_talentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        findFirst: {
          args: Prisma.user_talentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_talentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        findMany: {
          args: Prisma.user_talentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>[]
        }
        create: {
          args: Prisma.user_talentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        createMany: {
          args: Prisma.user_talentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_talentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        update: {
          args: Prisma.user_talentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        deleteMany: {
          args: Prisma.user_talentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_talentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_talentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_talentPayload>
        }
        aggregate: {
          args: Prisma.User_talentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_talent>
        }
        groupBy: {
          args: Prisma.user_talentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_talentGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_talentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_talentCountAggregateOutputType> | number
        }
      }
    }
    user_achievements: {
      payload: Prisma.$user_achievementsPayload<ExtArgs>
      fields: Prisma.user_achievementsFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_achievementsFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_achievementsFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        findFirst: {
          args: Prisma.user_achievementsFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_achievementsFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        findMany: {
          args: Prisma.user_achievementsFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>[]
        }
        create: {
          args: Prisma.user_achievementsCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        createMany: {
          args: Prisma.user_achievementsCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_achievementsDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        update: {
          args: Prisma.user_achievementsUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        deleteMany: {
          args: Prisma.user_achievementsDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_achievementsUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_achievementsUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_achievementsPayload>
        }
        aggregate: {
          args: Prisma.User_achievementsAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_achievements>
        }
        groupBy: {
          args: Prisma.user_achievementsGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_achievementsGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_achievementsCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_achievementsCountAggregateOutputType> | number
        }
      }
    }
    user_crafting_queue: {
      payload: Prisma.$user_crafting_queuePayload<ExtArgs>
      fields: Prisma.user_crafting_queueFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_crafting_queueFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_crafting_queueFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        findFirst: {
          args: Prisma.user_crafting_queueFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_crafting_queueFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        findMany: {
          args: Prisma.user_crafting_queueFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>[]
        }
        create: {
          args: Prisma.user_crafting_queueCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        createMany: {
          args: Prisma.user_crafting_queueCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_crafting_queueDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        update: {
          args: Prisma.user_crafting_queueUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        deleteMany: {
          args: Prisma.user_crafting_queueDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_crafting_queueUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_crafting_queueUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_crafting_queuePayload>
        }
        aggregate: {
          args: Prisma.User_crafting_queueAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_crafting_queue>
        }
        groupBy: {
          args: Prisma.user_crafting_queueGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_crafting_queueGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_crafting_queueCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_crafting_queueCountAggregateOutputType> | number
        }
      }
    }
    property: {
      payload: Prisma.$propertyPayload<ExtArgs>
      fields: Prisma.propertyFieldRefs
      operations: {
        findUnique: {
          args: Prisma.propertyFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.propertyFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        findFirst: {
          args: Prisma.propertyFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.propertyFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        findMany: {
          args: Prisma.propertyFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>[]
        }
        create: {
          args: Prisma.propertyCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        createMany: {
          args: Prisma.propertyCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.propertyDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        update: {
          args: Prisma.propertyUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        deleteMany: {
          args: Prisma.propertyDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.propertyUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.propertyUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$propertyPayload>
        }
        aggregate: {
          args: Prisma.PropertyAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateProperty>
        }
        groupBy: {
          args: Prisma.propertyGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PropertyGroupByOutputType>[]
        }
        count: {
          args: Prisma.propertyCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PropertyCountAggregateOutputType> | number
        }
      }
    }
    user_property: {
      payload: Prisma.$user_propertyPayload<ExtArgs>
      fields: Prisma.user_propertyFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_propertyFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_propertyFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        findFirst: {
          args: Prisma.user_propertyFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_propertyFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        findMany: {
          args: Prisma.user_propertyFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>[]
        }
        create: {
          args: Prisma.user_propertyCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        createMany: {
          args: Prisma.user_propertyCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_propertyDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        update: {
          args: Prisma.user_propertyUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        deleteMany: {
          args: Prisma.user_propertyDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_propertyUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_propertyUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_propertyPayload>
        }
        aggregate: {
          args: Prisma.User_propertyAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_property>
        }
        groupBy: {
          args: Prisma.user_propertyGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_propertyGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_propertyCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_propertyCountAggregateOutputType> | number
        }
      }
    }
    pet: {
      payload: Prisma.$petPayload<ExtArgs>
      fields: Prisma.petFieldRefs
      operations: {
        findUnique: {
          args: Prisma.petFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.petFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        findFirst: {
          args: Prisma.petFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.petFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        findMany: {
          args: Prisma.petFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>[]
        }
        create: {
          args: Prisma.petCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        createMany: {
          args: Prisma.petCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.petDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        update: {
          args: Prisma.petUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        deleteMany: {
          args: Prisma.petDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.petUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.petUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$petPayload>
        }
        aggregate: {
          args: Prisma.PetAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePet>
        }
        groupBy: {
          args: Prisma.petGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PetGroupByOutputType>[]
        }
        count: {
          args: Prisma.petCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PetCountAggregateOutputType> | number
        }
      }
    }
    user_pet: {
      payload: Prisma.$user_petPayload<ExtArgs>
      fields: Prisma.user_petFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_petFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_petFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        findFirst: {
          args: Prisma.user_petFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_petFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        findMany: {
          args: Prisma.user_petFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>[]
        }
        create: {
          args: Prisma.user_petCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        createMany: {
          args: Prisma.user_petCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_petDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        update: {
          args: Prisma.user_petUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        deleteMany: {
          args: Prisma.user_petDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_petUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_petUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_petPayload>
        }
        aggregate: {
          args: Prisma.User_petAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_pet>
        }
        groupBy: {
          args: Prisma.user_petGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_petGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_petCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_petCountAggregateOutputType> | number
        }
      }
    }
    friend_request: {
      payload: Prisma.$friend_requestPayload<ExtArgs>
      fields: Prisma.friend_requestFieldRefs
      operations: {
        findUnique: {
          args: Prisma.friend_requestFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.friend_requestFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        findFirst: {
          args: Prisma.friend_requestFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.friend_requestFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        findMany: {
          args: Prisma.friend_requestFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>[]
        }
        create: {
          args: Prisma.friend_requestCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        createMany: {
          args: Prisma.friend_requestCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.friend_requestDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        update: {
          args: Prisma.friend_requestUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        deleteMany: {
          args: Prisma.friend_requestDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.friend_requestUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.friend_requestUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friend_requestPayload>
        }
        aggregate: {
          args: Prisma.Friend_requestAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateFriend_request>
        }
        groupBy: {
          args: Prisma.friend_requestGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Friend_requestGroupByOutputType>[]
        }
        count: {
          args: Prisma.friend_requestCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Friend_requestCountAggregateOutputType> | number
        }
      }
    }
    friend: {
      payload: Prisma.$friendPayload<ExtArgs>
      fields: Prisma.friendFieldRefs
      operations: {
        findUnique: {
          args: Prisma.friendFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.friendFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        findFirst: {
          args: Prisma.friendFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.friendFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        findMany: {
          args: Prisma.friendFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>[]
        }
        create: {
          args: Prisma.friendCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        createMany: {
          args: Prisma.friendCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.friendDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        update: {
          args: Prisma.friendUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        deleteMany: {
          args: Prisma.friendDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.friendUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.friendUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$friendPayload>
        }
        aggregate: {
          args: Prisma.FriendAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateFriend>
        }
        groupBy: {
          args: Prisma.friendGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.FriendGroupByOutputType>[]
        }
        count: {
          args: Prisma.friendCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.FriendCountAggregateOutputType> | number
        }
      }
    }
    rival: {
      payload: Prisma.$rivalPayload<ExtArgs>
      fields: Prisma.rivalFieldRefs
      operations: {
        findUnique: {
          args: Prisma.rivalFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.rivalFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        findFirst: {
          args: Prisma.rivalFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.rivalFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        findMany: {
          args: Prisma.rivalFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>[]
        }
        create: {
          args: Prisma.rivalCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        createMany: {
          args: Prisma.rivalCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.rivalDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        update: {
          args: Prisma.rivalUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        deleteMany: {
          args: Prisma.rivalDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.rivalUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.rivalUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$rivalPayload>
        }
        aggregate: {
          args: Prisma.RivalAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRival>
        }
        groupBy: {
          args: Prisma.rivalGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RivalGroupByOutputType>[]
        }
        count: {
          args: Prisma.rivalCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RivalCountAggregateOutputType> | number
        }
      }
    }
    status_effect: {
      payload: Prisma.$status_effectPayload<ExtArgs>
      fields: Prisma.status_effectFieldRefs
      operations: {
        findUnique: {
          args: Prisma.status_effectFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.status_effectFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        findFirst: {
          args: Prisma.status_effectFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.status_effectFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        findMany: {
          args: Prisma.status_effectFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>[]
        }
        create: {
          args: Prisma.status_effectCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        createMany: {
          args: Prisma.status_effectCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.status_effectDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        update: {
          args: Prisma.status_effectUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        deleteMany: {
          args: Prisma.status_effectDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.status_effectUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.status_effectUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$status_effectPayload>
        }
        aggregate: {
          args: Prisma.Status_effectAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStatus_effect>
        }
        groupBy: {
          args: Prisma.status_effectGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Status_effectGroupByOutputType>[]
        }
        count: {
          args: Prisma.status_effectCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Status_effectCountAggregateOutputType> | number
        }
      }
    }
    user_status_effect: {
      payload: Prisma.$user_status_effectPayload<ExtArgs>
      fields: Prisma.user_status_effectFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_status_effectFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_status_effectFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        findFirst: {
          args: Prisma.user_status_effectFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_status_effectFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        findMany: {
          args: Prisma.user_status_effectFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>[]
        }
        create: {
          args: Prisma.user_status_effectCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        createMany: {
          args: Prisma.user_status_effectCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_status_effectDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        update: {
          args: Prisma.user_status_effectUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        deleteMany: {
          args: Prisma.user_status_effectDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_status_effectUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_status_effectUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_status_effectPayload>
        }
        aggregate: {
          args: Prisma.User_status_effectAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_status_effect>
        }
        groupBy: {
          args: Prisma.user_status_effectGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_status_effectGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_status_effectCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_status_effectCountAggregateOutputType> | number
        }
      }
    }
    user_skill: {
      payload: Prisma.$user_skillPayload<ExtArgs>
      fields: Prisma.user_skillFieldRefs
      operations: {
        findUnique: {
          args: Prisma.user_skillFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.user_skillFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        findFirst: {
          args: Prisma.user_skillFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.user_skillFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        findMany: {
          args: Prisma.user_skillFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>[]
        }
        create: {
          args: Prisma.user_skillCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        createMany: {
          args: Prisma.user_skillCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.user_skillDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        update: {
          args: Prisma.user_skillUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        deleteMany: {
          args: Prisma.user_skillDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.user_skillUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.user_skillUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$user_skillPayload>
        }
        aggregate: {
          args: Prisma.User_skillAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser_skill>
        }
        groupBy: {
          args: Prisma.user_skillGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_skillGroupByOutputType>[]
        }
        count: {
          args: Prisma.user_skillCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.User_skillCountAggregateOutputType> | number
        }
      }
    }
    explore_static_node: {
      payload: Prisma.$explore_static_nodePayload<ExtArgs>
      fields: Prisma.explore_static_nodeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.explore_static_nodeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.explore_static_nodeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        findFirst: {
          args: Prisma.explore_static_nodeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.explore_static_nodeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        findMany: {
          args: Prisma.explore_static_nodeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>[]
        }
        create: {
          args: Prisma.explore_static_nodeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        createMany: {
          args: Prisma.explore_static_nodeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.explore_static_nodeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        update: {
          args: Prisma.explore_static_nodeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        deleteMany: {
          args: Prisma.explore_static_nodeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.explore_static_nodeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.explore_static_nodeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_static_nodePayload>
        }
        aggregate: {
          args: Prisma.Explore_static_nodeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateExplore_static_node>
        }
        groupBy: {
          args: Prisma.explore_static_nodeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Explore_static_nodeGroupByOutputType>[]
        }
        count: {
          args: Prisma.explore_static_nodeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Explore_static_nodeCountAggregateOutputType> | number
        }
      }
    }
    explore_player_node: {
      payload: Prisma.$explore_player_nodePayload<ExtArgs>
      fields: Prisma.explore_player_nodeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.explore_player_nodeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.explore_player_nodeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        findFirst: {
          args: Prisma.explore_player_nodeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.explore_player_nodeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        findMany: {
          args: Prisma.explore_player_nodeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>[]
        }
        create: {
          args: Prisma.explore_player_nodeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        createMany: {
          args: Prisma.explore_player_nodeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.explore_player_nodeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        update: {
          args: Prisma.explore_player_nodeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        deleteMany: {
          args: Prisma.explore_player_nodeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.explore_player_nodeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.explore_player_nodeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$explore_player_nodePayload>
        }
        aggregate: {
          args: Prisma.Explore_player_nodeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateExplore_player_node>
        }
        groupBy: {
          args: Prisma.explore_player_nodeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Explore_player_nodeGroupByOutputType>[]
        }
        count: {
          args: Prisma.explore_player_nodeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Explore_player_nodeCountAggregateOutputType> | number
        }
      }
    }
    story_season: {
      payload: Prisma.$story_seasonPayload<ExtArgs>
      fields: Prisma.story_seasonFieldRefs
      operations: {
        findUnique: {
          args: Prisma.story_seasonFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.story_seasonFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        findFirst: {
          args: Prisma.story_seasonFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.story_seasonFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        findMany: {
          args: Prisma.story_seasonFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>[]
        }
        create: {
          args: Prisma.story_seasonCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        createMany: {
          args: Prisma.story_seasonCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.story_seasonDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        update: {
          args: Prisma.story_seasonUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        deleteMany: {
          args: Prisma.story_seasonDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.story_seasonUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.story_seasonUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_seasonPayload>
        }
        aggregate: {
          args: Prisma.Story_seasonAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStory_season>
        }
        groupBy: {
          args: Prisma.story_seasonGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_seasonGroupByOutputType>[]
        }
        count: {
          args: Prisma.story_seasonCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_seasonCountAggregateOutputType> | number
        }
      }
    }
    story_chapter: {
      payload: Prisma.$story_chapterPayload<ExtArgs>
      fields: Prisma.story_chapterFieldRefs
      operations: {
        findUnique: {
          args: Prisma.story_chapterFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.story_chapterFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        findFirst: {
          args: Prisma.story_chapterFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.story_chapterFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        findMany: {
          args: Prisma.story_chapterFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>[]
        }
        create: {
          args: Prisma.story_chapterCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        createMany: {
          args: Prisma.story_chapterCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.story_chapterDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        update: {
          args: Prisma.story_chapterUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        deleteMany: {
          args: Prisma.story_chapterDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.story_chapterUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.story_chapterUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_chapterPayload>
        }
        aggregate: {
          args: Prisma.Story_chapterAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStory_chapter>
        }
        groupBy: {
          args: Prisma.story_chapterGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_chapterGroupByOutputType>[]
        }
        count: {
          args: Prisma.story_chapterCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_chapterCountAggregateOutputType> | number
        }
      }
    }
    story_episode: {
      payload: Prisma.$story_episodePayload<ExtArgs>
      fields: Prisma.story_episodeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.story_episodeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.story_episodeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        findFirst: {
          args: Prisma.story_episodeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.story_episodeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        findMany: {
          args: Prisma.story_episodeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>[]
        }
        create: {
          args: Prisma.story_episodeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        createMany: {
          args: Prisma.story_episodeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.story_episodeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        update: {
          args: Prisma.story_episodeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        deleteMany: {
          args: Prisma.story_episodeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.story_episodeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.story_episodeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$story_episodePayload>
        }
        aggregate: {
          args: Prisma.Story_episodeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStory_episode>
        }
        groupBy: {
          args: Prisma.story_episodeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_episodeGroupByOutputType>[]
        }
        count: {
          args: Prisma.story_episodeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.Story_episodeCountAggregateOutputType> | number
        }
      }
    }
  }
} & {
  other: {
    payload: any
    operations: {
      $executeRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $executeRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
      $queryRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $queryRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
    }
  }
}

/**
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const Action_logScalarFieldEnum = {
  id: 'id',
  logType: 'logType',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  playerId: 'playerId',
  secondPartyId: 'secondPartyId'
} as const

export type Action_logScalarFieldEnum = (typeof Action_logScalarFieldEnum)[keyof typeof Action_logScalarFieldEnum]


export const Game_configScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  category: 'category',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Game_configScalarFieldEnum = (typeof Game_configScalarFieldEnum)[keyof typeof Game_configScalarFieldEnum]


export const Auction_itemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  deposit: 'deposit',
  buyoutPrice: 'buyoutPrice',
  endsAt: 'endsAt',
  status: 'status',
  bankFunds: 'bankFunds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemId: 'itemId',
  sellerId: 'sellerId'
} as const

export type Auction_itemScalarFieldEnum = (typeof Auction_itemScalarFieldEnum)[keyof typeof Auction_itemScalarFieldEnum]


export const Bank_transactionScalarFieldEnum = {
  id: 'id',
  transaction_type: 'transaction_type',
  cash: 'cash',
  transactionFee: 'transactionFee',
  initiatorCashBalance: 'initiatorCashBalance',
  initiatorBankBalance: 'initiatorBankBalance',
  secondPartyCashBalance: 'secondPartyCashBalance',
  secondPartyBankBalance: 'secondPartyBankBalance',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  initiatorId: 'initiatorId',
  secondPartyId: 'secondPartyId',
  userId: 'userId'
} as const

export type Bank_transactionScalarFieldEnum = (typeof Bank_transactionScalarFieldEnum)[keyof typeof Bank_transactionScalarFieldEnum]


export const Battle_logScalarFieldEnum = {
  id: 'id',
  victory: 'victory',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  attackerId: 'attackerId',
  defenderId: 'defenderId'
} as const

export type Battle_logScalarFieldEnum = (typeof Battle_logScalarFieldEnum)[keyof typeof Battle_logScalarFieldEnum]


export const BountyScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  reason: 'reason',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  placerId: 'placerId',
  targetId: 'targetId',
  claimedById: 'claimedById'
} as const

export type BountyScalarFieldEnum = (typeof BountyScalarFieldEnum)[keyof typeof BountyScalarFieldEnum]


export const Chat_messageScalarFieldEnum = {
  id: 'id',
  message: 'message',
  hidden: 'hidden',
  announcementType: 'announcementType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  chatRoomId: 'chatRoomId',
  userId: 'userId',
  parentMessageId: 'parentMessageId'
} as const

export type Chat_messageScalarFieldEnum = (typeof Chat_messageScalarFieldEnum)[keyof typeof Chat_messageScalarFieldEnum]


export const Chat_roomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  gangId: 'gangId'
} as const

export type Chat_roomScalarFieldEnum = (typeof Chat_roomScalarFieldEnum)[keyof typeof Chat_roomScalarFieldEnum]


export const Crafting_recipeScalarFieldEnum = {
  id: 'id',
  cost: 'cost',
  craftTime: 'craftTime',
  isUnlockable: 'isUnlockable',
  requiredSkillType: 'requiredSkillType',
  requiredSkillLevel: 'requiredSkillLevel'
} as const

export type Crafting_recipeScalarFieldEnum = (typeof Crafting_recipeScalarFieldEnum)[keyof typeof Crafting_recipeScalarFieldEnum]


export const CreatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  minFloor: 'minFloor',
  maxFloor: 'maxFloor',
  boss: 'boss',
  health: 'health',
  currentHealth: 'currentHealth',
  strength: 'strength',
  defence: 'defence',
  weaponDamage: 'weaponDamage',
  location: 'location',
  statType: 'statType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type CreatureScalarFieldEnum = (typeof CreatureScalarFieldEnum)[keyof typeof CreatureScalarFieldEnum]


export const Daily_missionScalarFieldEnum = {
  id: 'id',
  tier: 'tier',
  missionName: 'missionName',
  description: 'description',
  missionDate: 'missionDate',
  duration: 'duration',
  minCashReward: 'minCashReward',
  maxCashReward: 'maxCashReward',
  minExpReward: 'minExpReward',
  maxExpReward: 'maxExpReward',
  levelReq: 'levelReq',
  hoursReq: 'hoursReq',
  itemRewardQuantity: 'itemRewardQuantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemRewardId: 'itemRewardId'
} as const

export type Daily_missionScalarFieldEnum = (typeof Daily_missionScalarFieldEnum)[keyof typeof Daily_missionScalarFieldEnum]


export const Drop_chanceScalarFieldEnum = {
  id: 'id',
  dropRate: 'dropRate',
  dropChanceType: 'dropChanceType',
  quantity: 'quantity',
  location: 'location',
  minLevel: 'minLevel',
  maxLevel: 'maxLevel',
  scavengeType: 'scavengeType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemId: 'itemId'
} as const

export type Drop_chanceScalarFieldEnum = (typeof Drop_chanceScalarFieldEnum)[keyof typeof Drop_chanceScalarFieldEnum]


export const Equipped_itemScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  slot: 'slot',
  userItemId: 'userItemId'
} as const

export type Equipped_itemScalarFieldEnum = (typeof Equipped_itemScalarFieldEnum)[keyof typeof Equipped_itemScalarFieldEnum]


export const Game_statsScalarFieldEnum = {
  id: 'id',
  stats_type: 'stats_type',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  playerId: 'playerId'
} as const

export type Game_statsScalarFieldEnum = (typeof Game_statsScalarFieldEnum)[keyof typeof Game_statsScalarFieldEnum]


export const GangScalarFieldEnum = {
  id: 'id',
  name: 'name',
  about: 'about',
  avatar: 'avatar',
  treasury_balance: 'treasury_balance',
  hideout_level: 'hideout_level',
  materialsResource: 'materialsResource',
  essenceResource: 'essenceResource',
  dailyEssenceGained: 'dailyEssenceGained',
  toolsResource: 'toolsResource',
  techResource: 'techResource',
  weeklyRespect: 'weeklyRespect',
  totalRespect: 'totalRespect',
  gangMOTD: 'gangMOTD',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId'
} as const

export type GangScalarFieldEnum = (typeof GangScalarFieldEnum)[keyof typeof GangScalarFieldEnum]


export const Gang_inviteScalarFieldEnum = {
  id: 'id',
  inviteType: 'inviteType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  senderId: 'senderId',
  recipientId: 'recipientId'
} as const

export type Gang_inviteScalarFieldEnum = (typeof Gang_inviteScalarFieldEnum)[keyof typeof Gang_inviteScalarFieldEnum]


export const Gang_logScalarFieldEnum = {
  id: 'id',
  action: 'action',
  info: 'info',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  gangMemberId: 'gangMemberId',
  secondPartyId: 'secondPartyId'
} as const

export type Gang_logScalarFieldEnum = (typeof Gang_logScalarFieldEnum)[keyof typeof Gang_logScalarFieldEnum]


export const Gang_memberScalarFieldEnum = {
  id: 'id',
  rank: 'rank',
  payoutShare: 'payoutShare',
  weeklyMaterials: 'weeklyMaterials',
  weeklyEssence: 'weeklyEssence',
  weeklyTools: 'weeklyTools',
  weeklyRespect: 'weeklyRespect',
  totalContribution: 'totalContribution',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  userId: 'userId'
} as const

export type Gang_memberScalarFieldEnum = (typeof Gang_memberScalarFieldEnum)[keyof typeof Gang_memberScalarFieldEnum]


export const ItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  itemType: 'itemType',
  rarity: 'rarity',
  level: 'level',
  about: 'about',
  cashValue: 'cashValue',
  image: 'image',
  damage: 'damage',
  armour: 'armour',
  health: 'health',
  energy: 'energy',
  actionPoints: 'actionPoints',
  baseAmmo: 'baseAmmo',
  itemEffects: 'itemEffects',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  recipeUnlockId: 'recipeUnlockId',
  petUnlockId: 'petUnlockId'
} as const

export type ItemScalarFieldEnum = (typeof ItemScalarFieldEnum)[keyof typeof ItemScalarFieldEnum]


export const JobScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  avatar: 'avatar',
  payFormula: 'payFormula',
  strengthFormula: 'strengthFormula',
  intelligenceFormula: 'intelligenceFormula',
  dexterityFormula: 'dexterityFormula',
  defenceFormula: 'defenceFormula',
  enduranceFormula: 'enduranceFormula',
  vitalityFormula: 'vitalityFormula',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type JobScalarFieldEnum = (typeof JobScalarFieldEnum)[keyof typeof JobScalarFieldEnum]


export const LotteryScalarFieldEnum = {
  id: 'id',
  drawDate: 'drawDate',
  prizeAmount: 'prizeAmount',
  entries: 'entries',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  winnerId: 'winnerId'
} as const

export type LotteryScalarFieldEnum = (typeof LotteryScalarFieldEnum)[keyof typeof LotteryScalarFieldEnum]


export const Lottery_entryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lotteryId: 'lotteryId',
  userId: 'userId'
} as const

export type Lottery_entryScalarFieldEnum = (typeof Lottery_entryScalarFieldEnum)[keyof typeof Lottery_entryScalarFieldEnum]


export const NotificationScalarFieldEnum = {
  id: 'id',
  notificationType: 'notificationType',
  details: 'details',
  read: 'read',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type NotificationScalarFieldEnum = (typeof NotificationScalarFieldEnum)[keyof typeof NotificationScalarFieldEnum]


export const PollScalarFieldEnum = {
  id: 'id',
  title: 'title',
  ended: 'ended',
  showResults: 'showResults',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PollScalarFieldEnum = (typeof PollScalarFieldEnum)[keyof typeof PollScalarFieldEnum]


export const Poll_responseScalarFieldEnum = {
  id: 'id',
  answer: 'answer',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  pollId: 'pollId'
} as const

export type Poll_responseScalarFieldEnum = (typeof Poll_responseScalarFieldEnum)[keyof typeof Poll_responseScalarFieldEnum]


export const Private_messageScalarFieldEnum = {
  id: 'id',
  message: 'message',
  read: 'read',
  isGlobal: 'isGlobal',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Private_messageScalarFieldEnum = (typeof Private_messageScalarFieldEnum)[keyof typeof Private_messageScalarFieldEnum]


export const Profile_commentScalarFieldEnum = {
  id: 'id',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Profile_commentScalarFieldEnum = (typeof Profile_commentScalarFieldEnum)[keyof typeof Profile_commentScalarFieldEnum]


export const Push_tokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type Push_tokenScalarFieldEnum = (typeof Push_tokenScalarFieldEnum)[keyof typeof Push_tokenScalarFieldEnum]


export const Daily_questScalarFieldEnum = {
  id: 'id',
  objectiveType: 'objectiveType',
  target: 'target',
  targetAction: 'targetAction',
  quantity: 'quantity',
  questStatus: 'questStatus',
  count: 'count',
  cashReward: 'cashReward',
  xpReward: 'xpReward',
  itemRewardQuantity: 'itemRewardQuantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  itemRewardId: 'itemRewardId',
  userId: 'userId'
} as const

export type Daily_questScalarFieldEnum = (typeof Daily_questScalarFieldEnum)[keyof typeof Daily_questScalarFieldEnum]


export const QuestScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  questInfo: 'questInfo',
  levelReq: 'levelReq',
  disabled: 'disabled',
  questChainName: 'questChainName',
  xpReward: 'xpReward',
  cashReward: 'cashReward',
  repReward: 'repReward',
  talentPointReward: 'talentPointReward',
  shopId: 'shopId',
  requiredQuestId: 'requiredQuestId',
  isStoryQuest: 'isStoryQuest',
  chapterId: 'chapterId',
  orderInChapter: 'orderInChapter'
} as const

export type QuestScalarFieldEnum = (typeof QuestScalarFieldEnum)[keyof typeof QuestScalarFieldEnum]


export const Quest_progressScalarFieldEnum = {
  id: 'id',
  questStatus: 'questStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questId: 'questId',
  userId: 'userId'
} as const

export type Quest_progressScalarFieldEnum = (typeof Quest_progressScalarFieldEnum)[keyof typeof Quest_progressScalarFieldEnum]


export const Quest_objectiveScalarFieldEnum = {
  id: 'id',
  description: 'description',
  objectiveType: 'objectiveType',
  target: 'target',
  targetAction: 'targetAction',
  quantity: 'quantity',
  location: 'location',
  isRequired: 'isRequired',
  questId: 'questId',
  creatureId: 'creatureId',
  itemId: 'itemId'
} as const

export type Quest_objectiveScalarFieldEnum = (typeof Quest_objectiveScalarFieldEnum)[keyof typeof Quest_objectiveScalarFieldEnum]


export const Quest_objective_progressScalarFieldEnum = {
  id: 'id',
  count: 'count',
  status: 'status',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questObjectiveId: 'questObjectiveId'
} as const

export type Quest_objective_progressScalarFieldEnum = (typeof Quest_objective_progressScalarFieldEnum)[keyof typeof Quest_objective_progressScalarFieldEnum]


export const Quest_rewardScalarFieldEnum = {
  id: 'id',
  rewardType: 'rewardType',
  quantity: 'quantity',
  isChoice: 'isChoice',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questId: 'questId',
  dailyQuestId: 'dailyQuestId',
  itemId: 'itemId'
} as const

export type Quest_rewardScalarFieldEnum = (typeof Quest_rewardScalarFieldEnum)[keyof typeof Quest_rewardScalarFieldEnum]


export const Recipe_itemScalarFieldEnum = {
  count: 'count',
  itemType: 'itemType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  craftingRecipeId: 'craftingRecipeId',
  itemId: 'itemId'
} as const

export type Recipe_itemScalarFieldEnum = (typeof Recipe_itemScalarFieldEnum)[keyof typeof Recipe_itemScalarFieldEnum]


export const Registration_codeScalarFieldEnum = {
  id: 'id',
  code: 'code',
  note: 'note',
  unlimitedUse: 'unlimitedUse',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  referrerId: 'referrerId',
  claimerId: 'claimerId'
} as const

export type Registration_codeScalarFieldEnum = (typeof Registration_codeScalarFieldEnum)[keyof typeof Registration_codeScalarFieldEnum]


export const ShopScalarFieldEnum = {
  id: 'id',
  name: 'name',
  shopType: 'shopType',
  avatar: 'avatar',
  description: 'description',
  disabled: 'disabled'
} as const

export type ShopScalarFieldEnum = (typeof ShopScalarFieldEnum)[keyof typeof ShopScalarFieldEnum]


export const Shop_listingScalarFieldEnum = {
  id: 'id',
  customCost: 'customCost',
  repRequired: 'repRequired',
  stock: 'stock',
  currency: 'currency',
  shopId: 'shopId',
  itemId: 'itemId'
} as const

export type Shop_listingScalarFieldEnum = (typeof Shop_listingScalarFieldEnum)[keyof typeof Shop_listingScalarFieldEnum]


export const Shrine_donationScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  date: 'date',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  shrineGoalId: 'shrineGoalId',
  userId: 'userId'
} as const

export type Shrine_donationScalarFieldEnum = (typeof Shrine_donationScalarFieldEnum)[keyof typeof Shrine_donationScalarFieldEnum]


export const Shrine_goalScalarFieldEnum = {
  id: 'id',
  donationGoal: 'donationGoal',
  donationAmount: 'donationAmount',
  goalReached: 'goalReached',
  goalDate: 'goalDate',
  buffRewards: 'buffRewards',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Shrine_goalScalarFieldEnum = (typeof Shrine_goalScalarFieldEnum)[keyof typeof Shrine_goalScalarFieldEnum]


export const SuggestionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  state: 'state',
  upvotes: 'upvotes',
  downvotes: 'downvotes',
  totalComments: 'totalComments',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type SuggestionScalarFieldEnum = (typeof SuggestionScalarFieldEnum)[keyof typeof SuggestionScalarFieldEnum]


export const Suggestion_commentScalarFieldEnum = {
  id: 'id',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  suggestionId: 'suggestionId'
} as const

export type Suggestion_commentScalarFieldEnum = (typeof Suggestion_commentScalarFieldEnum)[keyof typeof Suggestion_commentScalarFieldEnum]


export const Suggestion_voteScalarFieldEnum = {
  id: 'id',
  voteType: 'voteType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  suggestionId: 'suggestionId'
} as const

export type Suggestion_voteScalarFieldEnum = (typeof Suggestion_voteScalarFieldEnum)[keyof typeof Suggestion_voteScalarFieldEnum]


export const TalentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  tree: 'tree',
  talentType: 'talentType',
  description: 'description',
  skillLevelRequired: 'skillLevelRequired',
  pointsInTreeRequired: 'pointsInTreeRequired',
  pointsCost: 'pointsCost',
  maxPoints: 'maxPoints',
  staminaCost: 'staminaCost',
  tier1Modifier: 'tier1Modifier',
  tier2Modifier: 'tier2Modifier',
  tier3Modifier: 'tier3Modifier',
  secondaryModifier: 'secondaryModifier'
} as const

export type TalentScalarFieldEnum = (typeof TalentScalarFieldEnum)[keyof typeof TalentScalarFieldEnum]


export const Trader_repScalarFieldEnum = {
  id: 'id',
  reputationLevel: 'reputationLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  shopId: 'shopId',
  userId: 'userId'
} as const

export type Trader_repScalarFieldEnum = (typeof Trader_repScalarFieldEnum)[keyof typeof Trader_repScalarFieldEnum]


export const UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  displayUsername: 'displayUsername',
  about: 'about',
  email: 'email',
  emailVerified: 'emailVerified',
  banned: 'banned',
  banReason: 'banReason',
  banExpires: 'banExpires',
  usernameSet: 'usernameSet',
  cash: 'cash',
  bank_balance: 'bank_balance',
  last_activity: 'last_activity',
  chatBannedUntil: 'chatBannedUntil',
  userType: 'userType',
  avatar: 'avatar',
  profileBanner: 'profileBanner',
  jobLevel: 'jobLevel',
  jobPayoutHour: 'jobPayoutHour',
  blockNextJobPayout: 'blockNextJobPayout',
  roguelikeMap: 'roguelikeMap',
  roguelikeLevel: 'roguelikeLevel',
  roguelikeHighscore: 'roguelikeHighscore',
  jailedUntil: 'jailedUntil',
  jailReason: 'jailReason',
  hospitalisedUntil: 'hospitalisedUntil',
  hospitalisedHealingType: 'hospitalisedHealingType',
  hospitalisedReason: 'hospitalisedReason',
  energy: 'energy',
  lastEnergyTick: 'lastEnergyTick',
  focus: 'focus',
  dailyFatigueUsed: 'dailyFatigueUsed',
  lastFatigueReset: 'lastFatigueReset',
  level: 'level',
  xp: 'xp',
  actionPoints: 'actionPoints',
  nextAPTick: 'nextAPTick',
  maxActionPoints: 'maxActionPoints',
  currentHealth: 'currentHealth',
  nextHPTick: 'nextHPTick',
  health: 'health',
  talentPoints: 'talentPoints',
  activeCourseId: 'activeCourseId',
  courseEnds: 'courseEnds',
  class: 'class',
  classPoints: 'classPoints',
  adminNotes: 'adminNotes',
  combatLevel: 'combatLevel',
  discordID: 'discordID',
  currentMission: 'currentMission',
  missionEnds: 'missionEnds',
  profileDetailBanUntil: 'profileDetailBanUntil',
  weeklyBuyLimitRemaining: 'weeklyBuyLimitRemaining',
  dailyQuestsRewardClaimed: 'dailyQuestsRewardClaimed',
  currentMapLocation: 'currentMapLocation',
  travelStartTime: 'travelStartTime',
  travelEndTime: 'travelEndTime',
  travelMethod: 'travelMethod',
  defeatedNpcs: 'defeatedNpcs',
  pushNotificationsEnabled: 'pushNotificationsEnabled',
  gangCreds: 'gangCreds',
  lastNewsIDRead: 'lastNewsIDRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  gangId: 'gangId',
  jobId: 'jobId',
  referrerId: 'referrerId',
  statusMessage: 'statusMessage',
  statusMessageUpdatedAt: 'statusMessageUpdatedAt',
  showLastOnline: 'showLastOnline'
} as const

export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


export const User_equipped_abilitiesScalarFieldEnum = {
  userId: 'userId',
  equippedAbility1Id: 'equippedAbility1Id',
  equippedAbility2Id: 'equippedAbility2Id',
  equippedAbility3Id: 'equippedAbility3Id',
  equippedAbility4Id: 'equippedAbility4Id'
} as const

export type User_equipped_abilitiesScalarFieldEnum = (typeof User_equipped_abilitiesScalarFieldEnum)[keyof typeof User_equipped_abilitiesScalarFieldEnum]


export const SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  token: 'token',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  impersonatedBy: 'impersonatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type SessionScalarFieldEnum = (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]


export const AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type AccountScalarFieldEnum = (typeof AccountScalarFieldEnum)[keyof typeof AccountScalarFieldEnum]


export const VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type VerificationScalarFieldEnum = (typeof VerificationScalarFieldEnum)[keyof typeof VerificationScalarFieldEnum]


export const User_itemScalarFieldEnum = {
  id: 'id',
  count: 'count',
  upgradeLevel: 'upgradeLevel',
  quality: 'quality',
  isTradeable: 'isTradeable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  itemId: 'itemId'
} as const

export type User_itemScalarFieldEnum = (typeof User_itemScalarFieldEnum)[keyof typeof User_itemScalarFieldEnum]


export const User_recipeScalarFieldEnum = {
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  craftingRecipeId: 'craftingRecipeId',
  userId: 'userId'
} as const

export type User_recipeScalarFieldEnum = (typeof User_recipeScalarFieldEnum)[keyof typeof User_recipeScalarFieldEnum]


export const User_completed_courseScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type User_completed_courseScalarFieldEnum = (typeof User_completed_courseScalarFieldEnum)[keyof typeof User_completed_courseScalarFieldEnum]


export const User_talentScalarFieldEnum = {
  level: 'level',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  talentId: 'talentId',
  userId: 'userId'
} as const

export type User_talentScalarFieldEnum = (typeof User_talentScalarFieldEnum)[keyof typeof User_talentScalarFieldEnum]


export const User_achievementsScalarFieldEnum = {
  userId: 'userId',
  battleWins: 'battleWins',
  npcBattleWins: 'npcBattleWins',
  craftsCompleted: 'craftsCompleted',
  marketItemsSold: 'marketItemsSold',
  marketMoneyMade: 'marketMoneyMade',
  totalMuggingGain: 'totalMuggingGain',
  totalMuggingLoss: 'totalMuggingLoss',
  totalCasinoProfitLoss: 'totalCasinoProfitLoss',
  questsCompleted: 'questsCompleted',
  dailyQuestsCompleted: 'dailyQuestsCompleted',
  coursesCompleted: 'coursesCompleted',
  roguelikeNodesCompleted: 'roguelikeNodesCompleted',
  roguelikeMapsCompleted: 'roguelikeMapsCompleted',
  examsCompleted: 'examsCompleted',
  totalBountyRewards: 'totalBountyRewards',
  totalBountyPlaced: 'totalBountyPlaced',
  totalMissionHours: 'totalMissionHours',
  suggestionsVoted: 'suggestionsVoted',
  encountersCompleted: 'encountersCompleted'
} as const

export type User_achievementsScalarFieldEnum = (typeof User_achievementsScalarFieldEnum)[keyof typeof User_achievementsScalarFieldEnum]


export const User_crafting_queueScalarFieldEnum = {
  id: 'id',
  startedAt: 'startedAt',
  endsAt: 'endsAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  craftingRecipeId: 'craftingRecipeId'
} as const

export type User_crafting_queueScalarFieldEnum = (typeof User_crafting_queueScalarFieldEnum)[keyof typeof User_crafting_queueScalarFieldEnum]


export const PropertyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  propertyType: 'propertyType',
  cost: 'cost',
  upkeep: 'upkeep',
  slots: 'slots',
  buffs: 'buffs',
  description: 'description',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PropertyScalarFieldEnum = (typeof PropertyScalarFieldEnum)[keyof typeof PropertyScalarFieldEnum]


export const User_propertyScalarFieldEnum = {
  id: 'id',
  purchaseDate: 'purchaseDate',
  lastUpkeepPaid: 'lastUpkeepPaid',
  furniture: 'furniture',
  customization: 'customization',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  propertyId: 'propertyId'
} as const

export type User_propertyScalarFieldEnum = (typeof User_propertyScalarFieldEnum)[keyof typeof User_propertyScalarFieldEnum]


export const PetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  species: 'species',
  maxLevel: 'maxLevel',
  evolution_stages: 'evolution_stages',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PetScalarFieldEnum = (typeof PetScalarFieldEnum)[keyof typeof PetScalarFieldEnum]


export const User_petScalarFieldEnum = {
  id: 'id',
  name: 'name',
  level: 'level',
  isActive: 'isActive',
  xp: 'xp',
  nextLevelXp: 'nextLevelXp',
  happiness: 'happiness',
  energy: 'energy',
  evolution: 'evolution',
  userId: 'userId',
  petId: 'petId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type User_petScalarFieldEnum = (typeof User_petScalarFieldEnum)[keyof typeof User_petScalarFieldEnum]


export const Friend_requestScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
} as const

export type Friend_requestScalarFieldEnum = (typeof Friend_requestScalarFieldEnum)[keyof typeof Friend_requestScalarFieldEnum]


export const FriendScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  friendId: 'friendId',
  note: 'note'
} as const

export type FriendScalarFieldEnum = (typeof FriendScalarFieldEnum)[keyof typeof FriendScalarFieldEnum]


export const RivalScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  rivalId: 'rivalId',
  note: 'note'
} as const

export type RivalScalarFieldEnum = (typeof RivalScalarFieldEnum)[keyof typeof RivalScalarFieldEnum]


export const Status_effectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  source: 'source',
  effectType: 'effectType',
  category: 'category',
  tier: 'tier',
  duration: 'duration',
  modifier: 'modifier',
  modifierType: 'modifierType',
  stackable: 'stackable',
  maxStacks: 'maxStacks',
  disabled: 'disabled',
  description: 'description'
} as const

export type Status_effectScalarFieldEnum = (typeof Status_effectScalarFieldEnum)[keyof typeof Status_effectScalarFieldEnum]


export const User_status_effectScalarFieldEnum = {
  id: 'id',
  stacks: 'stacks',
  endsAt: 'endsAt',
  customName: 'customName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  effectId: 'effectId',
  userId: 'userId'
} as const

export type User_status_effectScalarFieldEnum = (typeof User_status_effectScalarFieldEnum)[keyof typeof User_status_effectScalarFieldEnum]


export const User_skillScalarFieldEnum = {
  id: 'id',
  skillType: 'skillType',
  level: 'level',
  experience: 'experience',
  talentPoints: 'talentPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type User_skillScalarFieldEnum = (typeof User_skillScalarFieldEnum)[keyof typeof User_skillScalarFieldEnum]


export const Explore_static_nodeScalarFieldEnum = {
  id: 'id',
  nodeType: 'nodeType',
  title: 'title',
  description: 'description',
  position: 'position',
  metadata: 'metadata',
  location: 'location',
  shopId: 'shopId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Explore_static_nodeScalarFieldEnum = (typeof Explore_static_nodeScalarFieldEnum)[keyof typeof Explore_static_nodeScalarFieldEnum]


export const Explore_player_nodeScalarFieldEnum = {
  id: 'id',
  nodeType: 'nodeType',
  title: 'title',
  description: 'description',
  position: 'position',
  metadata: 'metadata',
  location: 'location',
  status: 'status',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
} as const

export type Explore_player_nodeScalarFieldEnum = (typeof Explore_player_nodeScalarFieldEnum)[keyof typeof Explore_player_nodeScalarFieldEnum]


export const Story_seasonScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  requiredLevel: 'requiredLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_seasonScalarFieldEnum = (typeof Story_seasonScalarFieldEnum)[keyof typeof Story_seasonScalarFieldEnum]


export const Story_chapterScalarFieldEnum = {
  id: 'id',
  seasonId: 'seasonId',
  name: 'name',
  description: 'description',
  order: 'order',
  unlockDate: 'unlockDate',
  requiredLevel: 'requiredLevel',
  requiredChapterIds: 'requiredChapterIds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_chapterScalarFieldEnum = (typeof Story_chapterScalarFieldEnum)[keyof typeof Story_chapterScalarFieldEnum]


export const Story_episodeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  episodeType: 'episodeType',
  exploreLocation: 'exploreLocation',
  content: 'content',
  choices: 'choices',
  objectiveId: 'objectiveId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type Story_episodeScalarFieldEnum = (typeof Story_episodeScalarFieldEnum)[keyof typeof Story_episodeScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const JsonNullValueInput = {
  JsonNull: JsonNull
} as const

export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


export const NullableJsonNullValueInput = {
  DbNull: DbNull,
  JsonNull: JsonNull
} as const

export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


export const JsonNullValueFilter = {
  DbNull: DbNull,
  JsonNull: JsonNull,
  AnyNull: AnyNull
} as const

export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


export const action_logOrderByRelevanceFieldEnum = {
  logType: 'logType'
} as const

export type action_logOrderByRelevanceFieldEnum = (typeof action_logOrderByRelevanceFieldEnum)[keyof typeof action_logOrderByRelevanceFieldEnum]


export const game_configOrderByRelevanceFieldEnum = {
  key: 'key',
  category: 'category'
} as const

export type game_configOrderByRelevanceFieldEnum = (typeof game_configOrderByRelevanceFieldEnum)[keyof typeof game_configOrderByRelevanceFieldEnum]


export const bountyOrderByRelevanceFieldEnum = {
  reason: 'reason'
} as const

export type bountyOrderByRelevanceFieldEnum = (typeof bountyOrderByRelevanceFieldEnum)[keyof typeof bountyOrderByRelevanceFieldEnum]


export const chat_messageOrderByRelevanceFieldEnum = {
  message: 'message',
  announcementType: 'announcementType'
} as const

export type chat_messageOrderByRelevanceFieldEnum = (typeof chat_messageOrderByRelevanceFieldEnum)[keyof typeof chat_messageOrderByRelevanceFieldEnum]


export const chat_roomOrderByRelevanceFieldEnum = {
  name: 'name'
} as const

export type chat_roomOrderByRelevanceFieldEnum = (typeof chat_roomOrderByRelevanceFieldEnum)[keyof typeof chat_roomOrderByRelevanceFieldEnum]


export const creatureOrderByRelevanceFieldEnum = {
  name: 'name',
  image: 'image'
} as const

export type creatureOrderByRelevanceFieldEnum = (typeof creatureOrderByRelevanceFieldEnum)[keyof typeof creatureOrderByRelevanceFieldEnum]


export const daily_missionOrderByRelevanceFieldEnum = {
  missionName: 'missionName',
  description: 'description'
} as const

export type daily_missionOrderByRelevanceFieldEnum = (typeof daily_missionOrderByRelevanceFieldEnum)[keyof typeof daily_missionOrderByRelevanceFieldEnum]


export const drop_chanceOrderByRelevanceFieldEnum = {
  scavengeType: 'scavengeType'
} as const

export type drop_chanceOrderByRelevanceFieldEnum = (typeof drop_chanceOrderByRelevanceFieldEnum)[keyof typeof drop_chanceOrderByRelevanceFieldEnum]


export const game_statsOrderByRelevanceFieldEnum = {
  stats_type: 'stats_type',
  info: 'info'
} as const

export type game_statsOrderByRelevanceFieldEnum = (typeof game_statsOrderByRelevanceFieldEnum)[keyof typeof game_statsOrderByRelevanceFieldEnum]


export const gangOrderByRelevanceFieldEnum = {
  name: 'name',
  about: 'about',
  avatar: 'avatar',
  gangMOTD: 'gangMOTD'
} as const

export type gangOrderByRelevanceFieldEnum = (typeof gangOrderByRelevanceFieldEnum)[keyof typeof gangOrderByRelevanceFieldEnum]


export const gang_logOrderByRelevanceFieldEnum = {
  action: 'action',
  info: 'info'
} as const

export type gang_logOrderByRelevanceFieldEnum = (typeof gang_logOrderByRelevanceFieldEnum)[keyof typeof gang_logOrderByRelevanceFieldEnum]


export const itemOrderByRelevanceFieldEnum = {
  name: 'name',
  about: 'about',
  image: 'image'
} as const

export type itemOrderByRelevanceFieldEnum = (typeof itemOrderByRelevanceFieldEnum)[keyof typeof itemOrderByRelevanceFieldEnum]


export const jobOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  avatar: 'avatar',
  payFormula: 'payFormula',
  strengthFormula: 'strengthFormula',
  intelligenceFormula: 'intelligenceFormula',
  dexterityFormula: 'dexterityFormula',
  defenceFormula: 'defenceFormula',
  enduranceFormula: 'enduranceFormula',
  vitalityFormula: 'vitalityFormula'
} as const

export type jobOrderByRelevanceFieldEnum = (typeof jobOrderByRelevanceFieldEnum)[keyof typeof jobOrderByRelevanceFieldEnum]


export const notificationOrderByRelevanceFieldEnum = {
  notificationType: 'notificationType',
  details: 'details'
} as const

export type notificationOrderByRelevanceFieldEnum = (typeof notificationOrderByRelevanceFieldEnum)[keyof typeof notificationOrderByRelevanceFieldEnum]


export const pollOrderByRelevanceFieldEnum = {
  title: 'title'
} as const

export type pollOrderByRelevanceFieldEnum = (typeof pollOrderByRelevanceFieldEnum)[keyof typeof pollOrderByRelevanceFieldEnum]


export const private_messageOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type private_messageOrderByRelevanceFieldEnum = (typeof private_messageOrderByRelevanceFieldEnum)[keyof typeof private_messageOrderByRelevanceFieldEnum]


export const profile_commentOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type profile_commentOrderByRelevanceFieldEnum = (typeof profile_commentOrderByRelevanceFieldEnum)[keyof typeof profile_commentOrderByRelevanceFieldEnum]


export const push_tokenOrderByRelevanceFieldEnum = {
  token: 'token'
} as const

export type push_tokenOrderByRelevanceFieldEnum = (typeof push_tokenOrderByRelevanceFieldEnum)[keyof typeof push_tokenOrderByRelevanceFieldEnum]


export const daily_questOrderByRelevanceFieldEnum = {
  targetAction: 'targetAction'
} as const

export type daily_questOrderByRelevanceFieldEnum = (typeof daily_questOrderByRelevanceFieldEnum)[keyof typeof daily_questOrderByRelevanceFieldEnum]


export const questOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  questInfo: 'questInfo',
  questChainName: 'questChainName'
} as const

export type questOrderByRelevanceFieldEnum = (typeof questOrderByRelevanceFieldEnum)[keyof typeof questOrderByRelevanceFieldEnum]


export const quest_objectiveOrderByRelevanceFieldEnum = {
  description: 'description',
  targetAction: 'targetAction'
} as const

export type quest_objectiveOrderByRelevanceFieldEnum = (typeof quest_objectiveOrderByRelevanceFieldEnum)[keyof typeof quest_objectiveOrderByRelevanceFieldEnum]


export const registration_codeOrderByRelevanceFieldEnum = {
  code: 'code',
  note: 'note'
} as const

export type registration_codeOrderByRelevanceFieldEnum = (typeof registration_codeOrderByRelevanceFieldEnum)[keyof typeof registration_codeOrderByRelevanceFieldEnum]


export const shopOrderByRelevanceFieldEnum = {
  name: 'name',
  avatar: 'avatar',
  description: 'description'
} as const

export type shopOrderByRelevanceFieldEnum = (typeof shopOrderByRelevanceFieldEnum)[keyof typeof shopOrderByRelevanceFieldEnum]


export const suggestionOrderByRelevanceFieldEnum = {
  title: 'title',
  content: 'content'
} as const

export type suggestionOrderByRelevanceFieldEnum = (typeof suggestionOrderByRelevanceFieldEnum)[keyof typeof suggestionOrderByRelevanceFieldEnum]


export const suggestion_commentOrderByRelevanceFieldEnum = {
  message: 'message'
} as const

export type suggestion_commentOrderByRelevanceFieldEnum = (typeof suggestion_commentOrderByRelevanceFieldEnum)[keyof typeof suggestion_commentOrderByRelevanceFieldEnum]


export const talentOrderByRelevanceFieldEnum = {
  name: 'name',
  displayName: 'displayName',
  description: 'description'
} as const

export type talentOrderByRelevanceFieldEnum = (typeof talentOrderByRelevanceFieldEnum)[keyof typeof talentOrderByRelevanceFieldEnum]


export const userOrderByRelevanceFieldEnum = {
  username: 'username',
  displayUsername: 'displayUsername',
  about: 'about',
  email: 'email',
  banReason: 'banReason',
  avatar: 'avatar',
  profileBanner: 'profileBanner',
  jailReason: 'jailReason',
  hospitalisedReason: 'hospitalisedReason',
  class: 'class',
  adminNotes: 'adminNotes',
  discordID: 'discordID',
  statusMessage: 'statusMessage'
} as const

export type userOrderByRelevanceFieldEnum = (typeof userOrderByRelevanceFieldEnum)[keyof typeof userOrderByRelevanceFieldEnum]


export const sessionOrderByRelevanceFieldEnum = {
  token: 'token',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  impersonatedBy: 'impersonatedBy'
} as const

export type sessionOrderByRelevanceFieldEnum = (typeof sessionOrderByRelevanceFieldEnum)[keyof typeof sessionOrderByRelevanceFieldEnum]


export const accountOrderByRelevanceFieldEnum = {
  accountId: 'accountId',
  providerId: 'providerId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  scope: 'scope',
  password: 'password'
} as const

export type accountOrderByRelevanceFieldEnum = (typeof accountOrderByRelevanceFieldEnum)[keyof typeof accountOrderByRelevanceFieldEnum]


export const verificationOrderByRelevanceFieldEnum = {
  identifier: 'identifier',
  value: 'value'
} as const

export type verificationOrderByRelevanceFieldEnum = (typeof verificationOrderByRelevanceFieldEnum)[keyof typeof verificationOrderByRelevanceFieldEnum]


export const propertyOrderByRelevanceFieldEnum = {
  name: 'name',
  propertyType: 'propertyType',
  description: 'description',
  image: 'image'
} as const

export type propertyOrderByRelevanceFieldEnum = (typeof propertyOrderByRelevanceFieldEnum)[keyof typeof propertyOrderByRelevanceFieldEnum]


export const petOrderByRelevanceFieldEnum = {
  name: 'name',
  species: 'species'
} as const

export type petOrderByRelevanceFieldEnum = (typeof petOrderByRelevanceFieldEnum)[keyof typeof petOrderByRelevanceFieldEnum]


export const user_petOrderByRelevanceFieldEnum = {
  name: 'name'
} as const

export type user_petOrderByRelevanceFieldEnum = (typeof user_petOrderByRelevanceFieldEnum)[keyof typeof user_petOrderByRelevanceFieldEnum]


export const friendOrderByRelevanceFieldEnum = {
  note: 'note'
} as const

export type friendOrderByRelevanceFieldEnum = (typeof friendOrderByRelevanceFieldEnum)[keyof typeof friendOrderByRelevanceFieldEnum]


export const rivalOrderByRelevanceFieldEnum = {
  note: 'note'
} as const

export type rivalOrderByRelevanceFieldEnum = (typeof rivalOrderByRelevanceFieldEnum)[keyof typeof rivalOrderByRelevanceFieldEnum]


export const status_effectOrderByRelevanceFieldEnum = {
  name: 'name',
  source: 'source',
  category: 'category',
  description: 'description'
} as const

export type status_effectOrderByRelevanceFieldEnum = (typeof status_effectOrderByRelevanceFieldEnum)[keyof typeof status_effectOrderByRelevanceFieldEnum]


export const user_status_effectOrderByRelevanceFieldEnum = {
  customName: 'customName'
} as const

export type user_status_effectOrderByRelevanceFieldEnum = (typeof user_status_effectOrderByRelevanceFieldEnum)[keyof typeof user_status_effectOrderByRelevanceFieldEnum]


export const explore_static_nodeOrderByRelevanceFieldEnum = {
  title: 'title',
  description: 'description'
} as const

export type explore_static_nodeOrderByRelevanceFieldEnum = (typeof explore_static_nodeOrderByRelevanceFieldEnum)[keyof typeof explore_static_nodeOrderByRelevanceFieldEnum]


export const explore_player_nodeOrderByRelevanceFieldEnum = {
  title: 'title',
  description: 'description'
} as const

export type explore_player_nodeOrderByRelevanceFieldEnum = (typeof explore_player_nodeOrderByRelevanceFieldEnum)[keyof typeof explore_player_nodeOrderByRelevanceFieldEnum]


export const story_seasonOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_seasonOrderByRelevanceFieldEnum = (typeof story_seasonOrderByRelevanceFieldEnum)[keyof typeof story_seasonOrderByRelevanceFieldEnum]


export const story_chapterOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_chapterOrderByRelevanceFieldEnum = (typeof story_chapterOrderByRelevanceFieldEnum)[keyof typeof story_chapterOrderByRelevanceFieldEnum]


export const story_episodeOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description'
} as const

export type story_episodeOrderByRelevanceFieldEnum = (typeof story_episodeOrderByRelevanceFieldEnum)[keyof typeof story_episodeOrderByRelevanceFieldEnum]



/**
 * Field references
 */


/**
 * Reference to a field of type 'Int'
 */
export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


/**
 * Reference to a field of type 'String'
 */
export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


/**
 * Reference to a field of type 'Json'
 */
export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


/**
 * Reference to a field of type 'QueryMode'
 */
export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


/**
 * Reference to a field of type 'DateTime'
 */
export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


/**
 * Reference to a field of type 'Boolean'
 */
export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


/**
 * Reference to a field of type 'AuctionItemStatus'
 */
export type EnumAuctionItemStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'AuctionItemStatus'>
    


/**
 * Reference to a field of type 'BankTransactionTypes'
 */
export type EnumBankTransactionTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'BankTransactionTypes'>
    


/**
 * Reference to a field of type 'CraftingSkills'
 */
export type EnumCraftingSkillsFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'CraftingSkills'>
    


/**
 * Reference to a field of type 'LocationTypes'
 */
export type EnumLocationTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'LocationTypes'>
    


/**
 * Reference to a field of type 'CreatureStatTypes'
 */
export type EnumCreatureStatTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'CreatureStatTypes'>
    


/**
 * Reference to a field of type 'Float'
 */
export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


/**
 * Reference to a field of type 'DropChanceTypes'
 */
export type EnumDropChanceTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DropChanceTypes'>
    


/**
 * Reference to a field of type 'EquipSlots'
 */
export type EnumEquipSlotsFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'EquipSlots'>
    


/**
 * Reference to a field of type 'GangInviteTypes'
 */
export type EnumGangInviteTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'GangInviteTypes'>
    


/**
 * Reference to a field of type 'ItemTypes'
 */
export type EnumItemTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ItemTypes'>
    


/**
 * Reference to a field of type 'ItemRarities'
 */
export type EnumItemRaritiesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ItemRarities'>
    


/**
 * Reference to a field of type 'QuestObjectiveTypes'
 */
export type EnumQuestObjectiveTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QuestObjectiveTypes'>
    


/**
 * Reference to a field of type 'QuestProgressStatus'
 */
export type EnumQuestProgressStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QuestProgressStatus'>
    


/**
 * Reference to a field of type 'QuestRewardType'
 */
export type EnumQuestRewardTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QuestRewardType'>
    


/**
 * Reference to a field of type 'RecipeItemTypes'
 */
export type EnumRecipeItemTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'RecipeItemTypes'>
    


/**
 * Reference to a field of type 'ShopTypes'
 */
export type EnumShopTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ShopTypes'>
    


/**
 * Reference to a field of type 'ShopListingCurrency'
 */
export type EnumShopListingCurrencyFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ShopListingCurrency'>
    


/**
 * Reference to a field of type 'SuggestionStates'
 */
export type EnumSuggestionStatesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SuggestionStates'>
    


/**
 * Reference to a field of type 'SuggestionVoteTypes'
 */
export type EnumSuggestionVoteTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SuggestionVoteTypes'>
    


/**
 * Reference to a field of type 'TalentTree'
 */
export type EnumTalentTreeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'TalentTree'>
    


/**
 * Reference to a field of type 'TalentType'
 */
export type EnumTalentTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'TalentType'>
    


/**
 * Reference to a field of type 'BigInt'
 */
export type BigIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'BigInt'>
    


/**
 * Reference to a field of type 'UserTypes'
 */
export type EnumUserTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserTypes'>
    


/**
 * Reference to a field of type 'HospitalisedHealingTypes'
 */
export type EnumHospitalisedHealingTypesFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'HospitalisedHealingTypes'>
    


/**
 * Reference to a field of type 'ExploreNodeLocation'
 */
export type EnumExploreNodeLocationFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ExploreNodeLocation'>
    


/**
 * Reference to a field of type 'TravelMethod'
 */
export type EnumTravelMethodFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'TravelMethod'>
    


/**
 * Reference to a field of type 'ItemQuality'
 */
export type EnumItemQualityFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ItemQuality'>
    


/**
 * Reference to a field of type 'StatusEffectType'
 */
export type EnumStatusEffectTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StatusEffectType'>
    


/**
 * Reference to a field of type 'StatusEffectTier'
 */
export type EnumStatusEffectTierFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StatusEffectTier'>
    


/**
 * Reference to a field of type 'StatusEffectModifierType'
 */
export type EnumStatusEffectModifierTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StatusEffectModifierType'>
    


/**
 * Reference to a field of type 'SkillType'
 */
export type EnumSkillTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SkillType'>
    


/**
 * Reference to a field of type 'ExploreNodeType'
 */
export type EnumExploreNodeTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ExploreNodeType'>
    


/**
 * Reference to a field of type 'ExploreNodeStatus'
 */
export type EnumExploreNodeStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ExploreNodeStatus'>
    


/**
 * Reference to a field of type 'StoryEpisodeType'
 */
export type EnumStoryEpisodeTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StoryEpisodeType'>
    

/**
 * Batch Payload for updateMany & deleteMany & createMany
 */
export type BatchPayload = {
  count: number
}


export type Datasource = {
  url?: string
}
export type Datasources = {
  db?: Datasource
}

export const defineExtension = runtime.Extensions.defineExtension as unknown as runtime.Types.Extensions.ExtendsHook<"define", TypeMapCb, runtime.Types.Extensions.DefaultArgs>
export type DefaultPrismaClient = PrismaClient
export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
export interface PrismaClientOptions {
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasources?: Datasources
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasourceUrl?: string
  /**
   * @default "colorless"
   */
  errorFormat?: ErrorFormat
  /**
   * @example
   * ```
   * // Shorthand for `emit: 'stdout'`
   * log: ['query', 'info', 'warn', 'error']
   * 
   * // Emit as events only
   * log: [
   *   { emit: 'event', level: 'query' },
   *   { emit: 'event', level: 'info' },
   *   { emit: 'event', level: 'warn' }
   *   { emit: 'event', level: 'error' }
   * ]
   * 
   * / Emit as events and log to stdout
   * og: [
   *  { emit: 'stdout', level: 'query' },
   *  { emit: 'stdout', level: 'info' },
   *  { emit: 'stdout', level: 'warn' }
   *  { emit: 'stdout', level: 'error' }
   * 
   * ```
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
   */
  log?: (LogLevel | LogDefinition)[]
  /**
   * The default values for transactionOptions
   * maxWait ?= 2000
   * timeout ?= 5000
   */
  transactionOptions?: {
    maxWait?: number
    timeout?: number
    isolationLevel?: TransactionIsolationLevel
  }
  /**
   * Instance of a Driver Adapter, e.g., like one provided by `@prisma/adapter-planetscale`
   */
  adapter?: runtime.SqlDriverAdapterFactory | null
  /**
   * Global configuration for omitting model fields by default.
   * 
   * @example
   * ```
   * const prisma = new PrismaClient({
   *   omit: {
   *     user: {
   *       password: true
   *     }
   *   }
   * })
   * ```
   */
  omit?: GlobalOmitConfig
}
export type GlobalOmitConfig = {
  action_log?: Prisma.action_logOmit
  game_config?: Prisma.game_configOmit
  auction_item?: Prisma.auction_itemOmit
  bank_transaction?: Prisma.bank_transactionOmit
  battle_log?: Prisma.battle_logOmit
  bounty?: Prisma.bountyOmit
  chat_message?: Prisma.chat_messageOmit
  chat_room?: Prisma.chat_roomOmit
  crafting_recipe?: Prisma.crafting_recipeOmit
  creature?: Prisma.creatureOmit
  daily_mission?: Prisma.daily_missionOmit
  drop_chance?: Prisma.drop_chanceOmit
  equipped_item?: Prisma.equipped_itemOmit
  game_stats?: Prisma.game_statsOmit
  gang?: Prisma.gangOmit
  gang_invite?: Prisma.gang_inviteOmit
  gang_log?: Prisma.gang_logOmit
  gang_member?: Prisma.gang_memberOmit
  item?: Prisma.itemOmit
  job?: Prisma.jobOmit
  lottery?: Prisma.lotteryOmit
  lottery_entry?: Prisma.lottery_entryOmit
  notification?: Prisma.notificationOmit
  poll?: Prisma.pollOmit
  poll_response?: Prisma.poll_responseOmit
  private_message?: Prisma.private_messageOmit
  profile_comment?: Prisma.profile_commentOmit
  push_token?: Prisma.push_tokenOmit
  daily_quest?: Prisma.daily_questOmit
  quest?: Prisma.questOmit
  quest_progress?: Prisma.quest_progressOmit
  quest_objective?: Prisma.quest_objectiveOmit
  quest_objective_progress?: Prisma.quest_objective_progressOmit
  quest_reward?: Prisma.quest_rewardOmit
  recipe_item?: Prisma.recipe_itemOmit
  registration_code?: Prisma.registration_codeOmit
  shop?: Prisma.shopOmit
  shop_listing?: Prisma.shop_listingOmit
  shrine_donation?: Prisma.shrine_donationOmit
  shrine_goal?: Prisma.shrine_goalOmit
  suggestion?: Prisma.suggestionOmit
  suggestion_comment?: Prisma.suggestion_commentOmit
  suggestion_vote?: Prisma.suggestion_voteOmit
  talent?: Prisma.talentOmit
  trader_rep?: Prisma.trader_repOmit
  user?: Prisma.userOmit
  user_equipped_abilities?: Prisma.user_equipped_abilitiesOmit
  session?: Prisma.sessionOmit
  account?: Prisma.accountOmit
  verification?: Prisma.verificationOmit
  user_item?: Prisma.user_itemOmit
  user_recipe?: Prisma.user_recipeOmit
  user_completed_course?: Prisma.user_completed_courseOmit
  user_talent?: Prisma.user_talentOmit
  user_achievements?: Prisma.user_achievementsOmit
  user_crafting_queue?: Prisma.user_crafting_queueOmit
  property?: Prisma.propertyOmit
  user_property?: Prisma.user_propertyOmit
  pet?: Prisma.petOmit
  user_pet?: Prisma.user_petOmit
  friend_request?: Prisma.friend_requestOmit
  friend?: Prisma.friendOmit
  rival?: Prisma.rivalOmit
  status_effect?: Prisma.status_effectOmit
  user_status_effect?: Prisma.user_status_effectOmit
  user_skill?: Prisma.user_skillOmit
  explore_static_node?: Prisma.explore_static_nodeOmit
  explore_player_node?: Prisma.explore_player_nodeOmit
  story_season?: Prisma.story_seasonOmit
  story_chapter?: Prisma.story_chapterOmit
  story_episode?: Prisma.story_episodeOmit
}

/* Types for Logging */
export type LogLevel = 'info' | 'query' | 'warn' | 'error'
export type LogDefinition = {
  level: LogLevel
  emit: 'stdout' | 'event'
}

export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

export type GetLogType<T> = CheckIsLogLevel<
  T extends LogDefinition ? T['level'] : T
>;

export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
  ? GetLogType<T[number]>
  : never;

export type QueryEvent = {
  timestamp: Date
  query: string
  params: string
  duration: number
  target: string
}

export type LogEvent = {
  timestamp: Date
  message: string
  target: string
}
/* End Types for Logging */


export type PrismaAction =
  | 'findUnique'
  | 'findUniqueOrThrow'
  | 'findMany'
  | 'findFirst'
  | 'findFirstOrThrow'
  | 'create'
  | 'createMany'
  | 'createManyAndReturn'
  | 'update'
  | 'updateMany'
  | 'updateManyAndReturn'
  | 'upsert'
  | 'delete'
  | 'deleteMany'
  | 'executeRaw'
  | 'queryRaw'
  | 'aggregate'
  | 'count'
  | 'runCommandRaw'
  | 'findRaw'
  | 'groupBy'

/**
 * `PrismaClient` proxy available in interactive transactions.
 */
export type TransactionClient = Omit<DefaultPrismaClient, runtime.ITXClientDenyList>

