
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * Please import the `PrismaClient` class from the `client.ts` file instead.
 */

import * as runtime from "@prisma/client/runtime/client"
import type * as Prisma from "./prismaNamespace.js"


const config: runtime.GetPrismaClientConfig = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client"
    },
    "output": {
      "value": "/Users/<USER>/Developer/GitHub/chikara-academy/chikara-backend/generated/prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "client",
      "runtime": "bun"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "darwin-arm64",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "/Users/<USER>/Developer/GitHub/chikara-academy/chikara-backend/prisma/schema.prisma",
    "isCustomOutput": true
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.17.1",
  "engineVersion": "272a37d34178c2894197e17273bf937f25acdeac",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "mysql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "generator client {\n  provider   = \"prisma-client\"\n  output     = \"../generated/prisma\"\n  runtime    = \"bun\"\n  engineType = \"client\"\n}\n\ngenerator json {\n  provider = \"prisma-json-types-generator\"\n}\n\ndatasource db {\n  provider = \"mysql\"\n  url      = env(\"DATABASE_URL\")\n}\n\nmodel action_log {\n  id            Int      @id @default(autoincrement())\n  logType       String?  @db.VarChar(255)\n  info          Json     @default(\"{}\")\n  createdAt     DateTime @default(now()) @db.DateTime(0)\n  updatedAt     DateTime @updatedAt @db.DateTime(0)\n  playerId      Int?\n  secondPartyId Int?\n  player        user?    @relation(\"actionsAsPlayer\", fields: [playerId], references: [id])\n  secondParty   user?    @relation(\"actionsAsSecondParty\", fields: [secondPartyId], references: [id])\n\n  @@index([playerId])\n  @@index([secondPartyId])\n}\n\nmodel game_config {\n  id        Int      @id @default(autoincrement())\n  key       String   @unique @db.VarChar(255)\n  value     Json\n  category  String?  @db.VarChar(100)\n  isPublic  Boolean  @default(true)\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n\n  @@index([category])\n  @@index([isPublic])\n}\n\nmodel auction_item {\n  id          Int               @id @default(autoincrement())\n  quantity    Int               @default(1) @db.UnsignedInt\n  deposit     Int               @db.UnsignedInt\n  buyoutPrice Int               @db.UnsignedInt\n  endsAt      DateTime          @db.DateTime(0)\n  status      AuctionItemStatus\n  bankFunds   Boolean           @default(false)\n  createdAt   DateTime          @default(now()) @db.DateTime(0)\n  updatedAt   DateTime          @updatedAt @db.DateTime(0)\n  itemId      Int?\n  sellerId    Int?\n  item        item?             @relation(fields: [itemId], references: [id])\n  user        user?             @relation(fields: [sellerId], references: [id])\n\n  @@index([itemId])\n  @@index([sellerId])\n}\n\nmodel bank_transaction {\n  id                     Int                  @id @default(autoincrement())\n  transaction_type       BankTransactionTypes\n  cash                   Int                  @db.UnsignedInt\n  transactionFee         Int                  @db.UnsignedInt\n  initiatorCashBalance   Int                  @db.UnsignedInt\n  initiatorBankBalance   Int                  @db.UnsignedInt\n  secondPartyCashBalance Int?                 @db.UnsignedInt\n  secondPartyBankBalance Int?                 @db.UnsignedInt\n  createdAt              DateTime             @default(now()) @db.DateTime(0)\n  updatedAt              DateTime             @updatedAt @db.DateTime(0)\n  initiatorId            Int?\n  secondPartyId          Int?\n  userId                 Int?\n  initiator              user?                @relation(\"initiatedBankTransactions\", fields: [initiatorId], references: [id])\n  secondParty            user?                @relation(\"secondPartyBankTransactions\", fields: [secondPartyId], references: [id])\n  user                   user?                @relation(\"userBankTransactions\", fields: [userId], references: [id])\n\n  @@index([initiatorId])\n  @@index([secondPartyId])\n  @@index([userId])\n}\n\nmodel battle_log {\n  id         Int      @id @default(autoincrement())\n  victory    Boolean\n  createdAt  DateTime @default(now()) @db.DateTime(0)\n  updatedAt  DateTime @updatedAt @db.DateTime(0)\n  attackerId Int?\n  defenderId Int?\n  attacker   user?    @relation(\"battlesAsAttacker\", fields: [attackerId], references: [id])\n  defender   user?    @relation(\"battlesAsDefender\", fields: [defenderId], references: [id])\n\n  @@index([attackerId])\n  @@index([defenderId])\n}\n\nmodel bounty {\n  id          Int      @id @default(autoincrement())\n  amount      Int\n  reason      String?  @db.VarChar(255)\n  active      Boolean? @default(true)\n  createdAt   DateTime @default(now()) @db.DateTime(0)\n  updatedAt   DateTime @updatedAt @db.DateTime(0)\n  placerId    Int?\n  targetId    Int?\n  claimedById Int?\n  placer      user?    @relation(\"placedBounties\", fields: [placerId], references: [id])\n  target      user?    @relation(\"targetedBounties\", fields: [targetId], references: [id])\n  claimer     user?    @relation(\"claimedBounties\", fields: [claimedById], references: [id])\n\n  @@index([claimedById])\n  @@index([placerId])\n  @@index([targetId])\n}\n\nmodel chat_message {\n  id               Int            @id @default(autoincrement())\n  message          String         @db.VarChar(255)\n  hidden           Boolean?       @default(false)\n  announcementType String?        @db.VarChar(50)\n  createdAt        DateTime       @default(now()) @db.DateTime(0)\n  updatedAt        DateTime       @updatedAt @db.DateTime(0)\n  chatRoomId       Int?\n  userId           Int?\n  parentMessageId  Int?\n  chat_room        chat_room?     @relation(fields: [chatRoomId], references: [id])\n  user             user?          @relation(fields: [userId], references: [id])\n  chat_message     chat_message?  @relation(\"chat_messageTochat_message\", fields: [parentMessageId], references: [id])\n  parent_message   chat_message[] @relation(\"chat_messageTochat_message\")\n\n  @@index([chatRoomId])\n  @@index([parentMessageId])\n  @@index([userId])\n}\n\nmodel chat_room {\n  id           Int            @id @default(autoincrement())\n  name         String         @db.VarChar(255)\n  gangId       Int?\n  chat_message chat_message[]\n  gang         gang?          @relation(fields: [gangId], references: [id])\n\n  @@index([gangId])\n}\n\nmodel crafting_recipe {\n  id                  Int                   @id @default(autoincrement())\n  cost                Int\n  craftTime           Int                   @db.UnsignedInt\n  isUnlockable        Boolean               @default(false)\n  requiredSkillType   CraftingSkills?\n  requiredSkillLevel  Int                   @default(0) @db.UnsignedInt\n  item                item[]\n  recipe_item         recipe_item[]\n  user_crafting_queue user_crafting_queue[]\n  user_recipe         user_recipe[]\n}\n\nmodel creature {\n  id              Int               @id @default(autoincrement())\n  name            String            @db.VarChar(255)\n  image           String            @db.VarChar(255)\n  minFloor        Int               @default(0)\n  maxFloor        Int               @default(99999)\n  boss            Boolean\n  health          Int               @default(1)\n  currentHealth   Int?\n  strength        Int               @default(1)\n  defence         Int               @default(1)\n  weaponDamage    Int               @default(1)\n  location        LocationTypes?\n  statType        CreatureStatTypes\n  createdAt       DateTime          @default(now()) @db.DateTime(0)\n  updatedAt       DateTime          @updatedAt @db.DateTime(0)\n  quest_objective quest_objective[]\n}\n\nmodel daily_mission {\n  id                 Int      @id @default(autoincrement())\n  tier               Int\n  missionName        String   @db.VarChar(255)\n  description        String   @db.Text\n  missionDate        DateTime @db.Date\n  duration           Int      @db.UnsignedInt\n  minCashReward      Int      @default(0) @db.UnsignedInt\n  maxCashReward      Int      @default(0) @db.UnsignedInt\n  minExpReward       Int      @default(0) @db.UnsignedInt\n  maxExpReward       Int      @default(0) @db.UnsignedInt\n  levelReq           Int      @default(0) @db.UnsignedInt\n  hoursReq           Int      @default(0) @db.UnsignedInt\n  itemRewardQuantity Int?     @db.UnsignedInt\n  createdAt          DateTime @default(now()) @db.DateTime(0)\n  updatedAt          DateTime @updatedAt @db.DateTime(0)\n  itemRewardId       Int?\n  item               item?    @relation(fields: [itemRewardId], references: [id])\n\n  @@index([itemRewardId])\n}\n\nmodel drop_chance {\n  id             Int             @id @default(autoincrement())\n  dropRate       Float\n  dropChanceType DropChanceTypes\n  quantity       Int             @default(1) @db.UnsignedInt\n  location       LocationTypes?\n  minLevel       Int?\n  maxLevel       Int?\n  scavengeType   String?         @db.VarChar(255)\n  createdAt      DateTime        @default(now()) @db.DateTime(0)\n  updatedAt      DateTime        @updatedAt @db.DateTime(0)\n  itemId         Int?\n  item           item?           @relation(fields: [itemId], references: [id])\n\n  @@index([itemId])\n  @@index([dropChanceType, location, minLevel, maxLevel])\n}\n\nmodel equipped_item {\n  id         Int        @id @default(autoincrement())\n  createdAt  DateTime   @default(now()) @db.DateTime(0)\n  updatedAt  DateTime   @updatedAt @db.DateTime(0)\n  userId     Int?\n  slot       EquipSlots\n  userItemId Int?\n  user       user?      @relation(fields: [userId], references: [id])\n  user_item  user_item? @relation(fields: [userItemId], references: [id])\n\n  @@unique([userId, slot])\n  @@index([userItemId])\n  @@index([userId])\n}\n\nmodel game_stats {\n  id         Int      @id @default(autoincrement())\n  stats_type String   @db.VarChar(255)\n  info       String   @db.VarChar(2000)\n  createdAt  DateTime @default(now()) @db.DateTime(0)\n  updatedAt  DateTime @updatedAt @db.DateTime(0)\n  playerId   Int?\n  user       user?    @relation(fields: [playerId], references: [id])\n\n  @@index([playerId])\n}\n\nmodel gang {\n  id                 Int           @id @default(autoincrement())\n  name               String        @db.VarChar(255)\n  about              String?       @db.Text\n  avatar             String?       @db.VarChar(255)\n  treasury_balance   Int?          @default(0) @db.UnsignedInt\n  hideout_level      Int?          @default(0) @db.UnsignedInt\n  materialsResource  Int?          @default(0) @db.UnsignedInt\n  essenceResource    Int?          @default(0) @db.UnsignedInt\n  dailyEssenceGained Int?          @default(0) @db.UnsignedInt\n  toolsResource      Int?          @default(0) @db.UnsignedInt\n  techResource       Int?          @default(0) @db.UnsignedInt\n  weeklyRespect      Int?          @default(0) @db.UnsignedInt\n  totalRespect       Int?          @default(0) @db.UnsignedInt\n  gangMOTD           String?       @db.Text\n  createdAt          DateTime      @default(now()) @db.DateTime(0)\n  updatedAt          DateTime      @updatedAt @db.DateTime(0)\n  ownerId            Int\n  chat_room          chat_room[]\n  user               user?         @relation(\"gangOwner\", fields: [ownerId], references: [id])\n  gang_invite        gang_invite[]\n  gang_log           gang_log[]\n  gang_member        gang_member[]\n  members            user[]        @relation(\"gangMembers\")\n\n  @@index([ownerId])\n}\n\nmodel gang_invite {\n  id                                 Int             @id @default(autoincrement())\n  inviteType                         GangInviteTypes\n  createdAt                          DateTime        @default(now()) @db.DateTime(0)\n  updatedAt                          DateTime        @updatedAt @db.DateTime(0)\n  gangId                             Int?\n  senderId                           Int?\n  recipientId                        Int?\n  gang                               gang?           @relation(fields: [gangId], references: [id])\n  user_gang_invite_senderIdTouser    user?           @relation(\"gang_invite_senderIdTouser\", fields: [senderId], references: [id])\n  user_gang_invite_recipientIdTouser user?           @relation(\"gang_invite_recipientIdTouser\", fields: [recipientId], references: [id])\n\n  @@index([gangId])\n  @@index([recipientId])\n  @@index([senderId])\n}\n\nmodel gang_log {\n  id                                Int      @id @default(autoincrement())\n  action                            String?  @db.VarChar(255)\n  info                              String   @db.Text\n  createdAt                         DateTime @default(now()) @db.DateTime(0)\n  updatedAt                         DateTime @updatedAt @db.DateTime(0)\n  gangId                            Int?\n  gangMemberId                      Int?\n  secondPartyId                     Int?\n  gang                              gang?    @relation(fields: [gangId], references: [id])\n  user_gang_log_gangMemberIdTouser  user?    @relation(\"gang_log_gangMemberIdTouser\", fields: [gangMemberId], references: [id])\n  user_gang_log_secondPartyIdTouser user?    @relation(\"gang_log_secondPartyIdTouser\", fields: [secondPartyId], references: [id])\n\n  @@index([gangId])\n  @@index([gangMemberId])\n  @@index([secondPartyId])\n}\n\nmodel gang_member {\n  id                Int      @id @default(autoincrement())\n  rank              Int      @default(0) @db.UnsignedInt\n  payoutShare       Float    @default(0) @db.Float\n  weeklyMaterials   Int?     @default(0) @db.UnsignedInt\n  weeklyEssence     Int?     @default(0) @db.UnsignedInt\n  weeklyTools       Int?     @default(0) @db.UnsignedInt\n  weeklyRespect     Int?     @default(0) @db.UnsignedInt\n  totalContribution Int?     @default(0) @db.UnsignedInt\n  createdAt         DateTime @default(now()) @db.DateTime(0)\n  updatedAt         DateTime @updatedAt @db.DateTime(0)\n  gangId            Int?\n  userId            Int?\n  gang              gang?    @relation(fields: [gangId], references: [id])\n  user              user?    @relation(fields: [userId], references: [id])\n\n  @@index([gangId])\n  @@index([userId])\n}\n\nmodel item {\n  id           Int          @id @default(autoincrement())\n  name         String       @db.Text\n  itemType     ItemTypes\n  rarity       ItemRarities\n  level        Int          @default(1)\n  about        String?      @db.Text\n  cashValue    Int?         @default(0)\n  image        String?      @db.VarChar(255)\n  damage       Int?         @default(0)\n  armour       Int?         @default(0)\n  health       Int?         @default(0)\n  energy       Int?         @default(0)\n  actionPoints Int?         @default(0)\n  baseAmmo     Int?         @default(0)\n\n  /// [ItemEffects]\n  itemEffects Json?\n\n  createdAt       DateTime          @default(now()) @db.DateTime(0)\n  updatedAt       DateTime          @updatedAt @db.DateTime(0)\n  recipeUnlockId  Int?              @unique\n  petUnlockId     Int?              @unique\n  auction_item    auction_item[]\n  daily_mission   daily_mission[]\n  drop_chance     drop_chance[]\n  crafting_recipe crafting_recipe?  @relation(fields: [recipeUnlockId], references: [id])\n  pet             pet?              @relation(fields: [petUnlockId], references: [id])\n  recipe_item     recipe_item[]\n  shop_listing    shop_listing[]\n  user_item       user_item[]\n  quest_reward    quest_reward[]\n  daily_quest     daily_quest[]\n  quest_objective quest_objective[]\n\n  @@index([recipeUnlockId])\n}\n\nmodel job {\n  id                  Int      @id @default(autoincrement())\n  name                String   @db.VarChar(255)\n  description         String?  @db.VarChar(255)\n  avatar              String?  @db.VarChar(255)\n  payFormula          String?  @db.VarChar(255)\n  strengthFormula     String?  @db.VarChar(255)\n  intelligenceFormula String?  @db.VarChar(255)\n  dexterityFormula    String?  @db.VarChar(255)\n  defenceFormula      String?  @db.VarChar(255)\n  enduranceFormula    String?  @db.VarChar(255)\n  vitalityFormula     String?  @db.VarChar(255)\n  createdAt           DateTime @default(now()) @db.DateTime(0)\n  updatedAt           DateTime @updatedAt @db.DateTime(0)\n  user                user[]\n}\n\nmodel lottery {\n  id            Int             @id @default(autoincrement())\n  drawDate      DateTime        @unique(map: \"drawDate\") @db.DateTime(0)\n  prizeAmount   Int             @default(0) @db.UnsignedInt\n  entries       Int             @default(0) @db.UnsignedInt\n  createdAt     DateTime        @default(now()) @db.DateTime(0)\n  updatedAt     DateTime        @updatedAt @db.DateTime(0)\n  winnerId      Int?\n  user          user?           @relation(fields: [winnerId], references: [id])\n  lottery_entry lottery_entry[]\n\n  @@index([winnerId])\n}\n\nmodel lottery_entry {\n  id        Int      @id @default(autoincrement())\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  lotteryId Int?\n  userId    Int?\n  lottery   lottery? @relation(fields: [lotteryId], references: [id])\n  user      user?    @relation(fields: [userId], references: [id])\n\n  @@index([lotteryId])\n  @@index([userId])\n}\n\nmodel notification {\n  id               Int      @id @default(autoincrement())\n  notificationType String   @db.VarChar(100)\n  details          String?  @db.Text\n  read             Boolean?\n  createdAt        DateTime @default(now()) @db.DateTime(0)\n  updatedAt        DateTime @updatedAt @db.DateTime(0)\n  userId           Int?\n  user             user?    @relation(fields: [userId], references: [id])\n\n  @@index([userId])\n}\n\nmodel poll {\n  id            Int             @id @default(autoincrement())\n  title         String          @db.VarChar(255)\n  ended         Boolean         @default(false)\n  showResults   Boolean         @default(false)\n  createdAt     DateTime        @default(now()) @db.DateTime(0)\n  updatedAt     DateTime        @updatedAt @db.DateTime(0)\n  poll_response poll_response[]\n}\n\nmodel poll_response {\n  id        Int      @id @default(autoincrement())\n  answer    Json\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  userId    Int?\n  pollId    Int?\n  user      user?    @relation(fields: [userId], references: [id])\n  poll      poll?    @relation(fields: [pollId], references: [id])\n\n  @@index([pollId])\n  @@index([userId])\n}\n\nmodel private_message {\n  id                                    Int      @id @default(autoincrement())\n  message                               String   @db.Text\n  read                                  Boolean?\n  isGlobal                              Boolean? @default(false)\n  createdAt                             DateTime @default(now()) @db.DateTime(0)\n  updatedAt                             DateTime @updatedAt @db.DateTime(0)\n  senderId                              Int?\n  receiverId                            Int?\n  user_private_message_senderIdTouser   user?    @relation(\"private_message_senderIdTouser\", fields: [senderId], references: [id])\n  user_private_message_receiverIdTouser user?    @relation(\"private_message_receiverIdTouser\", fields: [receiverId], references: [id])\n\n  @@index([receiverId])\n  @@index([senderId])\n}\n\nmodel profile_comment {\n  id                                    Int      @id @default(autoincrement())\n  message                               String   @db.VarChar(255)\n  createdAt                             DateTime @default(now()) @db.DateTime(0)\n  updatedAt                             DateTime @updatedAt @db.DateTime(0)\n  senderId                              Int?\n  receiverId                            Int?\n  user_profile_comment_senderIdTouser   user?    @relation(\"profile_comment_senderIdTouser\", fields: [senderId], references: [id])\n  user_profile_comment_receiverIdTouser user?    @relation(\"profile_comment_receiverIdTouser\", fields: [receiverId], references: [id])\n\n  @@index([receiverId])\n  @@index([senderId])\n}\n\nmodel push_token {\n  id        Int      @id @default(autoincrement())\n  token     String   @unique(map: \"token\") @db.VarChar(255)\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  userId    Int?\n  user      user?    @relation(fields: [userId], references: [id])\n\n  @@index([userId])\n}\n\nmodel daily_quest {\n  id                 Int                 @id @default(autoincrement())\n  objectiveType      QuestObjectiveTypes\n  target             Int?                @db.UnsignedInt\n  targetAction       String?             @db.VarChar(255)\n  quantity           Int?                @db.UnsignedInt\n  questStatus        QuestProgressStatus @default(in_progress)\n  count              Int                 @default(0) @db.UnsignedInt\n  cashReward         Int                 @default(0) @db.UnsignedInt\n  xpReward           Int                 @default(0) @db.UnsignedInt\n  itemRewardQuantity Int?                @db.UnsignedInt\n  createdAt          DateTime            @default(now()) @db.DateTime(0)\n  updatedAt          DateTime            @updatedAt @db.DateTime(0)\n  itemRewardId       Int?\n  userId             Int?\n  item               item?               @relation(fields: [itemRewardId], references: [id])\n  user               user?               @relation(fields: [userId], references: [id])\n\n  @@index([itemRewardId])\n  @@index([userId])\n}\n\nmodel quest {\n  id                Int      @id @default(autoincrement())\n  name              String   @db.VarChar(255)\n  description       String?  @db.VarChar(255)\n  questInfo         String?  @db.VarChar(255)\n  levelReq          Int      @default(0) @db.UnsignedInt\n  disabled          Boolean? @default(false)\n  questChainName    String?  @db.VarChar(255)\n  xpReward          Int?     @default(1) @db.UnsignedInt\n  cashReward        Int      @default(0) @db.UnsignedInt\n  repReward         Float    @default(0) @db.Float\n  talentPointReward Int      @default(0) @db.UnsignedInt\n  shopId            Int?\n  requiredQuestId   Int?\n\n  isStoryQuest   Boolean        @default(false)\n  chapterId      Int?\n  story_chapter  story_chapter? @relation(fields: [chapterId], references: [id])\n  orderInChapter Int?\n\n  shop            shop?             @relation(fields: [shopId], references: [id])\n  quest           quest?            @relation(\"questToquest\", fields: [requiredQuestId], references: [id])\n  other_quest     quest[]           @relation(\"questToquest\")\n  quest_progress  quest_progress[]\n  quest_reward    quest_reward[]\n  quest_objective quest_objective[]\n\n  @@unique([chapterId, orderInChapter])\n  @@index([requiredQuestId])\n  @@index([shopId])\n}\n\nmodel quest_progress {\n  id          Int                 @id @default(autoincrement())\n  questStatus QuestProgressStatus @default(in_progress)\n  createdAt   DateTime            @default(now()) @db.DateTime(0)\n  updatedAt   DateTime            @updatedAt @db.DateTime(0)\n  questId     Int?\n  userId      Int?\n  quest       quest?              @relation(fields: [questId], references: [id])\n  user        user?               @relation(fields: [userId], references: [id])\n\n  @@index([questId])\n  @@index([userId])\n}\n\nmodel quest_objective {\n  id                       Int                        @id @default(autoincrement())\n  description              String?                    @db.VarChar(255)\n  objectiveType            QuestObjectiveTypes\n  target                   Int?                       @db.UnsignedInt\n  targetAction             String?\n  quantity                 Int?                       @db.UnsignedInt\n  location                 LocationTypes?\n  isRequired               Boolean                    @default(true)\n  questId                  Int?\n  creatureId               Int?\n  itemId                   Int?\n  quest                    quest?                     @relation(fields: [questId], references: [id], onDelete: Cascade)\n  creature                 creature?                  @relation(fields: [creatureId], references: [id])\n  item                     item?                      @relation(fields: [itemId], references: [id])\n  quest_objective_progress quest_objective_progress[]\n  story_episode            story_episode?\n\n  @@index([questId])\n  @@index([creatureId])\n  @@index([itemId])\n}\n\nmodel quest_objective_progress {\n  id               Int                 @id @default(autoincrement())\n  count            Int                 @default(0) @db.UnsignedInt\n  status           QuestProgressStatus @default(in_progress)\n  userId           Int\n  createdAt        DateTime            @default(now()) @db.DateTime(0)\n  updatedAt        DateTime            @updatedAt @db.DateTime(0)\n  questObjectiveId Int\n  user             user                @relation(fields: [userId], references: [id], onDelete: Cascade)\n  quest_objective  quest_objective     @relation(fields: [questObjectiveId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, questObjectiveId])\n  @@index([userId])\n  @@index([questObjectiveId])\n}\n\nmodel quest_reward {\n  id           Int             @id @default(autoincrement())\n  rewardType   QuestRewardType\n  quantity     Int             @db.UnsignedInt\n  isChoice     Boolean         @default(false)\n  createdAt    DateTime        @default(now()) @db.DateTime(0)\n  updatedAt    DateTime        @updatedAt @db.DateTime(0)\n  questId      Int?\n  dailyQuestId Int?\n  itemId       Int?\n  quest        quest?          @relation(fields: [questId], references: [id], onDelete: Cascade)\n  item         item?           @relation(fields: [itemId], references: [id])\n\n  @@index([questId])\n  @@index([dailyQuestId])\n  @@index([itemId])\n}\n\nmodel recipe_item {\n  count            Int?\n  itemType         RecipeItemTypes? @default(input)\n  createdAt        DateTime         @default(now()) @db.DateTime(0)\n  updatedAt        DateTime         @updatedAt @db.DateTime(0)\n  craftingRecipeId Int\n  itemId           Int\n  crafting_recipe  crafting_recipe  @relation(fields: [craftingRecipeId], references: [id], onDelete: Cascade)\n  item             item             @relation(fields: [itemId], references: [id], onDelete: Cascade)\n\n  @@id([craftingRecipeId, itemId])\n  @@index([itemId])\n}\n\nmodel registration_code {\n  id                                      Int      @id @default(autoincrement())\n  code                                    String   @db.VarChar(255)\n  note                                    String   @db.VarChar(255)\n  unlimitedUse                            Boolean? @default(false)\n  createdAt                               DateTime @default(now()) @db.DateTime(0)\n  updatedAt                               DateTime @updatedAt @db.DateTime(0)\n  referrerId                              Int?\n  claimerId                               Int?\n  user_registration_code_referrerIdTouser user?    @relation(\"registration_code_referrerIdTouser\", fields: [referrerId], references: [id])\n  user_registration_code_claimerIdTouser  user?    @relation(\"registration_code_claimerIdTouser\", fields: [claimerId], references: [id])\n\n  @@index([claimerId])\n  @@index([referrerId])\n}\n\nmodel shop {\n  id                  Int                   @id @default(autoincrement())\n  name                String                @db.VarChar(255)\n  shopType            ShopTypes\n  avatar              String?               @db.VarChar(255)\n  description         String                @db.VarChar(255)\n  disabled            Boolean?              @default(false)\n  quest               quest[]\n  shop_listing        shop_listing[]\n  trader_rep          trader_rep[]\n  explore_static_node explore_static_node[]\n}\n\nmodel shop_listing {\n  id          Int                 @id @default(autoincrement())\n  customCost  Int?                @db.UnsignedInt\n  repRequired Int?                @db.UnsignedInt\n  stock       Int?\n  currency    ShopListingCurrency @default(yen)\n  shopId      Int?\n  itemId      Int?\n  shop        shop?               @relation(fields: [shopId], references: [id])\n  item        item?               @relation(fields: [itemId], references: [id])\n\n  @@index([itemId])\n  @@index([shopId])\n}\n\nmodel shrine_donation {\n  id           Int          @id @default(autoincrement())\n  amount       Int          @default(0) @db.UnsignedInt\n  date         DateTime     @db.Date\n  createdAt    DateTime     @default(now()) @db.DateTime(0)\n  updatedAt    DateTime     @updatedAt @db.DateTime(0)\n  shrineGoalId Int?\n  userId       Int?\n  shrine_goal  shrine_goal? @relation(fields: [shrineGoalId], references: [id])\n  user         user?        @relation(fields: [userId], references: [id])\n\n  @@index([shrineGoalId])\n  @@index([userId])\n}\n\nmodel shrine_goal {\n  id             Int      @id @default(autoincrement())\n  donationGoal   Int      @db.UnsignedInt\n  donationAmount Int      @default(0) @db.UnsignedInt\n  goalReached    Boolean  @default(false)\n  goalDate       DateTime @db.Date\n\n  /// [ShrineBuffRewards]\n  buffRewards Json\n\n  createdAt       DateTime          @default(now()) @db.DateTime(0)\n  updatedAt       DateTime          @updatedAt @db.DateTime(0)\n  shrine_donation shrine_donation[]\n}\n\nmodel suggestion {\n  id                 Int                  @id @default(autoincrement())\n  title              String               @db.VarChar(255)\n  content            String               @db.Text\n  state              SuggestionStates?    @default(New)\n  upvotes            Int?                 @default(0)\n  downvotes          Int?                 @default(0)\n  totalComments      Int?                 @default(0)\n  createdAt          DateTime             @default(now()) @db.DateTime(0)\n  updatedAt          DateTime             @updatedAt @db.DateTime(0)\n  userId             Int?\n  user               user?                @relation(fields: [userId], references: [id])\n  suggestion_comment suggestion_comment[]\n  suggestion_vote    suggestion_vote[]\n\n  @@index([userId])\n}\n\nmodel suggestion_comment {\n  id           Int         @id @default(autoincrement())\n  message      String      @db.Text\n  createdAt    DateTime    @default(now()) @db.DateTime(0)\n  updatedAt    DateTime    @updatedAt @db.DateTime(0)\n  userId       Int?\n  suggestionId Int?\n  user         user?       @relation(fields: [userId], references: [id])\n  suggestion   suggestion? @relation(fields: [suggestionId], references: [id])\n\n  @@index([suggestionId])\n  @@index([userId])\n}\n\nmodel suggestion_vote {\n  id           Int                 @id @default(autoincrement())\n  voteType     SuggestionVoteTypes\n  createdAt    DateTime            @default(now()) @db.DateTime(0)\n  updatedAt    DateTime            @updatedAt @db.DateTime(0)\n  userId       Int?\n  suggestionId Int?\n  user         user?               @relation(fields: [userId], references: [id])\n  suggestion   suggestion?         @relation(fields: [suggestionId], references: [id])\n\n  @@index([suggestionId])\n  @@index([userId])\n}\n\nmodel talent {\n  id                   Int                       @id @default(autoincrement())\n  name                 String                    @db.VarChar(255)\n  displayName          String                    @db.VarChar(255)\n  tree                 TalentTree\n  talentType           TalentType                @default(passive)\n  description          String?                   @db.Text\n  skillLevelRequired   Int                       @db.UnsignedInt\n  pointsInTreeRequired Int                       @db.UnsignedInt\n  pointsCost           Int                       @default(1) @db.UnsignedInt\n  maxPoints            Int                       @db.UnsignedInt\n  staminaCost          Int?                      @db.UnsignedInt\n  tier1Modifier        Float?                    @db.Float\n  tier2Modifier        Float?                    @db.Float\n  tier3Modifier        Float?                    @db.Float\n  secondaryModifier    Float?                    @db.Float\n  usersWithAbility1    user_equipped_abilities[] @relation(\"userEquippedAbility1\")\n  usersWithAbility2    user_equipped_abilities[] @relation(\"userEquippedAbility2\")\n  usersWithAbility3    user_equipped_abilities[] @relation(\"userEquippedAbility3\")\n  usersWithAbility4    user_equipped_abilities[] @relation(\"userEquippedAbility4\")\n  user_talent          user_talent[]\n}\n\nmodel trader_rep {\n  id              Int      @id @default(autoincrement())\n  reputationLevel Float    @default(0) @db.Float\n  createdAt       DateTime @default(now()) @db.DateTime(0)\n  updatedAt       DateTime @updatedAt @db.DateTime(0)\n  shopId          Int?\n  userId          Int?\n  shop            shop?    @relation(fields: [shopId], references: [id])\n  user            user?    @relation(fields: [userId], references: [id])\n\n  @@index([shopId])\n  @@index([userId])\n}\n\nmodel user {\n  id                 Int        @id @default(autoincrement())\n  username           String     @db.VarChar(255)\n  displayUsername    String     @db.VarChar(255)\n  about              String?    @db.Text\n  email              String     @db.VarChar(255)\n  emailVerified      Boolean    @default(false)\n  banned             Boolean?   @default(false)\n  banReason          String?    @db.Text\n  banExpires         DateTime?  @db.DateTime(0)\n  usernameSet        Boolean?   @default(true)\n  cash               Int        @default(100) @db.UnsignedInt\n  bank_balance       Int        @default(200) @db.UnsignedInt\n  last_activity      DateTime?  @db.DateTime(0)\n  chatBannedUntil    BigInt?    @db.UnsignedBigInt\n  userType           UserTypes? @default(student)\n  avatar             String?    @db.VarChar(255)\n  profileBanner      String?    @db.VarChar(255)\n  jobLevel           Int?       @db.UnsignedInt\n  jobPayoutHour      Int?       @db.UnsignedInt\n  blockNextJobPayout Boolean    @default(false)\n\n  /// [RoguelikeMapType]\n  roguelikeMap Json?\n\n  roguelikeLevel           Int                       @default(1) @db.UnsignedInt\n  roguelikeHighscore       Int                       @default(1) @db.UnsignedInt\n  jailedUntil              BigInt?                   @db.UnsignedBigInt\n  jailReason               String?                   @db.VarChar(255)\n  hospitalisedUntil        BigInt?                   @db.UnsignedBigInt\n  hospitalisedHealingType  HospitalisedHealingTypes?\n  hospitalisedReason       String?                   @db.VarChar(255)\n  energy                   Int                       @default(100) @db.UnsignedInt\n  lastEnergyTick           BigInt?                   @db.UnsignedBigInt\n  focus                    Int                       @default(0) @db.UnsignedInt\n  dailyFatigueUsed         Int                       @default(0) @db.UnsignedInt\n  lastFatigueReset         DateTime?                 @db.Date\n  level                    Int                       @default(1) @db.UnsignedInt\n  xp                       Int                       @default(0) @db.UnsignedInt\n  actionPoints             Int                       @default(10) @db.UnsignedInt\n  nextAPTick               BigInt?                   @db.UnsignedBigInt\n  maxActionPoints          Int                       @default(10) @db.UnsignedInt\n  currentHealth            Int                       @default(200) @db.UnsignedInt\n  nextHPTick               BigInt?                   @db.UnsignedBigInt\n  health                   Int                       @default(200) @db.UnsignedInt\n  talentPoints             Int                       @default(0) @db.UnsignedInt\n  activeCourseId           Int?\n  courseEnds               BigInt?                   @db.UnsignedBigInt\n  class                    String?                   @db.VarChar(255)\n  classPoints              Int                       @default(0) @db.UnsignedInt\n  adminNotes               String?                   @db.VarChar(255)\n  combatLevel              Int                       @default(1) @db.UnsignedInt\n  discordID                String?                   @unique(map: \"discordID\") @db.VarChar(255)\n  currentMission           Int?\n  missionEnds              BigInt?                   @db.UnsignedBigInt\n  profileDetailBanUntil    BigInt?                   @db.UnsignedBigInt\n  weeklyBuyLimitRemaining  Int                       @default(4) @db.UnsignedInt\n  dailyQuestsRewardClaimed DateTime?                 @db.Date\n  currentMapLocation       ExploreNodeLocation?      @default(shibuya)\n\n  // Travel-related fields\n  travelStartTime DateTime?     @db.DateTime(0)\n  travelEndTime   DateTime?     @db.DateTime(0)\n  travelMethod    TravelMethod?\n\n  /// [RooftopDefeatedNpcs]\n  defeatedNpcs Json?\n\n  pushNotificationsEnabled                             Boolean                    @default(true)\n  gangCreds                                            Int                        @default(0) @db.UnsignedInt\n  lastNewsIDRead                                       Int                        @default(0) @db.UnsignedInt\n  createdAt                                            DateTime                   @default(now()) @db.DateTime(0)\n  updatedAt                                            DateTime                   @updatedAt @db.DateTime(0)\n  gangId                                               Int?\n  jobId                                                Int?\n  referrerId                                           Int?\n  accounts                                             account[]\n  actionsAsPlayer                                      action_log[]               @relation(\"actionsAsPlayer\")\n  actionsAsSecondParty                                 action_log[]               @relation(\"actionsAsSecondParty\")\n  auction_item                                         auction_item[]\n  initiatedBankTransactions                            bank_transaction[]         @relation(\"initiatedBankTransactions\")\n  secondPartyBankTransactions                          bank_transaction[]         @relation(\"secondPartyBankTransactions\")\n  userBankTransactions                                 bank_transaction[]         @relation(\"userBankTransactions\")\n  battlesAsAttacker                                    battle_log[]               @relation(\"battlesAsAttacker\")\n  battlesAsDefender                                    battle_log[]               @relation(\"battlesAsDefender\")\n  placedBounties                                       bounty[]                   @relation(\"placedBounties\")\n  targetedBounties                                     bounty[]                   @relation(\"targetedBounties\")\n  claimedBounties                                      bounty[]                   @relation(\"claimedBounties\")\n  chat_message                                         chat_message[]\n  daily_quest                                          daily_quest[]\n  equipped_item                                        equipped_item[]\n  game_stats                                           game_stats[]\n  gang                                                 gang?                      @relation(\"gangMembers\", fields: [gangId], references: [id])\n  gang_invite_gang_invite_senderIdTouser               gang_invite[]              @relation(\"gang_invite_senderIdTouser\")\n  gang_invite_gang_invite_recipientIdTouser            gang_invite[]              @relation(\"gang_invite_recipientIdTouser\")\n  gang_log_gang_log_gangMemberIdTouser                 gang_log[]                 @relation(\"gang_log_gangMemberIdTouser\")\n  gang_log_gang_log_secondPartyIdTouser                gang_log[]                 @relation(\"gang_log_secondPartyIdTouser\")\n  gang_member                                          gang_member[]\n  lottery                                              lottery[]\n  lottery_entry                                        lottery_entry[]\n  notification                                         notification[]\n  poll_response                                        poll_response[]\n  private_message_private_message_senderIdTouser       private_message[]          @relation(\"private_message_senderIdTouser\")\n  private_message_private_message_receiverIdTouser     private_message[]          @relation(\"private_message_receiverIdTouser\")\n  profile_comment_profile_comment_senderIdTouser       profile_comment[]          @relation(\"profile_comment_senderIdTouser\")\n  profile_comment_profile_comment_receiverIdTouser     profile_comment[]          @relation(\"profile_comment_receiverIdTouser\")\n  push_token                                           push_token[]\n  quest_progress                                       quest_progress[]\n  quest_objective_progress                             quest_objective_progress[]\n  registration_code_registration_code_referrerIdTouser registration_code[]        @relation(\"registration_code_referrerIdTouser\")\n  registration_code_registration_code_claimerIdTouser  registration_code[]        @relation(\"registration_code_claimerIdTouser\")\n  session                                              session[]\n  shrine_donation                                      shrine_donation[]\n  suggestion                                           suggestion[]\n  suggestion_comment                                   suggestion_comment[]\n  suggestion_vote                                      suggestion_vote[]\n  trader_rep                                           trader_rep[]\n  job                                                  job?                       @relation(fields: [jobId], references: [id])\n  user                                                 user?                      @relation(\"userTouser\", fields: [referrerId], references: [id])\n  other_user                                           user[]                     @relation(\"userTouser\")\n  user_achievements                                    user_achievements?\n  user_completed_course                                user_completed_course[]\n  user_crafting_queue                                  user_crafting_queue[]\n  user_item                                            user_item[]\n  user_recipe                                          user_recipe[]\n  user_talent                                          user_talent[]\n  user_property                                        user_property[]\n  statusMessage                                        String?                    @db.VarChar(255)\n  statusMessageUpdatedAt                               DateTime?                  @db.DateTime(0)\n  showLastOnline                                       Boolean                    @default(true)\n  sent_friend_requests                                 friend_request[]           @relation(\"friend_request_sender\")\n  received_friend_requests                             friend_request[]           @relation(\"friend_request_receiver\")\n  friends                                              friend[]                   @relation(\"user_friends\")\n  friendOf                                             friend[]                   @relation(\"friend_of\")\n  rivals                                               rival[]                    @relation(\"user_rivals\")\n  rivalOf                                              rival[]                    @relation(\"rival_of\")\n  user_status_effect                                   user_status_effect[]\n  user_skills                                          user_skill[]\n  exploreNodes                                         explore_player_node[]\n  ownedGang                                            gang[]                     @relation(\"gangOwner\")\n  user_pet                                             user_pet[]\n\n  user_equipped_abilities user_equipped_abilities?\n\n  @@unique([email])\n  @@unique([username, email], map: \"user_unique\")\n  @@index([jobId])\n  @@index([referrerId])\n  @@index([gangId])\n  @@map(\"user\")\n}\n\nmodel user_equipped_abilities {\n  userId                  Int     @id\n  user                    user    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  equippedAbility1Id      Int?\n  equippedAbility2Id      Int?\n  equippedAbility3Id      Int?\n  equippedAbility4Id      Int?\n  talent_equippedAbility1 talent? @relation(\"userEquippedAbility1\", fields: [equippedAbility1Id], references: [id])\n  talent_equippedAbility2 talent? @relation(\"userEquippedAbility2\", fields: [equippedAbility2Id], references: [id])\n  talent_equippedAbility3 talent? @relation(\"userEquippedAbility3\", fields: [equippedAbility3Id], references: [id])\n  talent_equippedAbility4 talent? @relation(\"userEquippedAbility4\", fields: [equippedAbility4Id], references: [id])\n\n  @@index([equippedAbility1Id])\n  @@index([equippedAbility2Id])\n  @@index([equippedAbility3Id])\n  @@index([equippedAbility4Id])\n  @@map(\"user_equipped_abilities\")\n}\n\nmodel session {\n  id             Int      @id @default(autoincrement())\n  expiresAt      DateTime\n  token          String\n  ipAddress      String?  @db.Text\n  userAgent      String?  @db.Text\n  userId         Int\n  impersonatedBy String?  @db.Text\n  createdAt      DateTime\n  updatedAt      DateTime\n  user           user     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([token])\n  @@map(\"session\")\n}\n\nmodel account {\n  id                    Int       @id @default(autoincrement())\n  accountId             String    @db.Text\n  providerId            String    @db.Text\n  userId                Int\n  accessToken           String?   @db.Text\n  refreshToken          String?   @db.Text\n  idToken               String?   @db.Text\n  accessTokenExpiresAt  DateTime?\n  refreshTokenExpiresAt DateTime?\n  scope                 String?   @db.Text\n  password              String?   @db.Text\n  createdAt             DateTime\n  updatedAt             DateTime\n  user                  user      @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"account\")\n}\n\nmodel verification {\n  id         Int      @id @default(autoincrement())\n  identifier String   @db.Text\n  value      String   @db.Text\n  expiresAt  DateTime\n  createdAt  DateTime\n  updatedAt  DateTime\n\n  @@map(\"verification\")\n}\n\nmodel user_item {\n  id             Int             @id @default(autoincrement())\n  count          Int             @db.UnsignedInt\n  upgradeLevel   Int             @default(0) @db.UnsignedInt\n  quality        ItemQuality     @default(normal)\n  isTradeable    Boolean         @default(false)\n  createdAt      DateTime        @default(now()) @db.DateTime(0)\n  updatedAt      DateTime        @updatedAt @db.DateTime(0)\n  userId         Int?\n  itemId         Int?\n  equipped_items equipped_item[]\n  user           user?           @relation(fields: [userId], references: [id])\n  item           item?           @relation(fields: [itemId], references: [id])\n\n  @@index([itemId])\n  @@index([userId])\n}\n\nmodel user_recipe {\n  createdAt        DateTime        @default(now()) @db.DateTime(0)\n  updatedAt        DateTime        @updatedAt @db.DateTime(0)\n  craftingRecipeId Int\n  userId           Int\n  crafting_recipe  crafting_recipe @relation(fields: [craftingRecipeId], references: [id], onDelete: Cascade)\n  user             user            @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@id([craftingRecipeId, userId])\n  @@index([userId])\n}\n\nmodel user_completed_course {\n  id          Int      @id @default(autoincrement())\n  courseId    Int\n  completedAt DateTime @db.DateTime(0)\n  createdAt   DateTime @default(now()) @db.DateTime(0)\n  updatedAt   DateTime @updatedAt @db.DateTime(0)\n  userId      Int?\n  user        user?    @relation(fields: [userId], references: [id])\n\n  @@index([userId])\n}\n\nmodel user_talent {\n  level     Int?\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  talentId  Int\n  userId    Int\n  talent    talent   @relation(fields: [talentId], references: [id], onDelete: Cascade)\n  user      user     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@id([talentId, userId])\n  @@index([userId])\n}\n\nmodel user_achievements {\n  userId                  Int  @id\n  battleWins              Int? @default(0) @db.UnsignedInt\n  npcBattleWins           Int? @default(0) @db.UnsignedInt\n  craftsCompleted         Int? @default(0) @db.UnsignedInt\n  marketItemsSold         Int? @default(0) @db.UnsignedInt\n  marketMoneyMade         Int? @default(0) @db.UnsignedInt\n  totalMuggingGain        Int? @default(0) @db.UnsignedInt\n  totalMuggingLoss        Int? @default(0) @db.UnsignedInt\n  totalCasinoProfitLoss   Int? @default(0)\n  questsCompleted         Int? @default(0) @db.UnsignedInt\n  dailyQuestsCompleted    Int? @default(0) @db.UnsignedInt\n  coursesCompleted        Int? @default(0) @db.UnsignedInt\n  roguelikeNodesCompleted Int? @default(0) @db.UnsignedInt\n  roguelikeMapsCompleted  Int? @default(0) @db.UnsignedInt\n  examsCompleted          Int? @default(0) @db.UnsignedInt\n  totalBountyRewards      Int? @default(0) @db.UnsignedInt\n  totalBountyPlaced       Int? @default(0) @db.UnsignedInt\n  totalMissionHours       Int? @default(0) @db.UnsignedInt\n  suggestionsVoted        Int? @default(0) @db.UnsignedInt\n  encountersCompleted     Int? @default(0) @db.UnsignedInt\n  user                    user @relation(fields: [userId], references: [id], onDelete: Cascade)\n}\n\nmodel user_crafting_queue {\n  id               Int              @id @default(autoincrement())\n  startedAt        BigInt           @db.UnsignedBigInt\n  endsAt           BigInt           @db.UnsignedBigInt\n  createdAt        DateTime         @default(now()) @db.DateTime(0)\n  updatedAt        DateTime         @updatedAt @db.DateTime(0)\n  userId           Int?\n  craftingRecipeId Int?\n  user             user?            @relation(fields: [userId], references: [id])\n  crafting_recipe  crafting_recipe? @relation(fields: [craftingRecipeId], references: [id])\n\n  @@index([userId])\n  @@index([craftingRecipeId])\n}\n\nmodel property {\n  id            Int             @id @default(autoincrement())\n  name          String\n  propertyType  String          @default(\"housing\")\n  cost          Int             @db.UnsignedInt\n  upkeep        Int             @db.UnsignedInt\n  slots         Int             @db.UnsignedInt\n  buffs         Json\n  description   String          @db.Text\n  image         String?         @db.VarChar(255)\n  createdAt     DateTime        @default(now()) @db.DateTime(0)\n  updatedAt     DateTime        @updatedAt @db.DateTime(0)\n  user_property user_property[]\n}\n\nmodel user_property {\n  id             Int      @id @default(autoincrement())\n  purchaseDate   DateTime @default(now()) @db.DateTime(0)\n  lastUpkeepPaid DateTime @default(now()) @db.DateTime(0)\n  furniture      Json     @default(\"[]\")\n  customization  Json     @default(\"{}\")\n  createdAt      DateTime @default(now()) @db.DateTime(0)\n  updatedAt      DateTime @updatedAt @db.DateTime(0)\n  userId         Int\n  propertyId     Int\n  user           user     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  property       property @relation(fields: [propertyId], references: [id])\n\n  @@index([userId])\n  @@index([propertyId])\n}\n\nmodel pet {\n  id       Int    @id @default(autoincrement())\n  name     String @db.VarChar(255)\n  species  String @db.VarChar(255)\n  maxLevel Int    @default(100)\n\n  /// [EvolutionStages]\n  evolution_stages Json @default(\"[]\")\n\n  createdAt DateTime   @default(now()) @db.DateTime(0)\n  updatedAt DateTime   @updatedAt @db.DateTime(0)\n  user_pet  user_pet[]\n  item      item[]\n}\n\nmodel user_pet {\n  id          Int     @id @default(autoincrement())\n  name        String  @db.VarChar(255)\n  level       Int     @default(1)\n  isActive    Boolean @default(false)\n  xp          Int     @default(0)\n  nextLevelXp Int     @default(100)\n  happiness   Int     @default(50)\n  energy      Int     @default(100)\n\n  /// [Evolution]\n  evolution Json @default(\"{\\\"current\\\":\\\"egg\\\",\\\"next\\\":\\\"baby\\\",\\\"progress\\\":0,\\\"requiredLevel\\\":10}\")\n\n  userId    Int\n  petId     Int\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  user      user     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  pet       pet      @relation(fields: [petId], references: [id])\n\n  @@index([userId])\n  @@index([petId])\n}\n\nmodel friend_request {\n  id         Int      @id @default(autoincrement())\n  createdAt  DateTime @default(now()) @db.DateTime(0)\n  updatedAt  DateTime @updatedAt @db.DateTime(0)\n  senderId   Int\n  receiverId Int\n  sender     user     @relation(\"friend_request_sender\", fields: [senderId], references: [id], onDelete: Cascade)\n  receiver   user     @relation(\"friend_request_receiver\", fields: [receiverId], references: [id], onDelete: Cascade)\n\n  @@unique([senderId, receiverId])\n  @@index([senderId])\n  @@index([receiverId])\n}\n\nmodel friend {\n  id        Int      @id @default(autoincrement())\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  userId    Int\n  friendId  Int\n  note      String?  @db.VarChar(255)\n  user      user     @relation(\"user_friends\", fields: [userId], references: [id], onDelete: Cascade)\n  friend    user     @relation(\"friend_of\", fields: [friendId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, friendId])\n  @@index([userId])\n  @@index([friendId])\n}\n\nmodel rival {\n  id        Int      @id @default(autoincrement())\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n  userId    Int\n  rivalId   Int\n  note      String?  @db.VarChar(255)\n  user      user     @relation(\"user_rivals\", fields: [userId], references: [id], onDelete: Cascade)\n  rival     user     @relation(\"rival_of\", fields: [rivalId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, rivalId])\n  @@index([userId])\n  @@index([rivalId])\n}\n\nmodel status_effect {\n  id                 Int                      @id @default(autoincrement())\n  name               String                   @db.VarChar(255)\n  source             String?                  @db.VarChar(255)\n  effectType         StatusEffectType\n  category           String                   @db.VarChar(255)\n  tier               StatusEffectTier?\n  duration           Int                      @db.UnsignedInt\n  modifier           Float?                   @db.Float\n  modifierType       StatusEffectModifierType\n  stackable          Boolean                  @default(false)\n  maxStacks          Int?                     @db.UnsignedInt\n  disabled           Boolean                  @default(false)\n  description        String?                  @db.Text\n  user_status_effect user_status_effect[]\n}\n\nmodel user_status_effect {\n  id         Int            @id @default(autoincrement())\n  stacks     Int            @default(1) @db.UnsignedInt\n  endsAt     BigInt         @db.UnsignedBigInt\n  customName String?        @db.VarChar(255)\n  createdAt  DateTime       @default(now()) @db.DateTime(0)\n  updatedAt  DateTime       @updatedAt @db.DateTime(0)\n  effectId   Int?\n  userId     Int?\n  effect     status_effect? @relation(fields: [effectId], references: [id], onDelete: Cascade)\n  user       user?          @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([effectId, userId, customName])\n  @@index([userId])\n}\n\nmodel user_skill {\n  id           Int       @id @default(autoincrement())\n  skillType    SkillType\n  level        Int       @default(1) @db.UnsignedInt\n  experience   Int       @default(0) @db.UnsignedInt\n  talentPoints Int?      @default(0) @db.UnsignedInt\n  createdAt    DateTime  @default(now()) @db.DateTime(0)\n  updatedAt    DateTime  @updatedAt @db.DateTime(0)\n  userId       Int\n  user         user      @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, skillType])\n  @@index([userId])\n}\n\nmodel explore_static_node {\n  id          Int                 @id @default(autoincrement())\n  nodeType    ExploreNodeType\n  title       String              @db.VarChar(255)\n  description String              @db.Text\n  position    Json // { \"x\": number, \"y\": number }\n  metadata    Json? // e.g., { \"shopId\": 1 }\n  location    ExploreNodeLocation\n  shopId      Int?\n  shop        shop?               @relation(fields: [shopId], references: [id])\n  createdAt   DateTime            @default(now()) @db.DateTime(0)\n  updatedAt   DateTime            @updatedAt @db.DateTime(0)\n\n  @@index([shopId])\n}\n\nmodel explore_player_node {\n  id          Int                 @id @default(autoincrement())\n  nodeType    ExploreNodeType\n  title       String              @db.VarChar(255)\n  description String              @db.Text\n  position    Json // { \"x\": number, \"y\": number }\n  metadata    Json? // e.g., { \"creatureId\": 5 }\n  location    ExploreNodeLocation\n  status      ExploreNodeStatus   @default(available)\n  expiresAt   DateTime?           @db.DateTime(0)\n  createdAt   DateTime            @default(now()) @db.DateTime(0)\n  updatedAt   DateTime            @updatedAt @db.DateTime(0)\n  userId      Int\n  user        user                @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@index([userId])\n  @@index([location])\n  @@index([userId, location, expiresAt])\n}\n\n// ===============================================\n// Story Mode Models\n// ===============================================\n\nmodel story_season {\n  id          Int      @id @default(autoincrement())\n  name        String   @db.VarChar(255)\n  description String?  @db.Text\n  startDate   DateTime @db.DateTime(0)\n\n  requiredLevel Int @default(1) @db.UnsignedInt\n\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n\n  story_chapter story_chapter[]\n}\n\nmodel story_chapter {\n  id          Int     @id @default(autoincrement())\n  seasonId    Int\n  name        String  @db.VarChar(255)\n  description String? @db.Text\n  order       Int\n\n  unlockDate DateTime @db.DateTime(0)\n\n  requiredLevel Int @default(1) @db.UnsignedInt\n\n  /// [RequiredChapterIds]\n  requiredChapterIds Json?\n\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n\n  story_season story_season @relation(fields: [seasonId], references: [id], onDelete: Cascade)\n  quests       quest[]\n\n  @@unique([seasonId, order])\n  @@index([seasonId])\n  @@index([unlockDate])\n}\n\nmodel story_episode {\n  id              Int                 @id @default(autoincrement())\n  name            String              @db.VarChar(255)\n  description     String?             @db.Text\n  episodeType     StoryEpisodeType\n  exploreLocation ExploreNodeLocation @default(shibuya)\n\n  /// [StoryEpisodeContent]\n  content Json // Contains dialogue, scenes, etc.\n\n  /// [StoryEpisodeChoices]\n  choices Json? // Defines player options and consequences for CHOICE types\n\n  objectiveId     Int             @unique\n  quest_objective quest_objective @relation(fields: [objectiveId], references: [id])\n\n  createdAt DateTime @default(now()) @db.DateTime(0)\n  updatedAt DateTime @updatedAt @db.DateTime(0)\n\n  @@index([episodeType])\n  @@index([exploreLocation])\n}\n\nenum CraftingSkills {\n  fabrication\n  electronics\n  chemistry\n  outfitting\n}\n\nenum BankTransactionTypes {\n  bank_deposit\n  bank_withdrawal\n  bank_transfer\n  shop_purchase\n  player_trade\n}\n\nenum QuestObjectiveTypes {\n  // Battle objectives\n  DEFEAT_NPC // defeat specific NPC (target=creatureId) or any NPC (target=null)\n  DEFEAT_NPC_IN_TURNS // Defeat a specific NPC within a turn limit\n  DEFEAT_NPC_WITH_LOW_DAMAGE // Defeat any NPC while taking minimal damage\n  DEFEAT_BOSS // Defeat N boss NPCs.\n  DEFEAT_PLAYER // Defeat N players. Data specifies a specific player if needed.\n  PVP_POST_BATTLE_CHOICE // PVP battle won with specific post-battle choice\n  DEFEAT_PLAYER_XNAME // player with x in name defeated x times\n  DEFEAT_SPECIFIC_PLAYER // specific player defeated x times\n  WIN_BATTLE // Win N battles of any type (PvE or PvP).\n  USE_ABILITY // Use an ability N times. Data specifies ID for specific, or null for any.\n\n  // Item objectives\n  ACQUIRE_ITEM // Obtain N of item (from loot, shops, etc.). Data specifies the item.\n  CRAFT_ITEM // craft specific item (target=itemId) or any item (target=null)\n  DELIVER_ITEM // Hand in N items to a quest giver or location.\n  GATHER_RESOURCES // Gather N resources through mining, scavenging, or foraging. targetAction specifies activity type.\n\n  // Bounty objectives\n  PLACE_BOUNTY // Place N bounties on other players\n  COLLECT_BOUNTY_REWARD // Collect the reward for N bounties\n\n  // Misc objectives\n  COMPLETE_MISSIONS // Complete N missions\n  DONATE_TO_SHRINE // Donate N amount to the shrine\n  VOTE_ON_SUGGESTION // Cast a vote in N community suggestions\n  CHARACTER_ENCOUNTERS // Complete N character encounters\n  TRAIN_STATS // Train N stats\n  GAMBLING_SLOTS // Gamble N amount on slots\n\n  // Story objectives\n  COMPLETE_STORY_EPISODE\n  // MAKE_STORY_CHOICE // V2 Feature\n  // REACH_RELATIONSHIP_LEVEL // V2 Feature\n  UNIQUE_OBJECTIVE\n}\n\nenum GangInviteTypes {\n  invite\n  inviteRequest\n}\n\nenum QuestProgressStatus {\n  complete\n  in_progress\n  ready_to_complete\n}\n\nenum RecipeItemTypes {\n  input\n  output\n}\n\nenum SuggestionVoteTypes {\n  upvote\n  downvote\n}\n\nenum DropChanceTypes {\n  creature\n  boss\n  daily\n  roguelike\n  quest\n  scavenge\n  character_encounter\n}\n\nenum EquipSlots {\n  head\n  chest\n  hands\n  legs\n  feet\n  finger\n  offhand\n  shield\n  weapon\n  ranged\n}\n\nenum ItemQuality {\n  shoddy\n  normal\n  fine\n  excellent\n  superior\n  perfect\n  masterwork\n}\n\nenum ItemTypes {\n  weapon\n  ranged\n  head\n  chest\n  hands\n  legs\n  feet\n  finger\n  offhand\n  shield\n  consumable\n  crafting\n  junk\n  quest\n  special\n  recipe\n  upgrade\n  pet\n  pet_food\n}\n\nenum ShopTypes {\n  general\n  weapon\n  armour\n  food\n  furniture\n  gang\n}\n\nenum TalentTree {\n  strength\n  intelligence\n  dexterity\n  defence\n  endurance\n  vitality\n  mining\n  scavenging\n  foraging\n  fabrication\n  outfitting\n  chemistry\n  electronics\n}\n\nenum ItemRarities {\n  novice\n  standard\n  enhanced\n  specialist\n  military\n  legendary\n}\n\nenum SuggestionStates {\n  New\n  Accepted\n  Completed\n  Denied\n}\n\nenum ShopListingCurrency {\n  yen\n  gangCreds\n  classPoints\n}\n\nenum AuctionItemStatus {\n  complete\n  in_progress\n  expired\n  cancelled\n}\n\nenum QuestTargetAction {\n  mug\n  cripple\n  leave\n}\n\nenum ExploreNodeLocation {\n  shibuya\n  shinjuku\n  bunkyo\n  chiyoda\n  minato\n}\n\nenum TravelMethod {\n  walk\n  bus\n}\n\nenum LocationTypes {\n  church\n  shrine\n  mall\n  alley\n  school\n  sewers\n  themepark\n  shibuya\n  shinjuku\n  bunkyo\n  chiyoda\n  minato\n  any\n}\n\nenum CreatureStatTypes {\n  balanced\n  tank\n  dps\n}\n\nenum UserTypes {\n  student\n  prefect\n  admin\n  guest\n}\n\nenum HospitalisedHealingTypes {\n  full\n  injury\n}\n\nenum StatusEffectType {\n  BUFF\n  DEBUFF\n  NEUTRAL\n}\n\nenum StatusEffectTier {\n  Minor\n  Moderate\n  Severe\n  Critical\n}\n\nenum StatusEffectModifierType {\n  add\n  multiply\n  divide\n}\n\nenum QuestRewardType {\n  ITEM\n  TALENT_POINTS\n  GANG_CREDS\n  CLASS_POINTS\n}\n\nenum SkillType {\n  mining\n  scavenging\n  foraging\n  fabrication\n  outfitting\n  chemistry\n  electronics\n  strength\n  intelligence\n  dexterity\n  defence\n  endurance\n  vitality\n}\n\nenum ExploreNodeType {\n  SHOP\n  HOUSING\n  BATTLE\n  CHARACTER_ENCOUNTER\n  ACTION\n  CONDITION\n  CHOICE\n  STORY\n  MINING_NODE\n  SCAVENGE_NODE\n  FORAGING_NODE\n}\n\nenum ExploreNodeStatus {\n  completed\n  available\n  locked\n  current\n}\n\nenum TalentType {\n  passive\n  ability\n}\n\nenum StoryEpisodeType {\n  NARRATIVE\n  CHOICE\n  BATTLE // Future story episode type\n}\n",
  "inlineSchemaHash": "51babac64c1478ae1c2c6b9d9ef7b9f182315679584ff746723551975ee8a4fa",
  "copyEngine": true,
  "runtimeDataModel": {
    "models": {},
    "enums": {},
    "types": {}
  },
  "dirname": ""
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"action_log\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"logType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"info\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"playerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"secondPartyId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"player\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"actionsAsPlayer\"},{\"name\":\"secondParty\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"actionsAsSecondParty\"}],\"dbName\":null},\"game_config\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"key\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"category\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isPublic\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"auction_item\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"deposit\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"buyoutPrice\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"endsAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"AuctionItemStatus\"},{\"name\":\"bankFunds\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"sellerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"auction_itemToitem\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"auction_itemTouser\"}],\"dbName\":null},\"bank_transaction\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"transaction_type\",\"kind\":\"enum\",\"type\":\"BankTransactionTypes\"},{\"name\":\"cash\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"transactionFee\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"initiatorCashBalance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"initiatorBankBalance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"secondPartyCashBalance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"secondPartyBankBalance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"initiatorId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"secondPartyId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"initiator\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"initiatedBankTransactions\"},{\"name\":\"secondParty\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"secondPartyBankTransactions\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userBankTransactions\"}],\"dbName\":null},\"battle_log\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"victory\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"attackerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"defenderId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"attacker\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"battlesAsAttacker\"},{\"name\":\"defender\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"battlesAsDefender\"}],\"dbName\":null},\"bounty\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"reason\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"active\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"placerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"targetId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"claimedById\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"placer\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"placedBounties\"},{\"name\":\"target\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"targetedBounties\"},{\"name\":\"claimer\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"claimedBounties\"}],\"dbName\":null},\"chat_message\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"hidden\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"announcementType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"chatRoomId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"parentMessageId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"chat_room\",\"kind\":\"object\",\"type\":\"chat_room\",\"relationName\":\"chat_messageTochat_room\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"chat_messageTouser\"},{\"name\":\"chat_message\",\"kind\":\"object\",\"type\":\"chat_message\",\"relationName\":\"chat_messageTochat_message\"},{\"name\":\"parent_message\",\"kind\":\"object\",\"type\":\"chat_message\",\"relationName\":\"chat_messageTochat_message\"}],\"dbName\":null},\"chat_room\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"gangId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"chat_message\",\"kind\":\"object\",\"type\":\"chat_message\",\"relationName\":\"chat_messageTochat_room\"},{\"name\":\"gang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"chat_roomTogang\"}],\"dbName\":null},\"crafting_recipe\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"cost\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"craftTime\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isUnlockable\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"requiredSkillType\",\"kind\":\"enum\",\"type\":\"CraftingSkills\"},{\"name\":\"requiredSkillLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"crafting_recipeToitem\"},{\"name\":\"recipe_item\",\"kind\":\"object\",\"type\":\"recipe_item\",\"relationName\":\"crafting_recipeTorecipe_item\"},{\"name\":\"user_crafting_queue\",\"kind\":\"object\",\"type\":\"user_crafting_queue\",\"relationName\":\"crafting_recipeTouser_crafting_queue\"},{\"name\":\"user_recipe\",\"kind\":\"object\",\"type\":\"user_recipe\",\"relationName\":\"crafting_recipeTouser_recipe\"}],\"dbName\":null},\"creature\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"image\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"minFloor\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"maxFloor\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"boss\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"health\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"currentHealth\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"strength\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"defence\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weaponDamage\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"location\",\"kind\":\"enum\",\"type\":\"LocationTypes\"},{\"name\":\"statType\",\"kind\":\"enum\",\"type\":\"CreatureStatTypes\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"quest_objective\",\"kind\":\"object\",\"type\":\"quest_objective\",\"relationName\":\"creatureToquest_objective\"}],\"dbName\":null},\"daily_mission\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"tier\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"missionName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"missionDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"duration\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"minCashReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"maxCashReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"minExpReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"maxExpReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"levelReq\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"hoursReq\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemRewardQuantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"itemRewardId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"daily_missionToitem\"}],\"dbName\":null},\"drop_chance\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dropRate\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"dropChanceType\",\"kind\":\"enum\",\"type\":\"DropChanceTypes\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"location\",\"kind\":\"enum\",\"type\":\"LocationTypes\"},{\"name\":\"minLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"maxLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"scavengeType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"drop_chanceToitem\"}],\"dbName\":null},\"equipped_item\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"slot\",\"kind\":\"enum\",\"type\":\"EquipSlots\"},{\"name\":\"userItemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"equipped_itemTouser\"},{\"name\":\"user_item\",\"kind\":\"object\",\"type\":\"user_item\",\"relationName\":\"equipped_itemTouser_item\"}],\"dbName\":null},\"game_stats\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"stats_type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"info\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"playerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"game_statsTouser\"}],\"dbName\":null},\"gang\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"about\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"treasury_balance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"hideout_level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"materialsResource\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"essenceResource\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyEssenceGained\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"toolsResource\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"techResource\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyRespect\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalRespect\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gangMOTD\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"ownerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"chat_room\",\"kind\":\"object\",\"type\":\"chat_room\",\"relationName\":\"chat_roomTogang\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gangOwner\"},{\"name\":\"gang_invite\",\"kind\":\"object\",\"type\":\"gang_invite\",\"relationName\":\"gangTogang_invite\"},{\"name\":\"gang_log\",\"kind\":\"object\",\"type\":\"gang_log\",\"relationName\":\"gangTogang_log\"},{\"name\":\"gang_member\",\"kind\":\"object\",\"type\":\"gang_member\",\"relationName\":\"gangTogang_member\"},{\"name\":\"members\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gangMembers\"}],\"dbName\":null},\"gang_invite\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"inviteType\",\"kind\":\"enum\",\"type\":\"GangInviteTypes\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"gangId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"senderId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"recipientId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"gangTogang_invite\"},{\"name\":\"user_gang_invite_senderIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gang_invite_senderIdTouser\"},{\"name\":\"user_gang_invite_recipientIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gang_invite_recipientIdTouser\"}],\"dbName\":null},\"gang_log\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"action\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"info\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"gangId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gangMemberId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"secondPartyId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"gangTogang_log\"},{\"name\":\"user_gang_log_gangMemberIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gang_log_gangMemberIdTouser\"},{\"name\":\"user_gang_log_secondPartyIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gang_log_secondPartyIdTouser\"}],\"dbName\":null},\"gang_member\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"rank\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"payoutShare\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"weeklyMaterials\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyEssence\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyTools\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyRespect\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalContribution\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"gangId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"gangTogang_member\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"gang_memberTouser\"}],\"dbName\":null},\"item\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"itemType\",\"kind\":\"enum\",\"type\":\"ItemTypes\"},{\"name\":\"rarity\",\"kind\":\"enum\",\"type\":\"ItemRarities\"},{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"about\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"cashValue\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"image\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"damage\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"armour\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"health\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"energy\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"actionPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"baseAmmo\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemEffects\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"recipeUnlockId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"petUnlockId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"auction_item\",\"kind\":\"object\",\"type\":\"auction_item\",\"relationName\":\"auction_itemToitem\"},{\"name\":\"daily_mission\",\"kind\":\"object\",\"type\":\"daily_mission\",\"relationName\":\"daily_missionToitem\"},{\"name\":\"drop_chance\",\"kind\":\"object\",\"type\":\"drop_chance\",\"relationName\":\"drop_chanceToitem\"},{\"name\":\"crafting_recipe\",\"kind\":\"object\",\"type\":\"crafting_recipe\",\"relationName\":\"crafting_recipeToitem\"},{\"name\":\"pet\",\"kind\":\"object\",\"type\":\"pet\",\"relationName\":\"itemTopet\"},{\"name\":\"recipe_item\",\"kind\":\"object\",\"type\":\"recipe_item\",\"relationName\":\"itemTorecipe_item\"},{\"name\":\"shop_listing\",\"kind\":\"object\",\"type\":\"shop_listing\",\"relationName\":\"itemToshop_listing\"},{\"name\":\"user_item\",\"kind\":\"object\",\"type\":\"user_item\",\"relationName\":\"itemTouser_item\"},{\"name\":\"quest_reward\",\"kind\":\"object\",\"type\":\"quest_reward\",\"relationName\":\"itemToquest_reward\"},{\"name\":\"daily_quest\",\"kind\":\"object\",\"type\":\"daily_quest\",\"relationName\":\"daily_questToitem\"},{\"name\":\"quest_objective\",\"kind\":\"object\",\"type\":\"quest_objective\",\"relationName\":\"itemToquest_objective\"}],\"dbName\":null},\"job\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"payFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"strengthFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"intelligenceFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"dexterityFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"defenceFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"enduranceFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"vitalityFormula\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"jobTouser\"}],\"dbName\":null},\"lottery\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"drawDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"prizeAmount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"entries\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"winnerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"lotteryTouser\"},{\"name\":\"lottery_entry\",\"kind\":\"object\",\"type\":\"lottery_entry\",\"relationName\":\"lotteryTolottery_entry\"}],\"dbName\":null},\"lottery_entry\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lotteryId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lottery\",\"kind\":\"object\",\"type\":\"lottery\",\"relationName\":\"lotteryTolottery_entry\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"lottery_entryTouser\"}],\"dbName\":null},\"notification\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"notificationType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"details\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"read\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"notificationTouser\"}],\"dbName\":null},\"poll\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"ended\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"showResults\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"poll_response\",\"kind\":\"object\",\"type\":\"poll_response\",\"relationName\":\"pollTopoll_response\"}],\"dbName\":null},\"poll_response\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"answer\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"pollId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"poll_responseTouser\"},{\"name\":\"poll\",\"kind\":\"object\",\"type\":\"poll\",\"relationName\":\"pollTopoll_response\"}],\"dbName\":null},\"private_message\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"read\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"isGlobal\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"senderId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"receiverId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user_private_message_senderIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"private_message_senderIdTouser\"},{\"name\":\"user_private_message_receiverIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"private_message_receiverIdTouser\"}],\"dbName\":null},\"profile_comment\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"senderId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"receiverId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user_profile_comment_senderIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"profile_comment_senderIdTouser\"},{\"name\":\"user_profile_comment_receiverIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"profile_comment_receiverIdTouser\"}],\"dbName\":null},\"push_token\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"push_tokenTouser\"}],\"dbName\":null},\"daily_quest\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"objectiveType\",\"kind\":\"enum\",\"type\":\"QuestObjectiveTypes\"},{\"name\":\"target\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"targetAction\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"questStatus\",\"kind\":\"enum\",\"type\":\"QuestProgressStatus\"},{\"name\":\"count\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"cashReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"xpReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemRewardQuantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"itemRewardId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"daily_questToitem\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"daily_questTouser\"}],\"dbName\":null},\"quest\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"questInfo\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"levelReq\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"disabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"questChainName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"xpReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"cashReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"repReward\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"talentPointReward\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shopId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"requiredQuestId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isStoryQuest\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"chapterId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"story_chapter\",\"kind\":\"object\",\"type\":\"story_chapter\",\"relationName\":\"questTostory_chapter\"},{\"name\":\"orderInChapter\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shop\",\"kind\":\"object\",\"type\":\"shop\",\"relationName\":\"questToshop\"},{\"name\":\"quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToquest\"},{\"name\":\"other_quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToquest\"},{\"name\":\"quest_progress\",\"kind\":\"object\",\"type\":\"quest_progress\",\"relationName\":\"questToquest_progress\"},{\"name\":\"quest_reward\",\"kind\":\"object\",\"type\":\"quest_reward\",\"relationName\":\"questToquest_reward\"},{\"name\":\"quest_objective\",\"kind\":\"object\",\"type\":\"quest_objective\",\"relationName\":\"questToquest_objective\"}],\"dbName\":null},\"quest_progress\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"questStatus\",\"kind\":\"enum\",\"type\":\"QuestProgressStatus\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"questId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToquest_progress\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"quest_progressTouser\"}],\"dbName\":null},\"quest_objective\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"objectiveType\",\"kind\":\"enum\",\"type\":\"QuestObjectiveTypes\"},{\"name\":\"target\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"targetAction\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"location\",\"kind\":\"enum\",\"type\":\"LocationTypes\"},{\"name\":\"isRequired\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"questId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"creatureId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToquest_objective\"},{\"name\":\"creature\",\"kind\":\"object\",\"type\":\"creature\",\"relationName\":\"creatureToquest_objective\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemToquest_objective\"},{\"name\":\"quest_objective_progress\",\"kind\":\"object\",\"type\":\"quest_objective_progress\",\"relationName\":\"quest_objectiveToquest_objective_progress\"},{\"name\":\"story_episode\",\"kind\":\"object\",\"type\":\"story_episode\",\"relationName\":\"quest_objectiveTostory_episode\"}],\"dbName\":null},\"quest_objective_progress\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"count\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"QuestProgressStatus\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"questObjectiveId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"quest_objective_progressTouser\"},{\"name\":\"quest_objective\",\"kind\":\"object\",\"type\":\"quest_objective\",\"relationName\":\"quest_objectiveToquest_objective_progress\"}],\"dbName\":null},\"quest_reward\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"rewardType\",\"kind\":\"enum\",\"type\":\"QuestRewardType\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isChoice\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"questId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyQuestId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToquest_reward\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemToquest_reward\"}],\"dbName\":null},\"recipe_item\":{\"fields\":[{\"name\":\"count\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemType\",\"kind\":\"enum\",\"type\":\"RecipeItemTypes\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"craftingRecipeId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"crafting_recipe\",\"kind\":\"object\",\"type\":\"crafting_recipe\",\"relationName\":\"crafting_recipeTorecipe_item\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemTorecipe_item\"}],\"dbName\":null},\"registration_code\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"code\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"note\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"unlimitedUse\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"referrerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"claimerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user_registration_code_referrerIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"registration_code_referrerIdTouser\"},{\"name\":\"user_registration_code_claimerIdTouser\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"registration_code_claimerIdTouser\"}],\"dbName\":null},\"shop\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"shopType\",\"kind\":\"enum\",\"type\":\"ShopTypes\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"disabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"quest\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questToshop\"},{\"name\":\"shop_listing\",\"kind\":\"object\",\"type\":\"shop_listing\",\"relationName\":\"shopToshop_listing\"},{\"name\":\"trader_rep\",\"kind\":\"object\",\"type\":\"trader_rep\",\"relationName\":\"shopTotrader_rep\"},{\"name\":\"explore_static_node\",\"kind\":\"object\",\"type\":\"explore_static_node\",\"relationName\":\"explore_static_nodeToshop\"}],\"dbName\":null},\"shop_listing\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"customCost\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"repRequired\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"stock\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"currency\",\"kind\":\"enum\",\"type\":\"ShopListingCurrency\"},{\"name\":\"shopId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shop\",\"kind\":\"object\",\"type\":\"shop\",\"relationName\":\"shopToshop_listing\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemToshop_listing\"}],\"dbName\":null},\"shrine_donation\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"shrineGoalId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shrine_goal\",\"kind\":\"object\",\"type\":\"shrine_goal\",\"relationName\":\"shrine_donationToshrine_goal\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"shrine_donationTouser\"}],\"dbName\":null},\"shrine_goal\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"donationGoal\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"donationAmount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"goalReached\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"goalDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"buffRewards\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"shrine_donation\",\"kind\":\"object\",\"type\":\"shrine_donation\",\"relationName\":\"shrine_donationToshrine_goal\"}],\"dbName\":null},\"suggestion\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"content\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"state\",\"kind\":\"enum\",\"type\":\"SuggestionStates\"},{\"name\":\"upvotes\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"downvotes\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalComments\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"suggestionTouser\"},{\"name\":\"suggestion_comment\",\"kind\":\"object\",\"type\":\"suggestion_comment\",\"relationName\":\"suggestionTosuggestion_comment\"},{\"name\":\"suggestion_vote\",\"kind\":\"object\",\"type\":\"suggestion_vote\",\"relationName\":\"suggestionTosuggestion_vote\"}],\"dbName\":null},\"suggestion_comment\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"suggestionId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"suggestion_commentTouser\"},{\"name\":\"suggestion\",\"kind\":\"object\",\"type\":\"suggestion\",\"relationName\":\"suggestionTosuggestion_comment\"}],\"dbName\":null},\"suggestion_vote\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"voteType\",\"kind\":\"enum\",\"type\":\"SuggestionVoteTypes\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"suggestionId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"suggestion_voteTouser\"},{\"name\":\"suggestion\",\"kind\":\"object\",\"type\":\"suggestion\",\"relationName\":\"suggestionTosuggestion_vote\"}],\"dbName\":null},\"talent\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"displayName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"tree\",\"kind\":\"enum\",\"type\":\"TalentTree\"},{\"name\":\"talentType\",\"kind\":\"enum\",\"type\":\"TalentType\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"skillLevelRequired\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"pointsInTreeRequired\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"pointsCost\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"maxPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"staminaCost\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"tier1Modifier\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"tier2Modifier\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"tier3Modifier\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"secondaryModifier\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"usersWithAbility1\",\"kind\":\"object\",\"type\":\"user_equipped_abilities\",\"relationName\":\"userEquippedAbility1\"},{\"name\":\"usersWithAbility2\",\"kind\":\"object\",\"type\":\"user_equipped_abilities\",\"relationName\":\"userEquippedAbility2\"},{\"name\":\"usersWithAbility3\",\"kind\":\"object\",\"type\":\"user_equipped_abilities\",\"relationName\":\"userEquippedAbility3\"},{\"name\":\"usersWithAbility4\",\"kind\":\"object\",\"type\":\"user_equipped_abilities\",\"relationName\":\"userEquippedAbility4\"},{\"name\":\"user_talent\",\"kind\":\"object\",\"type\":\"user_talent\",\"relationName\":\"talentTouser_talent\"}],\"dbName\":null},\"trader_rep\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"reputationLevel\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"shopId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shop\",\"kind\":\"object\",\"type\":\"shop\",\"relationName\":\"shopTotrader_rep\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"trader_repTouser\"}],\"dbName\":null},\"user\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"username\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"displayUsername\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"about\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"emailVerified\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"banned\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"banReason\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"banExpires\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"usernameSet\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"cash\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"bank_balance\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"last_activity\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"chatBannedUntil\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"userType\",\"kind\":\"enum\",\"type\":\"UserTypes\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"profileBanner\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"jobLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"jobPayoutHour\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"blockNextJobPayout\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"roguelikeMap\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"roguelikeLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"roguelikeHighscore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"jailedUntil\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"jailReason\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"hospitalisedUntil\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"hospitalisedHealingType\",\"kind\":\"enum\",\"type\":\"HospitalisedHealingTypes\"},{\"name\":\"hospitalisedReason\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"energy\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lastEnergyTick\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"focus\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyFatigueUsed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lastFatigueReset\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"xp\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"actionPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"nextAPTick\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"maxActionPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"currentHealth\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"nextHPTick\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"health\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"talentPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"activeCourseId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"courseEnds\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"class\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"classPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"adminNotes\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"combatLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"discordID\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"currentMission\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"missionEnds\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"profileDetailBanUntil\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"weeklyBuyLimitRemaining\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyQuestsRewardClaimed\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"currentMapLocation\",\"kind\":\"enum\",\"type\":\"ExploreNodeLocation\"},{\"name\":\"travelStartTime\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"travelEndTime\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"travelMethod\",\"kind\":\"enum\",\"type\":\"TravelMethod\"},{\"name\":\"defeatedNpcs\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"pushNotificationsEnabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"gangCreds\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lastNewsIDRead\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"gangId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"jobId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"referrerId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"accounts\",\"kind\":\"object\",\"type\":\"account\",\"relationName\":\"accountTouser\"},{\"name\":\"actionsAsPlayer\",\"kind\":\"object\",\"type\":\"action_log\",\"relationName\":\"actionsAsPlayer\"},{\"name\":\"actionsAsSecondParty\",\"kind\":\"object\",\"type\":\"action_log\",\"relationName\":\"actionsAsSecondParty\"},{\"name\":\"auction_item\",\"kind\":\"object\",\"type\":\"auction_item\",\"relationName\":\"auction_itemTouser\"},{\"name\":\"initiatedBankTransactions\",\"kind\":\"object\",\"type\":\"bank_transaction\",\"relationName\":\"initiatedBankTransactions\"},{\"name\":\"secondPartyBankTransactions\",\"kind\":\"object\",\"type\":\"bank_transaction\",\"relationName\":\"secondPartyBankTransactions\"},{\"name\":\"userBankTransactions\",\"kind\":\"object\",\"type\":\"bank_transaction\",\"relationName\":\"userBankTransactions\"},{\"name\":\"battlesAsAttacker\",\"kind\":\"object\",\"type\":\"battle_log\",\"relationName\":\"battlesAsAttacker\"},{\"name\":\"battlesAsDefender\",\"kind\":\"object\",\"type\":\"battle_log\",\"relationName\":\"battlesAsDefender\"},{\"name\":\"placedBounties\",\"kind\":\"object\",\"type\":\"bounty\",\"relationName\":\"placedBounties\"},{\"name\":\"targetedBounties\",\"kind\":\"object\",\"type\":\"bounty\",\"relationName\":\"targetedBounties\"},{\"name\":\"claimedBounties\",\"kind\":\"object\",\"type\":\"bounty\",\"relationName\":\"claimedBounties\"},{\"name\":\"chat_message\",\"kind\":\"object\",\"type\":\"chat_message\",\"relationName\":\"chat_messageTouser\"},{\"name\":\"daily_quest\",\"kind\":\"object\",\"type\":\"daily_quest\",\"relationName\":\"daily_questTouser\"},{\"name\":\"equipped_item\",\"kind\":\"object\",\"type\":\"equipped_item\",\"relationName\":\"equipped_itemTouser\"},{\"name\":\"game_stats\",\"kind\":\"object\",\"type\":\"game_stats\",\"relationName\":\"game_statsTouser\"},{\"name\":\"gang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"gangMembers\"},{\"name\":\"gang_invite_gang_invite_senderIdTouser\",\"kind\":\"object\",\"type\":\"gang_invite\",\"relationName\":\"gang_invite_senderIdTouser\"},{\"name\":\"gang_invite_gang_invite_recipientIdTouser\",\"kind\":\"object\",\"type\":\"gang_invite\",\"relationName\":\"gang_invite_recipientIdTouser\"},{\"name\":\"gang_log_gang_log_gangMemberIdTouser\",\"kind\":\"object\",\"type\":\"gang_log\",\"relationName\":\"gang_log_gangMemberIdTouser\"},{\"name\":\"gang_log_gang_log_secondPartyIdTouser\",\"kind\":\"object\",\"type\":\"gang_log\",\"relationName\":\"gang_log_secondPartyIdTouser\"},{\"name\":\"gang_member\",\"kind\":\"object\",\"type\":\"gang_member\",\"relationName\":\"gang_memberTouser\"},{\"name\":\"lottery\",\"kind\":\"object\",\"type\":\"lottery\",\"relationName\":\"lotteryTouser\"},{\"name\":\"lottery_entry\",\"kind\":\"object\",\"type\":\"lottery_entry\",\"relationName\":\"lottery_entryTouser\"},{\"name\":\"notification\",\"kind\":\"object\",\"type\":\"notification\",\"relationName\":\"notificationTouser\"},{\"name\":\"poll_response\",\"kind\":\"object\",\"type\":\"poll_response\",\"relationName\":\"poll_responseTouser\"},{\"name\":\"private_message_private_message_senderIdTouser\",\"kind\":\"object\",\"type\":\"private_message\",\"relationName\":\"private_message_senderIdTouser\"},{\"name\":\"private_message_private_message_receiverIdTouser\",\"kind\":\"object\",\"type\":\"private_message\",\"relationName\":\"private_message_receiverIdTouser\"},{\"name\":\"profile_comment_profile_comment_senderIdTouser\",\"kind\":\"object\",\"type\":\"profile_comment\",\"relationName\":\"profile_comment_senderIdTouser\"},{\"name\":\"profile_comment_profile_comment_receiverIdTouser\",\"kind\":\"object\",\"type\":\"profile_comment\",\"relationName\":\"profile_comment_receiverIdTouser\"},{\"name\":\"push_token\",\"kind\":\"object\",\"type\":\"push_token\",\"relationName\":\"push_tokenTouser\"},{\"name\":\"quest_progress\",\"kind\":\"object\",\"type\":\"quest_progress\",\"relationName\":\"quest_progressTouser\"},{\"name\":\"quest_objective_progress\",\"kind\":\"object\",\"type\":\"quest_objective_progress\",\"relationName\":\"quest_objective_progressTouser\"},{\"name\":\"registration_code_registration_code_referrerIdTouser\",\"kind\":\"object\",\"type\":\"registration_code\",\"relationName\":\"registration_code_referrerIdTouser\"},{\"name\":\"registration_code_registration_code_claimerIdTouser\",\"kind\":\"object\",\"type\":\"registration_code\",\"relationName\":\"registration_code_claimerIdTouser\"},{\"name\":\"session\",\"kind\":\"object\",\"type\":\"session\",\"relationName\":\"sessionTouser\"},{\"name\":\"shrine_donation\",\"kind\":\"object\",\"type\":\"shrine_donation\",\"relationName\":\"shrine_donationTouser\"},{\"name\":\"suggestion\",\"kind\":\"object\",\"type\":\"suggestion\",\"relationName\":\"suggestionTouser\"},{\"name\":\"suggestion_comment\",\"kind\":\"object\",\"type\":\"suggestion_comment\",\"relationName\":\"suggestion_commentTouser\"},{\"name\":\"suggestion_vote\",\"kind\":\"object\",\"type\":\"suggestion_vote\",\"relationName\":\"suggestion_voteTouser\"},{\"name\":\"trader_rep\",\"kind\":\"object\",\"type\":\"trader_rep\",\"relationName\":\"trader_repTouser\"},{\"name\":\"job\",\"kind\":\"object\",\"type\":\"job\",\"relationName\":\"jobTouser\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser\"},{\"name\":\"other_user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser\"},{\"name\":\"user_achievements\",\"kind\":\"object\",\"type\":\"user_achievements\",\"relationName\":\"userTouser_achievements\"},{\"name\":\"user_completed_course\",\"kind\":\"object\",\"type\":\"user_completed_course\",\"relationName\":\"userTouser_completed_course\"},{\"name\":\"user_crafting_queue\",\"kind\":\"object\",\"type\":\"user_crafting_queue\",\"relationName\":\"userTouser_crafting_queue\"},{\"name\":\"user_item\",\"kind\":\"object\",\"type\":\"user_item\",\"relationName\":\"userTouser_item\"},{\"name\":\"user_recipe\",\"kind\":\"object\",\"type\":\"user_recipe\",\"relationName\":\"userTouser_recipe\"},{\"name\":\"user_talent\",\"kind\":\"object\",\"type\":\"user_talent\",\"relationName\":\"userTouser_talent\"},{\"name\":\"user_property\",\"kind\":\"object\",\"type\":\"user_property\",\"relationName\":\"userTouser_property\"},{\"name\":\"statusMessage\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"statusMessageUpdatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"showLastOnline\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"sent_friend_requests\",\"kind\":\"object\",\"type\":\"friend_request\",\"relationName\":\"friend_request_sender\"},{\"name\":\"received_friend_requests\",\"kind\":\"object\",\"type\":\"friend_request\",\"relationName\":\"friend_request_receiver\"},{\"name\":\"friends\",\"kind\":\"object\",\"type\":\"friend\",\"relationName\":\"user_friends\"},{\"name\":\"friendOf\",\"kind\":\"object\",\"type\":\"friend\",\"relationName\":\"friend_of\"},{\"name\":\"rivals\",\"kind\":\"object\",\"type\":\"rival\",\"relationName\":\"user_rivals\"},{\"name\":\"rivalOf\",\"kind\":\"object\",\"type\":\"rival\",\"relationName\":\"rival_of\"},{\"name\":\"user_status_effect\",\"kind\":\"object\",\"type\":\"user_status_effect\",\"relationName\":\"userTouser_status_effect\"},{\"name\":\"user_skills\",\"kind\":\"object\",\"type\":\"user_skill\",\"relationName\":\"userTouser_skill\"},{\"name\":\"exploreNodes\",\"kind\":\"object\",\"type\":\"explore_player_node\",\"relationName\":\"explore_player_nodeTouser\"},{\"name\":\"ownedGang\",\"kind\":\"object\",\"type\":\"gang\",\"relationName\":\"gangOwner\"},{\"name\":\"user_pet\",\"kind\":\"object\",\"type\":\"user_pet\",\"relationName\":\"userTouser_pet\"},{\"name\":\"user_equipped_abilities\",\"kind\":\"object\",\"type\":\"user_equipped_abilities\",\"relationName\":\"userTouser_equipped_abilities\"}],\"dbName\":\"user\"},\"user_equipped_abilities\":{\"fields\":[{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_equipped_abilities\"},{\"name\":\"equippedAbility1Id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"equippedAbility2Id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"equippedAbility3Id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"equippedAbility4Id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"talent_equippedAbility1\",\"kind\":\"object\",\"type\":\"talent\",\"relationName\":\"userEquippedAbility1\"},{\"name\":\"talent_equippedAbility2\",\"kind\":\"object\",\"type\":\"talent\",\"relationName\":\"userEquippedAbility2\"},{\"name\":\"talent_equippedAbility3\",\"kind\":\"object\",\"type\":\"talent\",\"relationName\":\"userEquippedAbility3\"},{\"name\":\"talent_equippedAbility4\",\"kind\":\"object\",\"type\":\"talent\",\"relationName\":\"userEquippedAbility4\"}],\"dbName\":\"user_equipped_abilities\"},\"session\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"impersonatedBy\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"sessionTouser\"}],\"dbName\":\"session\"},\"account\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"providerId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"accessToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"refreshToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"idToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"accessTokenExpiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"refreshTokenExpiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"scope\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"password\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"accountTouser\"}],\"dbName\":\"account\"},\"verification\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"identifier\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":\"verification\"},\"user_item\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"count\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"upgradeLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quality\",\"kind\":\"enum\",\"type\":\"ItemQuality\"},{\"name\":\"isTradeable\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"equipped_items\",\"kind\":\"object\",\"type\":\"equipped_item\",\"relationName\":\"equipped_itemTouser_item\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_item\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemTouser_item\"}],\"dbName\":null},\"user_recipe\":{\"fields\":[{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"craftingRecipeId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"crafting_recipe\",\"kind\":\"object\",\"type\":\"crafting_recipe\",\"relationName\":\"crafting_recipeTouser_recipe\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_recipe\"}],\"dbName\":null},\"user_completed_course\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"courseId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_completed_course\"}],\"dbName\":null},\"user_talent\":{\"fields\":[{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"talentId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"talent\",\"kind\":\"object\",\"type\":\"talent\",\"relationName\":\"talentTouser_talent\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_talent\"}],\"dbName\":null},\"user_achievements\":{\"fields\":[{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"battleWins\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"npcBattleWins\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"craftsCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"marketItemsSold\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"marketMoneyMade\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalMuggingGain\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalMuggingLoss\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalCasinoProfitLoss\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"questsCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyQuestsCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"coursesCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"roguelikeNodesCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"roguelikeMapsCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"examsCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalBountyRewards\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalBountyPlaced\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalMissionHours\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"suggestionsVoted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"encountersCompleted\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_achievements\"}],\"dbName\":null},\"user_crafting_queue\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"startedAt\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"endsAt\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"craftingRecipeId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_crafting_queue\"},{\"name\":\"crafting_recipe\",\"kind\":\"object\",\"type\":\"crafting_recipe\",\"relationName\":\"crafting_recipeTouser_crafting_queue\"}],\"dbName\":null},\"property\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"propertyType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"cost\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"upkeep\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"slots\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"buffs\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"image\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user_property\",\"kind\":\"object\",\"type\":\"user_property\",\"relationName\":\"propertyTouser_property\"}],\"dbName\":null},\"user_property\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"purchaseDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastUpkeepPaid\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"furniture\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"customization\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"propertyId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_property\"},{\"name\":\"property\",\"kind\":\"object\",\"type\":\"property\",\"relationName\":\"propertyTouser_property\"}],\"dbName\":null},\"pet\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"species\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"maxLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"evolution_stages\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user_pet\",\"kind\":\"object\",\"type\":\"user_pet\",\"relationName\":\"petTouser_pet\"},{\"name\":\"item\",\"kind\":\"object\",\"type\":\"item\",\"relationName\":\"itemTopet\"}],\"dbName\":null},\"user_pet\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"xp\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"nextLevelXp\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"happiness\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"energy\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"evolution\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"petId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_pet\"},{\"name\":\"pet\",\"kind\":\"object\",\"type\":\"pet\",\"relationName\":\"petTouser_pet\"}],\"dbName\":null},\"friend_request\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"senderId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"receiverId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"sender\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"friend_request_sender\"},{\"name\":\"receiver\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"friend_request_receiver\"}],\"dbName\":null},\"friend\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"friendId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"note\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"user_friends\"},{\"name\":\"friend\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"friend_of\"}],\"dbName\":null},\"rival\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"rivalId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"note\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"user_rivals\"},{\"name\":\"rival\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"rival_of\"}],\"dbName\":null},\"status_effect\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"source\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"effectType\",\"kind\":\"enum\",\"type\":\"StatusEffectType\"},{\"name\":\"category\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"tier\",\"kind\":\"enum\",\"type\":\"StatusEffectTier\"},{\"name\":\"duration\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"modifier\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"modifierType\",\"kind\":\"enum\",\"type\":\"StatusEffectModifierType\"},{\"name\":\"stackable\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"maxStacks\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"disabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user_status_effect\",\"kind\":\"object\",\"type\":\"user_status_effect\",\"relationName\":\"status_effectTouser_status_effect\"}],\"dbName\":null},\"user_status_effect\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"stacks\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"endsAt\",\"kind\":\"scalar\",\"type\":\"BigInt\"},{\"name\":\"customName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"effectId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"effect\",\"kind\":\"object\",\"type\":\"status_effect\",\"relationName\":\"status_effectTouser_status_effect\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_status_effect\"}],\"dbName\":null},\"user_skill\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"skillType\",\"kind\":\"enum\",\"type\":\"SkillType\"},{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"experience\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"talentPoints\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"userTouser_skill\"}],\"dbName\":null},\"explore_static_node\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"nodeType\",\"kind\":\"enum\",\"type\":\"ExploreNodeType\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"position\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"location\",\"kind\":\"enum\",\"type\":\"ExploreNodeLocation\"},{\"name\":\"shopId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"shop\",\"kind\":\"object\",\"type\":\"shop\",\"relationName\":\"explore_static_nodeToshop\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"explore_player_node\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"nodeType\",\"kind\":\"enum\",\"type\":\"ExploreNodeType\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"position\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"location\",\"kind\":\"enum\",\"type\":\"ExploreNodeLocation\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"ExploreNodeStatus\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"user\",\"relationName\":\"explore_player_nodeTouser\"}],\"dbName\":null},\"story_season\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"startDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"requiredLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"story_chapter\",\"kind\":\"object\",\"type\":\"story_chapter\",\"relationName\":\"story_chapterTostory_season\"}],\"dbName\":null},\"story_chapter\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"seasonId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"order\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"unlockDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"requiredLevel\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"requiredChapterIds\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"story_season\",\"kind\":\"object\",\"type\":\"story_season\",\"relationName\":\"story_chapterTostory_season\"},{\"name\":\"quests\",\"kind\":\"object\",\"type\":\"quest\",\"relationName\":\"questTostory_chapter\"}],\"dbName\":null},\"story_episode\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"episodeType\",\"kind\":\"enum\",\"type\":\"StoryEpisodeType\"},{\"name\":\"exploreLocation\",\"kind\":\"enum\",\"type\":\"ExploreNodeLocation\"},{\"name\":\"content\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"choices\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"objectiveId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"quest_objective\",\"kind\":\"object\",\"type\":\"quest_objective\",\"relationName\":\"quest_objectiveTostory_episode\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
config.engineWasm = undefined

async function decodeBase64AsWasm(wasmBase64: string): Promise<WebAssembly.Module> {
  const { Buffer } = await import('node:buffer')
  const wasmArray = Buffer.from(wasmBase64, 'base64')
  return new WebAssembly.Module(wasmArray)
}

config.compilerWasm = {
  getRuntime: async () => await import("@prisma/client/runtime/query_compiler_bg.mysql.mjs"),

  getQueryCompilerWasmModule: async () => {
    const { wasm } = await import("@prisma/client/runtime/query_compiler_bg.mysql.wasm-base64.mjs")
    return await decodeBase64AsWasm(wasm)
  }
}




export type LogOptions<ClientOptions extends Prisma.PrismaClientOptions> =
  'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never

export interface PrismaClientConstructor {
    /**
   * ## Prisma Client
   * 
   * Type-safe database client for TypeScript
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Action_logs
   * const action_logs = await prisma.action_log.findMany()
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  new <
    Options extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
    LogOpts extends LogOptions<Options> = LogOptions<Options>,
    OmitOpts extends Prisma.PrismaClientOptions['omit'] = Options extends { omit: infer U } ? U : Prisma.PrismaClientOptions['omit'],
    ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs
  >(options?: Prisma.Subset<Options, Prisma.PrismaClientOptions> ): PrismaClient<LogOpts, OmitOpts, ExtArgs>
}

/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Action_logs
 * const action_logs = await prisma.action_log.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */

export interface PrismaClient<
  in LogOpts extends Prisma.LogLevel = never,
  in out OmitOpts extends Prisma.PrismaClientOptions['omit'] = Prisma.PrismaClientOptions['omit'],
  in out ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

  $on<V extends LogOpts>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): runtime.Types.Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): runtime.Types.Utils.JsPromise<void>;

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): runtime.Types.Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => runtime.Types.Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): runtime.Types.Utils.JsPromise<R>


  $extends: runtime.Types.Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<OmitOpts>, ExtArgs, runtime.Types.Utils.Call<Prisma.TypeMapCb<OmitOpts>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.action_log`: Exposes CRUD operations for the **action_log** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Action_logs
    * const action_logs = await prisma.action_log.findMany()
    * ```
    */
  get action_log(): Prisma.action_logDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.game_config`: Exposes CRUD operations for the **game_config** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Game_configs
    * const game_configs = await prisma.game_config.findMany()
    * ```
    */
  get game_config(): Prisma.game_configDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.auction_item`: Exposes CRUD operations for the **auction_item** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Auction_items
    * const auction_items = await prisma.auction_item.findMany()
    * ```
    */
  get auction_item(): Prisma.auction_itemDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.bank_transaction`: Exposes CRUD operations for the **bank_transaction** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Bank_transactions
    * const bank_transactions = await prisma.bank_transaction.findMany()
    * ```
    */
  get bank_transaction(): Prisma.bank_transactionDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.battle_log`: Exposes CRUD operations for the **battle_log** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Battle_logs
    * const battle_logs = await prisma.battle_log.findMany()
    * ```
    */
  get battle_log(): Prisma.battle_logDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.bounty`: Exposes CRUD operations for the **bounty** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Bounties
    * const bounties = await prisma.bounty.findMany()
    * ```
    */
  get bounty(): Prisma.bountyDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.chat_message`: Exposes CRUD operations for the **chat_message** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Chat_messages
    * const chat_messages = await prisma.chat_message.findMany()
    * ```
    */
  get chat_message(): Prisma.chat_messageDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.chat_room`: Exposes CRUD operations for the **chat_room** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Chat_rooms
    * const chat_rooms = await prisma.chat_room.findMany()
    * ```
    */
  get chat_room(): Prisma.chat_roomDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.crafting_recipe`: Exposes CRUD operations for the **crafting_recipe** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Crafting_recipes
    * const crafting_recipes = await prisma.crafting_recipe.findMany()
    * ```
    */
  get crafting_recipe(): Prisma.crafting_recipeDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.creature`: Exposes CRUD operations for the **creature** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Creatures
    * const creatures = await prisma.creature.findMany()
    * ```
    */
  get creature(): Prisma.creatureDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.daily_mission`: Exposes CRUD operations for the **daily_mission** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Daily_missions
    * const daily_missions = await prisma.daily_mission.findMany()
    * ```
    */
  get daily_mission(): Prisma.daily_missionDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.drop_chance`: Exposes CRUD operations for the **drop_chance** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Drop_chances
    * const drop_chances = await prisma.drop_chance.findMany()
    * ```
    */
  get drop_chance(): Prisma.drop_chanceDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.equipped_item`: Exposes CRUD operations for the **equipped_item** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Equipped_items
    * const equipped_items = await prisma.equipped_item.findMany()
    * ```
    */
  get equipped_item(): Prisma.equipped_itemDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.game_stats`: Exposes CRUD operations for the **game_stats** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Game_stats
    * const game_stats = await prisma.game_stats.findMany()
    * ```
    */
  get game_stats(): Prisma.game_statsDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.gang`: Exposes CRUD operations for the **gang** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Gangs
    * const gangs = await prisma.gang.findMany()
    * ```
    */
  get gang(): Prisma.gangDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.gang_invite`: Exposes CRUD operations for the **gang_invite** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Gang_invites
    * const gang_invites = await prisma.gang_invite.findMany()
    * ```
    */
  get gang_invite(): Prisma.gang_inviteDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.gang_log`: Exposes CRUD operations for the **gang_log** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Gang_logs
    * const gang_logs = await prisma.gang_log.findMany()
    * ```
    */
  get gang_log(): Prisma.gang_logDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.gang_member`: Exposes CRUD operations for the **gang_member** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Gang_members
    * const gang_members = await prisma.gang_member.findMany()
    * ```
    */
  get gang_member(): Prisma.gang_memberDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.item`: Exposes CRUD operations for the **item** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Items
    * const items = await prisma.item.findMany()
    * ```
    */
  get item(): Prisma.itemDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.job`: Exposes CRUD operations for the **job** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Jobs
    * const jobs = await prisma.job.findMany()
    * ```
    */
  get job(): Prisma.jobDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.lottery`: Exposes CRUD operations for the **lottery** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Lotteries
    * const lotteries = await prisma.lottery.findMany()
    * ```
    */
  get lottery(): Prisma.lotteryDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.lottery_entry`: Exposes CRUD operations for the **lottery_entry** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Lottery_entries
    * const lottery_entries = await prisma.lottery_entry.findMany()
    * ```
    */
  get lottery_entry(): Prisma.lottery_entryDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.notification`: Exposes CRUD operations for the **notification** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Notifications
    * const notifications = await prisma.notification.findMany()
    * ```
    */
  get notification(): Prisma.notificationDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.poll`: Exposes CRUD operations for the **poll** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Polls
    * const polls = await prisma.poll.findMany()
    * ```
    */
  get poll(): Prisma.pollDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.poll_response`: Exposes CRUD operations for the **poll_response** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Poll_responses
    * const poll_responses = await prisma.poll_response.findMany()
    * ```
    */
  get poll_response(): Prisma.poll_responseDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.private_message`: Exposes CRUD operations for the **private_message** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Private_messages
    * const private_messages = await prisma.private_message.findMany()
    * ```
    */
  get private_message(): Prisma.private_messageDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.profile_comment`: Exposes CRUD operations for the **profile_comment** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Profile_comments
    * const profile_comments = await prisma.profile_comment.findMany()
    * ```
    */
  get profile_comment(): Prisma.profile_commentDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.push_token`: Exposes CRUD operations for the **push_token** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Push_tokens
    * const push_tokens = await prisma.push_token.findMany()
    * ```
    */
  get push_token(): Prisma.push_tokenDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.daily_quest`: Exposes CRUD operations for the **daily_quest** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Daily_quests
    * const daily_quests = await prisma.daily_quest.findMany()
    * ```
    */
  get daily_quest(): Prisma.daily_questDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.quest`: Exposes CRUD operations for the **quest** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Quests
    * const quests = await prisma.quest.findMany()
    * ```
    */
  get quest(): Prisma.questDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.quest_progress`: Exposes CRUD operations for the **quest_progress** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Quest_progresses
    * const quest_progresses = await prisma.quest_progress.findMany()
    * ```
    */
  get quest_progress(): Prisma.quest_progressDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.quest_objective`: Exposes CRUD operations for the **quest_objective** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Quest_objectives
    * const quest_objectives = await prisma.quest_objective.findMany()
    * ```
    */
  get quest_objective(): Prisma.quest_objectiveDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.quest_objective_progress`: Exposes CRUD operations for the **quest_objective_progress** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Quest_objective_progresses
    * const quest_objective_progresses = await prisma.quest_objective_progress.findMany()
    * ```
    */
  get quest_objective_progress(): Prisma.quest_objective_progressDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.quest_reward`: Exposes CRUD operations for the **quest_reward** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Quest_rewards
    * const quest_rewards = await prisma.quest_reward.findMany()
    * ```
    */
  get quest_reward(): Prisma.quest_rewardDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.recipe_item`: Exposes CRUD operations for the **recipe_item** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Recipe_items
    * const recipe_items = await prisma.recipe_item.findMany()
    * ```
    */
  get recipe_item(): Prisma.recipe_itemDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.registration_code`: Exposes CRUD operations for the **registration_code** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Registration_codes
    * const registration_codes = await prisma.registration_code.findMany()
    * ```
    */
  get registration_code(): Prisma.registration_codeDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.shop`: Exposes CRUD operations for the **shop** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Shops
    * const shops = await prisma.shop.findMany()
    * ```
    */
  get shop(): Prisma.shopDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.shop_listing`: Exposes CRUD operations for the **shop_listing** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Shop_listings
    * const shop_listings = await prisma.shop_listing.findMany()
    * ```
    */
  get shop_listing(): Prisma.shop_listingDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.shrine_donation`: Exposes CRUD operations for the **shrine_donation** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Shrine_donations
    * const shrine_donations = await prisma.shrine_donation.findMany()
    * ```
    */
  get shrine_donation(): Prisma.shrine_donationDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.shrine_goal`: Exposes CRUD operations for the **shrine_goal** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Shrine_goals
    * const shrine_goals = await prisma.shrine_goal.findMany()
    * ```
    */
  get shrine_goal(): Prisma.shrine_goalDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.suggestion`: Exposes CRUD operations for the **suggestion** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Suggestions
    * const suggestions = await prisma.suggestion.findMany()
    * ```
    */
  get suggestion(): Prisma.suggestionDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.suggestion_comment`: Exposes CRUD operations for the **suggestion_comment** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Suggestion_comments
    * const suggestion_comments = await prisma.suggestion_comment.findMany()
    * ```
    */
  get suggestion_comment(): Prisma.suggestion_commentDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.suggestion_vote`: Exposes CRUD operations for the **suggestion_vote** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Suggestion_votes
    * const suggestion_votes = await prisma.suggestion_vote.findMany()
    * ```
    */
  get suggestion_vote(): Prisma.suggestion_voteDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.talent`: Exposes CRUD operations for the **talent** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Talents
    * const talents = await prisma.talent.findMany()
    * ```
    */
  get talent(): Prisma.talentDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.trader_rep`: Exposes CRUD operations for the **trader_rep** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Trader_reps
    * const trader_reps = await prisma.trader_rep.findMany()
    * ```
    */
  get trader_rep(): Prisma.trader_repDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user`: Exposes CRUD operations for the **user** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.userDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_equipped_abilities`: Exposes CRUD operations for the **user_equipped_abilities** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_equipped_abilities
    * const user_equipped_abilities = await prisma.user_equipped_abilities.findMany()
    * ```
    */
  get user_equipped_abilities(): Prisma.user_equipped_abilitiesDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.session`: Exposes CRUD operations for the **session** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Sessions
    * const sessions = await prisma.session.findMany()
    * ```
    */
  get session(): Prisma.sessionDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.account`: Exposes CRUD operations for the **account** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Accounts
    * const accounts = await prisma.account.findMany()
    * ```
    */
  get account(): Prisma.accountDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.verification`: Exposes CRUD operations for the **verification** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Verifications
    * const verifications = await prisma.verification.findMany()
    * ```
    */
  get verification(): Prisma.verificationDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_item`: Exposes CRUD operations for the **user_item** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_items
    * const user_items = await prisma.user_item.findMany()
    * ```
    */
  get user_item(): Prisma.user_itemDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_recipe`: Exposes CRUD operations for the **user_recipe** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_recipes
    * const user_recipes = await prisma.user_recipe.findMany()
    * ```
    */
  get user_recipe(): Prisma.user_recipeDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_completed_course`: Exposes CRUD operations for the **user_completed_course** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_completed_courses
    * const user_completed_courses = await prisma.user_completed_course.findMany()
    * ```
    */
  get user_completed_course(): Prisma.user_completed_courseDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_talent`: Exposes CRUD operations for the **user_talent** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_talents
    * const user_talents = await prisma.user_talent.findMany()
    * ```
    */
  get user_talent(): Prisma.user_talentDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_achievements`: Exposes CRUD operations for the **user_achievements** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_achievements
    * const user_achievements = await prisma.user_achievements.findMany()
    * ```
    */
  get user_achievements(): Prisma.user_achievementsDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_crafting_queue`: Exposes CRUD operations for the **user_crafting_queue** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_crafting_queues
    * const user_crafting_queues = await prisma.user_crafting_queue.findMany()
    * ```
    */
  get user_crafting_queue(): Prisma.user_crafting_queueDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.property`: Exposes CRUD operations for the **property** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Properties
    * const properties = await prisma.property.findMany()
    * ```
    */
  get property(): Prisma.propertyDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_property`: Exposes CRUD operations for the **user_property** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_properties
    * const user_properties = await prisma.user_property.findMany()
    * ```
    */
  get user_property(): Prisma.user_propertyDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.pet`: Exposes CRUD operations for the **pet** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Pets
    * const pets = await prisma.pet.findMany()
    * ```
    */
  get pet(): Prisma.petDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_pet`: Exposes CRUD operations for the **user_pet** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_pets
    * const user_pets = await prisma.user_pet.findMany()
    * ```
    */
  get user_pet(): Prisma.user_petDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.friend_request`: Exposes CRUD operations for the **friend_request** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Friend_requests
    * const friend_requests = await prisma.friend_request.findMany()
    * ```
    */
  get friend_request(): Prisma.friend_requestDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.friend`: Exposes CRUD operations for the **friend** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Friends
    * const friends = await prisma.friend.findMany()
    * ```
    */
  get friend(): Prisma.friendDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.rival`: Exposes CRUD operations for the **rival** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Rivals
    * const rivals = await prisma.rival.findMany()
    * ```
    */
  get rival(): Prisma.rivalDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.status_effect`: Exposes CRUD operations for the **status_effect** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Status_effects
    * const status_effects = await prisma.status_effect.findMany()
    * ```
    */
  get status_effect(): Prisma.status_effectDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_status_effect`: Exposes CRUD operations for the **user_status_effect** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_status_effects
    * const user_status_effects = await prisma.user_status_effect.findMany()
    * ```
    */
  get user_status_effect(): Prisma.user_status_effectDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.user_skill`: Exposes CRUD operations for the **user_skill** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_skills
    * const user_skills = await prisma.user_skill.findMany()
    * ```
    */
  get user_skill(): Prisma.user_skillDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.explore_static_node`: Exposes CRUD operations for the **explore_static_node** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Explore_static_nodes
    * const explore_static_nodes = await prisma.explore_static_node.findMany()
    * ```
    */
  get explore_static_node(): Prisma.explore_static_nodeDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.explore_player_node`: Exposes CRUD operations for the **explore_player_node** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Explore_player_nodes
    * const explore_player_nodes = await prisma.explore_player_node.findMany()
    * ```
    */
  get explore_player_node(): Prisma.explore_player_nodeDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.story_season`: Exposes CRUD operations for the **story_season** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Story_seasons
    * const story_seasons = await prisma.story_season.findMany()
    * ```
    */
  get story_season(): Prisma.story_seasonDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.story_chapter`: Exposes CRUD operations for the **story_chapter** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Story_chapters
    * const story_chapters = await prisma.story_chapter.findMany()
    * ```
    */
  get story_chapter(): Prisma.story_chapterDelegate<ExtArgs, { omit: OmitOpts }>;

  /**
   * `prisma.story_episode`: Exposes CRUD operations for the **story_episode** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Story_episodes
    * const story_episodes = await prisma.story_episode.findMany()
    * ```
    */
  get story_episode(): Prisma.story_episodeDelegate<ExtArgs, { omit: OmitOpts }>;
}

export function getPrismaClientClass(dirname: string): PrismaClientConstructor {
  config.dirname = dirname
  return runtime.getPrismaClient(config) as unknown as PrismaClientConstructor
}
