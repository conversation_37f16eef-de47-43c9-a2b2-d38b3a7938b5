{"name": "chikara-backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "bun ./src/server.ts", "dev": "bun --watch --no-clear-screen ./src/server.ts", "build": "tsgo && tsc-alias", "seed": "bun run reset && bun prisma/seed.ts", "alpha-seed": "bun run reset && bun prisma/seeders/alphaSeed.ts", "migrate": "npx prisma migrate dev", "reset": "npx prisma db push --force-reset", "fetchJSON": "node ./prisma/seeders/downloadJson.js", "test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "test:clear": "vitest --clear-cache", "lint": "npx eslint src/**/*.js ./src/server.ts", "format": "prettier --write .", "type-check": "tsgo --noEmit", "generate": "prisma generate"}, "imports": {"#prismaz": "./generated/prisma/client.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.918.0", "@aws-sdk/s3-request-presigner": "^3.918.0", "@date-fns/utc": "^2.1.1", "@logtail/node": "^0.5.6", "@logtail/winston": "^0.5.6", "@orpc/server": "^1.10.2", "@prisma/adapter-mariadb": "^6.17.1", "@prisma/client": "^6.17.1", "@xata.io/client": "^0.30.1", "adm-zip": "0.5.14", "better-auth": "^1.3.33", "bullmq": "^5.62.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "firebase-admin": "^13.5.0", "http-graceful-shutdown": "^3.1.14", "mathjs": "^15.0.0", "ms": "^2.1.3", "multer": "^2.0.2", "nodemailer": "^7.0.10", "openai": "^6.6.0", "prisma-json-types-generator": "^3.6.2", "redis": "^5.8.3", "request-ip": "^3.3.0", "sharp": "^0.34.4", "socket.io": "^4.8.1", "winston": "^3.18.3", "zod": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.38.0", "@types/bun": "^1.3.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.5", "@types/ms": "~2.1.0", "@types/multer": "~2.0.0", "@types/node": "^24.9.1", "@types/nodemailer": "~7.0.3", "@types/request-ip": "~0.0.41", "@typescript/native-preview": "^7.0.0-dev.20250806.1", "@vitest/coverage-v8": "^4.0.4", "@vitest/eslint-plugin": "^1.3.26", "bun": "^1.3.1", "eslint": "^9.38.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-n": "^17.23.1", "eslint-plugin-unicorn": "^62.0.0", "globals": "^16.4.0", "prisma": "^6.17.1", "tsc-alias": "^1.8.16", "typescript": "^5.9.3", "typescript-eslint": "^8.46.2", "vitest": "^4.0.4", "vitest-mock-extended": "^3.1.0"}, "prisma": {"seed": "bun prisma/seed.ts", "schema": "prisma/schema.prisma"}, "engines": {"node": ">=21.2.0"}, "trustedDependencies": ["bun", "sharp", "@contrast/fn-inspect", "@firebase/util", "@prisma/client", "@prisma/engines", "@sentry/cli", "core-js", "esbuild", "msgpackr-extract", "prisma", "protobufjs", "unrs-resolver"]}