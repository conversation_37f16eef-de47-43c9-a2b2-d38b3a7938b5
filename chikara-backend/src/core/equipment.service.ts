import { BattlePlayer, EquippedItem } from "../features/battle/types/battle.types.js";
import { ItemModel, TransactionClient, UserModel, db } from "../lib/db.js";
import { logger } from "../utils/log.js";
import { EquipSlots, ItemQuality, ItemTypes } from "@prisma/client";
import type { ItemEffect } from "../types/item.js";

export interface FormattedEquippedItems {
    [ItemTypes.head]: EquippedItem | null;
    [ItemTypes.chest]: EquippedItem | null;
    [ItemTypes.hands]: EquippedItem | null;
    [ItemTypes.legs]: EquippedItem | null;
    [ItemTypes.feet]: EquippedItem | null;
    [ItemTypes.finger]: EquippedItem | null;
    [ItemTypes.weapon]: EquippedItem | null;
    [ItemTypes.ranged]: EquippedItem | null;
    [ItemTypes.offhand]: EquippedItem | null;
    [ItemTypes.shield]: EquippedItem | null;
}

export const IsItemEquippable = (item: ItemModel) => {
    if (!item) return false;

    return ["head", "chest", "hands", "legs", "feet", "finger", "offhand", "shield", "weapon", "ranged"].includes(
        item?.itemType
    );
};

const findEquippedItemsWithDetails = async (userId: number) => {
    return await db.equipped_item.findMany({
        where: { userId },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });
};

export const findEquippedItemBySlot = async (userId: number, slot: EquipSlots) => {
    return await db.equipped_item.findFirst({
        where: { userId, slot },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });
};

export const GetEquippedItems = async (userId: number) => {
    const equippedItems = await db.equipped_item.findMany({
        where: { userId },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });

    const formattedEquippedItems: FormattedEquippedItems = {
        [ItemTypes.head]: null,
        [ItemTypes.chest]: null,
        [ItemTypes.hands]: null,
        [ItemTypes.legs]: null,
        [ItemTypes.feet]: null,
        [ItemTypes.finger]: null,
        [ItemTypes.weapon]: null,
        [ItemTypes.ranged]: null,
        [ItemTypes.offhand]: null,
        [ItemTypes.shield]: null,
    };

    if (!equippedItems) return formattedEquippedItems;

    // Process each equipped item
    for (const equippedItem of equippedItems) {
        const userItem = equippedItem.user_item;

        if (!userItem || !userItem.item) {
            continue;
        }

        const itemData = {
            ...userItem.item,
            userItemId: userItem.id,
            upgradeLevel: userItem.upgradeLevel ?? 0,
            quality: userItem.quality ?? ItemQuality.normal,
            itemEffects: userItem.item.itemEffects ?? null,
        };

        formattedEquippedItems[equippedItem.slot] = itemData;
    }
    return formattedEquippedItems;
};

export const GetEquippedItem = async (user: UserModel, itemType: EquipSlots) => {
    const equippedItem = await findEquippedItemBySlot(user.id, itemType);

    if (!equippedItem?.user_item?.item) {
        return null;
    }

    const userItem = equippedItem.user_item;
    const upgradeLevel = userItem.upgradeLevel ?? 0;

    // Return the item with upgrade level
    return upgradeLevel > 0 ? { ...userItem.item, upgradeLevel } : userItem.item;
};

export const GetTotalEquippedValue = async (
    user: UserModel | BattlePlayer,
    attribute: keyof Pick<ItemModel, "damage" | "armour" | "health" | "energy" | "actionPoints" | "baseAmmo">,
    attackType?: "melee" | "ranged"
) => {
    const equippedItems = await findEquippedItemsWithDetails(Number(user.id));
    if (!equippedItems || equippedItems.length === 0) return 0;

    let statTotal = 0;

    // Helper function to process item stats
    const processItemStat = (equippedItem: (typeof equippedItems)[number]) => {
        if (!equippedItem?.user_item?.item) return 0;
        const item = equippedItem.user_item.item;
        const statValue = item[attribute] || 0;
        // Note: Upgrade modifiers are now automatically applied by the Prisma extension
        return Math.round(statValue);
    };

    // Process all equipped items
    for (const equippedItem of equippedItems) {
        // Skip weapon-type items if we're filtering by attack type
        if (attackType) {
            if (attackType === "ranged" && equippedItem.slot !== "ranged") continue;
            if (attackType === "melee" && !["weapon", "offhand"].includes(equippedItem.slot)) {
                continue;
            }
        }

        // Skip weapon items if we're not looking for damage
        if (attribute === "damage" && !["weapon", "ranged", "offhand"].includes(equippedItem.slot)) {
            continue;
        }

        statTotal += processItemStat(equippedItem);
    }

    return statTotal;
};

export const deleteEquippedItem = async (id: number, tx?: TransactionClient) => {
    if (tx) {
        return await tx.equipped_item.delete({
            where: { id },
        });
    }
    return await db.equipped_item.delete({
        where: { id },
    });
};

export const UnequipItemFromSlot = async (userId: number, slot: EquipSlots, tx?: TransactionClient) => {
    const equippedItem = await findEquippedItemBySlot(userId, slot);
    if (equippedItem) {
        const item = equippedItem.user_item?.item;

        if (!item) {
            throw `Item ${equippedItem.id} does not have an item obj for user ${userId}`;
        }

        await deleteEquippedItem(equippedItem.id, tx);
    }
};

/**
 * Generic helper function to calculate the effect value based on the modifier type
 * @param baseValue - The base value to apply the effect to
 * @param effect - The item effect configuration
 * @returns The calculated effect value
 */
export const CalculateItemEffectValue = (baseValue: number, effect: ItemEffect): number => {
    const { effectModifier, effectValue = 0 } = effect;

    switch (effectModifier) {
        case "multiply": {
            return baseValue * effectValue;
        }
        case "add": {
            return baseValue + effectValue;
        }
        case "subtract": {
            return baseValue - effectValue;
        }
        case "divide": {
            return effectValue === 0 ? baseValue : baseValue / effectValue;
        }
        case "set": {
            return effectValue;
        }
        default: {
            logger.warn(`Unknown effect modifier: ${effectModifier}. Using effectValue directly.`);
            return effectValue;
        }
    }
};
