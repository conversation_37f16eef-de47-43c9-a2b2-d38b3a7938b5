import * as StoryRepository from "../repositories/story.repository.js";
import * as QuestRepository from "../repositories/quest.repository.js";
import { QuestObjectiveTypes } from "../types/quest.js";
import { logger } from "../utils/log.js";
import { ExploreNodeLocation, Prisma } from "@prisma/client";

/**
 * Get active story nodes for the explore map
 * Now works with quest objectives instead of direct episode availability
 */
export const getActiveStoryNodes = async (userId: number, location?: ExploreNodeLocation) => {
    // Query for active quest objectives with COMPLETE_STORY_EPISODE type
    const whereClause: Prisma.quest_objectiveWhereInput = {
        objectiveType: QuestObjectiveTypes.COMPLETE_STORY_EPISODE,
    };

    logger.info(`Searching for quest objectives with criteria: ${JSON.stringify(whereClause)}`);

    const activeObjectives = await QuestRepository.findUserQuestObjectiveProgress(userId, whereClause);
    const storyNodes = [];

    logger.info(
        `Getting active story nodes for user ${userId} in location ${location || "all"}. Found ${activeObjectives.length} active quest objectives`
    );

    // Debug: Log all found objectives
    for (const objectiveProgress of activeObjectives) {
        logger.info(
            `Found quest objective ${objectiveProgress.quest_objective.id}: ${objectiveProgress.quest_objective.description} (type: ${objectiveProgress.quest_objective.objectiveType}, location: ${objectiveProgress.quest_objective.location}, status: ${objectiveProgress.status})`
        );
    }

    // For each objective, check if it has a linked story episode
    for (const objectiveProgress of activeObjectives) {
        const episode = await StoryRepository.findEpisodeByObjectiveId(objectiveProgress.quest_objective.id);
        if (episode) {
            // Use episode's exploreLocation
            const episodeLocation = episode.exploreLocation;

            // If a specific location is requested, only include episodes for that location
            if (location && episodeLocation !== location) {
                continue;
            }

            storyNodes.push({
                id: episode.id,
                name: episode.name,
                description: episode.description,
                location: episodeLocation,
                episodeType: episode.episodeType,
                expiresAt: null,
                questObjectiveId: objectiveProgress.quest_objective.id,
            });
            logger.info(
                `Found story episode ${episode.id} (${episode.name}) linked to quest objective ${objectiveProgress.quest_objective.id} at location ${episodeLocation}`
            );
        } else {
            logger.info(`No story episode found for quest objective ${objectiveProgress.quest_objective.id}`);
        }
    }

    logger.info(`Returning ${storyNodes.length} active story nodes for user ${userId}`);
    return storyNodes;
};
