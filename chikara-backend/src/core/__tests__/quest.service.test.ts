import { describe, expect, it, vi, beforeEach } from "vitest";
import * as SharedQuestUtils from "../quest.service.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { ExploreNodeLocation, LocationTypes, QuestProgressStatus, QuestTargetAction } from "@prisma/client";

vi.mock("../../repositories/quest.repository.js", () => ({
    findUserQuestObjectiveProgress: vi.fn(),
    getQuestObjectiveById: vi.fn(),
    getQuestById: vi.fn(),
    getUserQuestProgress: vi.fn(),
}));

vi.mock("../../repositories/dailyquest.repository.js", () => ({
    findDailyQuestProgress: vi.fn(),
}));

vi.mock("../../features/quest/quest.helpers.js", () => ({
    updateQuestObjectiveCount: vi.fn(),
    checkObjectiveComplete: vi.fn(),
}));

vi.mock("../../features/dailyquest/dailyquest.helpers.js", () => ({
    updateDailyQuest: vi.fn(),
}));

vi.mock("../../features/quest/quest.controller.js", () => ({
    CompleteQuest: vi.fn(),
}));

vi.mock("../../utils/dateHelpers.js", () => ({
    getToday: vi.fn(() => new Date("2023-01-01")),
}));

vi.mock("../../utils/log.js", () => ({
    logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
    },
    LogErrorStack: vi.fn(),
}));

type MockedFn = ReturnType<typeof vi.fn>;

type QuestRepositoryMock = {
    findUserQuestObjectiveProgress: MockedFn;
    getQuestObjectiveById: MockedFn;
    getQuestById: MockedFn;
    getUserQuestProgress: MockedFn;
};

type DailyQuestRepositoryMock = {
    findDailyQuestProgress: MockedFn;
};

type QuestHelpersMock = {
    updateQuestObjectiveCount: MockedFn;
    checkObjectiveComplete: MockedFn;
};

type DailyQuestHelpersMock = {
    updateDailyQuest: MockedFn;
};

type QuestControllerMock = {
    CompleteQuest: MockedFn;
};

type LogUtilsMock = {
    LogErrorStack: MockedFn;
    logger: {
        info: MockedFn;
        warn: MockedFn;
        error: MockedFn;
    };
};

describe("Shared Quest Utils", () => {
    let questRepository: QuestRepositoryMock;
    let dailyQuestRepository: DailyQuestRepositoryMock;
    let questHelpers: QuestHelpersMock;
    let dailyQuestHelpers: DailyQuestHelpersMock;
    let questController: QuestControllerMock;
    let logUtils: LogUtilsMock;

    beforeEach(async () => {
        vi.clearAllMocks();
        questRepository = (await import("../../repositories/quest.repository.js")) as unknown as QuestRepositoryMock;
        dailyQuestRepository = (await import(
            "../../repositories/dailyquest.repository.js"
        )) as unknown as DailyQuestRepositoryMock;
        questHelpers = (await import("../../features/quest/quest.helpers.js")) as unknown as QuestHelpersMock;
        dailyQuestHelpers = (await import(
            "../../features/dailyquest/dailyquest.helpers.js"
        )) as unknown as DailyQuestHelpersMock;
        questController = (await import("../../features/quest/quest.controller.js")) as unknown as QuestControllerMock;
        logUtils = (await import("../../utils/log.js")) as unknown as LogUtilsMock;
    });

    describe("handleQuestObjective", () => {
        it("delegates to regular and daily handlers", async () => {
            const criteria = { objectiveType: QuestObjectiveTypes.COMPLETE_MISSIONS };
            const regularProgress = { id: 1 };
            const dailyProgress = { id: 2 };
            questRepository.findUserQuestObjectiveProgress.mockResolvedValueOnce(regularProgress);
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValueOnce(dailyProgress);

            await SharedQuestUtils.handleQuestObjective(42, criteria, 3);

            expect(questHelpers.updateQuestObjectiveCount).toHaveBeenCalledWith(regularProgress, 3);
            expect(dailyQuestHelpers.updateDailyQuest).toHaveBeenCalledWith(dailyProgress, 3);
        });

        it("logs when an underlying handler throws", async () => {
            const error = new Error("Quest failure");
            const criteria = { objectiveType: QuestObjectiveTypes.TRAIN_STATS };
            questRepository.findUserQuestObjectiveProgress.mockResolvedValueOnce({ id: 99 });
            questHelpers.updateQuestObjectiveCount.mockRejectedValueOnce(error);

            await SharedQuestUtils.handleQuestObjective(7, criteria);

            expect(logUtils.LogErrorStack).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: expect.stringContaining("TRAIN_STATS"),
                    error,
                })
            );
            questHelpers.updateQuestObjectiveCount.mockReset();
            logUtils.LogErrorStack.mockReset();
        });
    });

    describe("handleRegularQuestObjective", () => {
        it("updates progress when objective exists", async () => {
            const progress = { id: 1 };
            questRepository.findUserQuestObjectiveProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleRegularQuestObjective(
                10,
                { objectiveType: QuestObjectiveTypes.DONATE_TO_SHRINE },
                5
            );

            expect(questHelpers.updateQuestObjectiveCount).toHaveBeenCalledWith(progress, 5);
        });

        it("does nothing when progress is missing", async () => {
            questRepository.findUserQuestObjectiveProgress.mockResolvedValue(undefined);

            await SharedQuestUtils.handleRegularQuestObjective(
                10,
                { objectiveType: QuestObjectiveTypes.DONATE_TO_SHRINE },
                5
            );

            expect(questHelpers.updateQuestObjectiveCount).not.toHaveBeenCalled();
        });
    });

    describe("handleDailyQuestObjective", () => {
        it("updates progress when objective exists", async () => {
            const progress = { id: 1 };
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleDailyQuestObjective(
                10,
                {
                    objectiveType: QuestObjectiveTypes.GAMBLING_SLOTS,
                    target: { lte: 10 },
                    targetAction: "slots",
                },
                2
            );

            expect(dailyQuestHelpers.updateDailyQuest).toHaveBeenCalledWith(progress, 2);
            expect(dailyQuestRepository.findDailyQuestProgress).toHaveBeenCalledWith(
                10,
                QuestObjectiveTypes.GAMBLING_SLOTS,
                expect.anything(),
                null,
                "slots"
            );
        });

        it("does nothing when daily quest progress is missing", async () => {
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValue(undefined);

            await SharedQuestUtils.handleDailyQuestObjective(
                10,
                {
                    objectiveType: QuestObjectiveTypes.GAMBLING_SLOTS,
                },
                2
            );

            expect(dailyQuestHelpers.updateDailyQuest).not.toHaveBeenCalled();
        });
    });

    describe("objective handlers", () => {
        it("handles defeat NPC for specific and any targets", async () => {
            await SharedQuestUtils.handleDefeatNPC(1, 2, LocationTypes.shibuya);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(2);

            await SharedQuestUtils.handleDefeatNPC(1, null, LocationTypes.shibuya);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(3);
        });

        it("handles defeating NPC within turns", async () => {
            await SharedQuestUtils.handleDefeatNPCInTurns(1, 4);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.DEFEAT_NPC_IN_TURNS,
                target: 4,
            });
        });

        it("handles low damage NPC defeats", async () => {
            await SharedQuestUtils.handleDefeatNPCWithLowDamage(1, 30);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.DEFEAT_NPC_WITH_LOW_DAMAGE,
                target: { gte: 30 },
            });

            await SharedQuestUtils.handleDefeatNPCWithLowDamage(1, null);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(1);
        });

        it("handles PvP post battle choices", async () => {
            await SharedQuestUtils.handlePvPPostBattleChoice(1, 2, QuestTargetAction.mug);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.PVP_POST_BATTLE_CHOICE,
                targetAction: QuestTargetAction.mug,
            });
        });

        it("handles PvP kills including guard for missing level", async () => {
            await SharedQuestUtils.handlePvPKill(1, 15);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER,
                target: { lte: 15 },
            });

            questRepository.findUserQuestObjectiveProgress.mockClear();
            await SharedQuestUtils.handlePvPKill(1, null);
            expect(questRepository.findUserQuestObjectiveProgress).not.toHaveBeenCalled();
        });

        it("handles bounty placement amounts", async () => {
            await SharedQuestUtils.handleBountyPlaced(1, 5000);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.PLACE_BOUNTY,
                target: { lte: 5000 },
            });
        });

        it("handles crafting specific and any items", async () => {
            await SharedQuestUtils.handleCraftItem(1, 12, 3);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(2);

            questRepository.findUserQuestObjectiveProgress.mockClear();
            await SharedQuestUtils.handleCraftItem(1, null, 3);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(1);
        });

        it("handles suggestion votes", async () => {
            await SharedQuestUtils.handleSuggestionVote(1, 99, 1);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.VOTE_ON_SUGGESTION,
            });
        });

        it("handles character encounters", async () => {
            await SharedQuestUtils.handleCharacterEncounter(1, 3, LocationTypes.shinjuku, 1);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.CHARACTER_ENCOUNTERS,
                location: LocationTypes.shinjuku,
            });
        });

        it("handles mission completion for quests and dailies", async () => {
            await SharedQuestUtils.handleMissionComplete(1);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.COMPLETE_MISSIONS,
            });
            expect(dailyQuestRepository.findDailyQuestProgress).toHaveBeenCalledTimes(1);
        });

        it("handles shrine donations and stat training", async () => {
            await SharedQuestUtils.handleShrineDonation(1, 200);
            await SharedQuestUtils.handleStatsTraining(1, 15);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenNthCalledWith(1, 1, {
                objectiveType: QuestObjectiveTypes.DONATE_TO_SHRINE,
            });
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenNthCalledWith(2, 1, {
                objectiveType: QuestObjectiveTypes.TRAIN_STATS,
            });
        });

        it("handles gambling slots only when amount is provided", async () => {
            await SharedQuestUtils.handleGambling(1, "slots", 1000);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.GAMBLING_SLOTS,
            });

            questRepository.findUserQuestObjectiveProgress.mockClear();
            await SharedQuestUtils.handleGambling(1, "roulette", 1000);
            expect(questRepository.findUserQuestObjectiveProgress).not.toHaveBeenCalled();
        });

        it("handles fetching items", async () => {
            await SharedQuestUtils.handleFetchItem(1, 33, 2);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.ACQUIRE_ITEM,
                target: 33,
            });
        });

        it("handles resource gathering variants", async () => {
            await SharedQuestUtils.handleResourceGathering(1, 101, "mining", 2);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledTimes(3);
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: 101,
                targetAction: "mining",
            });
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: null,
                targetAction: "mining",
            });
            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: 101,
                targetAction: null,
            });
        });

        it("handles completing story episodes", async () => {
            await SharedQuestUtils.handleCompleteStoryEpisode(1, ExploreNodeLocation.shinjuku);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, {
                objectiveType: QuestObjectiveTypes.COMPLETE_STORY_EPISODE,
                location: ExploreNodeLocation.shinjuku,
            });
        });

        it("handles daily-only objectives", async () => {
            const progress = { id: 10 };
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleDefeatBoss(1);
            await SharedQuestUtils.handleWinBattle(1);
            await SharedQuestUtils.handleBountyCollection(1);
            await SharedQuestUtils.handleDefeatSpecificPlayer(1, 77);

            expect(dailyQuestRepository.findDailyQuestProgress).toHaveBeenNthCalledWith(
                1,
                1,
                QuestObjectiveTypes.DEFEAT_BOSS,
                expect.anything(),
                undefined,
                null
            );
            expect(dailyQuestRepository.findDailyQuestProgress).toHaveBeenNthCalledWith(
                4,
                1,
                QuestObjectiveTypes.DEFEAT_SPECIFIC_PLAYER,
                expect.anything(),
                77,
                null
            );
            expect(dailyQuestHelpers.updateDailyQuest).toHaveBeenCalledTimes(4);
        });
    });

    describe("handleDefeatPlayerByUsername", () => {
        it("completes progress when username contains required letter", async () => {
            const progress = { targetAction: "x" };
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleDefeatPlayerByUsername(1, "Xeno");

            expect(dailyQuestHelpers.updateDailyQuest).toHaveBeenCalledWith(progress, 1);
        });

        it("does nothing when username does not match", async () => {
            const progress = { targetAction: "z" };
            dailyQuestRepository.findDailyQuestProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleDefeatPlayerByUsername(1, "Xeno");

            expect(dailyQuestHelpers.updateDailyQuest).not.toHaveBeenCalled();
        });
    });

    describe("handleSpecificObjective", () => {
        it("completes a specific objective when found", async () => {
            const progress = [{ id: 3 }];
            questRepository.findUserQuestObjectiveProgress.mockResolvedValue(progress);

            await SharedQuestUtils.handleSpecificObjective(1, 3);

            expect(questRepository.findUserQuestObjectiveProgress).toHaveBeenCalledWith(1, { id: 3 });
            expect(questHelpers.updateQuestObjectiveCount).toHaveBeenCalledWith(progress[0], 1);
        });

        it("avoids completion when objective progress is missing", async () => {
            questRepository.findUserQuestObjectiveProgress.mockResolvedValue([]);

            await SharedQuestUtils.handleSpecificObjective(1, 3);

            expect(questHelpers.updateQuestObjectiveCount).not.toHaveBeenCalled();
            expect(logUtils.logger.warn).toHaveBeenCalledWith(expect.stringContaining("No objective progress found"));
        });
    });

    describe("completeStoryQuestObjective", () => {
        it("handles story objectives and triggers auto completion", async () => {
            questRepository.getQuestObjectiveById.mockResolvedValue({
                objectiveType: QuestObjectiveTypes.COMPLETE_STORY_EPISODE,
                questId: 5,
                location: ExploreNodeLocation.shibuya,
            });
            questRepository.findUserQuestObjectiveProgress.mockResolvedValue([{ id: 12 }]);
            questRepository.getQuestById.mockResolvedValue({ id: 5, isStoryQuest: true, name: "Story Quest" });
            questRepository.getUserQuestProgress.mockResolvedValue({
                questStatus: QuestProgressStatus.ready_to_complete,
            });
            questController.CompleteQuest.mockResolvedValue({ data: { success: true } });

            await SharedQuestUtils.completeStoryQuestObjective(1, 12, 99);

            expect(questHelpers.updateQuestObjectiveCount).toHaveBeenCalledWith({ id: 12 }, 1);
            expect(questController.CompleteQuest).toHaveBeenCalledWith(1, 5);
        });

        it("skips when objective is not a story episode", async () => {
            questRepository.getQuestObjectiveById.mockResolvedValue({
                objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
            });
            const specificSpy = vi.spyOn(SharedQuestUtils, "handleSpecificObjective");

            await SharedQuestUtils.completeStoryQuestObjective(1, 12, 99);

            expect(specificSpy).not.toHaveBeenCalled();

            specificSpy.mockRestore();
        });
    });

    describe("autoCompleteStoryQuest", () => {
        it("completes story quests that are ready", async () => {
            questRepository.getQuestById.mockResolvedValue({ id: 5, isStoryQuest: true, name: "Story Quest" });
            questRepository.getUserQuestProgress.mockResolvedValue({
                questStatus: QuestProgressStatus.ready_to_complete,
            });
            questController.CompleteQuest.mockResolvedValue({ data: { rewards: true } });

            await SharedQuestUtils.autoCompleteStoryQuest(5, 1, 99);

            expect(questController.CompleteQuest).toHaveBeenCalledWith(1, 5);
        });

        it("avoids completion when quest is not story or not ready", async () => {
            questRepository.getQuestById.mockResolvedValue({ id: 6, isStoryQuest: false });

            await SharedQuestUtils.autoCompleteStoryQuest(6, 1, 99);
            expect(questController.CompleteQuest).not.toHaveBeenCalled();

            questRepository.getQuestById.mockResolvedValue({ id: 7, isStoryQuest: true });
            questRepository.getUserQuestProgress.mockResolvedValue({ questStatus: QuestProgressStatus.in_progress });

            await SharedQuestUtils.autoCompleteStoryQuest(7, 1, 99);
            expect(questController.CompleteQuest).not.toHaveBeenCalled();
        });
    });
});
