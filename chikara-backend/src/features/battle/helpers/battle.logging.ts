import type { BattleState, BattlePlayer, CombatLogEntry } from "../types/battle.types.js";

/**
 * Interface for battle details used in combat log
 */
interface BattleDetails {
    damage?: number;
    healing?: number;
    attackType?: string;
    effects?: string[];
    attackerHealth: number;
    targetHealth: number;
}

/**
 * Union of common combat log actions – extend as needed.
 */
export enum CombatLogAction {
    ATTACK = "attack",
    RANGED = "ranged",
    FLEE_SUCCESS = "flee_success",
    FLEE_FAILED = "flee_failed",
    BATTLE_WIN = "battle_win",
}

/**
 * Adds a combat log entry to the battle state
 */
export const addCombatLogEntry = (
    action: string,
    battleState: BattleState,
    actorId: string,
    targetId: string,
    details?: BattleDetails
): BattleState => {
    const logEntry: CombatLogEntry = {
        id:
            (Array.isArray(battleState.combatLog) ? battleState.combatLog : []).reduce((max, entry) => {
                const id = typeof entry?.id === "number" ? entry.id : 0;
                return Math.max(id, max);
            }, 0) + 1, // Unique sequential ID
        timestamp: Date.now(),
        round: battleState.currentRound,
        actorId,
        targetId,
        action,
        damage: details?.damage,
        healing: details?.healing,
        details: {
            attackType: details?.attackType,
            statusEffects: details?.effects,
        },
        remainingHealth: {
            actor: details?.attackerHealth ?? null,
            target: details?.targetHealth ?? null,
        },
    };

    // Insert the entry in chronological order
    const insertIndex = battleState.combatLog.findIndex((entry) => entry.timestamp > logEntry.timestamp);
    if (insertIndex === -1) {
        battleState.combatLog.push(logEntry);
    } else {
        battleState.combatLog.splice(insertIndex, 0, logEntry);
    }

    return battleState;
};

export const logActionResult = (
    battleState: BattleState,
    actor: BattlePlayer,
    target: BattlePlayer,
    result: {
        actionType: string;
        damage: number;
        lifeSteal?: number;
        bleedAmount?: number;
    }
) => {
    const details: BattleDetails = {
        damage: result.damage,
        attackType: result.actionType,
        effects: [
            ...(result.lifeSteal ? [`Lifesteal: ${result.lifeSteal}`] : []),
            ...(result.bleedAmount ? [`Bleed: ${result.bleedAmount}`] : []),
        ],
        attackerHealth: actor.currentHealth,
        targetHealth: target.currentHealth,
    };

    addCombatLogEntry(result.actionType, battleState, actor.id, target.id, details);
};
