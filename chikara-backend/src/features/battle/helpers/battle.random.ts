import { randomUUID } from "node:crypto";

import type { BattleSeedState } from "../types/battle.types.js";

const DEFAULT_SEED_FALLBACK = "battle_rng_seed";
const DEFAULT_STATE_FALLBACK = 0x1a2b3c4d;

const hashSeed = (seed: string): number => {
    let hash = 1779033703 ^ seed.length;

    for (let index = 0; index < seed.length; index++) {
        hash = Math.imul(hash ^ seed.charCodeAt(index), 3432918353);
        hash = (hash << 13) | (hash >>> 19);
    }

    hash = Math.imul(hash ^ (hash >>> 16), 2246822507);
    hash ^= hash >>> 13;
    hash = Math.imul(hash ^ (hash >>> 16), 3266489909);
    hash ^= hash >>> 16;

    const sanitized = hash >>> 0;
    return sanitized === 0 ? DEFAULT_STATE_FALLBACK : sanitized;
};

export const createBattleSeedState = (seed?: string): BattleSeedState => {
    const resolvedSeed = seed ?? randomUUID?.() ?? DEFAULT_SEED_FALLBACK;

    return {
        seed: resolvedSeed,
        state: hashSeed(resolvedSeed),
        calls: 0,
    } satisfies BattleSeedState;
};

export const ensureBattleSeedState = (
    battleSeed: BattleSeedState | undefined,
    seed?: string,
): BattleSeedState => {
    if (battleSeed && Number.isFinite(battleSeed.state)) {
        return battleSeed;
    }

    return createBattleSeedState(seed);
};

const nextRandom = (battleSeed: BattleSeedState): number => {
    battleSeed.state = (battleSeed.state + 0x6d2b79f5) | 0;
    let temp = Math.imul(battleSeed.state ^ (battleSeed.state >>> 15), 1 | battleSeed.state);
    temp ^= temp + Math.imul(temp ^ (temp >>> 7), 61 | temp);
    battleSeed.calls += 1;
    return ((temp ^ (temp >>> 14)) >>> 0) / 4294967296;
};

export const randomFloat = (battleSeed: BattleSeedState, min = 0, max = 1): number => {
    if (max <= min) {
        return min;
    }

    return min + nextRandom(battleSeed) * (max - min);
};

export const randomInt = (battleSeed: BattleSeedState, min: number, max: number): number => {
    if (max <= min) {
        return Math.floor(min);
    }

    return Math.floor(nextRandom(battleSeed) * (max - min + 1)) + min;
};

export const randomChance = (battleSeed: BattleSeedState, probability: number): boolean => {
    if (probability <= 0) {
        return false;
    }

    if (probability >= 1) {
        return true;
    }

    return nextRandom(battleSeed) < probability;
};

export const randomItem = <T>(battleSeed: BattleSeedState, items: readonly T[]): T => {
    if (items.length === 0) {
        throw new Error("Cannot pick a random item from an empty list");
    }

    const index = randomInt(battleSeed, 0, items.length - 1);
    return items[index];
};

export const randomSign = (battleSeed: BattleSeedState): -1 | 1 =>
    nextRandom(battleSeed) < 0.5 ? -1 : 1;

