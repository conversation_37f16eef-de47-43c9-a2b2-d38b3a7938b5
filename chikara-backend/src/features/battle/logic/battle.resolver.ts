import * as AchievementHelpers from "../../../core/achievement.service.js";
import * as FocusService from "../../../core/focus.service.js";
import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as UserService from "../../../core/user.service.js";
import { emitNPCBattleWon, emitPVPBattleWon } from "../../../core/events/index.js";
import { BATTLE_STATE } from "../helpers/battle.constants.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import { addCombatLogEntry } from "../helpers/battle.logging.js";
import * as BattleRepository from "../../../repositories/battle.repository.js";
import * as <PERSON>unty<PERSON>elper from "../../bounty/bounty.helpers.js";
import * as <PERSON><PERSON><PERSON>per from "../../gang/gang.helpers.js";
import * as UniqueItemHelpers from "../../item/uniqueitem.helpers.js";
import * as LootService from "../../../core/loot.service.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import { ItemModel, ExtUserModel } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { LogErrorStack, logger } from "../../../utils/log.js";
import { Prisma, LocationTypes } from "@prisma/client";
import type { BattlePlayer, BattleState, RooftopNPCUser, BattleSeedState } from "../types/battle.types.js";
import {
    cleanupBattleState,
    getActiveBattleForUser,
    updateBattleState,
    sanitizeBattleStateForFrontend,
} from "../battle.state.js";
import gameConfig from "../../../config/gameConfig.js";
import { rooftopNpcs } from "../../../data/uniqueNpcs.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as ItemRepository from "../../../repositories/item.repository.js";
import { ensureBattleSeedState, randomChance } from "../helpers/battle.random.js";

const POST_BATTLE_OPTIONS = { MUG: "mug", CRIPPLE: "cripple", LEAVE: "leave" };

const handleNPCBattleWin = async (
    currentUser: ExtUserModel,
    attacker: BattlePlayer,
    target: BattlePlayer,
    rewardInfo: { droppedItem?: ItemModel; droppedCrate?: ItemModel; xpReward?: number },
    battleState: BattleState
) => {
    const droppedItem = await LootService.generateNPCItemDrop(currentUser, target.isBoss);
    if (droppedItem) {
        rewardInfo.droppedItem = droppedItem;
    }

    const droppedCrateItem = await LootService.generateCrateDrop(currentUser);
    if (droppedCrateItem) {
        rewardInfo.droppedCrate = droppedCrateItem;
    }

    const xpModifier = BattleHelpers.xpPenaltyModifier(currentUser, target);

    const xpType = target.isBoss ? gameConfig.BOSS_KILL_XP : gameConfig.NPC_KILL_XP;

    rewardInfo.xpReward = await UserService.AddXPToUser(currentUser, xpType * xpModifier);

    // Award focus for winning the battle
    await FocusService.addBattleWinFocus(currentUser.id);

    // Apply rejuvenation talent health change
    const rejuvenationTalent = await TalentHelper.UserHasRejuvenationTalent(currentUser.id);
    if (rejuvenationTalent) {
        currentUser.currentHealth = Math.min(
            currentUser.currentHealth + currentUser.health * (rejuvenationTalent.modifier ?? 0),
            currentUser.health
        );
    }

    // Prepare update data for health and potentially highscore
    const updateData: Prisma.userUpdateInput = { currentHealth: currentUser.currentHealth };

    // Update roguelike highscore if applicable
    if (target.isBoss && currentUser.roguelikeLevel === currentUser.roguelikeHighscore) {
        currentUser.roguelikeHighscore++;
        updateData.roguelikeHighscore = { increment: 1 };
        await AchievementHelpers.UpdateUserAchievement(currentUser.id, "roguelikeMapsCompleted");
    }

    const location =
        battleState.battleType === "pve-explore" ? currentUser.currentMapLocation : currentUser.roguelikeMap?.location;

    await emitNPCBattleWon({
        userId: currentUser.id,
        creatureId: Number.parseInt(target.id.replace("npc_", "")),
        location: location as LocationTypes,
        turns: battleState.currentRound,
        damageTaken: attacker.damageTaken,
        percentDmgTaken:
            currentUser.health && currentUser.health > 0
                ? Math.round((attacker.damageTaken / currentUser.health) * 100)
                : 0,
        isBoss: target.isBoss,
    });

    // Save the health and highscore changes (XP/level already saved by AddXPToUser)
    await UserService.updateUser(currentUser.id, updateData);
};

/**
 * Processes rewards for a player after winning a rooftop battle
 */
export const handleRooftopBattleWin = async (
    currentUser: ExtUserModel,
    target: RooftopNPCUser,
    rewardInfo: { droppedItem?: ItemModel; droppedCrate?: ItemModel; xpReward?: number }
) => {
    const updateData: Prisma.userUpdateInput = {};
    const defeatedNpcs = currentUser.defeatedNpcs || [];

    if (!defeatedNpcs.includes(target.id)) {
        defeatedNpcs.push(target.id);
        updateData.defeatedNpcs = defeatedNpcs;
    }

    const rejuvenationTalent = await TalentHelper.UserHasRejuvenationTalent(currentUser.id);
    if (rejuvenationTalent) {
        const newHealth = Math.min(
            (currentUser.currentHealth ?? 0) + (currentUser.health ?? 0) * (rejuvenationTalent.modifier ?? 0),
            currentUser.health ?? 0
        );
        updateData.currentHealth = newHealth;
        currentUser.currentHealth = newHealth; // Update in-memory object for subsequent calls if needed
    } else {
        // Always save the attacker's health from battle state, regardless of talents
        updateData.currentHealth = currentUser.currentHealth;
    }

    // no post-battle actions for pve battles
    await AchievementHelpers.UpdateUserAchievement(currentUser.id, "npcBattleWins");

    if (Object.keys(updateData).length > 0) {
        await UserService.updateUser(currentUser.id, updateData);
    }

    if (target.itemRewardId && target.itemRewardId !== 0) {
        const userItem = await InventoryService.AddItemToUser({
            userId: currentUser.id,
            itemId: target.itemRewardId,
            amount: target.itemRewardQuantity,
            isTradeable: true,
        });

        // TODO: Get item details from AddItemToUser result instead of retrieving from database
        if (userItem?.itemId) {
            const itemDrop = await ItemRepository.findItemById(userItem.itemId);
            if (itemDrop) {
                rewardInfo.droppedItem = itemDrop;
            }
        }
    }

    await BattleHelpers.rooftopNPCDefeatedAnnouncement(currentUser, target);
};

export const handleVictoryNPC = async (
    battleState: BattleState,
    attackerState: BattlePlayer,
    targetState: BattlePlayer,
    rewardInfo: { droppedItem?: ItemModel; droppedCrate?: ItemModel; xpReward?: number }
) => {
    const currentUser = await UserRepository.getUserById(Number.parseInt(attackerState.id));

    if (!currentUser) {
        LogErrorStack({
            message: `User not found for battleState: ${battleState.id}`,
            error: new Error(`User not found for battleState: ${battleState.id}`),
        });
        throw new Error("User not found");
    }

    currentUser.currentHealth = attackerState.currentHealth;

    if (battleState.battleType === "pve-rooftop") {
        const npcID = Number.parseInt(targetState.id.replace("npc_", ""));
        const target = rooftopNpcs.find((npc) => npc.id === npcID);
        if (!target) {
            LogErrorStack({
                error: new Error(`Rooftop NPC not found for ID: ${npcID}`),
            });
            throw new Error("Rooftop NPC not found");
        }
        await handleRooftopBattleWin(currentUser, target, rewardInfo);
    } else {
        await handleNPCBattleWin(currentUser, attackerState, targetState, rewardInfo, battleState);
    }
    logAction({
        action: "BATTLE_PVE_WIN",
        userId: currentUser.id,
        info: {
            battleId: battleState.id,
            battleType: battleState.battleType,
            targetId: targetState.id,
            targetName: targetState.username,
            aggressorHealth: currentUser.currentHealth,
            isBoss: targetState.isBoss,
            ...rewardInfo,
        },
    });
};

export const handleSuccessfulFlee = async (
    battleState: BattleState,
    userId: string,
    playerState: BattlePlayer,
    targetState: BattlePlayer,
    currentUser: ExtUserModel
) => {
    battleState.state = BATTLE_STATE.FINISHED;

    // Add successful flee log entry
    addCombatLogEntry("flee_success", battleState, userId, targetState.id);

    logAction({
        action: "BATTLE_FLEE_SUCCESS",
        userId: currentUser.id,
        info: {
            battleId: battleState.id,
            battleType: battleState.battleType,
            targetId: targetState.id,
            targetName: targetState.username,
            aggressorHealth: playerState.currentHealth,
            targetHealth: targetState.currentHealth,
            isBoss: targetState.isBoss,
        },
    });

    // Prepare update data for the current user
    const updateData: Prisma.userUpdateInput = { currentHealth: playerState.currentHealth };

    // Handle roguelike map if applicable
    if (battleState.battleType === "pve" && targetState.userType === "npc") {
        updateData.roguelikeMap = null as unknown as Prisma.JsonNullValueInput;
    }

    // Save current user's health and potentially map status
    await UserService.updateUser(currentUser.id, updateData);

    // Update target's health if it's a player
    if (targetState.userType === "player") {
        const targetUser = await UserRepository.getUserById(Number.parseInt(targetState.id));
        if (targetUser && targetUser.currentHealth !== targetState.currentHealth) {
            await UserService.updateUser(targetUser.id, { currentHealth: targetState.currentHealth });
        }

        NotificationService.NotifyUser(Number.parseInt(targetState.id), NotificationTypes.fight_win_attacked, {
            attacked: Number.parseInt(userId),
            result: "flee",
        });
    }

    await updateBattleState(battleState.id, battleState);

    // Sanitize battle state for frontend by removing sensitive player data
    const sanitizedBattleState = sanitizeBattleStateForFrontend(battleState);

    return {
        data: {
            battleState: sanitizedBattleState,
            flee: "success",
        },
    };
};

/**
 * Centralized function to handle player defeat in battle
 * @param battleState - The current battle state
 * @param playerState - The defeated player's battle state
 * @param targetState - The target/opponent's battle state
 * @param currentUser - The defeated user's database record
 * @param context - The context of the defeat ("normal_loss" or "failed_flee")
 * @param shouldCleanupBattle - Whether to cleanup the battle state after processing
 */
export const handlePlayerDefeat = async (
    battleState: BattleState,
    playerState: BattlePlayer,
    targetState: BattlePlayer,
    currentUser: ExtUserModel,
    context: "normal_loss" | "failed_flee",
    shouldCleanupBattle = false,
    battleSeed: BattleSeedState
) => {
    // Set battle to finished
    battleState.state = BATTLE_STATE.FINISHED;

    // Handle different target types
    if (targetState.userType === "player") {
        // PvP loss - hospitalize the defeated player
        await BattleHelpers.hospitaliseUser(currentUser, targetState, "pvp_loss", battleSeed);

        // Notify the winning player
        NotificationService.NotifyUser(Number.parseInt(targetState.id), NotificationTypes.fight_win_attacked, {
            attacked: Number.parseInt(playerState.id),
            result: "hospitalised",
        });

        // Record the battle outcome
        await BattleRepository.recordBattleOutcome(playerState.id, targetState.id, false);
    } else {
        // NPC loss - handle roguelike map clearing and hospitalization
        if (targetState.userType === "npc") {
            // Clear roguelike map for regular NPC defeats
            const updateData: Prisma.userUpdateInput = {
                roguelikeMap: null as unknown as Prisma.JsonNullValueInput,
            };
            await UserService.updateUser(currentUser.id, updateData);
        }

        // Hospitalize the defeated player
        await BattleHelpers.hospitaliseUser(currentUser, targetState, "npc_loss", battleSeed);
    }

    // Log the appropriate action based on context
    const actionType = context === "failed_flee" ? "BATTLE_LOSS_FLEE" : "BATTLE_LOSS_ATTACK";
    logAction({
        action: actionType,
        userId: currentUser.id,
        info: {
            battleId: battleState.id,
            battleType: battleState.battleType,
            targetId: targetState.id,
            targetName: targetState.username,
            targetHealth: targetState.currentHealth,
            isBoss: targetState.isBoss,
        },
    });

    // Cleanup battle state if requested
    if (shouldCleanupBattle) {
        await cleanupBattleState(battleState);
    }
};

export const processVictoryAction = async (userId: number, action: "mug" | "cripple" | "leave") => {
    logger.profile("processVictoryAction");
    try {
        const userIdStr = userId.toString();
        const battleState = await getActiveBattleForUser(userIdStr);
        if (!battleState) {
            return { error: "No active battle found" };
        }

        battleState.battleSeed = ensureBattleSeedState(battleState.battleSeed);
        const battleSeed = battleState.battleSeed;

        if (!action) {
            return { error: "No action chosen" };
        }

        const attackerState = Object.values(battleState.players as Record<string, BattlePlayer>).find(
            (player) => player.id === userIdStr
        );
        const targetState = Object.values(battleState.players as Record<string, BattlePlayer>).find(
            (player) => player.id !== userIdStr
        );

        if (!attackerState || !targetState) {
            LogErrorStack({
                message: `Attacker or target state not found for userId: ${userId}`,
                error: new Error(`Attacker or target state not found for userId: ${userId}`),
            });
            return { error: "Attacker or target state not found" };
        }

        if (targetState.userType !== "player") {
            return { error: "Target is an NPC" };
        }

        if (battleState.state !== BATTLE_STATE.FINISHED || targetState.currentHealth > 0) {
            return { error: "Battle is not over" };
        }

        const currentUser = await UserRepository.getUserById(Number.parseInt(userIdStr));
        const target = await UserRepository.getUserById(Number.parseInt(targetState.id));

        if (!currentUser || !target) {
            return { error: "User not found" };
        }

        currentUser.currentHealth = attackerState.currentHealth;

        // Calculate jail chance
        const [cunningRatTalent, escapeArtistTalent] = await Promise.all([
            TalentHelper.UserHasCunningRatTalent(target.id),
            TalentHelper.UserHasEscapeArtistTalent(target.id),
        ]);
        let jailChance = cunningRatTalent
            ? gameConfig.BASE_JAIL_CHANCE * (cunningRatTalent.modifier ?? 1)
            : gameConfig.BASE_JAIL_CHANCE;

        if (escapeArtistTalent) {
            jailChance *= escapeArtistTalent.modifier ?? 1;
        }

        // Can't get jailed if claiming a bounty
        const bounty = await BattleRepository.findBountyByTargetId(target.id);

        if (bounty && bounty.placerId !== currentUser.id) {
            jailChance = 0;
        }

        const jail = randomChance(battleSeed, jailChance);
        let mugAmount = 0;
        let attackerId = currentUser.id;
        const isAnonymous = await UniqueItemHelpers.IsAnonymousItemEquipped(currentUser);

        if (isAnonymous) {
            attackerId = 0;
        }

        const xpModifier = BattleHelpers.xpPenaltyModifier(currentUser, targetState);

        // 10% penalty per level, capped at -25%. No yen bonus for killing higher levels
        const MAX_CASH_MODIFIER = 1;
        const MIN_CASH_MODIFIER = 0.75;

        const cashModifier = Math.min(
            Math.max(1 - (currentUser.level - target.level) * 0.1, MIN_CASH_MODIFIER),
            MAX_CASH_MODIFIER
        );

        let xpReward = 0;
        const shrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail", currentUser.id)) || 1;
        const currentUserUpdateData: Prisma.userUpdateInput = {};
        const targetUpdateData: Prisma.userUpdateInput = {};

        switch (action) {
            case POST_BATTLE_OPTIONS.MUG: {
                mugAmount = target.cash * gameConfig.BASE_MUG_AMOUNT * cashModifier;
                const muggerTalent = await TalentHelper.UserHasMuggerTalent(currentUser.id);

                if (muggerTalent) {
                    mugAmount *= muggerTalent.modifier ?? 1;
                }

                mugAmount = Math.ceil(mugAmount);
                targetUpdateData.cash = { decrement: mugAmount };
                await AchievementHelpers.UpdateUserAchievement(target.id, "totalMuggingLoss", mugAmount);
                currentUserUpdateData.cash = { increment: mugAmount };
                await AchievementHelpers.UpdateUserAchievement(currentUser.id, "totalMuggingGain", mugAmount);

                if (jail) {
                    const jailDuration = gameConfig.JAIL_DURATION_MS * shrineBuffActive;
                    const jailReason = "Assault";
                    await UserService.JailUser(currentUser.id, jailDuration, jailReason, { attacked: target.id });
                }

                xpReward = await UserService.AddXPToUser(currentUser, gameConfig.MUG_XP * xpModifier);
                break;
            }

            case POST_BATTLE_OPTIONS.CRIPPLE: {
                if (jail) {
                    const jailDuration = gameConfig.JAIL_DURATION_MS * shrineBuffActive;
                    const jailReason = "Assault";
                    await UserService.JailUser(currentUser.id, jailDuration, jailReason, { attacked: target.id });
                }

                xpReward = await UserService.AddXPToUser(currentUser, gameConfig.CRIPPLE_XP * xpModifier);
                break;
            }

            case POST_BATTLE_OPTIONS.LEAVE: {
                xpReward = await UserService.AddXPToUser(currentUser, gameConfig.LEAVE_XP * xpModifier);
                break;
            }

            default: {
                return { error: "Invalid choice" };
            }
        }

        const rejuvenationTalent = await TalentHelper.UserHasRejuvenationTalent(currentUser.id);
        // use prisma increment
        if (rejuvenationTalent) {
            currentUser.currentHealth = Math.min(
                currentUser.currentHealth + currentUser.health * (rejuvenationTalent.modifier ?? 0),
                currentUser.health
            );
        }

        currentUserUpdateData.currentHealth = currentUser.currentHealth;

        // Save changes not handled by other functions (XP, Jail, Hospitalization are saved within their respective functions)
        if (Object.keys(currentUserUpdateData).length > 0) {
            await UserService.updateUser(currentUser.id, currentUserUpdateData);
        }
        if (Object.keys(targetUpdateData).length > 0) {
            await UserService.updateUser(target.id, targetUpdateData);
        }

        // Award focus for winning the battle
        await FocusService.addBattleWinFocus(currentUser.id);

        const injury = await BattleHelpers.hospitaliseUser(target, currentUser, action, battleSeed);

        // Emit PVP battle won event for quest and achievement handling
        await emitPVPBattleWon({
            userId: currentUser.id,
            targetId: target.id,
            targetLevel: target.level,
            targetUsername: target.username,
            postBattleAction: action,
        });

        NotificationService.NotifyUser(target.id, NotificationTypes.hospitalised, {
            attackerId,
            attackerName: isAnonymous ? "Anonymous" : currentUser.username,
            action,
            mugAmount,
            injury: injury ? injury.name : null,
            injuryTier: injury ? injury.tier : null,
        });

        NotificationService.NotifyUser(currentUser.id, NotificationTypes.fight_win, {
            defeated: target.id,
            mugAmount,
            action,
        });

        // Record battle outcome and create action log
        await BattleRepository.recordBattleOutcome(String(currentUser.id), String(target.id), true);

        logAction({
            action: "BATTLE_PVP_WIN",
            userId: currentUser.id,
            info: {
                battleId: battleState.id,
                targetId: targetState.id,
                targetName: targetState.username,
                aggressorHealth: currentUser.currentHealth,
                action,
                isAnonymous,
            },
        });

        // Handle bounties
        if (bounty && bounty.placerId !== currentUser.id && action === POST_BATTLE_OPTIONS.CRIPPLE) {
            await BountyHelper.CollectBounty({ bounty, currentUser, target });
        } else if (
            currentUser.level > gameConfig.BOUNTY_MIN_LEVEL &&
            randomChance(battleSeed, gameConfig.RANDOM_BOUNTY_CHANCE)
        ) {
            // if not claiming a bounty, roll to have a bounty placed on the attacker
            await BountyHelper.ApplyBounty(
                currentUser.id,
                currentUser.level * gameConfig.RANDOM_BOUNTY_AMOUNT,
                `Assault of ${target.username}`
            );
        }

        let essenceReward = null;
        let respectReward = null;
        let targetRespectChange = null;
        // Collect Life Essence/Respect when the user is in a gang
        if (currentUser.gangId && currentUser.gangId !== target.gangId) {
            const gangRewards = await GangHelper.CollectGangRewards(currentUser, target);
            essenceReward = gangRewards.essenceReward || null;
            respectReward = gangRewards.respectReward || null;
            targetRespectChange = gangRewards.targetRespectChange || null;
        }

        // Clean up battle state in Redis
        await cleanupBattleState(battleState);
        logger.profile("processVictoryAction");
        return {
            data: {
                mugAmount,
                xpReward,
                essenceReward,
                respectReward,
                targetRespectChange,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to process victory action" };
    }
};
