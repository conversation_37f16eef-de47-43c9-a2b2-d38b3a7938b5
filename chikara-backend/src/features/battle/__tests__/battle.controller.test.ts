import gameConfig from "../../../config/gameConfig.js";
import { redisClient } from "../../../config/redisClient.js";
import * as EquipmentService from "../../../core/equipment.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as BattleRepository from "../../../repositories/battle.repository.js";
import * as ItemRepository from "../../../repositories/item.repository.js";
import * as BattleController from "../battle.controller.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import * as BattleAction from "../helpers/battle.action.js";
import * as BattleState from "../battle.state.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { UserModel } from "../../../lib/db.js";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import * as BattleResolver from "../logic/battle.resolver.js";
import * as BattleRound from "../logic/battle.round.js";
import * as BattleInitiation from "../logic/battle.initiation.js";

// Mock the modules
vi.mock("@/features/battle/battle.state.js");
vi.mock("@/core/user.service.js");
vi.mock("@/repositories/user.repository.js");
vi.mock("@/repositories/battle.repository.js");
vi.mock("@/repositories/item.repository.js");
vi.mock("@/features/battle/helpers/battle.helpers.js");
vi.mock("@/features/battle/helpers/battle.action.js");
vi.mock("@/features/talents/talents.helpers.js");
vi.mock("@/core/statuseffect.service.js");
vi.mock("@/core/equipment.service.js");
vi.mock("@/core/notification.service.js");
vi.mock("@/features/battle/logic/battle.round.js");
vi.mock("@/features/battle/logic/battle.initiation.js");

// Mock battle abilities
vi.mock("@/features/battle/logic/battle.abilities.js", () => ({
    ABILITY_NAMES: {
        RAGE_ABILITY_NAME: "rage",
        CRIPPLE_ABILITY_NAME: "cripple",
        HEADBUTT_ABILITY_NAME: "headbutt",
        SHIELD_BASH_ABILITY_NAME: "shield_bash",
        SHOCKWAVE_ABILITY_NAME: "shockwave",
        EXHAUST_ABILITY_NAME: "exhaust",
        HEAL_ABILITY_NAME: "heal",
        HEAL_OVER_TIME_ABILITY_NAME: "heal_over_time",
        SLEEP_ABILITY_NAME: "sleep",
        SELF_HARM_ABILITY_NAME: "self_harm",
        MAX_HP_HEAL_ABILITY_NAME: "max_hp_heal",
        SAP_SLEEP_ABILITY_NAME: "sap_sleep",
        RELOAD_ABILITY_NAME: "reload",
        SPRAY_ABILITY_NAME: "spray",
        TOXIC_DART_ABILITY_NAME: "toxic_dart",
        DISARM_ABILITY_NAME: "disarm",
        GIANT_KILLING_SLINGSHOT_ABILITY_NAME: "giant_killing_slingshot",
        HIGH_GUARD_ABILITY_NAME: "high_guard",
    },
    STATUS_ABILITIES: [],
    STUN_EFFECTS: [],
    GetGiantKillingSlingshotDamage: vi.fn(),
    GetShieldBashDamage: vi.fn(),
    GetSprayDamage: vi.fn(),
    GetToxicDartDamage: vi.fn(),
}));

describe("Battle Controller Tests", () => {
    // Mock data
    const mockAttacker = {
        id: 1,
        username: "Attacker",
        level: 10,
        currentHealth: 100,
        health: 100,
        maxHealth: 100,
        strength: 50,
        defence: 40,
        dexterity: 30,
        intelligence: 20,
        stamina: 100,
        actionPoints: 10,
        userType: "player",
        roguelikeMap: null,
        gangId: null,
        cash: 1000,
    } as unknown as UserModel;

    const mockDefender = {
        id: 2,
        username: "Defender",
        level: 10,
        currentHealth: 100,
        health: 100,
        maxHealth: 100,
        strength: 40,
        defence: 50,
        dexterity: 25,
        intelligence: 25,
        stamina: 100,
        actionPoints: 10,
        userType: "player",
        roguelikeMap: null,
        gangId: null,
        cash: 1000,
    } as unknown as UserModel;

    const mockBattleState = {
        id: "battle_pvp_123456789_1_2",
        state: "in_progress" as const,
        battleType: "pvp" as const,
        startTime: Date.now(),
        validUntil: Date.now() + 300000, // 5 minutes
        currentRound: 1,
        aggressorId: "1",
        firstAttackerId: "1",
        combatLog: [],
        battleSeed: { seed: "test-seed", state: 1, calls: 0 },
        players: {
            "1": {
                id: "1",
                username: "Attacker",
                userType: "player" as const,
                avatar: "avatar1.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
            "2": {
                id: "2",
                username: "Defender",
                userType: "player" as const,
                avatar: "avatar2.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
        },
    };

    const mockNPCBattleState = {
        id: "battle_pve_123456789_1_npc_101",
        state: "in_progress" as const,
        battleType: "pve" as const,
        startTime: Date.now(),
        validUntil: Date.now() + 300000, // 5 minutes
        currentRound: 1,
        aggressorId: "1",
        firstAttackerId: "1",
        combatLog: [],
        rng: { seed: "test-seed", state: 1, calls: 0 },
        players: {
            "1": {
                id: "1",
                username: "Attacker",
                userType: "player" as const,
                avatar: "avatar1.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
            npc_101: {
                id: "npc_101",
                username: "Enemy NPC",
                userType: "npc" as const,
                avatar: "npc.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 50,
                maxStamina: 50,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                weaponDamage: 20,
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
        },
    };

    beforeEach(() => {
        vi.resetAllMocks();

        // Default mocks
        vi.mocked(UserRepository.getUserById).mockImplementation((id) => {
            if (id === mockAttacker.id) return Promise.resolve(mockAttacker);
            if (id === mockDefender.id) return Promise.resolve(mockDefender);
            return Promise.resolve(null);
        });

        vi.mocked(BattleHelpers.GetBeginBattleError).mockResolvedValue(null);
        vi.mocked(UserService.updateUser).mockResolvedValue(mockAttacker);
        vi.mocked(StatusEffectService.GetBattleStatusEffects).mockResolvedValue({});
        vi.mocked(EquipmentService.GetEquippedItems).mockResolvedValue({
            weapon: null,
            ranged: null,
            head: null,
            chest: null,
            hands: null,
            legs: null,
            feet: null,
            finger: null,
            offhand: null,
            shield: null,
        });
        vi.mocked(TalentHelper.GetEquippedAbilities).mockResolvedValue([]);
        vi.mocked(TalentHelper.UserHasRecoveryTalent).mockResolvedValue(null);
        vi.mocked(TalentHelper.UserHasCombatRegenerationTalent).mockResolvedValue(null);
        vi.mocked(TalentHelper.UserHasRejuvenationTalent).mockResolvedValue(null);
        vi.mocked(NotificationService.NotifyUser).mockImplementation(() => Promise.resolve());

        vi.mocked(BattleState.generateBattleId).mockReturnValue("battle_pvp_123456789_1_2");
        vi.mocked(BattleState.createBattleState).mockResolvedValue(mockBattleState);
        vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(mockBattleState);
        vi.mocked(BattleState.validateBattleState).mockResolvedValue({
            battleState: mockBattleState,
            playerState: mockBattleState.players["1"],
            targetState: mockBattleState.players["2"],
        });
        vi.mocked(BattleState.updateBattleState).mockImplementation(() => Promise.resolve("success"));
        vi.mocked(BattleState.cleanupBattleState).mockImplementation(() => Promise.resolve());
        vi.mocked(BattleState.sanitizeBattleStateForFrontend).mockImplementation((state) => ({ ...state }));

        // Mock initiateBattle for both PVP and rooftop battles
        vi.mocked(BattleInitiation.initiateBattle).mockResolvedValue({
            data: {
                battleId: "battle_pvp_123456789_1_2",
            },
        });

        // Mock battle repository
        vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(0);
    });

    afterEach(() => {
        vi.spyOn(globalThis.Math, "random").mockRestore();
    });

    describe("initiatePVPBattle", () => {
        it("should successfully initiate a PVP battle between two users", async () => {
            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
            expect(result.data!.battleId).toBe("battle_pvp_123456789_1_2");

            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockAttacker.id);
            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockDefender.id);
            expect(BattleInitiation.initiateBattle).toHaveBeenCalled();
        });

        it("should return an error if attacker or defender is not found", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(null);

            const result = await BattleController.initiatePVPBattle(999, mockDefender.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("User not found");
        });

        it("should return an error if battle validation fails", async () => {
            vi.mocked(BattleInitiation.initiateBattle).mockResolvedValueOnce({
                error: "Not enough action points",
            });

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Not enough action points");
        });
    });

    describe("processAttack", () => {
        it("should successfully process an attack action", async () => {
            // Mock the battle round processing
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 25, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
            expect(BattleState.validateBattleState).toHaveBeenCalledWith("1");
            expect(BattleRound.processBattleRound).toHaveBeenCalled();
            expect(BattleState.updateBattleState).toHaveBeenCalled();
        });

        it("should throw an error if battle state is not found", async () => {
            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                error: "Active battle not found",
            });

            await expect(BattleController.processAttack(mockAttacker.id, "melee")).rejects.toThrow(
                "Active battle not found"
            );
        });

        it("should throw an error if battle is already over", async () => {
            const finishedBattleState = {
                ...mockBattleState,
                state: "finished" as const,
            };

            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                battleState: finishedBattleState,
                playerState: finishedBattleState.players["1"],
                targetState: finishedBattleState.players["2"],
                error: "Battle is already over",
            });

            await expect(BattleController.processAttack(mockAttacker.id, "melee")).rejects.toThrow(
                "Battle is already over"
            );
        });

        it("should successfully process a flee action with success", async () => {
            // Mock a successful flee (low random value)
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.1);

            // Mock the battle round processing for flee
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 0, type: "flee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                flee: "success",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "flee");

            expect(result).toHaveProperty("data");
            expect(BattleRound.processBattleRound).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Object),
                expect.any(Object),
                "flee"
            );
        });

        it("should successfully process a flee action with failure", async () => {
            // Mock a failed flee (high random value)
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.9);

            // Mock the battle round processing for flee
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 0, type: "flee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                flee: "failed",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "flee");

            expect(result).toHaveProperty("data");
            expect(BattleRound.processBattleRound).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Object),
                expect.any(Object),
                "flee"
            );
        });
    });

    describe("processVictoryAction", () => {
        beforeEach(() => {
            // Setup for victory action tests - battle is finished and defender has 0 health
            const finishedBattleState = {
                ...mockBattleState,
                state: "finished" as const,
                players: {
                    "1": mockBattleState.players["1"],
                    "2": {
                        ...mockBattleState.players["2"],
                        currentHealth: 0,
                    },
                },
            };

            vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(finishedBattleState);

            // Mock UserService.AddXPToUser to return some XP
            vi.mocked(UserService.AddXPToUser).mockResolvedValue(100);

            // Mock the hospital function
            vi.spyOn(BattleHelpers, "hospitaliseUser").mockImplementation(() => Promise.resolve(null));
        });

        it("should successfully process a 'mug' victory action", async () => {
            // Mock a successful mug without jail
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.9); // High random value = no jail

            // Mock the victory action response
            const mockMugResponse = {
                data: {
                    mugAmount: 50,
                    xpReward: 100,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockMugResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "mug");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("mugAmount");
            expect(result.data).toHaveProperty("xpReward");
        });

        it("should successfully process a 'cripple' victory action", async () => {
            // Mock the victory action response
            const mockCrippleResponse = {
                data: {
                    xpReward: 150,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockCrippleResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "cripple");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("xpReward");
        });

        it("should successfully process a 'leave' victory action", async () => {
            // Mock the victory action response
            const mockLeaveResponse = {
                data: {
                    xpReward: 50,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockLeaveResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "leave");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("xpReward");
        });
    });

    describe("PvE Battle Scenarios", () => {
        beforeEach(() => {
            // Setup for PvE battle tests
            vi.mocked(BattleState.validateBattleState).mockResolvedValue({
                battleState: mockNPCBattleState,
                playerState: mockNPCBattleState.players["1"],
                targetState: mockNPCBattleState.players["npc_101"],
            });

            vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(mockNPCBattleState);
        });

        it("should successfully process an attack against an NPC", async () => {
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockNPCBattleState,
                attackedFirst: "1",
                playerAction: { damage: 30, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 5, type: "melee", lifeSteal: 0, bleedAmount: 0 },
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
        });

        it("should handle NPC defeat and drop rewards", async () => {
            // Setup for NPC defeat
            const defeatedNPCBattleState = {
                ...mockNPCBattleState,
                state: "FINISHED", // Set the battle state to finished
                players: {
                    "1": mockNPCBattleState.players["1"],
                    npc_101: {
                        ...mockNPCBattleState.players["npc_101"],
                        currentHealth: 0,
                    },
                },
            };

            // Mock the response for a defeated NPC
            const mockDefeatResponse = {
                data: {
                    battleState: defeatedNPCBattleState,
                    xpReward: 100,
                    droppedItem: { id: 123, name: "Test Item" },
                },
            };

            // Use spyOn to mock the actual implementation
            const processAttackSpy = vi.spyOn(BattleController, "processAttack");
            processAttackSpy.mockResolvedValueOnce(mockDefeatResponse);

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
        });
    });

    describe("getBattleStatus", () => {
        it("should successfully return battle status", async () => {
            const result = await BattleController.getBattleStatus(mockAttacker.id);

            expect(result).toHaveProperty("data");
            expect(BattleState.validateBattleState).toHaveBeenCalledWith("1");
            expect(BattleState.sanitizeBattleStateForFrontend).toHaveBeenCalled();
        });

        it("should return error if battle state validation fails", async () => {
            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                error: "Active battle not found",
            });

            await expect(BattleController.getBattleStatus(mockAttacker.id)).rejects.toThrow(
                "Active battle not found"
            );
        });

        it("should return error if player state is missing", async () => {
            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                battleState: mockBattleState,
                playerState: null,
                targetState: mockBattleState.players["2"],
            });

            const result = await BattleController.getBattleStatus(mockAttacker.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Invalid battle state");
        });

        it("should return error if target state is missing", async () => {
            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                battleState: mockBattleState,
                playerState: mockBattleState.players["1"],
                targetState: null,
            });

            const result = await BattleController.getBattleStatus(mockAttacker.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Invalid battle state");
        });
    });

    describe("listRooftopBattles", () => {
        const mockItem = {
            id: 107,
            name: "Test Item",
            description: "A test item",
            itemType: "special",
            stackable: false,
        };

        beforeEach(() => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                defeatedNpcs: [1, 3], // User has defeated NPCs 1 and 3
            });
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockItem as any);
        });

        it("should successfully list rooftop battles with defeat status", async () => {
            const result = await BattleController.listRooftopBattles(mockAttacker.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toBeInstanceOf(Array);
            expect(result.data.length).toBeGreaterThan(0);

            // Check that defeated NPCs are marked correctly
            const npc1 = result.data.find((npc: any) => npc.id === 1);
            const npc2 = result.data.find((npc: any) => npc.id === 2);

            expect(npc1?.defeated).toBe(true);
            expect(npc2?.defeated).toBe(false);

            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockAttacker.id);
        });

        it("should include item details for NPCs with item rewards", async () => {
            const result = await BattleController.listRooftopBattles(mockAttacker.id);

            expect(result).toHaveProperty("data");

            // Find an NPC with an item reward
            const npcWithItem = result.data.find((npc: any) => npc.itemRewardId === 107);

            expect(npcWithItem).toBeDefined();
            expect(npcWithItem.item).toBeDefined();
            expect(npcWithItem.item.id).toBe(107);
            expect(ItemRepository.findItemById).toHaveBeenCalled();
        });

        it("should handle user with no defeated NPCs", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                defeatedNpcs: [],
            });

            const result = await BattleController.listRooftopBattles(mockAttacker.id);

            expect(result).toHaveProperty("data");

            // All NPCs should not be defeated
            result.data.forEach((npc: any) => {
                expect(npc.defeated).toBe(false);
            });
        });

        it("should handle user not found", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue(null);

            const result = await BattleController.listRooftopBattles(mockAttacker.id);

            expect(result).toHaveProperty("data");

            // All NPCs should not be defeated when user is not found
            result.data.forEach((npc: any) => {
                expect(npc.defeated).toBe(false);
            });
        });

        it("should handle errors gracefully", async () => {
            vi.mocked(UserRepository.getUserById).mockRejectedValue(new Error("Database error"));

            const result = await BattleController.listRooftopBattles(mockAttacker.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Failed to list rooftop battles");
        });
    });

    describe("initiateRooftopBattle", () => {
        beforeEach(() => {
            vi.mocked(BattleInitiation.initiateBattle).mockResolvedValue({
                data: {
                    battleId: "battle_pve_rooftop_123456789_1_npc_1",
                },
            });
        });

        it("should successfully initiate a rooftop battle", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                level: 15,
                defeatedNpcs: [],
            });

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 1);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockAttacker.id);
        });

        it("should return error if NPC is not found", async () => {
            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 999);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("NPC not found");
        });

        it("should return error if user is not found", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue(null);

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 1);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("User not found");
        });

        it("should return error if user level is too low", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                level: 5, // Below ROOFTOP_BATTLES_LEVEL_GATE (10)
                defeatedNpcs: [],
            });

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 1);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Level too low!");
        });

        it("should return error if NPC has already been defeated", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                level: 15,
                defeatedNpcs: [1], // NPC 1 already defeated
            });

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 1);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("You've already defeated this NPC.");
        });

        it("should handle NPC with ID at boundary (first NPC)", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                level: 15,
                defeatedNpcs: [],
            });

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 1);

            expect(result).toHaveProperty("data");
        });

        it("should handle NPC with ID at boundary (last NPC)", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValue({
                ...mockAttacker,
                level: 40,
                defeatedNpcs: [],
            });

            const result = await BattleController.initiateRooftopBattle(mockAttacker.id, 8);

            expect(result).toHaveProperty("data");
        });
    });

    describe("antiBullyCheck (via initiatePVPBattle)", () => {
        beforeEach(() => {
            vi.mocked(UserRepository.getUserById).mockImplementation((id) => {
                if (id === mockAttacker.id) return Promise.resolve(mockAttacker);
                if (id === mockDefender.id) return Promise.resolve(mockDefender);
                return Promise.resolve(null);
            });
            vi.mocked(BattleHelpers.GetBeginBattleError).mockResolvedValue(null);
        });

        it("should allow battle when under daily attack limit", async () => {
            // Mock that user has attacked target 2 times today (under limit of 3)
            vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(2);

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
            expect(BattleRepository.countBattleWinsAgainstTarget).toHaveBeenCalled();
        });

        it("should prevent battle when daily attack limit is reached", async () => {
            // Mock that user has attacked target 3 times today (at limit)
            vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(3);

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Attacked this user too many times today");
        });

        it("should allow battle when daily attack limit is reached but user is admin", async () => {
            const adminUser = {
                ...mockAttacker,
                userType: "admin",
            } as unknown as UserModel;

            vi.mocked(UserRepository.getUserById).mockImplementation((id) => {
                if (id === mockAttacker.id) return Promise.resolve(adminUser);
                if (id === mockDefender.id) return Promise.resolve(mockDefender);
                return Promise.resolve(null);
            });

            // Mock that user has attacked target 3 times today (at limit)
            vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(3);

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
        });

        it("should allow battle on first attack of the day", async () => {
            // Mock that user has not attacked target today
            vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(0);

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
        });

        it("should allow battle when exactly at limit minus one", async () => {
            // Mock that user has attacked target 2 times today (limit is 3)
            vi.mocked(BattleRepository.countBattleWinsAgainstTarget).mockResolvedValue(2);

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
        });
    });
});
