import { HEALTH_THRESHOLD, PRIORITY_MULTIPLIERS, calculateMeleeDamage, chooseAction } from "../../ai/battle.ai.js";
import { evaluateBuffAbility } from "../../ai/buffAbilityScores.js";
import { evaluateDamageAbility } from "../../ai/damageAbilityScores.js";
import { evaluateDebuffAbility } from "../../ai/debuffAbilityScores.js";
import { evaluateHealingAbility } from "../../ai/healingAbilityScores.js";
import type { BattlePlayer, BattleSeedState, EquippedItem } from "../../types/battle.types.js";
import { createBattleSeedState } from "../../helpers/battle.random.js";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock the helper functions
vi.mock("../../ai/buffAbilityScores.js");
vi.mock("../../ai/damageAbilityScores.js");
vi.mock("../../ai/debuffAbilityScores.js");
vi.mock("../../ai/healingAbilityScores.js");

vi.mock("../../logic/battle.abilities.js", () => ({
    ABILITY_NAMES: {
        RAGE_ABILITY_NAME: "rage",
        CRIPPLE_ABILITY_NAME: "cripple",
        HEADBUTT_ABILITY_NAME: "headbutt",
        SHIELD_BASH_ABILITY_NAME: "shield_bash",
        SHOCKWAVE_ABILITY_NAME: "shockwave",
        EXHAUST_ABILITY_NAME: "exhaust",
        HEAL_ABILITY_NAME: "heal",
        HEAL_OVER_TIME_ABILITY_NAME: "heal_over_time",
        SLEEP_ABILITY_NAME: "sleep",
        SELF_HARM_ABILITY_NAME: "self_harm",
        MAX_HP_HEAL_ABILITY_NAME: "max_hp_heal",
        SAP_SLEEP_ABILITY_NAME: "sap_sleep",
        RELOAD_ABILITY_NAME: "reload",
        SPRAY_ABILITY_NAME: "spray",
        TOXIC_DART_ABILITY_NAME: "toxic_dart",
        DISARM_ABILITY_NAME: "disarm",
        GIANT_KILLING_SLINGSHOT_ABILITY_NAME: "giant_killing_slingshot",
        HIGH_GUARD_ABILITY_NAME: "high_guard",
    },
    STATUS_ABILITIES: [],
    STUN_EFFECTS: [],
    GetGiantKillingSlingshotDamage: vi.fn(),
    GetShieldBashDamage: vi.fn(),
    GetSprayDamage: vi.fn(),
    GetToxicDartDamage: vi.fn(),
}));

const mockWeapon: EquippedItem = {
    id: 1,
    name: "Test Sword",
    damage: 20,
    itemType: "weapon",
    rarity: "common",
    level: 1,
    upgradeLevel: 0,
    cashValue: 10,
    armour: null,
    health: null,
    energy: null,
    actionPoints: null,
    baseAmmo: null,
    itemEffects: null,
};

describe("Battle AI", () => {
    const mockPlayer: BattlePlayer = {
        id: "player1",
        username: "Test Player",
        userType: "player",
        avatar: "default.png",
        level: 1,
        maxHealth: 100,
        currentHealth: 100,
        attributes: {
            strength: 50,
            dexterity: 40,
            defence: 30,
            intelligence: 20,
            endurance: 10,
            vitality: 5,
        },
        maxStamina: 100,
        currentStamina: 100,
        isBoss: false,
        abilities: [
            {
                name: "headbutt",
                staminaCost: 20,
                currentModifier: null,
                secondaryModifier: null,
            },
            {
                name: "heal_over_time",
                staminaCost: 30,
                currentModifier: null,
                secondaryModifier: null,
            },
            {
                name: "rage",
                staminaCost: 25,
                currentModifier: null,
                secondaryModifier: null,
            },
            {
                name: "reload",
                staminaCost: 20,
                currentModifier: null,
                secondaryModifier: null,
            },
        ],
        currentTurn: 1,
        damageTaken: 0,
        ammo: 3,
        statusEffects: {},
        equipment: {
            weapon: mockWeapon,
            ranged: null,
            head: null,
            chest: null,
            legs: null,
            feet: null,
            hands: null,
            finger: null,
            shield: null,
            offhand: null,
        },
    };

    const mockEnemy: BattlePlayer = {
        id: "enemy1",
        username: "Test Enemy",
        userType: "npc",
        avatar: "enemy.png",
        level: 1,
        maxHealth: 100,
        currentHealth: 100,
        attributes: {
            strength: 30,
            dexterity: 30,
            defence: 30,
            intelligence: 20,
            endurance: 10,
            vitality: 5,
        },
        maxStamina: 100,
        currentStamina: 100,
        isBoss: false,
        abilities: [],
        currentTurn: 1,
        damageTaken: 0,
        ammo: 0,
        statusEffects: {},
        equipment: {
            weapon: null,
            ranged: null,
            head: null,
            chest: null,
            legs: null,
            feet: null,
            hands: null,
            finger: null,
            shield: null,
            offhand: null,
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
        // Mock ability evaluation functions to return low scores by default
        // Note: Using very low scores (0.2) to ensure ranged and reload actions are chosen
        vi.mocked(evaluateDamageAbility).mockResolvedValue(0.2);
        vi.mocked(evaluateHealingAbility).mockReturnValue(0.5);
        vi.mocked(evaluateBuffAbility).mockReturnValue(0.5);
        vi.mocked(evaluateDebuffAbility).mockReturnValue(0.5);
    });

    describe("calculateMeleeDamage", () => {
        it("should calculate melee damage correctly", () => {
            const damage = calculateMeleeDamage(mockPlayer, mockEnemy);
            // Base damage: 20, strength modifier: 0.5, defense modifier: 0.15
            // Expected: 20 * (1 + 0.5) * (1 - 0.15) = 25.5 floored to 25
            expect(damage).toBe(25);
        });

        it("should use default damage when no weapon equipped", () => {
            const playerNoWeapon: BattlePlayer = {
                ...mockPlayer,
                equipment: {
                    ...mockPlayer.equipment,
                    weapon: null,
                    ranged: null,
                    offhand: null,
                },
            };
            const damage = calculateMeleeDamage(playerNoWeapon, mockEnemy);
            // Base damage: 10, strength modifier: 0.5, defense modifier: 0.15
            expect(damage).toBe(12); // 10 * (1 + 0.5) * (1 - 0.15) floored
        });
    });

    describe("chooseAction", () => {
        it("should choose melee attack when it's the best option", async () => {
            const weakEnemy = { ...mockEnemy, currentHealth: 20 };
            const battleSeed: BattleSeedState = createBattleSeedState("test-melee");
            const action = await chooseAction(mockPlayer, weakEnemy, battleSeed);
            expect(action).toBe("attack");
        });

        it("should choose ranged attack when dexterity is higher", async () => {
            const dexterousPlayer = {
                ...mockPlayer,
                attributes: {
                    ...mockPlayer.attributes,
                    dexterity: 80,
                    strength: 30,
                },
                equipment: {
                    ...mockPlayer.equipment,
                    ranged: {
                        id: 1,
                        name: "bow",
                        damage: 15,
                        itemType: "ranged",
                        rarity: "common",
                        level: 1,
                        upgradeLevel: 0,
                        cashValue: 10,
                        armour: null,
                        health: null,
                        energy: null,
                        actionPoints: null,
                        baseAmmo: null,
                        itemEffects: null,
                    },
                },
                currentTurn: 1,
                damageTaken: 0,
                ammo: 5,
                statusEffects: {},
            };
            const battleSeed: BattleSeedState = createBattleSeedState("test-ranged");
            const action = await chooseAction(dexterousPlayer, mockEnemy, battleSeed);
            expect(action).toBe("ranged");
        });

        it("should choose reload when out of ammo", async () => {
            const noAmmoPlayer: BattlePlayer = {
                ...mockPlayer,
                attributes: {
                    ...mockPlayer.attributes,
                    dexterity: 80,
                    strength: 30,
                },
                equipment: {
                    ...mockPlayer.equipment,
                    ranged: {
                        id: 1,
                        name: "bow",
                        damage: 15,
                        itemType: "ranged",
                        rarity: "common",
                        level: 1,
                        upgradeLevel: 0,
                        cashValue: 10,
                        armour: null,
                        health: null,
                        energy: null,
                        actionPoints: null,
                        baseAmmo: null,
                        itemEffects: null,
                    },
                },
                currentTurn: 1,
                damageTaken: 0,
                ammo: 0,
                statusEffects: {},
                currentStamina: 50,
                abilities: [
                    { name: "reload", staminaCost: 20, currentModifier: null, secondaryModifier: null },
                    { name: "headbutt", staminaCost: 20, currentModifier: null, secondaryModifier: null },
                ],
            };
            const battleSeed: BattleSeedState = createBattleSeedState("test-reload");
            const action = await chooseAction(noAmmoPlayer, mockEnemy, battleSeed);
            expect(action).toBe("reload");
        });

        it("should evaluate abilities and choose highest scoring action", async () => {
            vi.mocked(evaluateDamageAbility).mockResolvedValue(5.0); // Make damage abilities very attractive
            const battleSeed: BattleSeedState = createBattleSeedState("test-ability");
            const action = await chooseAction(mockPlayer, mockEnemy, battleSeed);
            expect(action).toBe("headbutt");
        });

        it("should not choose abilities when insufficient stamina", async () => {
            const lowStaminaPlayer = {
                ...mockPlayer,
                currentStamina: 10,
            };
            const battleSeed: BattleSeedState = createBattleSeedState("test-low-stamina");
            const action = await chooseAction(lowStaminaPlayer, mockEnemy, battleSeed);
            expect(action).toBe("attack");
        });
    });

    describe("Constants", () => {
        it("should have correct health thresholds", () => {
            expect(HEALTH_THRESHOLD.CRITICAL).toBe(0.3);
            expect(HEALTH_THRESHOLD.LOW).toBe(0.5);
            expect(HEALTH_THRESHOLD.MEDIUM).toBe(0.7);
        });

        it("should have correct priority multipliers", () => {
            expect(PRIORITY_MULTIPLIERS.KILLING_BLOW).toBe(10.0);
            expect(PRIORITY_MULTIPLIERS.LOW_HEALTH_HEALING).toBe(8.0);
            expect(PRIORITY_MULTIPLIERS.HIGH_DAMAGE_ABILITY).toBe(3.0);
            expect(PRIORITY_MULTIPLIERS.POISON_EFFECT).toBe(2.5);
        });
    });
});
