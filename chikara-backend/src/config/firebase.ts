import { logger } from "../utils/log.js";
/* eslint-disable unicorn/prefer-export-from */
import * as admin from "firebase-admin";
import { App, cert, initializeApp } from "firebase-admin/app";
import { getMessaging } from "firebase-admin/messaging";

const appBaseUrl = process.env.APP_BASE_URL || "https://api.battleacademy.io";

// Keep track of app initialization status
let firebaseApp: App | null = null;

// Initialize Firebase conditionally
export const initializeFirebase = (): Promise<void> => {
    return new Promise<void>((resolve) => {
        if (process.env.FIREBASE_ENABLED === "true") {
            try {
                // Initialize the app if it hasn't been initialized already
                if (!firebaseApp) {
                    firebaseApp = initializeApp({
                        credential: cert({
                            projectId: "chikara-academy",
                            clientEmail: "<EMAIL>",
                            privateKey: process.env.FIREBASE_PRIVATE_KEY?.replaceAll(String.raw`\n`, "\n") ?? "",
                        }),
                    });
                }
                logger.info("Firebase Notifications Service initialized successfully");
            } catch (error) {
                logger.warn(
                    `Failed to initialize Firebase Notifications Service - Invalid credentials: ${String(error)}`
                );
            }
        }
        resolve();
    });
};

// Function to send push notification to a specific token
export async function sendPushNotification(userId: number, token: string, message: string): Promise<string | null> {
    if (token && token.length > 1) {
        const payload = {
            token: token,
            notification: {
                title: "Chikara Academy MMO",
                body: message,
            },
            webpush: {
                headers: {
                    image: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                },
                fcm_options: {
                    link: appBaseUrl,
                },
            },
            android: {
                notification: {
                    imageUrl: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                },
            },
        };

        try {
            const messaging = getMessaging();
            const response = await messaging.send(payload);
            if (response) {
                logger.info(`Push notification sent for userId: ${userId}, response ID: ${response}`);
                return response;
            }
        } catch (error) {
            // Silent catch
            logger.debug(`Failed to send push notification: ${String(error)}`);
        }
    }
    return null;
}

// Function to send push notifications to multiple tokens
export async function sendPushNotifications(tokens: { userId: number | null; token: string }[], message: string) {
    const promises = tokens.map(
        (tokenRecord) => tokenRecord.userId && sendPushNotification(tokenRecord.userId, tokenRecord.token, message)
    );
    await Promise.allSettled(promises); // Use allSettled to handle individual errors
}

// Export Firebase admin for direct access if needed
export { admin };
