import { PrismaMariaDb } from "@prisma/adapter-mariadb";
import { logger } from "../utils/log.js";

const FALLBACK_DATABASE_URL = "mysql://root:secret@127.0.0.1:3306/chikara";

const isProduction = process.env.NODE_ENV === "production";
const isCi = process.env.CI === "true";

const resolveDatabaseUrl = (): string => {
    const configuredUrl = process.env.DATABASE_URL?.trim();

    if (configuredUrl) {
        try {
            // Validate that the configured value is a URL Prisma can consume
            new URL(configuredUrl);
            return configuredUrl;
        } catch (error) {
            const reason = error instanceof Error ? error.message : "Unknown validation error";
            throw new Error(
                `DATABASE_URL is invalid. Received "${configuredUrl}". Ensure it is a valid connection string. ${reason}`
            );
        }
    }

    // In CI and production we require the environment variable to be set explicitly
    if (isProduction || isCi) {
        throw new Error(
            `DATABASE_URL is not set. Provide a valid connection string via the environment (local default: ${FALLBACK_DATABASE_URL}).`
        );
    }

    logger.warn(`DATABASE_URL not set; falling back to local default (${FALLBACK_DATABASE_URL}).`);

    return FALLBACK_DATABASE_URL;
};

const databaseUrl = resolveDatabaseUrl();

// Export a default adapter instance
export const defaultDatabaseAdapter = new PrismaMariaDb(databaseUrl);
