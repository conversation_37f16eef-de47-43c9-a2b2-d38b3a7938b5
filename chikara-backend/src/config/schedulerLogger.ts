import { inspect } from "node:util";
import { Logtail } from "@logtail/node";
import { LogtailTransport } from "@logtail/winston";
import winston from "winston";

const logtail = new Logtail("gyahwzGspsB7tgG7JYq25V2v", {
    endpoint: "https://s1221485.eu-nbg-2.betterstackdata.com",
});

interface SchedulerLoggerType {
    profile: (label: string) => void;
    info: (message: string | object) => void;
    error: (message: string | object, meta?: Error | object) => void;
    warn: (message: string | object) => void;
    debug: (message: string | object) => void;
    operational: (message: string | object) => void;
}

const logLevel = process.env.LOG_LEVEL || "info";
const envType = process.env.NODE_ENV || "development";
const disableSchedulerConsole = process.env.DISABLE_SCHEDULER_CONSOLE === "true";

// Define custom log levels
const customLevels = {
    error: 0,
    warn: 1,
    info: 2,
    operational: 3,
    debug: 4,
};

const logFormat = winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
);

// Custom format for logtail that includes scheduler service identifier
const logtailFormat = winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format((info) => {
        info.service = "scheduler";
        return info;
    })(),
    winston.format.json()
);

const consoleFormat = winston.format.combine(
    logFormat,
    winston.format.colorize(),
    winston.format.printf((info) => {
        // Custom formatting for objects and arrays
        const formatMessage = (msg: unknown): string => {
            if (msg === null || msg === undefined) return String(msg);
            if (typeof msg === "object") {
                return inspect(msg, {
                    colors: true,
                    depth: null,
                    compact: false,
                });
            }
            return `[SCHEDULER] ${msg}`;
        };

        // Format the message, handling potential objects
        const formattedMessage = formatMessage(info.message);

        // Include any additional metadata from the info object
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { message, level, timestamp, splat, durationMs, ...metadata } = info;

        const hasMetadata = Object.keys(metadata).length > 0;
        const metadataString = hasMetadata
            ? `\n${inspect(metadata, { colors: true, depth: null, compact: false })}`
            : "";

        return (
            `${info.timestamp} [${info.level}] ${formattedMessage}` +
            (info.splat === undefined
                ? " " + (info.durationMs === undefined ? " " : `<durationMs: ${info.durationMs}>`)
                : `${formatMessage(info.splat)}`) +
            metadataString
        );
    })
);

if (envType === "development") {
    winston.addColors({
        info: "green",
        warn: "yellow",
        error: "red",
        debug: "cyan",
        operational: "magenta",
    });
}

const transports: winston.transport[] = [];

if (envType === "production" || envType === "staging") {
    transports.push(
        new winston.transports.File({
            filename: "logs/scheduler-error.log",
            level: "error",
            format: logFormat,
            handleExceptions: true,
            handleRejections: true,
        }),
        new winston.transports.File({
            filename: "logs/scheduler.log",
            format: logFormat,
            handleExceptions: true,
            handleRejections: true,
        })
    );
}

if (envType !== "production" && !disableSchedulerConsole) {
    transports.push(
        new winston.transports.Console({
            handleExceptions: true,
            handleRejections: true,
            format: consoleFormat,
        })
    );
}

if (envType === "staging") {
    transports.push(
        new LogtailTransport(logtail, {
            format: logtailFormat,
        })
    );
}

const schedulerLoggerConfig: winston.LoggerOptions = {
    level: logLevel,
    levels: customLevels,
    transports: transports,
    exitOnError: false,
};

const schedulerLogger = winston.loggers.get("scheduler");
schedulerLogger.configure(schedulerLoggerConfig);

const baseSchedulerLogger = winston.createLogger(schedulerLoggerConfig);

const exportedSchedulerLogger: SchedulerLoggerType = disableSchedulerConsole 
    ? {
        profile: () => {},
        info: () => {},
        error: () => {},
        warn: () => {},
        debug: () => {},
        operational: () => {},
    }
    : {
        profile: baseSchedulerLogger.profile.bind(baseSchedulerLogger),
        info: baseSchedulerLogger.info.bind(baseSchedulerLogger),
        error: baseSchedulerLogger.error.bind(baseSchedulerLogger),
        warn: baseSchedulerLogger.warn.bind(baseSchedulerLogger),
        debug: baseSchedulerLogger.debug.bind(baseSchedulerLogger),
        operational: (message: string | object) => {
            baseSchedulerLogger.log("operational", message);
        },
    };

if (!disableSchedulerConsole) {
    schedulerLogger.info("Scheduler Logger initialised");
    schedulerLogger.info(`Environment: ${envType} | Log Level: ${logLevel}`);
}

export default exportedSchedulerLogger;
