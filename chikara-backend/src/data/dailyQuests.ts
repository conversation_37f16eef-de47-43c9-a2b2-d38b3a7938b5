const easyQuests = [
    { objectiveType: "TRAIN_STATS", minQuantity: 15, maxQuantity: 30 },
    { objectiveType: "VOTE_ON_SUGGESTION", minQuantity: 2, maxQuantity: 4, requiredLevel: 5 },
    { objectiveType: "COMPLETE_MISSIONS", minQuantity: 1, maxQuantity: 2 },
    { objectiveType: "CHARACTER_ENCOUNTERS", minQuantity: 6, maxQuantity: 10 },
    { objectiveType: "CRAFT_ITEM", minQuantity: 1, maxQuantity: 3 },
    { objectiveType: "GAMBLING_SLOTS" },
    { objectiveType: "DONATE_TO_SHRINE" },
] as const;

const mediumQuests = [
    { objectiveType: "DEFEAT_NPC_IN_TURNS", minQuantity: 2, maxQuantity: 4, minTarget: 3, maxTarget: 5 },
    { objectiveType: "DEFEAT_NPC_WITH_LOW_DAMAGE", minQuantity: 2, maxQuantity: 4, minTarget: 15, maxTarget: 25 },
    // { objectiveType: "DEFEAT_BOSS", minQuantity: 2, maxQuantity: 4 },
    // { objectiveType: "USE_ABILITY", minQuantity: 12, maxQuantity: 18, requiredLevel: 8 },
    { objectiveType: "WIN_BATTLE", minQuantity: 7, maxQuantity: 14 },
    { objectiveType: "DEFEAT_NPC", minQuantity: 6, maxQuantity: 12 }, // specific location only
] as const;

const hardQuests = [
    // { objectiveType: "DEFEAT_BOSS", minQuantity: 5, maxQuantity: 8, requiredLevel: 5 },
    { objectiveType: "DEFEAT_PLAYER", minQuantity: 4, maxQuantity: 8 },
    { objectiveType: "PVP_POST_BATTLE_CHOICE", minQuantity: 3, maxQuantity: 5 },
    { objectiveType: "COLLECT_BOUNTY_REWARD", minQuantity: 1, maxQuantity: 3 },
    { objectiveType: "DEFEAT_PLAYER_XNAME", minQuantity: 2, maxQuantity: 4 }, // players with x in name
    { objectiveType: "DEFEAT_SPECIFIC_PLAYER", minQuantity: 2, maxQuantity: 2 }, // specific player
] as const;

// TODO - hardcoded drop chances for now
const potentialDrops = [
    {
        // stim
        dropRate: 0.05,
        itemId: 140,
        itemQuantity: 1,
    },
    {
        // cola
        dropRate: 0.05,
        itemId: 132,
        itemQuantity: 1,
    },
    {
        // pens
        dropRate: 0.05,
        itemId: 1,
        itemQuantity: 20,
    },
    {
        // death note
        dropRate: 0.05,
        itemId: 150,
        itemQuantity: 1,
    },
    {
        // life note
        dropRate: 0.05,
        itemId: 151,
        itemQuantity: 2,
    },
    {
        // bitcoin
        dropRate: 0.005,
        itemId: 149,
        itemQuantity: 1,
    },
    {
        // suitcase
        dropRate: 0.05,
        itemId: 148,
        itemQuantity: 1,
    },
    {
        // bag of money
        dropRate: 0.1,
        itemId: 147,
        itemQuantity: 2,
    },
    {
        // earrings
        dropRate: 0.35,
        itemId: 144,
        itemQuantity: 2,
    },
    {
        // smartphone
        dropRate: 0.2,
        itemId: 145,
        itemQuantity: 2,
    },
];

export default {
    easyQuests,
    mediumQuests,
    hardQuests,
    potentialDrops,
} as const;
