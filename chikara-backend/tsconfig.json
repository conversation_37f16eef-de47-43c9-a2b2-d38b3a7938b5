{
    "compilerOptions": {
        "target": "ESNext",
        "module": "nodenext",
        "moduleResolution": "nodenext",
        "outDir": "dist",
        "allowJs": true,
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true,
        "resolvePackageJsonImports": true,
        "forceConsistentCasingInFileNames": true,
        "sourceMap": false,
        "declaration": false,
        "removeComments": true,
        // "exactOptionalPropertyTypes": true,
        "noUncheckedIndexedAccess": true,
        "resolveJsonModule": true,
        "strictNullChecks": true,
        "typeRoots": ["./node_modules/@types", "./src/types"],
        "maxNodeModuleJsDepth": 0,
        "incremental": true,
        "tsBuildInfoFile": "./.tsbuildinfo",
        "paths": {
            "@/*": ["./src/*"]
        }
    },
    "include": ["src/**/*", "*.ts"],
    "exclude": ["node_modules", "dist", "**/*.test.ts", "**/__tests__/**/*", "**/*.spec.ts", "vitest.config.ts"]
}
