import { expect, test, type Page, type Route } from "@playwright/test";

type BankTransactionType = "bank_deposit" | "bank_withdrawal" | "bank_transfer";

type BankTransaction = {
    transaction_type: BankTransactionType;
    cash: number;
    transactionFee: number;
    initiatorId: number | null;
    secondPartyId: number | null;
    createdAt: Date;
    cashBalance?: number;
    bankBalance?: number;
};

type MockUser = {
    id: number;
    username: string;
    cash: number;
    bank_balance: number;
    gangCreds: number;
    reputation: number;
    avatar: string | null;
    userType: string;
    level: number;
    actionPoints: number;
    maxActionPoints: number;
    xp: number;
    xpForNextLevel: number;
    currentHealth: number;
    health: number;
    chatBannedUntil: string | null;
    lastNewsIDRead: number;
    gangId: number | null;
    class: string | null;
};

type GameConfig = {
    MINIMUM_WITHDRAWAL: number;
    MINIMUM_DEPOSIT: number;
    MINIMUM_TRANSFER: number;
    TRANSACTION_HISTORY_LIMIT: number;
    TRANSACTION_FEE: number;
    BANK_DISABLED: boolean;
    DEPOSIT_DISABLED: boolean;
};

type MockState = {
    user: MockUser;
    config: GameConfig;
    transactions: BankTransaction[];
    chatHistory: Array<{
        id: number;
        message: string;
        hidden: boolean;
        announcementType: string | null;
        createdAt: string;
        updatedAt: string;
        chatRoomId: number;
        userId: number | null;
        parentMessageId: number | null;
        parentMessage: null;
        user: {
            id: number;
            avatar: string | null;
            username: string;
            userType: string;
            level: number;
        };
    }>;
    userDirectory: Record<string, { id: number; username: string; avatar: string | null }>;
    nextCreatedAt: number;
};

type Serializable = string | number | boolean | null | Date | Serializable[] | { [key: string]: Serializable };

type SerializedResponse = {
    json: any;
    meta?: Array<Array<string | number>>;
};

const INITIAL_CASH_ON_HAND = 1200;
const INITIAL_BANK_BALANCE = 3500;

const serializeForOrpc = (input: Serializable): SerializedResponse => {
    const meta: Array<Array<string | number>> = [];

    const encode = (value: Serializable, segments: Array<string | number>): any => {
        if (value instanceof Date) {
            meta.push([1, ...segments]);
            return value.toISOString();
        }

        if (Array.isArray(value)) {
            return value.map((item, index) => encode(item as Serializable, [...segments, index]));
        }

        if (value && typeof value === "object") {
            const result: Record<string, unknown> = {};
            for (const [key, nestedValue] of Object.entries(value)) {
                result[key] = encode(nestedValue as Serializable, [...segments, key]);
            }
            return result;
        }

        return value;
    };

    const json = encode(input, []);
    return meta.length > 0 ? { json, meta } : { json };
};

const fulfillJson = async (route: Route, payload: Serializable) => {
    const body = serializeForOrpc(payload);
    await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify(body),
    });
};

const toYen = (amount: number) =>
    new Intl.NumberFormat("ja-JP", {
        style: "currency",
        currency: "JPY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    })
        .format(amount)
        .replace("JP¥", "¥");

const ONE_MINUTE = 60 * 1000;

const createInitialState = (): MockState => {
    const baseTime = Date.parse("2025-06-05T10:00:00Z");
    const config: GameConfig = {
        MINIMUM_DEPOSIT: 100,
        MINIMUM_WITHDRAWAL: 100,
        MINIMUM_TRANSFER: 100,
        TRANSACTION_HISTORY_LIMIT: 5,
        TRANSACTION_FEE: 0.05,
        BANK_DISABLED: false,
        DEPOSIT_DISABLED: false,
    };

    const user: MockUser = {
        id: 101,
        username: "Test Student",
        cash: INITIAL_CASH_ON_HAND,
        bank_balance: INITIAL_BANK_BALANCE,
        gangCreds: 250,
        reputation: 42,
        avatar: null,
        userType: "student",
        level: 15,
        actionPoints: 40,
        maxActionPoints: 50,
        xp: 1200,
        xpForNextLevel: 2000,
        currentHealth: 90,
        health: 120,
        chatBannedUntil: null,
        lastNewsIDRead: 10,
        gangId: null,
        class: null,
    };

    const transactions: BankTransaction[] = [
        {
            transaction_type: "bank_transfer",
            cash: 275,
            transactionFee: Math.floor(275 * config.TRANSACTION_FEE),
            initiatorId: 402,
            secondPartyId: user.id,
            createdAt: new Date(baseTime - ONE_MINUTE),
            bankBalance: user.bank_balance,
            cashBalance: user.cash,
        },
        {
            transaction_type: "bank_withdrawal",
            cash: 450,
            transactionFee: 0,
            initiatorId: user.id,
            secondPartyId: null,
            createdAt: new Date(baseTime - 2 * ONE_MINUTE),
            bankBalance: user.bank_balance - 450,
            cashBalance: user.cash + 450,
        },
        {
            transaction_type: "bank_deposit",
            cash: 300,
            transactionFee: Math.floor(300 * config.TRANSACTION_FEE),
            initiatorId: user.id,
            secondPartyId: null,
            createdAt: new Date(baseTime - 3 * ONE_MINUTE),
            bankBalance: user.bank_balance - 450 + (300 - Math.floor(300 * config.TRANSACTION_FEE)),
            cashBalance: user.cash + 450 - 300,
        },
    ];

    return {
        user,
        config,
        transactions,
        chatHistory: [
            {
                id: 1,
                message: "Welcome to Chikara Academy!",
                hidden: false,
                announcementType: null,
                createdAt: new Date(baseTime - 5 * ONE_MINUTE).toISOString(),
                updatedAt: new Date(baseTime - 5 * ONE_MINUTE).toISOString(),
                chatRoomId: 1,
                userId: 101,
                parentMessageId: null,
                parentMessage: null,
                user: {
                    id: 101,
                    avatar: null,
                    username: "Test Student",
                    userType: "student",
                    level: 15,
                },
            },
            {
                id: 2,
                message: "Remember to secure your funds in the bank!",
                hidden: false,
                announcementType: null,
                createdAt: new Date(baseTime - 4 * ONE_MINUTE).toISOString(),
                updatedAt: new Date(baseTime - 4 * ONE_MINUTE).toISOString(),
                chatRoomId: 1,
                userId: 202,
                parentMessageId: null,
                parentMessage: null,
                user: {
                    id: 202,
                    avatar: null,
                    username: "StudentTwo",
                    userType: "student",
                    level: 12,
                },
            },
        ],
        userDirectory: {
            "101": { id: 101, username: "Test Student", avatar: null },
            "202": { id: 202, username: "StudentTwo", avatar: null },
            "402": { id: 402, username: "Mentor", avatar: null },
        },
        nextCreatedAt: baseTime,
    };
};

const setupBankingMocks = async (page: Page, state: MockState) => {
    const getNextTimestamp = () => {
        state.nextCreatedAt += ONE_MINUTE;
        return new Date(state.nextCreatedAt);
    };

    await page.route("**/rpc/**", async (route) => {
        const request = route.request();
        const url = new URL(request.url());
        const path = url.pathname.split("/rpc/")[1] || "";
        const [namespace, procedure] = path.split("/");
        const bodyText = request.postData();
        const parsedBody = bodyText ? JSON.parse(bodyText) : {};
        const input = parsedBody?.json ?? {};

        if (namespace === "user" && procedure === "getCurrentUserInfo") {
            await fulfillJson(route, state.user);
            return;
        }

        if (namespace === "user" && procedure === "getGameConfig") {
            await fulfillJson(route, state.config);
            return;
        }

        if (namespace === "user" && procedure === "getUserInfo") {
            const identifier = String(input?.id ?? "");
            const userInfo = state.userDirectory[identifier] ?? {
                id: Number(identifier),
                username: `Student #${identifier}`,
                avatar: null,
            };
            await fulfillJson(route, {
                id: userInfo.id,
                username: userInfo.username,
                avatar: userInfo.avatar,
            });
            return;
        }

        if (namespace === "bank" && procedure === "getBankTransactions") {
            await fulfillJson(route, state.transactions);
            return;
        }

        if (namespace === "bank" && procedure === "deposit") {
            const amount = Number(input?.amount ?? 0);
            const fee = Math.floor(amount * state.config.TRANSACTION_FEE);
            state.user.cash -= amount;
            state.user.bank_balance += amount - fee;

            state.transactions.unshift({
                transaction_type: "bank_deposit",
                cash: amount,
                transactionFee: fee,
                initiatorId: state.user.id,
                secondPartyId: null,
                createdAt: getNextTimestamp(),
                bankBalance: state.user.bank_balance,
                cashBalance: state.user.cash,
            });
            if (state.transactions.length > state.config.TRANSACTION_HISTORY_LIMIT) {
                state.transactions.splice(state.config.TRANSACTION_HISTORY_LIMIT);
            }

            await fulfillJson(route, { success: true });
            return;
        }

        if (namespace === "bank" && procedure === "withdraw") {
            const amount = Number(input?.amount ?? 0);
            state.user.cash += amount;
            state.user.bank_balance -= amount;

            state.transactions.unshift({
                transaction_type: "bank_withdrawal",
                cash: amount,
                transactionFee: 0,
                initiatorId: state.user.id,
                secondPartyId: null,
                createdAt: getNextTimestamp(),
                bankBalance: state.user.bank_balance,
                cashBalance: state.user.cash,
            });
            if (state.transactions.length > state.config.TRANSACTION_HISTORY_LIMIT) {
                state.transactions.splice(state.config.TRANSACTION_HISTORY_LIMIT);
            }

            await fulfillJson(route, { success: true });
            return;
        }

        if (namespace === "bank" && procedure === "transfer") {
            const amount = Number(input?.transferAmount ?? 0);
            const recipientId = Number(input?.recipientId ?? 0);
            const fee = Math.floor(amount * state.config.TRANSACTION_FEE);
            state.user.bank_balance -= amount + fee;

            state.transactions.unshift({
                transaction_type: "bank_transfer",
                cash: amount,
                transactionFee: fee,
                initiatorId: state.user.id,
                secondPartyId: recipientId,
                createdAt: getNextTimestamp(),
                bankBalance: state.user.bank_balance,
                cashBalance: state.user.cash,
            });
            if (state.transactions.length > state.config.TRANSACTION_HISTORY_LIMIT) {
                state.transactions.splice(state.config.TRANSACTION_HISTORY_LIMIT);
            }

            if (!state.userDirectory[String(recipientId)]) {
                state.userDirectory[String(recipientId)] = {
                    id: recipientId,
                    username: `Student #${recipientId}`,
                    avatar: null,
                };
            }

            await fulfillJson(route, { success: true });
            return;
        }

        if (namespace === "notifications" && procedure === "getUnreadCount") {
            await fulfillJson(route, { unread: 0 });
            return;
        }

        if (namespace === "messaging" && procedure === "getUnreadCount") {
            await fulfillJson(route, { unread: 0 });
            return;
        }

        if (namespace === "shrine" && procedure === "getActiveBuff") {
            await fulfillJson(route, { buffRewards: {} });
            return;
        }

        if (namespace === "chat" && procedure === "getHistory") {
            await fulfillJson(route, state.chatHistory);
            return;
        }

        if (namespace === "suggestions" && procedure === "getAvailablePolls") {
            await fulfillJson(route, []);
            return;
        }

        if (namespace === "battle" && procedure === "getStatus") {
            await fulfillJson(route, null);
            return;
        }

        if (namespace === "notifications" && procedure === "getList") {
            await fulfillJson(route, []);
            return;
        }

        await fulfillJson(route, null);
    });
};

let mockState: MockState;

test.describe("Banking page interactions", () => {
    test.beforeEach(async ({ page }) => {
        mockState = createInitialState();
        await setupBankingMocks(page, mockState);
        await page.addInitScript(
            ({ key, value }) => {
                window.localStorage.setItem(key, JSON.stringify(value));
            },
            { key: "auth", value: { state: { authed: true }, version: 1.1 } }
        );
        await page.goto("/bank");
        await page.waitForLoadState("networkidle");
    });

    test("allows depositing yen into the bank", async ({ page }) => {
        const depositAmount = 200;
        const depositFee = Math.floor(depositAmount * mockState.config.TRANSACTION_FEE);

        const amountInput = page.getByPlaceholder("0");
        await amountInput.fill(depositAmount.toString());
        await page.getByRole("button", { name: `Deposit ${toYen(depositAmount)}` }).click();

        await expect(page.getByText(`Successfully deposited ${toYen(depositAmount)}`)).toBeVisible();

        const bankStat = page.locator(".stat").filter({ hasText: "Bank Balance" });
        await expect(bankStat).toContainText(toYen(mockState.user.bank_balance));

        const cashStat = page.locator(".stat").filter({ hasText: "Cash on Hand" });
        await expect(cashStat).toContainText(toYen(mockState.user.cash));

        const firstRow = page.locator("table tbody tr").first();
        await expect(firstRow.locator("td").nth(2)).toHaveText("Deposit");
        await expect(firstRow.locator("td").nth(4)).toContainText("-");
        await expect(firstRow.locator("td").nth(4)).toContainText(toYen(depositAmount));

        const expectedBankBalance = INITIAL_BANK_BALANCE + depositAmount - depositFee;
        const expectedCash = INITIAL_CASH_ON_HAND - depositAmount;
        expect(mockState.user.bank_balance).toBe(expectedBankBalance);
        expect(mockState.user.cash).toBe(expectedCash);
    });

    test("allows withdrawing yen from the bank", async ({ page }) => {
        const withdrawAmount = 400;
        await page.getByRole("button", { name: /^Withdraw$/ }).click();

        const amountInput = page.getByPlaceholder("0");
        await amountInput.fill(withdrawAmount.toString());
        await page.getByRole("button", { name: `Withdraw ${toYen(withdrawAmount)}` }).click();

        await expect(page.getByText(`Successfully withdrew ${toYen(withdrawAmount)}`)).toBeVisible();

        const bankStat = page.locator(".stat").filter({ hasText: "Bank Balance" });
        await expect(bankStat).toContainText(toYen(mockState.user.bank_balance));

        const cashStat = page.locator(".stat").filter({ hasText: "Cash on Hand" });
        await expect(cashStat).toContainText(toYen(mockState.user.cash));

        const firstRow = page.locator("table tbody tr").first();
        await expect(firstRow.locator("td").nth(2)).toHaveText("Withdrawal");
        await expect(firstRow.locator("td").nth(4)).toContainText("+");
        await expect(firstRow.locator("td").nth(4)).toContainText(toYen(withdrawAmount));

        const expectedBankBalance = INITIAL_BANK_BALANCE - withdrawAmount;
        const expectedCash = INITIAL_CASH_ON_HAND + withdrawAmount;
        expect(mockState.user.bank_balance).toBe(expectedBankBalance);
        expect(mockState.user.cash).toBe(expectedCash);
    });

    test("allows transferring yen to another student", async ({ page }) => {
        const transferAmount = 300;
        const recipientId = 202;
        const transferFee = Math.floor(transferAmount * mockState.config.TRANSACTION_FEE);

        await page.getByRole("button", { name: /^Transfer$/ }).click();

        const amountInput = page.getByPlaceholder("0");
        await amountInput.fill(transferAmount.toString());

        await page.getByPlaceholder("Enter student ID").fill(recipientId.toString());
        await page.getByRole("button", { name: `Transfer ${toYen(transferAmount)}` }).click();

        await expect(
            page.getByText(
                `Successfully transferred ${toYen(transferAmount)} to Student #${recipientId} (Fee: ${toYen(transferFee)})`
            )
        ).toBeVisible();

        const bankStat = page.locator(".stat").filter({ hasText: "Bank Balance" });
        await expect(bankStat).toContainText(toYen(mockState.user.bank_balance));

        const cashStat = page.locator(".stat").filter({ hasText: "Cash on Hand" });
        await expect(cashStat).toContainText(toYen(mockState.user.cash));

        const firstRow = page.locator("table tbody tr").first();
        await expect(firstRow.locator("td").nth(2)).toHaveText("Transfer");
        await expect(firstRow.locator("td").nth(3)).toContainText("To");
        await expect(firstRow.locator("td").nth(3).locator("a", { hasText: "StudentTwo" })).toBeVisible();
        await expect(firstRow.locator("td").nth(4)).toContainText("-");
        await expect(firstRow.locator("td").nth(4)).toContainText(toYen(transferAmount));

        const expectedBankBalance = INITIAL_BANK_BALANCE - transferAmount - transferFee;
        expect(mockState.user.bank_balance).toBe(expectedBankBalance);
        expect(mockState.user.cash).toBe(INITIAL_CASH_ON_HAND);
    });

    test("renders the latest transactions in chronological order", async ({ page }) => {
        const rows = page.locator("table tbody tr");
        await expect(rows).toHaveCount(mockState.transactions.length);

        const typeTexts = (await rows.locator("td:nth-child(3)").allInnerTexts()).map((text) => text.trim());
        expect(typeTexts).toEqual(["Transfer", "Withdrawal", "Deposit"]);

        const detailFirstRow = rows.first().locator("td").nth(3);
        await expect(detailFirstRow).toContainText("From");
        await expect(detailFirstRow.locator("a", { hasText: "Mentor" })).toBeVisible();
    });
});
