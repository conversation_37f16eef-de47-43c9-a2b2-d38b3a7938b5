import { useMemo, useState } from "react";

export type SortDirection = "asc" | "desc";

export interface FilterConfig<T> {
    searchFields?: (keyof T)[];
    customFilters?: {
        [key: string]: (item: T, value: any) => boolean;
    };
    customSorters?: {
        [key: string]: (a: T, b: T) => number;
    };
}

export interface UseSortAndFilterProps<T, S extends string> {
    data: T[];
    defaultSortOption: S;
    defaultSortDirection?: SortDirection;
    defaultFilters?: Record<string, any>;
    config?: FilterConfig<T>;
}

export default function useSortAndFilter<T, S extends string>({
    data = [],
    defaultSortOption,
    defaultSortDirection = "asc",
    defaultFilters = {},
    config = {},
}: UseSortAndFilterProps<T, S>) {
    const [sortOption, setSortOption] = useState<S>(defaultSortOption);
    const [sortDirection, setSortDirection] = useState<SortDirection>(defaultSortDirection);
    const [filterOptions, setFilterOptions] = useState<Record<string, any>>(defaultFilters);

    const filteredAndSortedData = useMemo(() => {
        if (!data || data.length === 0) return [];

        // First apply filters
        const filtered = data.filter((item) => {
            // Apply search term filter
            if (filterOptions.searchTerm && filterOptions.searchTerm.trim() !== "") {
                const searchTerm = filterOptions.searchTerm.toLowerCase().trim();
                const searchFields = config.searchFields || [];
                
                if (searchFields.length > 0) {
                    const matchesSearch = searchFields.some(field => {
                        const value = item[field];
                        if (typeof value === 'string') {
                            return value.toLowerCase().includes(searchTerm);
                        }
                        if (typeof value === 'number') {
                            return value.toString().includes(searchTerm);
                        }
                        return false;
                    });
                    if (!matchesSearch) return false;
                }
            }

            // Apply custom filters
            if (config.customFilters) {
                for (const [filterKey, filterFn] of Object.entries(config.customFilters)) {
                    const filterValue = filterOptions[filterKey];
                    if (filterValue !== undefined && filterValue !== null && filterValue !== '' && filterValue !== 'all' && filterValue !== 'any') {
                        if (!filterFn(item, filterValue)) {
                            return false;
                        }
                    }
                }
            }

            // Apply standard filters (for properties that exist directly on the item)
            for (const [key, value] of Object.entries(filterOptions)) {
                if (key === 'searchTerm' || (config.customFilters && key in config.customFilters)) {
                    continue; // Skip search term and custom filters as they're handled above
                }
                
                if (value !== undefined && value !== null && value !== '' && value !== 'all' && value !== 'any') {
                    const itemValue = (item as any)[key];
                    
                    // Handle boolean filters
                    if (typeof value === 'boolean' && value === true) {
                        if (!itemValue) return false;
                    }
                    // Handle string/number exact matches
                    else if (itemValue !== value) {
                        return false;
                    }
                }
            }

            return true;
        });

        // Then sort the filtered results
        return filtered.sort((a, b) => {
            let comparison = 0;

            // Use custom sorter if available
            if (config.customSorters && sortOption in config.customSorters) {
                comparison = config.customSorters[sortOption](a, b);
            } else {
                // Default sorting behavior
                const aValue = (a as any)[sortOption];
                const bValue = (b as any)[sortOption];

                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    comparison = aValue.localeCompare(bValue);
                } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    comparison = aValue - bValue;
                } else {
                    // Fallback to string comparison
                    comparison = String(aValue).localeCompare(String(bValue));
                }
            }

            return sortDirection === "asc" ? comparison : -comparison;
        });
    }, [data, sortOption, sortDirection, filterOptions, config]);

    // Helper to reset all filters
    const resetFilters = () => {
        setFilterOptions(defaultFilters);
    };

    // Helper to check if any filters are active
    const hasActiveFilters = useMemo(() => {
        return Object.entries(filterOptions).some(([key, value]) => {
            const defaultValue = defaultFilters[key];
            if (key === 'searchTerm') return value && value.trim() !== '';
            if (typeof value === 'boolean') return value === true;
            if (typeof value === 'string') return value !== '' && value !== 'all' && value !== 'any' && value !== defaultValue;
            if (typeof value === 'number') return value !== undefined && value !== defaultValue;
            return false;
        });
    }, [filterOptions, defaultFilters]);

    return {
        // Data
        filteredAndSortedData,
        
        // Sort controls
        sortOption,
        setSortOption,
        sortDirection,
        setSortDirection,
        
        // Filter controls
        filterOptions,
        setFilterOptions,
        resetFilters,
        hasActiveFilters,
        
        // Stats
        totalItems: data.length,
        filteredItems: filteredAndSortedData.length,
    };
}