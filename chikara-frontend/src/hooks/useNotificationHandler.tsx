import { useEffect, useCallback } from "react";
import { toast } from "react-hot-toast";
import { api } from "@/helpers/api";
import { useQueryClient } from "@tanstack/react-query";
import { useLocation, useNavigate } from "react-router-dom";
import { useNormalStore, useSessionStore, useSocketStore } from "../app/store/stores";
import { getUserInfo } from "@/hooks/api/useGetUserInfo";
import { MessageToast } from "@/components/MessageToast";

// Define types for notification details
interface NotificationDetails {
    type?: string;
    reason?: string;
    duration?: number;
    newLevel?: number;
    senderId?: number;
    [key: string]: unknown;
}

export type NotificationType =
    | "profile_comment"
    | "jail"
    | "hospitalised"
    | "death_noted"
    | "life_noted"
    | "mission_completed"
    | "admin_action"
    | "levelup"
    | "message"
    | "quest_complete";

// Define types for socket notification message
interface NotificationMessage {
    type: NotificationType;
    details: string | Record<string, unknown>;
}

const replyMessage = (id: string, navigate: ReturnType<typeof useNavigate>): void => {
    toast.dismiss(id);
    navigate("/inbox");
};

export const useNotificationHandler = () => {
    const { socket } = useSocketStore();
    const { setJustJailed } = useNormalStore();
    const { setLevelupValue } = useSessionStore();
    const queryClient = useQueryClient();
    const location = useLocation();
    const navigate = useNavigate();

    const handleNotification = useCallback(
        async (type: NotificationType, detailsObj: string | NotificationDetails): Promise<void> => {
            let data: { username: string; avatar: string | null; [key: string]: unknown } | undefined;
            const details: NotificationDetails = typeof detailsObj === "string" ? JSON.parse(detailsObj) : detailsObj;

            switch (type) {
                case "profile_comment":
                    if (details?.type === "suggestion_reply") {
                        break;
                    }
                    if (details?.type === "suggestion") {
                        toast.success("You received a comment on your suggestion!");
                        break;
                    }
                    toast.success("You received a comment on your profile!");
                    break;
                case "jail":
                    toast.error("You were caught attacking another student and sent to jail!");
                    break;
                case "hospitalised":
                    if (details.reason === "character_encounter") {
                        return;
                    } else {
                        await queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        });
                        // Only show the "knocked unconscious" message if the user was not the attacker
                        // When a user attacks and loses, they get details.reason === "battle"
                        // When a user is attacked and loses, they don't have a reason field
                        if (details.reason !== "battle") {
                            toast.error("You were knocked unconscious by another student!");
                        }
                    }
                    break;
                case "death_noted":
                    toast.error(`A death book was used on you! You were injured!`);
                    await queryClient.invalidateQueries({
                        queryKey: api.user.getCurrentUserInfo.key(),
                    });
                    break;
                case "life_noted":
                    toast.success(`A life book was used on you! You were fully healed!`);
                    await queryClient.invalidateQueries({
                        queryKey: api.user.getCurrentUserInfo.key(),
                    });
                    break;
                case "mission_completed":
                    toast.success(`Your mission was completed!`);
                    await queryClient.invalidateQueries({
                        queryKey: api.user.getCurrentUserInfo.key(),
                    });
                    break;
                case "admin_action":
                    if (details?.type === "item_gift") {
                        toast.success(`A staff member sent you a gift!`);
                        await queryClient.invalidateQueries({
                            queryKey: api.user.getInventory.key(),
                        });
                    }
                    if (details?.type === "chat_ban") {
                        toast.error(
                            `You were banned from chat for ${details?.duration} minutes by Haruka Ito for ${details?.reason}`
                        );
                        await queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        });
                    }
                    if (details?.type === "jail") {
                        toast.error(
                            `You were jailed for ${details?.duration} minutes by Haruka Ito for ${details?.reason}`
                        );
                        await queryClient.invalidateQueries({
                            queryKey: api.user.getInventory.key(),
                        });
                    }
                    break;
                case "levelup":
                    setTimeout(() => {
                        queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        });
                        if (details.newLevel) {
                            setLevelupValue(details.newLevel);
                        }
                    }, 1500);
                    break;
                case "message":
                    queryClient.invalidateQueries({
                        queryKey: api.messaging.getChatHistory.key(),
                    });
                    try {
                        if (details.senderId) {
                            data = await getUserInfo(details.senderId);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    toast.custom((t) => (
                        <MessageToast toastId={t.id} userData={data} navigate={navigate} onReply={replyMessage} />
                    ));
                    break;
                case "quest_complete":
                    if (details?.questType === "daily") {
                        toast.success("Daily quest ready to complete!");
                        await queryClient.invalidateQueries({
                            queryKey: api.dailyQuest.getDailyQuests.key(),
                        });
                    }
                    break;
                default:
                    console.log({ type, details });
            }
        },
        [navigate, queryClient, setLevelupValue]
    );

    useEffect(() => {
        if (socket) {
            socket.on("notification", (msg: NotificationMessage) => {
                if (msg.type === "jail") {
                    setJustJailed(true);
                }
                if (msg.type === "message") {
                    if (location.pathname.includes("/inbox")) {
                        // If the user gets a message notification while they are on the inbox page,
                        // invalidate the chat history and return so that the toast is not shown
                        queryClient.invalidateQueries({
                            queryKey: api.messaging.getChatHistory.key(),
                        });
                        return;
                    }
                }

                queryClient.invalidateQueries({
                    queryKey: api.notifications.getList.key(),
                });

                // Handle the notification and display the toast
                void handleNotification(msg.type, msg.details);
            });
            return () => {
                socket.off("notification");
            };
        }
    }, [socket, location.pathname, navigate, queryClient, setJustJailed, setLevelupValue, handleNotification]);
};
