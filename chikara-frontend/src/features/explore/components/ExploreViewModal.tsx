import React, { useEffect } from "react";
import { motion, useAnimation, type Variants } from "framer-motion";
import { cn } from "@/lib/utils";

// Animation variants
const ANIMATION_VARIANTS = {
    container: {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 1 } },
    } as Variants,
    wipe: {
        hidden: { width: "100%", left: 0 },
        visible: {
            width: 0,
            left: "50%",
            transition: { ease: "easeInOut", duration: 0.75 },
        },
    } as Variants,
    dialogueBox: {
        hidden: { opacity: 0, scale: 0.9 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3, ease: "easeOut" },
        },
    } as Variants,
};

interface ExploreViewModalProps {
    children: React.ReactNode;
    title: string;
    subtitle?: React.ReactNode;
    borderColor?: string;
    headerBorderColor?: string;
    titleColor?: string;
    backgroundImage?: string;
    showWipeAnimation?: boolean;
    className?: string;
    contentClassName?: string;
    maxWidth?: string;
}

export const ExploreViewModal: React.FC<ExploreViewModalProps> = ({
    children,
    title,
    subtitle,
    borderColor = "border-indigo-500/80",
    headerBorderColor = "border-indigo-500/30",
    titleColor = "text-indigo-400",
    backgroundImage,
    showWipeAnimation = true,
    className,
    contentClassName,
    maxWidth = "max-w-4xl",
}) => {
    const controls = useAnimation();

    useEffect(() => {
        controls.start("visible");
    }, [controls]);

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
            <motion.div
                variants={ANIMATION_VARIANTS.container}
                initial="hidden"
                animate={controls}
                className={cn(
                    "relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",
                    className
                )}
            >
                {/* Background Image/Gradient */}
                <div className="absolute inset-0">
                    {backgroundImage ? (
                        <>
                            <img className="w-full h-full object-cover" src={backgroundImage} alt="Background" />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50" />
                        </>
                    ) : (
                        <>
                            <div className="w-full h-full bg-gradient-to-b from-gray-900 to-black" />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50" />
                        </>
                    )}
                </div>

                {/* Animated Wipe Effect */}
                {showWipeAnimation && (
                    <motion.div
                        className="absolute inset-0 bg-black z-10"
                        variants={ANIMATION_VARIANTS.wipe}
                        initial="hidden"
                        animate="visible"
                    />
                )}

                {/* Content Container */}
                <motion.div
                    variants={ANIMATION_VARIANTS.dialogueBox}
                    initial="hidden"
                    animate="visible"
                    className="relative z-20 h-full flex items-center justify-center p-4"
                >
                    <div
                        className={cn(
                            "w-full bg-slate-800/95 backdrop-blur-md border-4 rounded-2xl shadow-2xl",
                            borderColor,
                            maxWidth,
                            contentClassName
                        )}
                    >
                        {/* Header */}
                        {(title || subtitle) && (
                            <div className={cn("px-6 py-4 border-b", headerBorderColor)}>
                                <h2
                                    className={cn(
                                        "text-center text-xl md:text-2xl lg:text-3xl font-semibold",
                                        titleColor
                                    )}
                                >
                                    {title}
                                </h2>
                                {subtitle && (
                                    <div className="text-center mt-2">
                                        {typeof subtitle === "string" ? (
                                            <span className="text-sm text-gray-300">{subtitle}</span>
                                        ) : (
                                            subtitle
                                        )}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Main Content */}
                        <div className="p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col">{children}</div>
                    </div>
                </motion.div>
            </motion.div>
        </div>
    );
};
