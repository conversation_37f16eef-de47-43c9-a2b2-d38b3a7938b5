import { DisplayItem } from "@/components/DisplayItem";
import Tabs from "@/components/Tabs";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { useState, useMemo } from "react";
import useGetInventory from "@/hooks/api/useGetInventory";
import type { InventoryItem } from "@/types/item";
import type { User } from "@/types/user";
import { ItemRarities } from "@/types/item";
import { Search, X, Package, Sword, Shield, Gem, Archive } from "lucide-react";

interface EquippedItems {
    [key: string]: {
        userItemId: number;
        id: number;
    };
}

interface InventoryGridProps {
    equippedItems: EquippedItems;
    currentUser: User;
}

// Rarity order for sorting (highest first)
const RARITY_ORDER = {
    [ItemRarities.legendary]: 6,
    [ItemRarities.military]: 5,
    [ItemRarities.specialist]: 4,
    [ItemRarities.enhanced]: 3,
    [ItemRarities.standard]: 2,
    [ItemRarities.novice]: 1,
} as const;

type ItemTypeFilter = {
    types: string[];
    icon: React.ElementType;
    color: string;
};

function InventoryGrid({ equippedItems, currentUser }: InventoryGridProps) {
    const isMobile = useCheckMobileScreen();
    const { isLoading, error, data } = useGetInventory();
    const [selectedTab, setSelectedTab] = useState<string>("All");
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

    const itemTypeFilters: Record<string, ItemTypeFilter> = {
        All: { types: [], icon: Package, color: "text-primary" },
        Weapons: { types: ["weapon", "ranged", "offhand"], icon: Sword, color: "text-error" },
        Armor: {
            types: ["head", "chest", "hands", "legs", "feet", "finger", "shield"],
            icon: Shield,
            color: "text-info",
        },
        Consumables: { types: ["consumable", "recipe", "pet"], icon: Gem, color: "text-success" },
        Material: { types: ["crafting", "upgrade"], icon: Gem, color: "text-warning" },
        Misc: { types: ["special", "quest", "junk"], icon: Archive, color: "text-secondary" },
    };

    // Filter and sort items
    const filteredItems = useMemo(() => {
        if (!data) return [];

        let items = [...data];

        // Apply tab filter
        if (selectedTab !== "All") {
            const allowedTypes = itemTypeFilters[selectedTab].types;
            items = items.filter((item) => item.item && allowedTypes.includes(item.item.itemType));
        }

        // Apply search filter
        if (searchQuery) {
            items = items.filter(
                (item) => item.item && item.item.name.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Sort by rarity (highest first), then by level, then by name
        items.sort((a, b) => {
            if (!a.item || !b.item) return 0;

            const rarityA = RARITY_ORDER[a.item.rarity as keyof typeof RARITY_ORDER] || 0;
            const rarityB = RARITY_ORDER[b.item.rarity as keyof typeof RARITY_ORDER] || 0;

            if (rarityA !== rarityB) return rarityB - rarityA;
            if (a.item.level !== b.item.level) return (b.item.level || 0) - (a.item.level || 0);
            return a.item.name.localeCompare(b.item.name);
        });

        return items;
    }, [data, selectedTab, searchQuery]);

    // Calculate dynamic grid columns based on item count
    const getGridColumns = useMemo(() => {
        const itemCount = filteredItems.length;

        if (isMobile) {
            if (itemCount <= 6) return "grid-cols-3";
            return "grid-cols-4";
        }

        // Desktop breakpoints based on item count
        if (itemCount <= 20) return "grid-cols-6";
        if (itemCount <= 40) return "grid-cols-7";
        if (itemCount <= 64) return "grid-cols-8";
        if (itemCount <= 80) return "grid-cols-9";
        return "grid-cols-10";
    }, [filteredItems.length, isMobile]);

    const ItemCard = ({ userItem }: { userItem: InventoryItem }) => {
        const { item } = userItem;

        if (!item) return null;

        const isEquipped = equippedItems?.[item.itemType]?.userItemId === userItem.id;
        const canEquip = currentUser?.level && item.level ? currentUser.level >= item.level : false;
        const isSelected = selectedItem?.id === userItem.id;

        return (
            <div
                className={cn(
                    "relative group cursor-pointer rounded-xl transition-all duration-300 aspect-square",
                    isSelected ? "ring-2 ring-white/70 shadow-xl" : "shadow-md",
                    "hover:scale-110 hover:shadow-xl hover:z-10"
                )}
                onClick={() => setSelectedItem(userItem)}
            >
                {/* Item Image Container */}
                <div className="relative h-full w-full flex items-center justify-center">
                    <DisplayItem
                        itemTypeFrame
                        item={userItem}
                        quantity={userItem.count > 1 ? userItem.count : null}
                        className="h-full w-full"
                    />

                    {/* Upgrade level */}
                    {userItem.upgradeLevel > 0 && (
                        <div className="absolute bottom-1 left-1 z-20 bg-gradient-to-br from-yellow-400 to-yellow-600 text-white text-xs font-bold px-1.5 py-0.5 rounded-md shadow-md">
                            +{userItem.upgradeLevel}
                        </div>
                    )}
                </div>
            </div>
        );
    };

    if (error) return "An error has occurred: " + error.message;

    return (
        <>
            <div className="flex flex-col h-full border rounded-lg bg-base-300 border-base-100">
                {/* Header with tabs and search */}
                <div className="mb-4 space-y-3">
                    {/* Tabs */}
                    <Tabs
                        selectedTab={selectedTab}
                        tabs={Object.entries(itemTypeFilters).map(([key, filter]) => ({
                            value: key,
                            label: key,
                            icon: filter.icon,
                            color: filter.color,
                        }))}
                        onTabChange={setSelectedTab}
                    />

                    {/* Search bar with item count */}
                    <div className="flex items-center gap-4 mx-2 md:mx-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <input
                                type="text"
                                placeholder="Search items..."
                                value={searchQuery}
                                className="w-full pl-10 pr-10 py-2 bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-indigo-500/50 transition-colors"
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            {searchQuery && (
                                <button
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                                    onClick={() => setSearchQuery("")}
                                >
                                    <X className="h-4 w-4" />
                                </button>
                            )}
                        </div>
                        {/* Item count */}
                        <div className="badge badge-lg badge-neutral self-center sm:self-auto">
                            <Package className="w-3 h-3 mr-1" />
                            {filteredItems.length}
                        </div>
                    </div>
                </div>

                {/* Inventory grid */}
                <div className="flex-1 overflow-y-auto px-2 md:px-0">
                    {isLoading ? (
                        <div className="flex items-center justify-center h-64">
                            <p className="text-gray-400">Loading inventory...</p>
                        </div>
                    ) : filteredItems.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-64">
                            <div className="text-center space-y-2">
                                <Package className="w-16 h-16 mx-auto text-base-content/20" />
                                <p className="text-base-content/60">
                                    {searchQuery ? "No items match your search" : "No items in this category"}
                                </p>
                            </div>
                        </div>
                    ) : (
                        <div className={cn("grid gap-3 pb-4 pt-2 px-3", getGridColumns)}>
                            {filteredItems.map((userItem) => (
                                <ItemCard key={userItem.id} userItem={userItem} />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}

export default InventoryGrid;
