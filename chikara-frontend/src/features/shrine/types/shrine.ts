import type { AppRouterClient } from "@/lib/orpc";
import type { InferClientBodyOutputs } from "@orpc/client";

type BodyOutputs = InferClientBodyOutputs<AppRouterClient>;

export type ShrineGoal = BodyOutputs["shrine"]["getGoal"];
export type ShrineDonationList = BodyOutputs["shrine"]["getDonations"];
export type ShrineDonation = ShrineDonationList[number];

export type ShrineActiveBuffs = BodyOutputs["shrine"]["getActiveBuff"];

export type ShrineUserDonationStatus = BodyOutputs["shrine"]["getUserDonationStatus"];
