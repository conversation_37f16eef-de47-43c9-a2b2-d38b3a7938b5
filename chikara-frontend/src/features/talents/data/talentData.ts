import { TALENT_NAMES as TALENTS } from "../types/talents";
import type { TalentNameType } from "../types/talents";

// Strength
import berserkerImg from "@/assets/icons/talents/strength/berserker.png";
import bullyImg from "@/assets/icons/talents/strength/bully.png";
import combatRegenImg from "@/assets/icons/talents/strength/combatRegen.png";
import crippleImg from "@/assets/icons/talents/strength/cripple.png";
import headbuttImg from "@/assets/icons/talents/strength/headbutt.png";
import meleeDmgImg from "@/assets/icons/talents/strength/meleeDmg.png";
import offhandsImg from "@/assets/icons/talents/strength/offhands.png";
import rageImg from "@/assets/icons/talents/strength/rage.png";
import stunImg from "@/assets/icons/talents/strength/stun.png";

// Defence
import clawImg from "@/assets/icons/talents/defence/claw.png";
import deflectImg from "@/assets/icons/talents/defence/deflect.png";
import highGuardImg from "@/assets/icons/talents/defence/highguard.png";
import metabolism from "@/assets/icons/talents/defence/metabolism.png";
import mitigation from "@/assets/icons/talents/defence/mitigation.png";
import rigidity from "@/assets/icons/talents/defence/rigidity.png";
import shieldbash from "@/assets/icons/talents/defence/shieldbash.png";
import shieldbearer from "@/assets/icons/talents/defence/shieldbearer.png";
import strongbones from "@/assets/icons/talents/defence/strongbones.png";

// Intelligence
import chainingImg from "@/assets/icons/talents/intelligence/chaining.png";
import cunningImg from "@/assets/icons/talents/intelligence/cunning.png";
import healImg from "@/assets/icons/talents/intelligence/heal.png";
import healovertimeImg from "@/assets/icons/talents/intelligence/healovertime.png";
import healthyCasterImg from "@/assets/icons/talents/intelligence/healthyCaster.png";
import investorImg from "@/assets/icons/talents/intelligence/investor.png";
import learnerImg from "@/assets/icons/talents/intelligence/learner.png";
import multitaskerImg from "@/assets/icons/talents/intelligence/multitasker.png";
import sleepImg from "@/assets/icons/talents/intelligence/sleep.png";
import speedcrafterImg from "@/assets/icons/talents/intelligence/speedcrafter.png";

// Endurance
import bodyRegenImg from "@/assets/icons/talents/endurance/bodyRegen.png";
import builtImg from "@/assets/icons/talents/endurance/built.png";
import cowardImg from "@/assets/icons/talents/endurance/coward.png";
import disarmImg from "@/assets/icons/talents/endurance/disarm.png";
import energeticImg from "@/assets/icons/talents/endurance/energetic.png";
import freeMovementImg from "@/assets/icons/talents/endurance/freeMovement.png";
import muggerImg from "@/assets/icons/talents/endurance/mugger.png";
import rejuvenationImg from "@/assets/icons/talents/endurance/rejuvenation.png";
import secondWindImg from "@/assets/icons/talents/endurance/secondWind.png";
import selfHarmImg from "@/assets/icons/talents/endurance/selfHarm.png";
import staminaRegenImg from "@/assets/icons/talents/endurance/staminaRegen.png";

// Dexterity
import escapeArtistImg from "@/assets/icons/talents/dexterity/escapeArtist.png";
import exhaust from "@/assets/icons/talents/dexterity/exhaust.png";
import quiverImg from "@/assets/icons/talents/dexterity/quiver.png";
import rangerImg from "@/assets/icons/talents/dexterity/ranger.png";
import reloadImg from "@/assets/icons/talents/dexterity/reload.png";
import slingshotImg from "@/assets/icons/talents/dexterity/slingshot.png";
import speedsterImg from "@/assets/icons/talents/dexterity/speedster.png";
import sprayImg from "@/assets/icons/talents/dexterity/spray.png";
import toxicdartImg from "@/assets/icons/talents/dexterity/toxicdart.png";

/**
 * Mapping of talent names to their positions in the UI grid.
 * This is UI-specific data that defines where each talent appears in the talent tree.
 */
export const talentData: Partial<Record<TalentNameType, { position: number; image: string }>> = {
    // Strength talents
    [TALENTS.MELEE_DAMAGE_INCREASE]: { position: 1, image: meleeDmgImg },
    [TALENTS.BERSERKER]: { position: 10, image: berserkerImg },
    [TALENTS.BULLY]: { position: 3, image: bullyImg },
    [TALENTS.OFFENSIVE_OFFHANDS]: { position: 7, image: offhandsImg },
    [TALENTS.COMBAT_REGENERATION]: { position: 9, image: combatRegenImg },
    [TALENTS.RAGE]: { position: 5, image: rageImg },
    [TALENTS.CRIPPLE]: { position: 12, image: crippleImg },
    [TALENTS.STUN]: { position: 14, image: stunImg },
    [TALENTS.HEADBUTT]: { position: 11, image: headbuttImg },

    // Defense talents
    [TALENTS.ACTIVE_DEFENCE]: { position: 1, image: rigidity },
    [TALENTS.STRONG_BONES]: { position: 6, image: strongbones },
    [TALENTS.GOOD_STOMACH]: { position: 2, image: metabolism },
    [TALENTS.SHIELDBEARER]: { position: 7, image: shieldbearer },
    [TALENTS.MITIGATION]: { position: 11, image: mitigation },
    [TALENTS.DEFLECT_DAMAGE]: { position: 12, image: deflectImg },
    [TALENTS.SHOCKWAVE]: { position: 14, image: clawImg },
    [TALENTS.HIGH_GUARD]: { position: 10, image: highGuardImg },
    [TALENTS.SHIELD_BASH]: { position: 5, image: shieldbash },

    // Intelligence talents
    [TALENTS.HEALTHY_CASTER]: { position: 10, image: healthyCasterImg },
    [TALENTS.CUNNING_RAT]: { position: 1, image: cunningImg },
    [TALENTS.SPEED_CRAFTER]: { position: 4, image: speedcrafterImg },
    [TALENTS.MULTITASKER]: { position: 6, image: multitaskerImg },
    [TALENTS.INVESTOR]: { position: 7, image: investorImg },
    [TALENTS.LEARNER]: { position: 5, image: learnerImg },
    [TALENTS.REVIVE]: { position: 9, image: healthyCasterImg },
    [TALENTS.ABILITY_EFFICIENCY]: { position: 11, image: chainingImg },
    [TALENTS.HEAL]: { position: 8, image: healImg },
    [TALENTS.HEAL_OVER_TIME]: { position: 12, image: healovertimeImg },
    [TALENTS.SLEEP]: { position: 14, image: sleepImg },

    // Endurance talents
    [TALENTS.ENERGETIC]: { position: 12, image: energeticImg },
    [TALENTS.BUILT]: { position: 7, image: builtImg },
    [TALENTS.MUGGER]: { position: 1, image: muggerImg },
    [TALENTS.COWARD]: { position: 4, image: cowardImg },
    [TALENTS.OUTSIDE_COMBAT_REGENERATION]: { position: 2, image: bodyRegenImg },
    [TALENTS.RECOVERY]: { position: 6, image: staminaRegenImg },
    [TALENTS.FREE_MOVEMENT]: { position: 5, image: freeMovementImg },
    [TALENTS.REJUVENATION]: { position: 9, image: rejuvenationImg },
    [TALENTS.SELF_HARM]: { position: 8, image: selfHarmImg },
    [TALENTS.MAX_HP_HEAL]: { position: 13, image: secondWindImg },
    [TALENTS.DISARM]: { position: 11, image: disarmImg },

    // Dexterity talents
    [TALENTS.RANGER]: { position: 2, image: rangerImg },
    [TALENTS.ESCAPE_ARTIST]: { position: 1, image: escapeArtistImg },
    [TALENTS.QUIVER]: { position: 9, image: quiverImg },
    [TALENTS.QUICK_TURN_TAKER]: { position: 8, image: speedsterImg },
    [TALENTS.SHADOW_STEP]: { position: 11, image: speedsterImg },
    [TALENTS.RELOAD]: { position: 7, image: reloadImg },
    [TALENTS.SPRAY]: { position: 5, image: sprayImg },
    [TALENTS.TOXIC_DART]: { position: 10, image: toxicdartImg },
    [TALENTS.EXHAUST]: { position: 12, image: exhaust },
    [TALENTS.GIANT_KILLING_SLINGSHOT]: { position: 14, image: slingshotImg },
};
