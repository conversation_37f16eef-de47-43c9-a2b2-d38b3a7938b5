import type { AppRouterClient } from "@/lib/orpc";
import type { InferClientBodyOutputs } from "@orpc/client";

type BodyOutputs = InferClientBodyOutputs<AppRouterClient>;

export type UnlockedTalents = BodyOutputs["talents"]["getUnlockedTalents"];
export type UnlockedTalentList = NonNullable<UnlockedTalents["talentList"]>;
export type UserTalent = UnlockedTalentList[number];
export type TreePoints = NonNullable<UnlockedTalents["treePoints"]>;
// export type TalentInfo = NonNullable<UserTalent["talentInfo"]>;

export interface Talent {
    tier1Modifier: number;
    tier2Modifier: number;
    tier3Modifier: number;
    secondaryModifier: number;
}

export interface TalentDisplay {
    displayName: string;
    talentType: string;
    position: number;
    image: string;
    description?: (talent: Talent) => string;
    level1Desc?: (talent: Talent) => string;
    level2Desc?: (talent: Talent) => string;
    level3Desc?: (talent: Talent) => string;
    subDescription?: (talent: Talent) => string;
}

export interface Ability {
    id: number;
    name: string;
    image?: string;
    staminaCost?: number | null;
    displayName?: string;
    description?: string | ((_talent: Talent) => string) | null;
    subDescription?: string | ((_talent: Talent) => string) | null;
    talentType?: string;
    tier1Modifier?: number | null;
    tier2Modifier?: number | null;
    tier3Modifier?: number | null;
    secondaryModifier?: number | null;
}

export type StatTypes = "strength" | "dexterity" | "defence" | "intelligence" | "endurance" | "vitality";

export interface TalentInfo {
    id: number;
    maxPoints: number;
    name: string;
    displayName: string;
    talentType: string;
    image: string | null;
    description: string | null;
    pointsCost: number;
    pointsInTreeRequired: number;
    secondaryModifier: number | null;
    staminaCost: number;
    skillLevelRequired: number;
    tier1Modifier: number;
    tier2Modifier: number | null;
    tier3Modifier: number | null;
    tree: StatTypes;
    position?: number;
    level1Desc?: (talent: Talent) => string;
    level2Desc?: (talent: Talent) => string;
    level3Desc?: (talent: Talent) => string;
    subDescription?: (talent: Talent) => string;
}

// export interface UserTalent {
//     level: number;
//     talentId: number;
//     userId: number;
//     talentInfo: TalentInfo;
// }

// interface TreePoints {
//     strength?: number;
//     defence?: number;
//     endurance?: number;
//     dexterity?: number;
//     intelligence?: number;
//     vitality?: number;
// }

// export interface UnlockedTalents {
//     talentList: UserTalent[];
//     treePoints: TreePoints;
// }

/**
 * Enum representing all available talent names
 */
export enum TALENT_NAMES {
    // Strength talents
    MELEE_DAMAGE_INCREASE = "melee_damage_increase",
    BERSERKER = "berserker",
    BULLY = "bully",
    OFFENSIVE_OFFHANDS = "offensive_offhands",
    COMBAT_REGENERATION = "combat_regeneration",
    RAGE = "rage",
    CRIPPLE = "cripple",
    STUN = "stun",
    HEADBUTT = "headbutt",

    // Defense talents
    ACTIVE_DEFENCE = "active_defence",
    STRONG_BONES = "strong_bones",
    GOOD_STOMACH = "good_stomach",
    SHIELDBEARER = "shieldbearer",
    MITIGATION = "mitigation",
    DEFLECT_DAMAGE = "deflect_damage",
    SHOCKWAVE = "shockwave",
    HIGH_GUARD = "high_guard",
    SHIELD_BASH = "shield_bash",

    // Intelligence talents
    HEALTHY_CASTER = "healthy_caster",
    CUNNING_RAT = "cunning_rat",
    SPEED_CRAFTER = "speed_crafter",
    MULTITASKER = "multitasker",
    INVESTOR = "investor",
    LEARNER = "learner",
    REVIVE = "revive",
    HEAL = "heal",
    HEAL_OVER_TIME = "heal_over_time",
    SLEEP = "sleep",
    ABILITY_EFFICIENCY = "ability_efficiency",

    // Endurance talents
    ENERGETIC = "energetic",
    BUILT = "built",
    MUGGER = "mugger",
    COWARD = "coward",
    OUTSIDE_COMBAT_REGENERATION = "outside_combat_regeneration",
    RECOVERY = "recovery",
    FREE_MOVEMENT = "free_movement",
    REJUVENATION = "rejuvenation",
    SELF_HARM = "self_harm",
    MAX_HP_HEAL = "max_hp_heal",
    DISARM = "disarm",

    // Dexterity talents
    RANGER = "ranger",
    ESCAPE_ARTIST = "escape_artist",
    QUIVER = "quiver",
    QUICK_TURN_TAKER = "quick_turn_taker",
    SHADOW_STEP = "shadow_step",
    RELOAD = "reload",
    SPRAY = "spray",
    TOXIC_DART = "toxic_dart",
    EXHAUST = "exhaust",
    GIANT_KILLING_SLINGSHOT = "giant_killing_slingshot",
}

export type TalentNameType = TALENT_NAMES;

export type AbilitySlot = 1 | 2 | 3 | 4;
