import type { AppRouterClient } from "@/lib/orpc";
import type { InferClientBodyOutputs } from "@orpc/client";

type BodyOutputs = InferClientBodyOutputs<AppRouterClient>;

export type UserSkills = BodyOutputs["user"]["getSkills"];
export type SkillData = UserSkills[keyof UserSkills];

export type CraftingSkill = "fabrication" | "outfitting" | "chemistry" | "electronics";

export interface SkillTalent {
    id: string;
    name: string;
    description: string;
    icon: string;
    requiredPoints: number;
    requiredTalents: string[];
    bonuses: string[];
    unlocked: boolean;
    position: { x: number; y: number };
}

export interface SkillTalentTree {
    skill: CraftingSkill;
    talents: SkillTalent[];
    spentPoints: number;
}
