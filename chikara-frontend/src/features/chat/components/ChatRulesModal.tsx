import { Modal } from "@/components/Modal/Modal";
import { ScrollText, Shield, AlertCircle, Users, MessageSquare, Ban } from "lucide-react";
import { Callout } from "@/components/TestComponents/Callout";

interface ChatRulesModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

const ChatRulesModal = ({ open, onOpenChange }: ChatRulesModalProps) => {
    const rules = [
        {
            icon: <Users className="w-5 h-5" />,
            title: "Be Respectful",
            description: "Treat all players with respect and courtesy. No personal attacks or harassment.",
        },
        {
            icon: <Ban className="w-5 h-5" />,
            title: "No Spamming",
            description: "Avoid flooding the chat with repetitive messages or excessive caps.",
        },
        {
            icon: <Shield className="w-5 h-5" />,
            title: "No Hate Speech",
            description: "Zero tolerance for discriminatory language, bullying, or offensive content.",
        },
        {
            icon: <MessageSquare className="w-5 h-5" />,
            title: "Stay On Topic",
            description: "Keep conversations relevant to the game and appropriate for all ages.",
        },
        {
            icon: <AlertCircle className="w-5 h-5" />,
            title: "Protect Privacy",
            description: "Never share personal information like addresses, phone numbers, or passwords.",
        },
    ];

    return (
        <Modal
            showClose
            title=""
            open={open}
            modalMaxWidth="max-w-2xl"
            contentHeight="max-h-[80vh]"
            onOpenChange={onOpenChange}
        >
            {/* Header with Icon */}
            <div className="flex items-center gap-3 mb-6">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10">
                    <ScrollText className="w-6 h-6 text-primary" />
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-base-content">Global Chat Rules</h2>
                    <p className="text-sm text-base-content/60 mt-1">
                        Please follow these guidelines to maintain a friendly community
                    </p>
                </div>
            </div>

            {/* Rules List */}
            <div className="space-y-4">
                {rules.map((rule, index) => (
                    <div key={index} className="card bg-base-200/50 border border-base-300">
                        <div className="card-body p-4">
                            <div className="flex gap-3">
                                <div className="flex-shrink-0">
                                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10 text-primary">
                                        {rule.icon}
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-semibold text-base-content mb-1">{rule.title}</h3>
                                    <p className="text-sm text-base-content/70">{rule.description}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Footer Alert */}
            <Callout
                className="mt-2"
                type="warning"
                title="Violation of these rules may result in chat restrictions or account suspension."
            />

            {/* Action Button */}
            <div className="flex justify-end mt-6">
                <button className="btn btn-primary" onClick={() => onOpenChange(false)}>
                    I Understand
                </button>
            </div>
        </Modal>
    );
};

export default ChatRulesModal;
