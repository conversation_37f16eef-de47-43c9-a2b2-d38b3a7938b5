import { DisplayAvatar } from "@/components/DisplayAvatar";
import { Link } from "react-router-dom";
import type { User } from "@/types/user";
import SidebarStatBarCollapsed from "./SidebarStatBarCollapsed";
import { isTimestampValid } from "@/utils/timestampHelpers";

interface SidebarProfileCollapsedProps {
    currentUser?: User | undefined;
}

export default function SidebarProfileCollapsed({ currentUser }: SidebarProfileCollapsedProps) {
    const percentHealth = currentUser?.health ? (currentUser.currentHealth ?? 0) / currentUser.health : 0;
    const percentAP = currentUser?.maxActionPoints ? (currentUser.actionPoints ?? 0) / currentUser.maxActionPoints : 0;

    return (
        <section className="relative mx-2 select-none rounded-md">
            <div className="flex flex-col items-center space-y-2">
                <Link className="flex" to={currentUser?.id ? `/profile/${currentUser.id}` : "#"}>
                    <div className="w-12 h-12 shadow-sm drop-shadow-lg">
                        <DisplayAvatar
                            src={currentUser}
                            className={`mx-auto h-12 w-12 rounded-lg border-2 border-black object-cover dark:border-gray-700 ${
                                isTimestampValid(currentUser?.hospitalisedUntil) ? `grayscale` : ``
                            }`}
                        />
                    </div>
                </Link>

                {/* Collapsed Stat Bars */}
                <div className="w-full space-y-1">
                    <SidebarStatBarCollapsed
                        type="health"
                        percentage={percentHealth}
                        current={currentUser?.currentHealth ?? 0}
                        max={currentUser?.health ?? 0}
                    />
                    <SidebarStatBarCollapsed
                        type="actionPoints"
                        percentage={percentAP}
                        current={currentUser?.actionPoints ?? 0}
                        max={currentUser?.maxActionPoints ?? 0}
                    />
                </div>
            </div>
        </section>
    );
}
