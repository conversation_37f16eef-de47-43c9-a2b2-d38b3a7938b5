import React, { useState, useEffect } from "react";
import { NavLink, useLocation, Link } from "react-router-dom";
import { navItems } from "@/helpers/navItems";
import { cn } from "@/lib/utils";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { format } from "date-fns";
import { UTCDateMini } from "@date-fns/utc";
import DevMenu from "@/features/dev/components/DevMenu";
import { usePersistStore } from "@/app/store/stores";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import type { BattleState } from "@/features/battle/types/battle";
import StatusEffects from "@/components/StatusEffects";
import { useGetStatusEffects } from "@/hooks/api/useGetStatusEffects";
import apImg from "@/assets/icons/UI/APicon3.png";
import hpImg from "@/assets/icons/UI/HPicon.png";
import energyImg from "@/assets/icons/UI/energyicon.png";
import expImg from "@/assets/icons/UI/expBG.png";
import yenImg from "@/assets/icons/UI/yen.png";
import respectImg from "@/assets/icons/UI/respect.png";
import DiscordIcon from "@/assets/icons/logos/DiscordIcon";

// Additional navigation icons
import eventsImg from "@/assets/icons/navitems/events.png";
import messagesImg from "@/assets/icons/navitems/messages.png";
import leaderboardsImg from "@/assets/icons/navitems/leaderboards.png";
import newsImg from "@/assets/icons/navitems/news.png";
import inventoryIcon from "@/assets/icons/navitems/inventory.png";
import gangIcon from "@/assets/icons/navitems/gang.png";
import bankIcon from "@/assets/icons/navitems/admin.png"; // Using admin icon as placeholder
import casinoIcon from "@/assets/icons/navitems/admin.png"; // Using admin icon as placeholder

const quickNavItems = [
    { to: "/events", title: "Events", icon: eventsImg },
    { to: "/inbox", title: "Messages", icon: messagesImg },
    { to: "/leaderboard", title: "Leaderboards", icon: leaderboardsImg },
    { to: "/news", title: "News", icon: newsImg },
];

interface StatBarProps {
    label: string;
    value: number;
    max: number;
    color: string;
    iconSrc: string;
}

const StatBar: React.FC<StatBarProps> = ({ label, value, max, color, iconSrc }) => {
    const percentage = (value / max) * 100;

    return (
        <div className="mb-1.5">
            <div className="bg-gradient-to-br from-slate-900/95 via-blue-950/40 to-slate-800/95 p-2 rounded-lg border border-blue-500/30 shadow-lg shadow-black/40 backdrop-blur-sm">
                {/* Compact single-line layout */}
                <div className="flex items-center gap-2 mb-1">
                    <div className="p-1 bg-gradient-to-br from-blue-600/80 via-blue-700/60 to-slate-800/80 rounded border border-blue-400/50 shadow-inner">
                        <img src={iconSrc} alt={label} className="w-4 h-4 object-contain brightness-90" />
                    </div>
                    <span className="text-[10px] font-bold uppercase tracking-wider text-blue-100 drop-shadow-sm flex-1">
                        {label}
                    </span>
                    <span className="text-[10px] font-mono font-bold text-white drop-shadow-sm bg-black/40 px-1.5 py-0.5 rounded border border-blue-500/30">
                        {value}/{max}
                    </span>
                </div>

                {/* Compact progress bar */}
                <div className="relative h-2 bg-gradient-to-r from-black/80 via-slate-900/60 to-black/80 rounded overflow-hidden border border-slate-700/60 shadow-inner">
                    <div
                        className={cn("h-full transition-all duration-700 ease-out relative", color)}
                        style={{ width: `${percentage}%` }}
                    >
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-white/15" />
                        <div className="absolute top-0 left-0 right-0 h-px bg-white/25" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-transparent to-blue-500/10 pointer-events-none" />
                </div>
            </div>
        </div>
    );
};

export default function GameSidebar() {
    const location = useLocation();
    const { data: currentUser } = useFetchCurrentUser();
    const { data: statusEffects } = useGetStatusEffects({ enabled: Boolean(currentUser) });
    const { twelveHrClock } = usePersistStore();
    const [currentTime, setCurrentTime] = useState(new Date());
    const [showDevMenu, setShowDevMenu] = useState(false);

    // Check if user is in battle and get battle data if so
    const isInBattle = location.pathname === "/fight";
    const { data: battleStatus } = useQuery({
        ...api.battle.getStatus.queryOptions({ retry: false }),
        enabled: isInBattle,
    });

    // Get current HP and max HP from battle state if in battle, otherwise from user data
    const getCurrentHealthData = () => {
        if (
            isInBattle &&
            battleStatus &&
            currentUser &&
            typeof battleStatus === "object" &&
            "players" in battleStatus
        ) {
            const players = battleStatus.players;
            if (typeof players === "object" && players) {
                const playerInBattle = Object.values(players).find(
                    (player: any) => player.id === String(currentUser.id)
                );
                if (
                    playerInBattle &&
                    typeof playerInBattle.currentHealth === "number" &&
                    typeof playerInBattle.maxHealth === "number"
                ) {
                    return {
                        currentHealth: playerInBattle.currentHealth,
                        maxHealth: playerInBattle.maxHealth,
                    };
                }
            }
        }
        // Fallback to user data
        return {
            currentHealth: currentUser?.currentHealth || 0,
            maxHealth: currentUser?.health || 100,
        };
    };

    const { currentHealth, maxHealth } = getCurrentHealthData();

    const isDev = import.meta.env.MODE === "development";

    const navigation = navItems(currentUser?.userType ?? null);
    const hasStatusEffects = Array.isArray(statusEffects) && statusEffects.length > 0;

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const timeFormat = twelveHrClock ? "h:mm:ss a" : "HH:mm:ss";

    return (
        <div className="h-full w-80 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 border-blue-600/30 rounded-r-xl hidden overflow-hidden md:block ">
            <div className="absolute inset-0 bg-gradient-to-br from-black/20 via-transparent to-blue-900/10 pointer-events-none rounded-r-xl" />

            <div className="flex flex-col h-full relative z-10">
                {/* Header with logo/title */}
                {/* <GameSidebarHeader /> */}

                {/* User Profile Section */}
                {currentUser && (
                    <div className="mx-4 mt-3 rounded-xl shadow-lg relative">
                        {/* Player info header */}
                        <div className="flex items-center gap-3 mb-3 relative z-10">
                            <div className="relative group">
                                <div className="relative bg-gradient-to-br from-purple-900/50 via-slate-800 to-black rounded-md p-1 border-2 border-blue-400/60 shadow-lg">
                                    <img
                                        src={currentUser.avatar || "/default-avatar.png"}
                                        alt={currentUser.username}
                                        className="size-14 rounded-md object-cover"
                                    />
                                    {/* Anime-style status indicator */}
                                    <div className="absolute -bottom-1 -right-1">
                                        <div className="bg-green-400 size-4 rounded-full border-2 border-slate-900 shadow-lg animate-pulse" />
                                        <div className="absolute inset-0 bg-green-400/30 rounded-full animate-ping" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex-1">
                                <h3 className="font-black text-white text-base uppercase tracking-wider drop-shadow-md">
                                    {currentUser.username}
                                </h3>
                                <div className="flex items-center gap-2">
                                    <div className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 px-2 py-0.5 rounded border border-blue-400/60">
                                        <p className="text-xs font-bold text-white uppercase">
                                            LVL {currentUser.level}
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-1.5">
                                        {quickNavItems.map((item) => (
                                            <NavLink
                                                key={item.title}
                                                to={item.to}
                                                className="inline-flex size-7 items-center justify-center rounded-md border border-orange-400/40 bg-black/40 transition-colors duration-200 hover:border-orange-300/60 hover:bg-black/60"
                                                title={item.title}
                                                aria-label={item.title}
                                            >
                                                <img
                                                    src={item.icon}
                                                    alt={item.title}
                                                    className="size-4 object-contain"
                                                />
                                            </NavLink>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Stats */}
                        <div className="space-y-2">
                            <StatBar
                                label="HEALTH"
                                value={currentHealth}
                                max={maxHealth}
                                color="bg-gradient-to-r from-red-600 via-red-500 to-red-600"
                                iconSrc={hpImg}
                            />
                            <StatBar
                                label="ACTION POINTS"
                                value={currentUser.actionPoints || 0}
                                max={currentUser.maxActionPoints || 100}
                                color="bg-gradient-to-r from-yellow-500 via-yellow-400 to-orange-600"
                                iconSrc={apImg}
                            />
                            <StatBar
                                label="EXPERIENCE"
                                value={currentUser.xp || 0}
                                max={currentUser.xpForNextLevel || 100}
                                color="bg-gradient-to-r from-blue-600 via-blue-500 to-purple-600"
                                iconSrc={expImg}
                            />
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 gap-2 mt-3 mx-1">
                            <div className="bg-gradient-to-br from-emerald-950/80 via-green-900/60 to-slate-900/90 border border-green-400/50 p-2 relative overflow-hidden group hover:border-green-300/70 transition-all duration-300 shadow-lg shadow-black/50 backdrop-blur-sm">
                                {/* Minimal corner indicators */}
                                <div className="absolute top-0.5 left-0.5 w-2 h-2">
                                    <div className="absolute top-0 left-0 w-1 h-px bg-green-300/90" />
                                    <div className="absolute top-0 left-0 w-px h-1 bg-green-300/90" />
                                </div>
                                <div className="absolute top-0.5 right-0.5 w-2 h-2">
                                    <div className="absolute top-0 right-0 w-1 h-px bg-green-300/90" />
                                    <div className="absolute top-0 right-0 w-px h-1 bg-green-300/90" />
                                </div>

                                {/* Enhanced depth effects */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-green-500/5" />
                                <div className="absolute inset-0 bg-green-500/12 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                <div className="absolute inset-0 shadow-inner shadow-green-400/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <div className="relative z-10">
                                    {/* Horizontal layout - icon, label, and value in one line */}
                                    <div className="flex items-center gap-1.5">
                                        <div className="p-1 bg-gradient-to-br from-green-600/90 via-green-500/80 to-emerald-700/70 rounded border border-green-400/60 shadow-md shadow-green-500/20">
                                            <img
                                                src={yenImg}
                                                alt="Yen"
                                                className="w-4 h-4 object-contain brightness-90"
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-baseline justify-between">
                                                <span className="text-xs font-black text-green-200 uppercase tracking-[0.15em] drop-shadow-sm">
                                                    YEN
                                                </span>
                                                <p className="text-xs font-mono font-black text-white drop-shadow-md truncate">
                                                    ¥{(currentUser.cash || 0).toLocaleString()}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gradient-to-br from-blue-950/80 via-indigo-900/60 to-slate-900/90 border border-blue-400/50 p-2 relative overflow-hidden group hover:border-blue-300/70 transition-all duration-300 shadow-lg shadow-black/50 backdrop-blur-sm">
                                {/* Minimal corner indicators */}
                                <div className="absolute top-0.5 left-0.5 w-2 h-2">
                                    <div className="absolute top-0 left-0 w-1 h-px bg-blue-300/90" />
                                    <div className="absolute top-0 left-0 w-px h-1 bg-blue-300/90" />
                                </div>
                                <div className="absolute top-0.5 right-0.5 w-2 h-2">
                                    <div className="absolute top-0 right-0 w-1 h-px bg-blue-300/90" />
                                    <div className="absolute top-0 right-0 w-px h-1 bg-blue-300/90" />
                                </div>

                                {/* Enhanced depth effects */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-blue-500/5" />
                                <div className="absolute inset-0 bg-blue-500/12 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                <div className="absolute inset-0 shadow-inner shadow-blue-400/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <div className="relative z-10">
                                    {/* Horizontal layout - icon, label, and value in one line */}
                                    <div className="flex items-center gap-1.5">
                                        <div className="p-1 bg-gradient-to-br from-blue-600/90 via-blue-500/80 to-indigo-700/70 rounded border border-blue-400/60 shadow-md shadow-blue-500/20">
                                            <img
                                                src={respectImg}
                                                alt="Reputation"
                                                className="w-4 h-4 object-contain brightness-90"
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-baseline justify-between">
                                                <span className="text-xs font-black text-blue-200 uppercase tracking-[0.15em] drop-shadow-sm">
                                                    REP
                                                </span>
                                                <p className="text-xs font-mono font-black text-white drop-shadow-md">
                                                    {currentUser.gangCreds || 0}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {hasStatusEffects && (
                            <div className="relative mt-3 rounded-xl border border-red-500/40 bg-gradient-to-br from-red-950/40 via-slate-900/80 to-black/85 p-3 shadow-lg shadow-black/40">
                                <span className="-translate-x-1/2 absolute -top-3 left-1/2 rounded-md border border-red-500/50 bg-black/80 px-3 py-0.5 text-[10px] font-bold uppercase tracking-[0.3em] text-red-300 whitespace-nowrap">
                                    Status Effects
                                </span>

                                <StatusEffects currentEffects={statusEffects} className="justify-start gap-3" />
                            </div>
                        )}
                    </div>
                )}

                {/* Navigation */}
                <nav className="flex-1 p-4 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-600 scrollbar-track-black/50 bg-gradient-to-b from-transparent via-gray-800/20 to-black/40">
                    {/* Grid layout */}

                    <div className="space-y-2">
                        {navigation.map((item) => {
                            const isActive = location.pathname === item.href;

                            return (
                                <NavLink
                                    key={item.name}
                                    to={item.href}
                                    className={cn(
                                        "flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-300 group relative overflow-hidden text-sm",
                                        "transform-gpu",
                                        isActive
                                            ? "bg-gradient-to-r from-blue-600/90 via-blue-500/80 to-blue-600/90 text-white shadow-[0_4px_20px_rgba(59,130,246,0.5)] border border-blue-400/80"
                                            : "text-slate-300 hover:text-white border-slate-600/25 hover:border-blue-500/60 border"
                                    )}
                                    style={{
                                        boxShadow: isActive
                                            ? "inset 0 2px 4px rgba(255,255,255,0.15), inset 0 -2px 4px rgba(0,0,0,0.4), 0 4px 12px rgba(59,130,246,0.4)"
                                            : "inset 0 1px 3px rgba(255,255,255,0.08), inset 0 -1px 3px rgba(0,0,0,0.5), 0 2px 8px rgba(0,0,0,0.4)",
                                        background: isActive
                                            ? ""
                                            : "linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.98) 50%, rgba(30, 41, 59, 0.95) 100%)",
                                    }}
                                >
                                    {/* Premium glass effect overlay */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 rounded-xl transition-all duration-300",
                                            isActive
                                                ? "bg-gradient-to-tr from-blue-400/20 via-transparent to-cyan-300/10 opacity-100"
                                                : "bg-gradient-to-tr from-slate-600/20 via-transparent to-slate-700/10 opacity-100 group-hover:from-blue-600/15 group-hover:to-blue-700/10"
                                        )}
                                    />

                                    {/* Animated edge glow */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 rounded-xl transition-all duration-500",
                                            isActive && "animate-pulse"
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                "absolute inset-[2px] rounded-xl",
                                                isActive
                                                    ? "bg-gradient-to-r from-blue-400/30 via-cyan-400/20 to-blue-400/30"
                                                    : "bg-gradient-to-r from-transparent via-slate-600/10 to-transparent group-hover:from-blue-500/20 group-hover:via-cyan-500/15 group-hover:to-blue-500/20"
                                            )}
                                        />
                                    </div>

                                    {/* Icon container with 3D effect */}
                                    <div className="relative">
                                        <div
                                            className={cn(
                                                "p-1.5 rounded-lg transition-all duration-300 relative border",
                                                isActive
                                                    ? "bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 shadow-[inset_0_1px_2px_rgba(255,255,255,0.3),inset_0_-1px_2px_rgba(0,0,0,0.4)] border-blue-400/50"
                                                    : "bg-gradient-to-br from-slate-700/90 via-slate-800/95 to-slate-900 group-hover:from-blue-600/90 group-hover:via-blue-700/95 group-hover:to-blue-800 shadow-[inset_0_1px_1px_rgba(255,255,255,0.1),inset_0_-1px_1px_rgba(0,0,0,0.3)] border-slate-600/40 group-hover:border-blue-500/50"
                                            )}
                                        >
                                            {/* Inner glow */}
                                            <div
                                                className={cn(
                                                    "absolute inset-0 rounded-lg transition-opacity duration-300",
                                                    isActive
                                                        ? "bg-blue-400/30 blur-sm opacity-100"
                                                        : "bg-blue-400/0 blur-sm opacity-0 group-hover:bg-blue-400/20 group-hover:opacity-100"
                                                )}
                                            />

                                            <img
                                                src={item.icon}
                                                alt={item.name}
                                                className={cn(
                                                    "size-5 transition-all duration-300 object-contain relative z-10",
                                                    isActive
                                                        ? "brightness-110 drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)] saturate-125"
                                                        : "brightness-90 group-hover:brightness-110 group-hover:drop-shadow-[0_2px_4px_rgba(147,197,253,0.3)] saturate-75 group-hover:saturate-100"
                                                )}
                                            />
                                        </div>

                                        {/* Icon reflection */}
                                        <div
                                            className={cn(
                                                "absolute -bottom-1 left-1/2 -translate-x-1/2 w-4 h-2 rounded-full transition-all duration-300",
                                                isActive
                                                    ? "bg-blue-400/20 blur-md"
                                                    : "bg-slate-600/10 blur-sm group-hover:bg-blue-400/15 group-hover:blur-md"
                                            )}
                                        />
                                    </div>

                                    {/* Text with premium typography */}
                                    <span
                                        className={cn(
                                            "text-xs font-bold uppercase tracking-wider transition-all duration-300 relative z-10",
                                            isActive
                                                ? "text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)]"
                                                : "text-slate-400 group-hover:text-white group-hover:drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)]"
                                        )}
                                    >
                                        {item.name}
                                    </span>

                                    {/* Active indicator light */}
                                    {isActive && (
                                        <div className="absolute right-2 top-1/2 -translate-y-1/2">
                                            <div className="w-2 h-2 bg-cyan-400 rounded-full shadow-[0_0_10px_rgba(34,211,238,0.8)]">
                                                <div className="absolute inset-0 bg-cyan-400 rounded-full animate-ping opacity-75" />
                                            </div>
                                        </div>
                                    )}

                                    {/* Hover shine effect */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/0 to-transparent transition-all duration-700 pointer-events-none",
                                            "group-hover:via-white/8 group-hover:animate-shimmer"
                                        )}
                                    />

                                    {/* Top highlight for depth */}
                                    <div
                                        className={cn(
                                            "absolute top-0 left-3 right-3 h-px transition-opacity duration-300",
                                            isActive
                                                ? "bg-gradient-to-r from-transparent via-blue-300/50 to-transparent opacity-100"
                                                : "bg-gradient-to-r from-transparent via-slate-400/40 to-transparent opacity-100 group-hover:via-blue-400/50"
                                        )}
                                    />

                                    {/* Bottom shadow for depth */}
                                    <div
                                        className={cn(
                                            "absolute bottom-0 left-2 right-2 h-px transition-opacity duration-300",
                                            isActive
                                                ? "bg-gradient-to-r from-transparent via-blue-900/60 to-transparent opacity-100"
                                                : "bg-gradient-to-r from-transparent via-slate-900/80 to-transparent opacity-100 group-hover:via-blue-900/60"
                                        )}
                                    />
                                </NavLink>
                            );
                        })}
                    </div>
                </nav>

                {/* Footer */}
                <div className="p-4 border-t border-blue-600/50 bg-gradient-to-b from-gray-900/60 via-black to-gray-900 relative">
                    {/* Minimal top indicators */}
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 w-8 h-px bg-gradient-to-r from-transparent via-blue-400/80 to-transparent" />
                    <div className="absolute top-0 left-4 w-2 h-px bg-blue-400/60" />
                    <div className="absolute top-0 right-4 w-2 h-px bg-blue-400/60" />

                    {/* System Clock with External Links */}
                    <div className="bg-gradient-to-r from-slate-800/60 to-slate-900/60 border border-blue-500/30 px-3 py-1.5 mb-2 rounded-sm relative overflow-hidden group hover:border-blue-400/50 transition-all duration-200">
                        <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

                        <div className="flex items-center justify-between relative z-10">
                            <div className="flex items-center gap-1.5">
                                <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" />
                                <span className="text-xs font-bold text-blue-300 uppercase tracking-wider">
                                    SYSTEM TIME
                                </span>
                            </div>
                            <div className="flex items-center gap-2">
                                <p className="text-xs font-mono font-bold text-white">
                                    {format(new UTCDateMini(), timeFormat)}
                                </p>
                                <div className="flex items-center gap-1 border-l border-blue-500/30 pl-2">
                                    <Link to="discord" className="group">
                                        <div className="p-1 rounded bg-indigo-600/60 hover:bg-indigo-500/80 transition-all duration-200">
                                            <DiscordIcon className="w-3 h-3 text-white" />
                                        </div>
                                    </Link>
                                    <a
                                        href="https://chikaraacademymmo.wiki.gg/"
                                        target="_blank"
                                        rel="noreferrer"
                                        className="group"
                                    >
                                        <div className="p-1 rounded bg-blue-600/60 hover:bg-blue-500/80 transition-all duration-200">
                                            <img
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
                                                className="w-3 h-3"
                                                alt="Wiki"
                                            />
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Version info */}
                    <div className="text-center relative">
                        <div className="flex items-center justify-center gap-2 mb-1">
                            <div className="h-px flex-1 bg-gradient-to-r from-transparent to-blue-500/50" />
                            <div className="w-1.5 h-1.5 bg-blue-500/80 rounded-full" />
                            <div className="h-px flex-1 bg-gradient-to-l from-transparent to-blue-500/50" />
                        </div>
                        {isDev && showDevMenu && <DevMenu setShowDevMenu={setShowDevMenu} />}

                        <p
                            className={cn(
                                "text-[9px] font-black text-gray-500 uppercase tracking-[0.2em]",
                                isDev && "hover:text-purple-400 cursor-pointer"
                            )}
                            onClick={() => isDev && setShowDevMenu(!showDevMenu)}
                        >
                            CLOSED BETA {import.meta.env.VITE_PACKAGE_VERSION || "1.0.0"}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}

const GameSidebarHeader = () => {
    return (
        <div className="p-6 border-b border-blue-600/40 bg-gradient-to-b from-gray-900/95 via-gray-800/90 to-gray-900/95 relative">
            <div className="absolute top-3 left-3 w-4 h-4">
                <div className="absolute top-0 left-0 w-2 h-px bg-blue-400/80" />
                <div className="absolute top-0 left-0 w-px h-2 bg-blue-400/80" />
            </div>
            <div className="absolute top-3 right-3 w-4 h-4">
                <div className="absolute top-0 right-0 w-2 h-px bg-blue-400/80" />
                <div className="absolute top-0 right-0 w-px h-2 bg-blue-400/80" />
            </div>
            <div className="absolute bottom-3 left-3 w-4 h-4">
                <div className="absolute bottom-0 left-0 w-2 h-px bg-blue-400/80" />
                <div className="absolute bottom-0 left-0 w-px h-2 bg-blue-400/80" />
            </div>
            <div className="absolute bottom-3 right-3 w-4 h-4">
                <div className="absolute bottom-0 right-0 w-2 h-px bg-blue-400/80" />
                <div className="absolute bottom-0 right-0 w-px h-2 bg-blue-400/80" />
            </div>

            <div className="absolute left-0 top-1/2 -translate-y-1/2 w-px h-8 bg-gradient-to-b from-transparent via-blue-500/60 to-transparent" />
            <div className="absolute right-0 top-1/2 -translate-y-1/2 w-px h-8 bg-gradient-to-b from-transparent via-blue-500/60 to-transparent" />

            <div className="text-center relative z-10">
                <h1 className="text-4xl font-bold text-white tracking-wide">CHIKARA</h1>
                <div className="flex items-center justify-center gap-2">
                    <div className="h-0.5 flex-1 bg-gradient-to-r from-transparent to-blue-500/60" />
                    <p className="text-xs font-bold text-blue-300 uppercase tracking-wide px-2">ACADEMY</p>
                    <div className="h-0.5 flex-1 bg-gradient-to-l from-transparent to-blue-500/60" />
                </div>
            </div>
        </div>
    );
};
