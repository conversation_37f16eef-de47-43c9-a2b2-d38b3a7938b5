import { cn } from "@/lib/utils";
import { Fragment } from "react";

interface TabItem {
    value: string;
    label: string;
    icon: React.ReactNode;
    disabled?: boolean;
}

interface TabsProps {
    tabs: TabItem[];
    selectedTab: string;
    setSelectedTab: (value: string) => void;
}

export default function Tabs({ tabs, selectedTab, setSelectedTab }: TabsProps) {
    const handleTabsChange = (value: string) => {
        setSelectedTab(value);
    };

    return (
        <div className="flex flex-row px-[20px] pb-0">
            <div className="flex min-h-[48px]">
                <div className="relative mb-0 inline-block w-full flex-1 overflow-visible whitespace-nowrap">
                    <div className="flex" role="tablist">
                        {tabs.map((tab) => (
                            <Fragment key={tab.value}>
                                <Tab
                                    icon={tab.icon}
                                    label={tab.label}
                                    value={tab.value}
                                    isSelected={tab.value === selectedTab}
                                    handleTabsChange={handleTabsChange}
                                    disabled={tab.disabled}
                                />
                            </Fragment>
                        ))}
                    </div>
                    <span className="left-0 w-[113.453px]"></span>
                </div>
            </div>
        </div>
    );
}

interface TabProps {
    label: string;
    icon: React.ReactNode;
    isSelected?: boolean;
    value: string;
    handleTabsChange: (value: string) => void;
    disabled?: boolean;
}

const Tab = ({ label, icon, isSelected = false, value, handleTabsChange, disabled }: TabProps) => {
    return (
        <button
            tabIndex={0}
            type="button"
            role="tab"
            aria-selected={isSelected}
            className={cn(
                "flex-row! gap-3! flex text-white",
                isSelected ? "bg-[#1C2125]! testSelectedTab mb-[-0.5px]! overflow-visible!" : "testTab",
                disabled && "text-gray-500! cursor-not-allowed opacity-50"
            )}
            onClick={() => (disabled ? null : handleTabsChange(value))}
        >
            {icon} <span className="hidden lg:block">{label}</span>
        </button>
    );
};
