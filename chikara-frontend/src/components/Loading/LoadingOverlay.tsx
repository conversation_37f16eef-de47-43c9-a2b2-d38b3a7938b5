import React from "react";
import { createPortal } from "react-dom";
import { cn } from "@/lib/utils";
import { LoadingSpinner } from "./LoadingSpinner";

export interface LoadingOverlayProps {
    isVisible: boolean;
    message?: string;
    progress?: number;
    variant?: "spinner" | "dots" | "pulse";
    backdrop?: "blur" | "dark" | "light" | "none";
    size?: "sm" | "md" | "lg";
    className?: string;
    portal?: boolean;
    zIndex?: number;
}

/**
 * Loading overlay component that can be used to cover entire sections or the whole page
 */
export function LoadingOverlay({
    isVisible,
    message = "Loading...",
    progress,
    variant = "spinner",
    backdrop = "blur",
    size = "md",
    className,
    portal = false,
    zIndex = 50,
}: LoadingOverlayProps) {
    if (!isVisible) return null;
    
    const backdropClasses = {
        blur: "backdrop-blur-sm bg-white/80 dark:bg-gray-900/80",
        dark: "bg-black/50",
        light: "bg-white/90 dark:bg-gray-100/90",
        none: "bg-transparent",
    };
    
    const overlayContent = (
        <div
            style={{ zIndex }}
            className={cn(
                "fixed inset-0 flex items-center justify-center",
                backdropClasses[backdrop],
                className
            )}
        >
            <div className="flex flex-col items-center space-y-4 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-sm w-full mx-4">
                <LoadingSpinner
                    size={size === "sm" ? "md" : size === "md" ? "lg" : "xl"}
                    variant={variant}
                    color="primary"
                />
                
                {message && (
                    <p className="text-center text-gray-700 dark:text-gray-300 font-medium">
                        {message}
                    </p>
                )}
                
                {typeof progress === "number" && (
                    <div className="w-full">
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                            <span>Progress</span>
                            <span>{Math.round(progress)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
    
    if (portal) {
        return createPortal(overlayContent, document.body);
    }
    
    return overlayContent;
}

/**
 * Inline loading overlay for specific components
 */
export function InlineLoadingOverlay({
    isVisible,
    message,
    variant = "spinner",
    size = "md",
    className,
}: Pick<LoadingOverlayProps, "isVisible" | "message" | "variant" | "size" | "className">) {
    if (!isVisible) return null;
    
    return (
        <div className={cn(
            "absolute inset-0 flex items-center justify-center bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm",
            className
        )}>
            <div className="flex flex-col items-center space-y-3">
                <LoadingSpinner
                    size={size}
                    variant={variant}
                    color="primary"
                />
                {message && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                        {message}
                    </p>
                )}
            </div>
        </div>
    );
}

/**
 * Button loading overlay
 */
export function ButtonLoadingOverlay({
    isVisible,
    size = "sm",
    className,
}: Pick<LoadingOverlayProps, "isVisible" | "size" | "className">) {
    if (!isVisible) return null;
    
    return (
        <div className={cn(
            "absolute inset-0 flex items-center justify-center",
            className
        )}>
            <LoadingSpinner
                size={size}
                variant="spinner"
                color="primary"
            />
        </div>
    );
}

export default LoadingOverlay;
