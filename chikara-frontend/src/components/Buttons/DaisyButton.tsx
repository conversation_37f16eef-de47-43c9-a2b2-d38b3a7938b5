import React from "react";
import { cn } from "@/lib/utils";
import { type VariantProps, tv } from "tailwind-variants";

const daisyButtonVariants = tv({
    base: "btn",
    variants: {
        variant: {
            primary: "btn-primary",
            secondary: "btn-secondary",
            accent: "btn-accent",
            info: "btn-info",
            success: "btn-success",
            warning: "btn-warning",
            error: "btn-error",
            ghost: "btn-ghost",
            link: "btn-link",
            outline: "btn-outline",
            neutral: "btn-neutral",
        },
        size: {
            xs: "btn-xs",
            sm: "btn-sm",
            md: "",
            lg: "btn-lg",
        },
        fullWidth: {
            true: "btn-block",
        },
        disabled: {
            true: "btn-disabled",
        },
    },
    defaultVariants: {
        variant: "primary",
        size: "md",
        fullWidth: false,
    },
});

type DaisyButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
    VariantProps<typeof daisyButtonVariants> & {
        isLoading?: boolean;
        leftIcon?: React.ReactNode;
        rightIcon?: React.ReactNode;
        textSize?: string;
        fullWidth?: boolean;
    };

const DaisyButton = React.forwardRef<HTMLButtonElement, DaisyButtonProps>(
    (
        {
            variant,
            size,
            fullWidth,
            className,
            children,
            isLoading = false,
            leftIcon,
            rightIcon,
            disabled = false,
            onClick,
            textSize,
            ...props
        },
        ref
    ) => {
        const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
            if (disabled || isLoading) {
                e.preventDefault();
                return;
            }
            onClick?.(e);
        };

        return (
            <button
                ref={ref}
                disabled={disabled || isLoading}
                className={cn(
                    daisyButtonVariants({ variant, size, fullWidth, disabled: disabled || isLoading }),
                    textSize,
                    className
                )}
                onClick={handleClick}
                {...props}
            >
                {isLoading ? (
                    <div className="loading loading-spinner" />
                ) : (
                    <>
                        {leftIcon && <span className="mr-2">{leftIcon}</span>}
                        {children}
                        {rightIcon && <span className="ml-2">{rightIcon}</span>}
                    </>
                )}
            </button>
        );
    }
);

DaisyButton.displayName = "DaisyButton";
export default DaisyButton;
