import { cn } from "@/lib/utils";

interface PaginationProps {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
    itemName?: string;
    limitOptions?: number[];
}

export const Pagination = ({
    currentPage,
    totalPages,
    totalCount,
    limit,
    hasNextPage,
    hasPreviousPage,
    onPageChange,
    onLimitChange,
    itemName = "Items",
    limitOptions = [25, 50, 75, 100],
}: PaginationProps) => {
    const handleNextPage = () => {
        if (hasNextPage) {
            onPageChange(currentPage + 1);
        }
    };

    const handlePreviousPage = () => {
        if (hasPreviousPage) {
            onPageChange(currentPage - 1);
        }
    };

    const handleGoToPage = (targetPage: number) => {
        if (targetPage >= 1 && targetPage <= totalPages) {
            onPageChange(targetPage);
        }
    };

    return (
        <div className="bg-gradient-to-br from-slate-800/70 via-slate-900/85 to-black/50 rounded-xl border-2 border-blue-500/30 shadow-lg p-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                        <p className="text-sm font-bold text-blue-300 uppercase tracking-wider">
                            Page {currentPage} of {totalPages} ({totalCount} {itemName})
                        </p>
                    </div>

                    {/* Items per page dropdown */}
                    <div className="flex items-center gap-2">
                        <label className="text-xs font-semibold text-blue-300 uppercase tracking-wider">
                            Per Page:
                        </label>
                        <div className="dropdown dropdown-end dropdown-top">
                            <div
                                tabIndex={0}
                                role="button"
                                className="text-stroke-sm btn btn-sm bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 border border-slate-600/60 text-white text-xs font-semibold hover:from-blue-600/60 hover:via-blue-500/50 hover:to-blue-600/60 hover:border-blue-400/60 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/60"
                            >
                                {limit}
                                <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M19 9l-7 7-7-7"
                                    />
                                </svg>
                            </div>
                            <ul
                                tabIndex={0}
                                className="dropdown-content menu p-1 shadow-lg bg-gradient-to-br from-slate-800/95 via-slate-900/95 to-black/90 border border-blue-400/25 rounded-lg w-20 backdrop-blur-sm"
                            >
                                {limitOptions.map((value) => (
                                    <li key={value}>
                                        <a
                                            className={cn(
                                                "text-xs font-semibold transition-all duration-200 rounded",
                                                limit === value
                                                    ? "bg-blue-600/80 text-white shadow-md"
                                                    : "text-slate-300 hover:bg-blue-600/40 hover:text-white"
                                            )}
                                            onClick={() => onLimitChange(value)}
                                        >
                                            {value}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <button
                        disabled={!hasPreviousPage}
                        className={cn(
                            "px-3 py-2 rounded-lg font-bold text-xs uppercase tracking-wider transition-all duration-300",
                            "bg-gradient-to-r border shadow-md",
                            !hasPreviousPage
                                ? "from-slate-700 via-slate-600 to-slate-700 border-slate-600/60 text-slate-500 cursor-not-allowed opacity-50"
                                : "from-blue-600/80 via-blue-500/70 to-blue-600/80 border-blue-400/60 text-white hover:from-blue-600 hover:via-blue-500 hover:to-blue-600 hover:border-blue-400/80 hover:shadow-blue-500/30 active:scale-95"
                        )}
                        onClick={handlePreviousPage}
                    >
                        ← Previous
                    </button>

                    {/* Page Numbers */}
                    <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNumber;
                            if (totalPages <= 5) {
                                pageNumber = i + 1;
                            } else if (currentPage <= 3) {
                                pageNumber = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                                pageNumber = totalPages - 4 + i;
                            } else {
                                pageNumber = currentPage - 2 + i;
                            }

                            const isActive = pageNumber === currentPage;
                            return (
                                <button
                                    key={pageNumber}
                                    className={cn(
                                        "px-3 py-2 rounded-lg font-bold text-xs transition-all duration-300",
                                        "bg-gradient-to-r border shadow-md",
                                        isActive
                                            ? "from-blue-600 via-blue-500 to-blue-600 border-blue-400/80 text-white shadow-blue-500/30"
                                            : "from-slate-700/80 via-slate-600/70 to-slate-700/80 border-slate-600/60 text-slate-300 hover:from-blue-600/60 hover:via-blue-500/50 hover:to-blue-600/60 hover:border-blue-400/60 hover:text-white"
                                    )}
                                    onClick={() => handleGoToPage(pageNumber)}
                                >
                                    {pageNumber}
                                </button>
                            );
                        })}
                    </div>

                    <button
                        disabled={!hasNextPage}
                        className={cn(
                            "px-3 py-2 rounded-lg font-bold text-xs uppercase tracking-wider transition-all duration-300",
                            "bg-gradient-to-r border shadow-md",
                            !hasNextPage
                                ? "from-slate-700 via-slate-600 to-slate-700 border-slate-600/60 text-slate-500 cursor-not-allowed opacity-50"
                                : "from-blue-600/80 via-blue-500/70 to-blue-600/80 border-blue-400/60 text-white hover:from-blue-600 hover:via-blue-500 hover:to-blue-600 hover:border-blue-400/80 hover:shadow-blue-500/30 active:scale-95"
                        )}
                        onClick={handleNextPage}
                    >
                        Next →
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Pagination;
