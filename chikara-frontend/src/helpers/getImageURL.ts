import defaultGangIcon from "@/assets/icons/navitems/gang.png";
import defaultUserAvatar from "@/assets/images/defaultAvatar.png";

const CDN_URL = import.meta.env.VITE_IMAGE_CDN_URL || import.meta.env.VITE_API_BASE_URL;
const DEFAULT_GANG_ICON = defaultGangIcon || `https://cloudflare-image.jamessut.workers.dev/ui-images/OMun8OF.png`;

export const resolveUserAvatar = (avatar?: string | null) => {
    if (avatar) {
        if (avatar.startsWith("http")) {
            return avatar;
        }
        return `${CDN_URL}/${avatar}`;
    }

    return defaultUserAvatar;
};

export const resolveGangAvatar = (avatar?: string | null): string => {
    if (avatar) {
        if (avatar.startsWith("http")) {
            return avatar;
        }
        return `${CDN_URL}/${avatar}`;
    } else {
        return DEFAULT_GANG_ICON;
    }
};
